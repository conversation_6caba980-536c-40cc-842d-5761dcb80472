# 🚀 CRM AI - Painel de Agentes de IA

Um painel de CRM moderno e responsivo para gerenciar agentes de IA criados no n8n, com foco em atendimento automatizado via WhatsApp, Instagram, Site e outros canais.

## 🌐 **DEMO AO VIVO**
**🔗 https://crm-ai-modern.surge.sh**

## 🖥️ **LOCALHOST PARA TESTES**
```bash
npm run dev
# Acesse: http://localhost:5173
```

## ✨ **Características Principais**

### 🎨 **UI/UX Moderna (10x Melhorada)**
- **Design System** com Tailwind CSS v4
- **Animações Suaves** com Framer Motion
- **Tema Dark/Light** com toggle automático
- **Micro-interações** em todos os componentes
- **Glassmorphism** e efeitos visuais modernos
- **Gradientes** e sombras sofisticadas

### 📱 **Responsividade Total**
- **Mobile First** com navegação inferior
- **Sidebar Animada** para desktop
- **Cards Adaptativos** que se ajustam perfeitamente
- **Breakpoints Inteligentes** (xs, sm, md, lg, xl)
- **Touch Gestures** otimizados

### 📊 **Dashboard Avançado**
- **8 KPIs Principais**: CPL, CAC, CPC, Taxa de Conversão, Receita por Agente
- **Gráficos Interativos**: Pizza, linha e barras
- **Quick Stats** com animações
- **Performance Metrics** em tempo real
- **Filtros por Período** e canal

### 🤖 **Gestão de Agentes**
- **Cards Visuais** para cada agente
- **Status em Tempo Real** (online/offline)
- **Métricas de Performance** individuais
- **Ações Rápidas** (configurar, ativar/desativar)
- **Filtros Avançados** por canal e status

### 💬 **Sistema de Conversas**
- **Modal Interativo** com detalhes completos
- **Histórico de Mensagens** formatado
- **Ações Rápidas**: WhatsApp, agendamento, oportunidades
- **Classificação de Leads** (quente/morno/frio)
- **Integração n8n** preparada

## 🛠️ **Tecnologias**

- **React 18** com Hooks modernos
- **Framer Motion** para animações
- **Tailwind CSS v4** para styling
- **Lucide React** para ícones
- **Recharts** para gráficos
- **Date-fns** para datas
- **Axios** para APIs
- **Vite** para build e desenvolvimento

## 📦 **Instalação e Uso**

### 1. **Clone e Instale**
```bash
git clone <url-do-repositorio>
cd CRM
npm install
```

### 2. **Desenvolvimento**
```bash
npm run dev
# Acesse: http://localhost:5173
```

### 3. **Build para Produção**
```bash
npm run build
```

### 4. **Deploy**
```bash
npm run deploy
```

## 🔧 **Configuração do n8n**

### Webhook de Entrada
Configure um webhook no n8n para receber dados do CRM:
```
POST https://your-n8n-instance.com/webhook/crm-data
```

### Estrutura de Dados Esperada

**Agentes:**
```json
{
  "id": 1,
  "name": "Bot WhatsApp Vendas",
  "channel": "WhatsApp",
  "leadsAttended": 156,
  "activeConversations": 23,
  "responseRate": 94.2,
  "status": "online",
  "lastActivity": "2024-01-15T10:30:00Z"
}
```

**KPIs:**
```json
{
  "cpl": 45.50,
  "cac": 180.00,
  "cpc": 2.30,
  "conversionRate": 12.5,
  "revenuePerAgent": 15420.00,
  "activeLeads": 234,
  "totalConversations": 1847,
  "qualifiedConversations": 456
}
```

## 🎨 **Personalização**

### Cores do Tema
Edite as variáveis em `tailwind.config.js`:
```javascript
colors: {
  primary: {
    500: '#3b82f6', // Azul principal
    600: '#2563eb', // Azul escuro
  }
}
```

### Adicionando Novos Canais
1. Adicione o ícone em `src/utils/formatters.js`
2. Configure a cor em `getStatusColor()`
3. Atualize os dados mock se necessário

## 🔌 **Integrações**

### WhatsApp Business API
```javascript
export const sendWhatsAppMessage = async (phone, message) => {
  // Implementação da API do WhatsApp
};
```

### Calendário (Google Calendar, Outlook)
```javascript
export const scheduleCall = async (leadId, datetime, notes) => {
  // Integração com calendário
};
```

## 📊 **Analytics e Relatórios**

- **Filtros por Período**: Análise de performance em datas específicas
- **Exportação CSV**: Dados dos agentes e conversas
- **Gráficos Interativos**: Hover e zoom para análise detalhada
- **Métricas Comparativas**: Tendências e variações percentuais

## 🚀 **Deploy**

### Surge.sh (Atual)
```bash
npm run build
npx surge dist/ your-domain.surge.sh
```

### Vercel
```bash
npm install -g vercel
vercel --prod
```

### Netlify
```bash
npm run build
# Faça upload da pasta dist/
```

## 📱 **Funcionalidades Mobile**

- **Navegação Inferior**: Ícones para Dashboard, Agentes, Conversas, Analytics
- **Cards Responsivos**: Adaptam automaticamente ao tamanho da tela
- **Modais Otimizados**: Interface touch-friendly
- **Gestos Intuitivos**: Swipe e tap otimizados

## 🔗 **Links Importantes**

- **🌐 Demo Live**: https://crm-ai-modern.surge.sh
- **🖥️ Localhost**: http://localhost:5173
- **📚 Documentação n8n**: `docs/n8n-integration.md`
- **🎨 Design System**: `src/components/ui/`

---

**Desenvolvido com ❤️ para otimizar seu atendimento automatizado**

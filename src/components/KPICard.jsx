import React from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { formatCurrency, formatNumber, formatPercentage } from '../utils/formatters';

const KPICard = ({ 
  title, 
  value, 
  type = 'number', 
  trend = null, 
  trendValue = null,
  icon: Icon,
  color = 'blue',
  loading = false,
  subtitle = null,
  onClick = null
}) => {
  // Formata o valor baseado no tipo
  const formatValue = (val, valueType) => {
    if (val === null || val === undefined) return '-';
    
    switch (valueType) {
      case 'currency':
        return formatCurrency(val);
      case 'percentage':
        return formatPercentage(val);
      case 'number':
        return formatNumber(val);
      default:
        return val.toString();
    }
  };

  // Define a cor do trend
  const getTrendColor = (trendDirection) => {
    switch (trendDirection) {
      case 'up':
        return 'var(--success)';
      case 'down':
        return 'var(--danger)';
      case 'neutral':
        return 'var(--gray-500)';
      default:
        return 'var(--gray-500)';
    }
  };

  // Define o ícone do trend
  const getTrendIcon = (trendDirection) => {
    switch (trendDirection) {
      case 'up':
        return TrendingUp;
      case 'down':
        return TrendingDown;
      case 'neutral':
        return Minus;
      default:
        return null;
    }
  };

  // Define as cores baseadas no tipo
  const getColorClasses = (colorType) => {
    const colors = {
      blue: {
        icon: 'var(--primary-blue)',
        bg: 'var(--secondary-blue)'
      },
      green: {
        icon: 'var(--success)',
        bg: '#dcfce7'
      },
      yellow: {
        icon: 'var(--warning)',
        bg: '#fef3c7'
      },
      red: {
        icon: 'var(--danger)',
        bg: '#fee2e2'
      },
      gray: {
        icon: 'var(--gray-600)',
        bg: 'var(--gray-100)'
      }
    };

    return colors[colorType] || colors.blue;
  };

  const colorClasses = getColorClasses(color);
  const TrendIcon = getTrendIcon(trend);

  if (loading) {
    return (
      <div className="card">
        <div className="card-body">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-gray-200 rounded-lg animate-pulse"></div>
            <div className="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="w-24 h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
          <div className="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`card ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}`}
      onClick={onClick}
    >
      <div className="card-body">
        {/* Header com ícone e trend */}
        <div className="flex items-center justify-between mb-4">
          {Icon && (
            <div 
              className="w-12 h-12 rounded-lg flex items-center justify-center"
              style={{ 
                backgroundColor: colorClasses.bg,
                color: colorClasses.icon 
              }}
            >
              <Icon size={24} />
            </div>
          )}
          
          {trend && trendValue && (
            <div 
              className="flex items-center gap-1 text-sm font-medium"
              style={{ color: getTrendColor(trend) }}
            >
              {TrendIcon && <TrendIcon size={16} />}
              <span>{formatPercentage(Math.abs(trendValue))}</span>
            </div>
          )}
        </div>

        {/* Valor principal */}
        <div className="mb-2">
          <div className="text-2xl font-bold text-gray-900">
            {formatValue(value, type)}
          </div>
        </div>

        {/* Título e subtítulo */}
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-1">
            {title}
          </h3>
          {subtitle && (
            <p className="text-xs text-gray-500">
              {subtitle}
            </p>
          )}
        </div>

        {/* Descrição do trend */}
        {trend && trendValue && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            <p className="text-xs text-gray-500">
              {trend === 'up' && 'Aumento de '}
              {trend === 'down' && 'Redução de '}
              {trend === 'neutral' && 'Estável '}
              {trendValue && formatPercentage(Math.abs(trendValue))}
              {' vs. período anterior'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

// Componente para skeleton loading de múltiplos KPIs
export const KPICardSkeleton = ({ count = 4 }) => {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <KPICard key={index} loading={true} />
      ))}
    </>
  );
};

// Componente para grid de KPIs
export const KPIGrid = ({ kpis, loading = false, className = '' }) => {
  if (loading) {
    return (
      <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
        <KPICardSkeleton count={4} />
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
      {kpis.map((kpi, index) => (
        <KPICard key={index} {...kpi} />
      ))}
    </div>
  );
};

export default KPICard;

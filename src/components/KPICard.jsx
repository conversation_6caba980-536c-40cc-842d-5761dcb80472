import React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { formatCurrency, formatNumber, formatPercentage } from '../utils/formatters';
import { cn } from '../utils/cn';
import Card from './ui/Card';
import { SkeletonKPI } from './ui/Skeleton';

const KPICard = ({
  title,
  value,
  type = 'number',
  trend = null,
  trendValue = null,
  icon: Icon,
  color = 'blue',
  loading = false,
  subtitle = null,
  onClick = null,
  className,
  ...props
}) => {
  // Formata o valor baseado no tipo
  const formatValue = (val, valueType) => {
    if (val === null || val === undefined) return '-';

    switch (valueType) {
      case 'currency':
        return formatCurrency(val);
      case 'percentage':
        return formatPercentage(val);
      case 'number':
        return formatNumber(val);
      default:
        return val.toString();
    }
  };

  // Define o ícone do trend
  const getTrendIcon = (trendDirection) => {
    switch (trendDirection) {
      case 'up':
        return TrendingUp;
      case 'down':
        return TrendingDown;
      case 'neutral':
        return Minus;
      default:
        return null;
    }
  };

  // Define as cores baseadas no tipo
  const getColorConfig = (colorType) => {
    const colors = {
      blue: {
        icon: 'text-primary-600 dark:text-primary-400',
        bg: 'bg-primary-50 dark:bg-primary-900/20',
        trend: {
          up: 'text-success-600 bg-success-50 dark:bg-success-900/20',
          down: 'text-danger-600 bg-danger-50 dark:bg-danger-900/20',
          neutral: 'text-gray-600 bg-gray-50 dark:bg-gray-900/20'
        }
      },
      green: {
        icon: 'text-success-600 dark:text-success-400',
        bg: 'bg-success-50 dark:bg-success-900/20',
        trend: {
          up: 'text-success-600 bg-success-50 dark:bg-success-900/20',
          down: 'text-danger-600 bg-danger-50 dark:bg-danger-900/20',
          neutral: 'text-gray-600 bg-gray-50 dark:bg-gray-900/20'
        }
      },
      yellow: {
        icon: 'text-warning-600 dark:text-warning-400',
        bg: 'bg-warning-50 dark:bg-warning-900/20',
        trend: {
          up: 'text-success-600 bg-success-50 dark:bg-success-900/20',
          down: 'text-danger-600 bg-danger-50 dark:bg-danger-900/20',
          neutral: 'text-gray-600 bg-gray-50 dark:bg-gray-900/20'
        }
      },
      red: {
        icon: 'text-danger-600 dark:text-danger-400',
        bg: 'bg-danger-50 dark:bg-danger-900/20',
        trend: {
          up: 'text-success-600 bg-success-50 dark:bg-success-900/20',
          down: 'text-danger-600 bg-danger-50 dark:bg-danger-900/20',
          neutral: 'text-gray-600 bg-gray-50 dark:bg-gray-900/20'
        }
      },
      gray: {
        icon: 'text-gray-600 dark:text-gray-400',
        bg: 'bg-gray-50 dark:bg-gray-900/20',
        trend: {
          up: 'text-success-600 bg-success-50 dark:bg-success-900/20',
          down: 'text-danger-600 bg-danger-50 dark:bg-danger-900/20',
          neutral: 'text-gray-600 bg-gray-50 dark:bg-gray-900/20'
        }
      }
    };

    return colors[colorType] || colors.blue;
  };

  const colorConfig = getColorConfig(color);
  const TrendIcon = getTrendIcon(trend);

  if (loading) {
    return <SkeletonKPI className={className} />;
  }

  return (
    <Card
      variant={onClick ? 'interactive' : 'default'}
      hover={!!onClick}
      onClick={onClick}
      className={cn('group', className)}
      {...props}
    >
      {/* Header com ícone e trend */}
      <div className="flex items-center justify-between mb-6">
        {Icon && (
          <motion.div
            className={cn(
              'w-14 h-14 rounded-2xl flex items-center justify-center',
              colorConfig.bg,
              colorConfig.icon,
              'group-hover:scale-110 transition-transform duration-200'
            )}
            whileHover={{ rotate: 5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <Icon size={28} />
          </motion.div>
        )}

        {trend && trendValue && (
          <motion.div
            className={cn(
              'flex items-center gap-1.5 px-2.5 py-1.5 rounded-full text-sm font-medium',
              colorConfig.trend[trend]
            )}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            {TrendIcon && <TrendIcon size={14} />}
            <span>{formatPercentage(Math.abs(trendValue))}</span>
          </motion.div>
        )}
      </div>

      {/* Valor principal */}
      <motion.div
        className="mb-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1 gradient-text">
          {formatValue(value, type)}
        </div>
      </motion.div>

      {/* Título e subtítulo */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-1">
          {title}
        </h3>
        {subtitle && (
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {subtitle}
          </p>
        )}
      </motion.div>

      {/* Descrição do trend */}
      {trend && trendValue && (
        <motion.div
          className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          <p className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
            <span className={cn(
              'w-2 h-2 rounded-full',
              trend === 'up' && 'bg-success-500',
              trend === 'down' && 'bg-danger-500',
              trend === 'neutral' && 'bg-gray-400'
            )} />
            {trend === 'up' && 'Aumento de '}
            {trend === 'down' && 'Redução de '}
            {trend === 'neutral' && 'Estável '}
            {trendValue && formatPercentage(Math.abs(trendValue))}
            {' vs. período anterior'}
          </p>
        </motion.div>
      )}
    </Card>
  );
};

// Componente para skeleton loading de múltiplos KPIs
export const KPICardSkeleton = ({ count = 4, className }) => {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonKPI key={index} className={className} />
      ))}
    </>
  );
};

// Componente para grid de KPIs
export const KPIGrid = ({ kpis, loading = false, className = '' }) => {
  if (loading) {
    return (
      <motion.div
        className={cn('grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6', className)}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <KPICardSkeleton count={4} />
      </motion.div>
    );
  }

  return (
    <motion.div
      className={cn('grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6 lg:gap-8', className)}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {kpis.map((kpi, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1, duration: 0.5 }}
        >
          <KPICard {...kpi} />
        </motion.div>
      ))}
    </motion.div>
  );
};

export default KPICard;

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Save, 
  User, 
  MessageSquare, 
  Settings, 
  Power,
  Edit3,
  Trash2,
  Copy
} from 'lucide-react';
import Button from './ui/Button';
import Card from './ui/Card';
import Badge, { StatusBadge } from './ui/Badge';
import { cn } from '../utils/cn';
import { getChannelIcon } from '../utils/formatters';

const AgentEditModal = ({ agent, isOpen, onClose, onSave, onDelete }) => {
  const [editedAgent, setEditedAgent] = useState(agent || {});
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  if (!isOpen || !agent) return null;

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave?.(editedAgent);
      setIsEditing(false);
    } catch (error) {
      console.error('Erro ao salvar agente:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Tem certeza que deseja excluir este agente?')) {
      try {
        await onDelete?.(agent.id);
        onClose();
      } catch (error) {
        console.error('Erro ao excluir agente:', error);
      }
    }
  };

  const handleDuplicate = () => {
    const duplicatedAgent = {
      ...editedAgent,
      id: Date.now(),
      name: `${editedAgent.name} (Cópia)`,
      status: 'offline'
    };
    onSave?.(duplicatedAgent);
  };

  const channelIcon = getChannelIcon(editedAgent.channel);

  return (
    <AnimatePresence>
      <motion.div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-2xl shadow-lg">
                {channelIcon}
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                  {editedAgent.name}
                </h2>
                <div className="flex items-center gap-2 mt-1">
                  <StatusBadge status={editedAgent.status} size="sm" />
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {editedAgent.channel}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsEditing(!isEditing)}
                leftIcon={<Edit3 size={16} />}
              >
                {isEditing ? 'Cancelar' : 'Editar'}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
              >
                <X size={20} />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Informações Básicas */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                <User size={20} />
                Informações Básicas
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nome do Agente
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editedAgent.name || ''}
                      onChange={(e) => setEditedAgent({...editedAgent, name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  ) : (
                    <p className="text-gray-900 dark:text-white font-medium">{editedAgent.name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Canal
                  </label>
                  {isEditing ? (
                    <select
                      value={editedAgent.channel || ''}
                      onChange={(e) => setEditedAgent({...editedAgent, channel: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="WhatsApp">WhatsApp</option>
                      <option value="Instagram">Instagram</option>
                      <option value="Facebook">Facebook</option>
                      <option value="Site">Site</option>
                      <option value="Telegram">Telegram</option>
                      <option value="Email">Email</option>
                    </select>
                  ) : (
                    <p className="text-gray-900 dark:text-white font-medium">{editedAgent.channel}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Status
                  </label>
                  {isEditing ? (
                    <select
                      value={editedAgent.status || ''}
                      onChange={(e) => setEditedAgent({...editedAgent, status: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="online">Online</option>
                      <option value="offline">Offline</option>
                      <option value="busy">Ocupado</option>
                      <option value="away">Ausente</option>
                    </select>
                  ) : (
                    <StatusBadge status={editedAgent.status} />
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Taxa de Resposta
                  </label>
                  {isEditing ? (
                    <input
                      type="number"
                      min="0"
                      max="100"
                      step="0.1"
                      value={editedAgent.responseRate || ''}
                      onChange={(e) => setEditedAgent({...editedAgent, responseRate: parseFloat(e.target.value)})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  ) : (
                    <p className="text-gray-900 dark:text-white font-medium">{editedAgent.responseRate}%</p>
                  )}
                </div>
              </div>
            </Card>

            {/* Métricas de Performance */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                <MessageSquare size={20} />
                Métricas de Performance
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {editedAgent.leadsAttended || 0}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Leads Atendidos</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {editedAgent.activeConversations || 0}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Conversas Ativas</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {editedAgent.responseRate || 0}%
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Taxa de Resposta</div>
                </div>
              </div>
            </Card>

            {/* Configurações Avançadas */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                <Settings size={20} />
                Configurações Avançadas
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Auto-resposta</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Responder automaticamente às mensagens</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" defaultChecked />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">Notificações</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Receber notificações de novas mensagens</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" defaultChecked />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>
            </Card>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDuplicate}
                leftIcon={<Copy size={16} />}
              >
                Duplicar
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDelete}
                leftIcon={<Trash2 size={16} />}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                Excluir
              </Button>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="secondary"
                onClick={onClose}
              >
                Cancelar
              </Button>
              {isEditing && (
                <Button
                  variant="primary"
                  onClick={handleSave}
                  loading={isSaving}
                  leftIcon={<Save size={16} />}
                >
                  Salvar Alterações
                </Button>
              )}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default AgentEditModal;

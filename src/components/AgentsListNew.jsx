import React, { useState, useEffect } from 'react';
import { Search, Filter, Plus, Users, RefreshCw, Download } from 'lucide-react';
import Layout from './Layout';
import { AgentsGrid } from './AgentCardNew';
import Button from './ui/Button';
import ConversationModal from './ConversationModal';
import AgentEditModal from './AgentEditModal';
import { getAgents, getConversations, updateAgentStatus } from '../services/api';

const AgentsList = () => {
  const [agents, setAgents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [channelFilter, setChannelFilter] = useState('all');
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [showConversationModal, setShowConversationModal] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadAgents();
  }, []);

  const loadAgents = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await getAgents();
      setAgents(data);
    } catch (err) {
      console.error('Erro ao carregar agentes:', err);
      setError('Erro ao carregar agentes. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = async (agent) => {
    try {
      const conversations = await getConversations(agent.id);
      if (conversations && conversations.length > 0) {
        setSelectedConversation(conversations[0]);
        setShowConversationModal(true);
      } else {
        alert('Nenhuma conversa encontrada para este agente.');
      }
    } catch (err) {
      console.error('Erro ao carregar conversas:', err);
      alert('Erro ao carregar conversas.');
    }
  };

  const handleToggleStatus = async (agent) => {
    try {
      const newStatus = agent.status === 'online' ? 'offline' : 'online';
      await updateAgentStatus(agent.id, newStatus);
      
      setAgents(prevAgents =>
        prevAgents.map(a =>
          a.id === agent.id ? { ...a, status: newStatus } : a
        )
      );
    } catch (err) {
      console.error('Erro ao atualizar status:', err);
      alert('Erro ao atualizar status do agente.');
    }
  };

  const handleSettings = (agent) => {
    console.log('Configurações do agente:', agent);
    alert(`Configurações do ${agent.name} - Em desenvolvimento`);
  };

  const handleEdit = (agent) => {
    setSelectedAgent(agent);
    setShowEditModal(true);
  };

  const handleSaveAgent = async (editedAgent) => {
    try {
      setAgents(prevAgents =>
        prevAgents.map(a =>
          a.id === editedAgent.id ? editedAgent : a
        )
      );
      
      setShowEditModal(false);
      setSelectedAgent(null);
    } catch (err) {
      console.error('Erro ao salvar agente:', err);
      alert('Erro ao salvar agente.');
    }
  };

  const handleDeleteAgent = async (agentId) => {
    try {
      setAgents(prevAgents =>
        prevAgents.filter(a => a.id !== agentId)
      );
      
      setShowEditModal(false);
      setSelectedAgent(null);
    } catch (err) {
      console.error('Erro ao deletar agente:', err);
      alert('Erro ao deletar agente.');
    }
  };

  const exportAgentsData = () => {
    const csvContent = "data:text/csv;charset=utf-8," 
      + "Nome,Canal,Status,Leads Atendidos,Conversas Ativas,Taxa de Resposta\n"
      + agents.map(agent => 
          `${agent.name},${agent.channel},${agent.status},${agent.leadsAttended},${agent.activeConversations},${agent.responseRate}%`
        ).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "agentes_crm.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getUniqueChannels = () => {
    return [...new Set(agents.map(agent => agent.channel))];
  };

  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.channel.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || agent.status === statusFilter;
    const matchesChannel = channelFilter === 'all' || agent.channel === channelFilter;
    
    return matchesSearch && matchesStatus && matchesChannel;
  });

  const stats = {
    total: agents.length,
    online: agents.filter(a => a.status === 'online').length,
    totalConversations: agents.reduce((sum, a) => sum + a.activeConversations, 0),
    avgResponseRate: agents.length > 0 
      ? (agents.reduce((sum, a) => sum + a.responseRate, 0) / agents.length).toFixed(1)
      : 0
  };

  return (
    <Layout>
      {/* Header */}
      <Layout.Header
        title="Agentes de IA"
        subtitle="Gerencie e monitore seus agentes de atendimento automatizado"
        actions={[
          <Button
            key="refresh"
            variant="secondary"
            size="sm"
            onClick={loadAgents}
            disabled={loading}
            leftIcon={<RefreshCw size={16} className={loading ? 'animate-spin' : ''} />}
          >
            Atualizar
          </Button>,
          <Button
            key="export"
            variant="secondary"
            size="sm"
            onClick={exportAgentsData}
            leftIcon={<Download size={16} />}
          >
            Exportar
          </Button>,
          <Button
            key="new"
            variant="primary"
            size="sm"
            leftIcon={<Plus size={16} />}
          >
            Novo Agente
          </Button>
        ]}
      />

      {/* Stats */}
      <Layout.Section>
        <Layout.Grid cols={4}>
          <Layout.Card className="text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
              {stats.total}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Total de Agentes
            </div>
          </Layout.Card>
          
          <Layout.Card className="text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
              {stats.online}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Online
            </div>
          </Layout.Card>
          
          <Layout.Card className="text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">
              {stats.totalConversations}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Conversas Ativas
            </div>
          </Layout.Card>
          
          <Layout.Card className="text-center">
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-1">
              {stats.avgResponseRate}%
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Taxa Média
            </div>
          </Layout.Card>
        </Layout.Grid>
      </Layout.Section>

      {/* Filters */}
      <Layout.Section>
        <Layout.Card>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar agentes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">Todos os Status</option>
              <option value="online">Online</option>
              <option value="offline">Offline</option>
            </select>

            {/* Channel Filter */}
            <select
              value={channelFilter}
              onChange={(e) => setChannelFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">Todos os Canais</option>
              {getUniqueChannels().map(channel => (
                <option key={channel} value={channel}>{channel}</option>
              ))}
            </select>

            {/* Clear Filters */}
            <Button
              variant="secondary"
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('all');
                setChannelFilter('all');
              }}
              leftIcon={<Filter size={16} />}
            >
              Limpar Filtros
            </Button>
          </div>
        </Layout.Card>
      </Layout.Section>

      {/* Agents Grid */}
      <Layout.Section>
        {error ? (
          <div className="text-center py-8">
            <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
            <Button onClick={loadAgents} variant="primary">
              Tentar Novamente
            </Button>
          </div>
        ) : (
          <AgentsGrid 
            agents={filteredAgents}
            loading={loading}
            onViewDetails={handleViewDetails}
            onToggleStatus={handleToggleStatus}
            onSettings={handleSettings}
            onEdit={handleEdit}
          />
        )}
      </Layout.Section>

      {/* Modals */}
      <ConversationModal
        conversation={selectedConversation}
        isOpen={showConversationModal}
        onClose={() => {
          setShowConversationModal(false);
          setSelectedConversation(null);
        }}
      />

      <AgentEditModal
        agent={selectedAgent}
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setSelectedAgent(null);
        }}
        onSave={handleSaveAgent}
        onDelete={handleDeleteAgent}
      />
    </Layout>
  );
};

export default AgentsList;

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  DollarSign,
  Users,
  TrendingUp,
  Briefcase,
  Calendar,
  RefreshCw,
  MoreHorizontal,
  ArrowUp,
  ArrowDown,
  Clock,
  Target,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { cn } from '../utils/cn';

const ModernDashboard = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [loading, setLoading] = useState(false);

  const kpiData = [
    {
      title: 'Vendas do Mês',
      value: 'R$ 3.248.500',
      change: '+12%',
      trend: 'up',
      subtitle: 'desde o mês passado',
      icon: DollarSign,
      color: 'green'
    },
    {
      title: 'Novos Leads',
      value: '48',
      change: '+12%',
      trend: 'up',
      subtitle: 'desde o mês passado',
      icon: Users,
      color: 'blue'
    },
    {
      title: 'Taxa de Conversão',
      value: '28.5%',
      change: '+5%',
      trend: 'up',
      subtitle: 'desde o mês passado',
      icon: TrendingUp,
      color: 'orange'
    },
    {
      title: 'Projetos Ativos',
      value: '24',
      change: '+8%',
      trend: 'up',
      subtitle: 'desde o mês passado',
      icon: Briefcase,
      color: 'purple'
    }
  ];

  const performanceData = {
    conversionRate: 24.8,
    previousRate: 26.1,
    leadsGenerated: 129,
    leadsChange: '+8.4%',
    metrics: [
      { label: 'Leads (100%)', value: 129, percentage: 100, color: 'bg-blue-500' },
      { label: 'Contatos (87%)', value: 112, percentage: 87, color: 'bg-purple-500' },
      { label: 'Propostas (52%)', value: 67, percentage: 52, color: 'bg-pink-500' },
      { label: 'Vendas (24.8%)', value: 32, percentage: 24.8, color: 'bg-green-500' }
    ],
    costs: {
      costPerLead: 'R$ 58,30',
      costChange: '-2.1%',
      avgTime: '18 dias',
      timeChange: '-3.4%',
      cac: 'R$ 235,15',
      cacChange: '+1.3%'
    }
  };

  const tasks = [
    {
      id: 1,
      title: 'Finalizar projeto da cozinha',
      status: 'em andamento',
      assignee: 'João Silva',
      priority: 'high',
      dueDate: '24/05',
      progress: 60
    },
    {
      id: 2,
      title: 'Orçamento para cliente pendente',
      status: 'pendente',
      assignee: 'Maria Oliveira',
      priority: 'medium',
      dueDate: '24/05'
    },
    {
      id: 3,
      title: 'Agendar medição para próxima semana',
      status: 'atrasada',
      assignee: 'Pedro Santos',
      priority: 'high',
      dueDate: '23/05'
    },
    {
      id: 4,
      title: 'Orçamento para cliente pendente',
      status: 'pendente',
      assignee: 'Maria Oliveira',
      priority: 'low',
      dueDate: '24/05'
    },
    {
      id: 5,
      title: 'Finalizar projeto da cozinha',
      status: 'progresso',
      assignee: 'Ana Lima',
      priority: 'medium',
      dueDate: '25/05',
      progress: 60
    }
  ];

  const waitingClients = [
    { name: 'Cliente 1', time: '2h', status: 'waiting' },
    { name: 'Cliente 2', time: '4h', status: 'waiting' },
    { name: 'Cliente 3', time: '1d', status: 'waiting' }
  ];

  const KPICard = ({ item }) => {
    const colorClasses = {
      green: 'from-green-500 to-green-600',
      blue: 'from-blue-500 to-blue-600',
      orange: 'from-orange-500 to-orange-600',
      purple: 'from-purple-500 to-purple-600'
    };

    return (
      <motion.div
        className="bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-gray-600 transition-all duration-200"
        whileHover={{ y: -2 }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-start justify-between mb-4">
          <div className={cn(
            "w-12 h-12 rounded-lg bg-gradient-to-br flex items-center justify-center",
            colorClasses[item.color]
          )}>
            <item.icon size={24} className="text-white" />
          </div>
          <div className={cn(
            "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
            item.trend === 'up' 
              ? 'bg-green-500/10 text-green-400' 
              : 'bg-red-500/10 text-red-400'
          )}>
            {item.trend === 'up' ? <ArrowUp size={12} /> : <ArrowDown size={12} />}
            {item.change}
          </div>
        </div>
        
        <div className="text-2xl font-bold text-white mb-1">
          {item.value}
        </div>
        
        <div className="text-gray-400 text-sm">
          {item.title}
        </div>
        
        <div className="text-gray-500 text-xs mt-1">
          {item.subtitle}
        </div>
      </motion.div>
    );
  };

  const TaskCard = ({ task }) => {
    const statusColors = {
      'em andamento': 'bg-blue-500/10 text-blue-400 border-blue-500/20',
      'pendente': 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20',
      'atrasada': 'bg-red-500/10 text-red-400 border-red-500/20',
      'progresso': 'bg-green-500/10 text-green-400 border-green-500/20'
    };

    const priorityColors = {
      'high': 'bg-red-500',
      'medium': 'bg-yellow-500',
      'low': 'bg-green-500'
    };

    return (
      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            <div className={cn("w-2 h-2 rounded-full", priorityColors[task.priority])}></div>
            <span className="text-white text-sm font-medium">{task.title}</span>
          </div>
          <span className="text-gray-400 text-xs">{task.dueDate}</span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className={cn(
            "px-2 py-1 rounded-full text-xs border",
            statusColors[task.status]
          )}>
            {task.status}
          </span>
          <span className="text-gray-400 text-xs">{task.assignee}</span>
        </div>
        
        {task.progress && (
          <div className="mt-3">
            <div className="flex items-center justify-between mb-1">
              <span className="text-gray-400 text-xs">Progresso</span>
              <span className="text-white text-xs font-medium">{task.progress}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${task.progress}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Olá, Usuário</h1>
            <p className="text-gray-400">Sábado, 24 de maio de 2025</p>
          </div>
          
          <div className="flex items-center gap-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="week">Semana</option>
              <option value="month">Mês</option>
              <option value="quarter">Trimestre</option>
              <option value="year">Ano</option>
            </select>
            
            <button
              onClick={() => setLoading(!loading)}
              className="bg-gray-800 border border-gray-700 rounded-lg p-2 hover:bg-gray-700 transition-colors"
            >
              <RefreshCw size={16} className={cn("text-gray-400", loading && "animate-spin")} />
            </button>
          </div>
        </div>

        {/* KPIs */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {kpiData.map((item, index) => (
            <KPICard key={index} item={item} />
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Performance Analysis */}
          <div className="lg:col-span-2 bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">Análise de Performance</h2>
              <div className="flex items-center gap-2">
                <button className="px-3 py-1 bg-blue-600 text-white rounded-lg text-sm">Conversão</button>
                <button className="px-3 py-1 bg-gray-700 text-gray-300 rounded-lg text-sm">Vendas</button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <div className="text-3xl font-bold text-white mb-1">
                  {performanceData.conversionRate}%
                </div>
                <div className="text-gray-400 text-sm mb-4">Taxa de Conversão</div>
                <div className="text-red-400 text-sm">
                  -3.1% vs. mês anterior
                </div>
              </div>
              
              <div>
                <div className="text-3xl font-bold text-white mb-1">
                  {performanceData.leadsGenerated}
                </div>
                <div className="text-gray-400 text-sm mb-4">Leads Gerados</div>
                <div className="text-green-400 text-sm">
                  {performanceData.leadsChange}
                </div>
              </div>
            </div>
            
            {/* Funnel */}
            <div className="space-y-3">
              {performanceData.metrics.map((metric, index) => (
                <div key={index} className="flex items-center gap-4">
                  <div className="w-24 text-gray-400 text-sm">{metric.label}</div>
                  <div className="flex-1 bg-gray-700 rounded-full h-3 relative overflow-hidden">
                    <div 
                      className={cn("h-full rounded-full transition-all duration-500", metric.color)}
                      style={{ width: `${metric.percentage}%` }}
                    ></div>
                  </div>
                  <div className="w-8 text-white text-sm font-medium">{metric.value}</div>
                </div>
              ))}
            </div>
            
            {/* Cost Metrics */}
            <div className="grid grid-cols-3 gap-4 mt-6 pt-6 border-t border-gray-700">
              <div className="text-center">
                <div className="text-lg font-bold text-white">{performanceData.costs.costPerLead}</div>
                <div className="text-gray-400 text-xs">Custo por Lead</div>
                <div className="text-green-400 text-xs">{performanceData.costs.costChange}</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-white">{performanceData.costs.avgTime}</div>
                <div className="text-gray-400 text-xs">Tempo Médio</div>
                <div className="text-green-400 text-xs">{performanceData.costs.timeChange}</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-white">{performanceData.costs.cac}</div>
                <div className="text-gray-400 text-xs">CAC</div>
                <div className="text-red-400 text-xs">{performanceData.costs.cacChange}</div>
              </div>
            </div>
          </div>

          {/* Tasks */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">Cronograma de Tarefas</h2>
              <button className="text-blue-400 text-sm hover:text-blue-300">Ver tudo</button>
            </div>
            
            <div className="space-y-4">
              {tasks.slice(0, 5).map((task) => (
                <TaskCard key={task.id} task={task} />
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
          {/* Closed Sales */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white">Vendas Fechadas</h2>
              <button className="text-blue-400 text-sm hover:text-blue-300">Ver tudo</button>
            </div>
            <div className="text-gray-400 text-sm">Nenhuma venda fechada hoje</div>
          </div>

          {/* Waiting Clients */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white">Clientes Aguardando Resposta</h2>
              <button className="text-blue-400 text-sm hover:text-blue-300">Ver todos</button>
            </div>
            <div className="space-y-3">
              {waitingClients.map((client, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                  <span className="text-white text-sm">{client.name}</span>
                  <span className="text-orange-400 text-sm">{client.time}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernDashboard;

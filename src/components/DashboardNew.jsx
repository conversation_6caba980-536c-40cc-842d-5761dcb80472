import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Bot, 
  Calendar, 
  RefreshCw, 
  ArrowRight,
  DollarSign,
  Target,
  MousePointer,
  Percent
} from 'lucide-react';
import Layout from './Layout';
import KPI from './KPI';
import { AgentsGrid } from './AgentCard';
import Button from './ui/Button';
import { ChartsGrid } from './Charts';
import { getKPIs, getAgents, getChartData } from '../services/api';
import { formatCurrency, formatNumber, formatPercentage } from '../utils/formatters';
import { useNavigate } from 'react-router-dom';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [kpis, setKpis] = useState({});
  const [agents, setAgents] = useState([]);
  const [chartData, setChartData] = useState({});
  const navigate = useNavigate();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const [kpiData, agentsData, chartsData] = await Promise.all([
        getKPIs(),
        getAgents(),
        getChartData()
      ]);
      
      setKpis(kpiData);
      setAgents(agentsData);
      setChartData(chartsData);
    } catch (error) {
      console.error('Erro ao carregar dados do dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const kpiData = [
    {
      title: 'Custo por Lead (CPL)',
      value: formatCurrency(kpis.cpl || 0),
      trend: 'down',
      trendValue: 5.2,
      icon: DollarSign,
      color: 'blue'
    },
    {
      title: 'Custo de Aquisição (CAC)',
      value: formatCurrency(kpis.cac || 0),
      trend: 'down',
      trendValue: 8.1,
      icon: Target,
      color: 'green'
    },
    {
      title: 'Custo por Clique (CPC)',
      value: formatCurrency(kpis.cpc || 0),
      trend: 'up',
      trendValue: 2.3,
      icon: MousePointer,
      color: 'purple'
    },
    {
      title: 'Taxa de Conversão',
      value: formatPercentage(kpis.conversionRate || 0),
      trend: 'up',
      trendValue: 12.5,
      icon: Percent,
      color: 'orange'
    }
  ];

  const handleViewDetails = (agent) => {
    console.log('Ver detalhes do agente:', agent);
  };

  const handleToggleStatus = (agent) => {
    console.log('Toggle status do agente:', agent);
  };

  const handleSettings = (agent) => {
    console.log('Configurações do agente:', agent);
  };

  return (
    <Layout>
      {/* Header */}
      <Layout.Header
        title="Dashboard CRM"
        subtitle="Visão geral do desempenho dos seus agentes de IA"
        actions={[
          <Button
            key="period"
            variant="secondary"
            size="sm"
            leftIcon={<Calendar size={16} />}
          >
            Últimos 30 dias
          </Button>,
          <Button
            key="refresh"
            variant="ghost"
            size="sm"
            leftIcon={<RefreshCw size={16} className={loading ? 'animate-spin' : ''} />}
            onClick={loadDashboardData}
            disabled={loading}
          >
            Atualizar
          </Button>
        ]}
      />

      {/* KPIs */}
      <Layout.Section title="Indicadores Principais" icon={BarChart3}>
        <Layout.Grid cols={4}>
          {kpiData.map((kpi, index) => (
            <KPI
              key={index}
              {...kpi}
              loading={loading}
            />
          ))}
        </Layout.Grid>
      </Layout.Section>

      {/* Charts */}
      <Layout.Section title="Análise de Performance" icon={TrendingUp}>
        <ChartsGrid chartData={chartData} loading={loading} />
      </Layout.Section>

      {/* Agents */}
      <Layout.Section 
        title="Agentes de IA" 
        icon={Bot}
      >
        <div className="flex items-center justify-between mb-4">
          <div></div>
          <Button
            variant="ghost"
            size="sm"
            rightIcon={<ArrowRight size={16} />}
            onClick={() => navigate('/agents')}
          >
            Ver todos
          </Button>
        </div>
        
        <Layout.Grid cols={3}>
          {agents.slice(0, 3).map((agent) => (
            <Layout.Card key={agent.id} hover>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <Bot size={18} className="text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
                    {agent.name}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {agent.channel}
                  </p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-3 mb-3">
                <div>
                  <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                    {formatNumber(agent.leadsAttended)}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Leads</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-green-600 dark:text-green-400">
                    {formatNumber(agent.activeConversations)}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Conversas</div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <div className={`w-2 h-2 rounded-full ${
                    agent.status === 'online' ? 'bg-green-500' : 'bg-gray-400'
                  }`}></div>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {agent.status === 'online' ? 'Online' : 'Offline'}
                  </span>
                </div>
                <div className="text-sm font-semibold text-gray-900 dark:text-white">
                  {formatPercentage(agent.responseRate)}
                </div>
              </div>
            </Layout.Card>
          ))}
        </Layout.Grid>
      </Layout.Section>

      {/* Quick Stats */}
      <Layout.Section>
        <Layout.Grid cols={3}>
          <Layout.Card className="text-center">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Users size={24} className="text-blue-600 dark:text-blue-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
              {loading ? '-' : formatNumber(kpis.activeLeads || 0)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Leads Ativos
            </div>
          </Layout.Card>

          <Layout.Card className="text-center">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mx-auto mb-3">
              <TrendingUp size={24} className="text-green-600 dark:text-green-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
              {loading ? '-' : formatNumber(kpis.totalConversations || 0)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Total de Conversas
            </div>
          </Layout.Card>

          <Layout.Card className="text-center">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Target size={24} className="text-purple-600 dark:text-purple-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
              {loading ? '-' : formatCurrency(kpis.revenuePerAgent || 0)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Receita por Agente
            </div>
          </Layout.Card>
        </Layout.Grid>
      </Layout.Section>
    </Layout>
  );
};

export default Dashboard;

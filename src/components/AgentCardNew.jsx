import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  MessageCircle, 
  Users, 
  TrendingUp, 
  Clock, 
  MoreVertical,
  Eye,
  Settings,
  Power,
  Edit3
} from 'lucide-react';
import Layout from './Layout';
import Button from './ui/Button';
import { cn } from '../utils/cn';
import { getChannelIcon, formatNumber, formatPercentage, formatRelativeTime } from '../utils/formatters';

const AgentCard = ({ 
  agent, 
  onViewDetails, 
  onToggleStatus, 
  onSettings,
  onEdit,
  loading = false 
}) => {
  const [showMenu, setShowMenu] = useState(false);

  if (loading) {
    return (
      <Layout.Card>
        <div className="animate-pulse">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            <div>
              <div className="w-24 h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
              <div className="w-16 h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="w-full h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="w-full h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
          <div className="w-full h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </Layout.Card>
    );
  }

  const {
    id,
    name,
    channel,
    status,
    leadsAttended,
    activeConversations,
    responseRate,
    lastActivity
  } = agent;

  const channelIcon = getChannelIcon(channel);
  const statusColor = status === 'online' ? 'bg-green-500' : 'bg-gray-400';

  return (
    <Layout.Card hover className="relative">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center text-lg">
              {channelIcon}
            </div>
            <div 
              className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800 ${statusColor}`}
            ></div>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
              {name}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {channel}
            </p>
          </div>
        </div>

        {/* Menu */}
        <div className="relative">
          <button 
            onClick={() => setShowMenu(!showMenu)}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
          >
            <MoreVertical size={16} className="text-gray-400" />
          </button>

          {showMenu && (
            <div className="absolute right-0 top-8 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-10 min-w-[120px]">
              <button 
                onClick={() => {
                  onEdit?.(agent);
                  setShowMenu(false);
                }}
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300"
              >
                <Edit3 size={14} />
                Editar
              </button>
              <button 
                onClick={() => {
                  onViewDetails?.(agent);
                  setShowMenu(false);
                }}
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300"
              >
                <Eye size={14} />
                Ver detalhes
              </button>
              <button 
                onClick={() => {
                  onSettings?.(agent);
                  setShowMenu(false);
                }}
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300"
              >
                <Settings size={14} />
                Configurar
              </button>
              <button 
                onClick={() => {
                  onToggleStatus?.(agent);
                  setShowMenu(false);
                }}
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300"
              >
                <Power size={14} />
                {status === 'online' ? 'Desativar' : 'Ativar'}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Metrics */}
      <div className="grid grid-cols-2 gap-3 mb-4">
        <div className="text-center p-2 bg-blue-50 dark:bg-blue-900/10 rounded-lg">
          <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
            {formatNumber(leadsAttended)}
          </div>
          <div className="text-xs text-blue-600 dark:text-blue-400">Leads</div>
        </div>
        <div className="text-center p-2 bg-green-50 dark:bg-green-900/10 rounded-lg">
          <div className="text-lg font-bold text-green-600 dark:text-green-400">
            {formatNumber(activeConversations)}
          </div>
          <div className="text-xs text-green-600 dark:text-green-400">Conversas</div>
        </div>
      </div>

      {/* Response Rate */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs text-gray-600 dark:text-gray-400">Taxa de Resposta</span>
          <span className="text-sm font-semibold text-gray-900 dark:text-white">
            {formatPercentage(responseRate)}
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className="h-2 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300"
            style={{ width: `${Math.min(responseRate, 100)}%` }}
          ></div>
        </div>
      </div>

      {/* Last Activity */}
      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400">
          <Clock size={12} />
          <span>Última atividade</span>
        </div>
        <span className="text-gray-600 dark:text-gray-300">
          {formatRelativeTime(lastActivity)}
        </span>
      </div>

      {/* Action Button */}
      <div className="mt-4">
        <Button
          variant="primary"
          size="sm"
          onClick={() => onViewDetails?.(agent)}
          className="w-full"
        >
          Ver Conversas
        </Button>
      </div>
    </Layout.Card>
  );
};

const AgentsGrid = ({ 
  agents, 
  loading = false, 
  onViewDetails,
  onToggleStatus,
  onSettings,
  onEdit,
  className = '' 
}) => {
  if (loading) {
    return (
      <Layout.Grid cols={3} className={className}>
        {[...Array(6)].map((_, index) => (
          <AgentCard key={index} loading={true} />
        ))}
      </Layout.Grid>
    );
  }

  return (
    <Layout.Grid cols={3} className={className}>
      {agents.map((agent) => (
        <AgentCard 
          key={agent.id} 
          agent={agent}
          onViewDetails={onViewDetails}
          onToggleStatus={onToggleStatus}
          onSettings={onSettings}
          onEdit={onEdit}
        />
      ))}
    </Layout.Grid>
  );
};

export { AgentsGrid };
export default AgentCard;

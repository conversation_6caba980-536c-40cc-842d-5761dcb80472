import React from 'react';
import { useLocation, Link } from 'react-router-dom';
import { 
  BarChart3, 
  Users, 
  MessageSquare, 
  Settings,
  Home
} from 'lucide-react';

const MobileNavigation = () => {
  const location = useLocation();

  const navItems = [
    {
      path: '/',
      icon: Home,
      label: 'Dashboard',
      exact: true
    },
    {
      path: '/agents',
      icon: Users,
      label: 'Agent<PERSON>'
    },
    {
      path: '/conversations',
      icon: MessageSquare,
      label: 'Conversas'
    },
    {
      path: '/analytics',
      icon: BarChart3,
      label: 'Analytics'
    },
    {
      path: '/settings',
      icon: Settings,
      label: 'Config'
    }
  ];

  const isActive = (path, exact = false) => {
    if (exact) {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  return (
    <nav className="mobile-nav">
      <div className="flex justify-around items-center">
        {navItems.map(({ path, icon: Icon, label, exact }) => (
          <Link
            key={path}
            to={path}
            className={`mobile-nav-item ${isActive(path, exact) ? 'active' : ''}`}
          >
            <Icon size={20} />
            <span>{label}</span>
          </Link>
        ))}
      </div>
    </nav>
  );
};

export default MobileNavigation;

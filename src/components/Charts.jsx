import React from 'react';
import {
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Legend
} from 'recharts';
import { formatCurrency, formatNumber } from '../utils/formatters';

// Componente de Tooltip customizado
const CustomTooltip = ({ active, payload, label, type = 'default' }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        {label && (
          <p className="text-sm font-medium text-gray-900 mb-1">
            {label}
          </p>
        )}
        {payload.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {`${entry.name}: `}
            {type === 'currency' && formatCurrency(entry.value)}
            {type === 'number' && formatNumber(entry.value)}
            {type === 'percentage' && `${entry.value}%`}
            {type === 'default' && entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// Gráfico de Pizza para conversões por canal
export const ConversionByChannelChart = ({ data, loading = false }) => {
  if (loading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Conversões por Canal</h3>
        </div>
        <div className="card-body">
          <div className="h-64 bg-gray-100 rounded-lg animate-pulse"></div>
        </div>
      </div>
    );
  }

  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Conversões por Canal</h3>
      </div>
      <div className="card-body">
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color || COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip type="percentage" />} />
          </PieChart>
        </ResponsiveContainer>
        
        {/* Legenda customizada */}
        <div className="mt-4 grid grid-cols-2 gap-2">
          {data.map((entry, index) => (
            <div key={entry.name} className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color || COLORS[index % COLORS.length] }}
              ></div>
              <span className="text-sm text-gray-600">{entry.name}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Gráfico de Linha para receita ao longo do tempo
export const RevenueOverTimeChart = ({ data, loading = false }) => {
  if (loading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Receita ao Longo do Tempo</h3>
        </div>
        <div className="card-body">
          <div className="h-64 bg-gray-100 rounded-lg animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Receita ao Longo do Tempo</h3>
      </div>
      <div className="card-body">
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis 
              dataKey="month" 
              stroke="#64748b"
              fontSize={12}
            />
            <YAxis 
              stroke="#64748b"
              fontSize={12}
              tickFormatter={(value) => formatCurrency(value, { showSymbol: false })}
            />
            <Tooltip content={<CustomTooltip type="currency" />} />
            <Line 
              type="monotone" 
              dataKey="revenue" 
              stroke="#3b82f6" 
              strokeWidth={3}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

// Gráfico de Barras para leads ao longo do tempo
export const LeadsOverTimeChart = ({ data, loading = false }) => {
  if (loading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Leads ao Longo do Tempo</h3>
        </div>
        <div className="card-body">
          <div className="h-64 bg-gray-100 rounded-lg animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Leads ao Longo do Tempo</h3>
      </div>
      <div className="card-body">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis 
              dataKey="month" 
              stroke="#64748b"
              fontSize={12}
            />
            <YAxis 
              stroke="#64748b"
              fontSize={12}
            />
            <Tooltip content={<CustomTooltip type="number" />} />
            <Bar 
              dataKey="leads" 
              fill="#10b981"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

// Gráfico combinado para comparação
export const CombinedChart = ({ revenueData, leadsData, loading = false }) => {
  if (loading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold">Performance Geral</h3>
        </div>
        <div className="card-body">
          <div className="h-64 bg-gray-100 rounded-lg animate-pulse"></div>
        </div>
      </div>
    );
  }

  // Combina os dados de receita e leads
  const combinedData = revenueData.map((item, index) => ({
    month: item.month,
    revenue: item.revenue,
    leads: leadsData[index]?.leads || 0
  }));

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold">Performance Geral</h3>
      </div>
      <div className="card-body">
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={combinedData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis 
              dataKey="month" 
              stroke="#64748b"
              fontSize={12}
            />
            <YAxis 
              yAxisId="left"
              stroke="#64748b"
              fontSize={12}
              tickFormatter={(value) => formatCurrency(value, { showSymbol: false })}
            />
            <YAxis 
              yAxisId="right" 
              orientation="right"
              stroke="#64748b"
              fontSize={12}
            />
            <Tooltip 
              content={({ active, payload, label }) => {
                if (active && payload && payload.length) {
                  return (
                    <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
                      <p className="text-sm font-medium text-gray-900 mb-1">{label}</p>
                      <p className="text-sm" style={{ color: '#3b82f6' }}>
                        Receita: {formatCurrency(payload[0]?.value)}
                      </p>
                      <p className="text-sm" style={{ color: '#10b981' }}>
                        Leads: {formatNumber(payload[1]?.value)}
                      </p>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Legend />
            <Line 
              yAxisId="left"
              type="monotone" 
              dataKey="revenue" 
              stroke="#3b82f6" 
              strokeWidth={3}
              name="Receita"
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
            />
            <Line 
              yAxisId="right"
              type="monotone" 
              dataKey="leads" 
              stroke="#10b981" 
              strokeWidth={3}
              name="Leads"
              dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

// Componente wrapper para todos os gráficos
export const ChartsGrid = ({ chartData, loading = false }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <ConversionByChannelChart 
        data={chartData?.conversionByChannel || []} 
        loading={loading} 
      />
      <RevenueOverTimeChart 
        data={chartData?.revenueOverTime || []} 
        loading={loading} 
      />
      <div className="lg:col-span-2">
        <CombinedChart 
          revenueData={chartData?.revenueOverTime || []}
          leadsData={chartData?.leadsOverTime || []}
          loading={loading} 
        />
      </div>
    </div>
  );
};

export default {
  ConversionByChannelChart,
  RevenueOverTimeChart,
  LeadsOverTimeChart,
  CombinedChart,
  ChartsGrid
};

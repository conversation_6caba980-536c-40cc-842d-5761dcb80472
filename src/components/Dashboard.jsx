import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  DollarSign,
  Users,
  MousePointer,
  TrendingUp,
  MessageCircle,
  Target,
  Activity,
  Award,
  RefreshCw,
  Calendar,
  BarChart3
} from 'lucide-react';
import KPICard, { KPIGrid } from './KPICard';
import { ChartsGrid } from './Charts';
import { AgentsGrid } from './AgentCard';
import Button from './ui/Button';
import Card from './ui/Card';
import { getKPIs, getAgents, getChartData } from '../services/api';
import { formatCurrency, formatNumber, formatPercentage } from '../utils/formatters';
import { cn } from '../utils/cn';

const Dashboard = () => {
  const [kpis, setKpis] = useState(null);
  const [agents, setAgents] = useState([]);
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Carrega dados em paralelo
      const [kpisData, agentsData, chartsData] = await Promise.all([
        getKPIs(),
        getAgents(),
        getChartData()
      ]);

      setKpis(kpisData);
      setAgents(agentsData);
      setChartData(chartsData);
    } catch (err) {
      console.error('Erro ao carregar dados do dashboard:', err);
      setError('Erro ao carregar dados. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  // Prepara dados dos KPIs para o componente
  const kpiData = kpis ? [
    {
      title: 'Custo por Lead (CPL)',
      value: kpis.cpl,
      type: 'currency',
      icon: DollarSign,
      color: 'blue',
      trend: 'down',
      trendValue: 5.2,
      subtitle: 'Redução de 5.2% vs. mês anterior'
    },
    {
      title: 'Custo de Aquisição (CAC)',
      value: kpis.cac,
      type: 'currency',
      icon: Target,
      color: 'green',
      trend: 'down',
      trendValue: 8.1,
      subtitle: 'Otimização de 8.1% no período'
    },
    {
      title: 'Custo por Clique (CPC)',
      value: kpis.cpc,
      type: 'currency',
      icon: MousePointer,
      color: 'yellow',
      trend: 'up',
      trendValue: 2.3,
      subtitle: 'Aumento de 2.3% vs. período anterior'
    },
    {
      title: 'Taxa de Conversão',
      value: kpis.conversionRate,
      type: 'percentage',
      icon: TrendingUp,
      color: 'green',
      trend: 'up',
      trendValue: 12.5,
      subtitle: 'Melhoria significativa de 12.5%'
    },
    {
      title: 'Receita por Agente',
      value: kpis.revenuePerAgent,
      type: 'currency',
      icon: Award,
      color: 'blue',
      trend: 'up',
      trendValue: 15.8,
      subtitle: 'Crescimento de 15.8% na produtividade'
    },
    {
      title: 'Leads Ativos',
      value: kpis.activeLeads,
      type: 'number',
      icon: Users,
      color: 'green',
      trend: 'up',
      trendValue: 7.2,
      subtitle: 'Aumento de 7.2% em leads qualificados'
    },
    {
      title: 'Total de Conversas',
      value: kpis.totalConversations,
      type: 'number',
      icon: MessageCircle,
      color: 'blue',
      trend: 'up',
      trendValue: 18.9,
      subtitle: 'Crescimento de 18.9% no engajamento'
    },
    {
      title: 'Conversas Qualificadas',
      value: kpis.qualifiedConversations,
      type: 'number',
      icon: Activity,
      color: 'green',
      trend: 'up',
      trendValue: 22.1,
      subtitle: 'Melhoria de 22.1% na qualificação'
    }
  ] : [];

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <Card className="max-w-md w-full text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="w-16 h-16 bg-danger-100 dark:bg-danger-900/20 rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <Activity size={32} className="text-danger-500" />
          </motion.div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Erro ao carregar dashboard
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">{error}</p>
          <Button
            onClick={loadDashboardData}
            leftIcon={<RefreshCw size={16} />}
            className="w-full"
          >
            Tentar novamente
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-6 lg:px-8 py-6">
        {/* Header */}
        <motion.div
          className="mb-10"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="space-y-2">
              <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white gradient-text">
                Dashboard CRM
              </h1>
              <p className="text-gray-600 dark:text-gray-400 text-base lg:text-lg">
                Visão geral do desempenho dos seus agentes de IA
              </p>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="secondary"
                size="sm"
                leftIcon={<Calendar size={16} />}
                className="whitespace-nowrap"
              >
                Últimos 30 dias
              </Button>
              <Button
                variant="ghost"
                size="sm"
                leftIcon={<RefreshCw size={16} className={loading ? 'animate-spin' : ''} />}
                onClick={loadDashboardData}
                disabled={loading}
              >
                Atualizar
              </Button>
            </div>
          </div>
        </motion.div>

        {/* KPIs Grid */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <div className="flex items-center gap-3 mb-8">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
              <BarChart3 size={22} className="text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Indicadores Principais
            </h2>
          </div>
          <KPIGrid kpis={kpiData} loading={loading} />
        </motion.div>

        {/* Charts Section */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="w-8 h-8 bg-success-100 dark:bg-success-900/20 rounded-lg flex items-center justify-center">
              <TrendingUp size={20} className="text-success-600 dark:text-success-400" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
              Análise de Performance
            </h2>
          </div>
          <ChartsGrid chartData={chartData} loading={loading} />
        </motion.div>

        {/* Agents Overview */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-warning-100 dark:bg-warning-900/20 rounded-lg flex items-center justify-center">
                <Users size={20} className="text-warning-600 dark:text-warning-400" />
              </div>
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
                Agentes de IA
              </h2>
            </div>
            <Button variant="outline" size="sm">
              Ver Todos
            </Button>
          </div>

          {/* Mostra apenas os primeiros 3 agentes no dashboard */}
          <AgentsGrid
            agents={agents.slice(0, 3)}
            loading={loading}
            onViewDetails={(agent) => {
              console.log('Ver detalhes do agente:', agent);
              // Aqui você implementaria a navegação ou modal
            }}
            onToggleStatus={(agent) => {
              console.log('Toggle status do agente:', agent);
              // Aqui você implementaria a mudança de status
            }}
            onSettings={(agent) => {
              console.log('Configurações do agente:', agent);
              // Aqui você implementaria as configurações
            }}
          />
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <Card className="text-center group hover:shadow-glow transition-all duration-300">
            <motion.div
              className="w-14 h-14 bg-primary-100 dark:bg-primary-900/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200"
              whileHover={{ rotate: 5 }}
            >
              <MessageCircle size={28} className="text-primary-600 dark:text-primary-400" />
            </motion.div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
              {loading ? '-' : formatNumber(kpis?.totalConversations || 0)}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Conversas Hoje</p>
          </Card>

          <Card className="text-center group hover:shadow-glow-success transition-all duration-300">
            <motion.div
              className="w-14 h-14 bg-success-100 dark:bg-success-900/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200"
              whileHover={{ rotate: 5 }}
            >
              <TrendingUp size={28} className="text-success-600 dark:text-success-400" />
            </motion.div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
              {loading ? '-' : formatPercentage(kpis?.conversionRate || 0)}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Taxa de Conversão</p>
          </Card>

          <Card className="text-center group hover:shadow-glow-warning transition-all duration-300">
            <motion.div
              className="w-14 h-14 bg-warning-100 dark:bg-warning-900/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200"
              whileHover={{ rotate: 5 }}
            >
              <Award size={28} className="text-warning-600 dark:text-warning-400" />
            </motion.div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
              {loading ? '-' : formatCurrency(kpis?.revenuePerAgent || 0)}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Receita por Agente</p>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard;

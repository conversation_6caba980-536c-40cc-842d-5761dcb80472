import React, { useState, useEffect } from 'react';
import { 
  DollarSign, 
  Users, 
  MousePointer, 
  TrendingUp, 
  MessageCircle,
  Target,
  Activity,
  Award
} from 'lucide-react';
import KPICard, { KPIGrid } from './KPICard';
import { ChartsGrid } from './Charts';
import { AgentsGrid } from './AgentCard';
import { getKPIs, getAgents, getChartData } from '../services/api';
import { formatCurrency, formatNumber, formatPercentage } from '../utils/formatters';

const Dashboard = () => {
  const [kpis, setKpis] = useState(null);
  const [agents, setAgents] = useState([]);
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Carrega dados em paralelo
      const [kpisData, agentsData, chartsData] = await Promise.all([
        getKPIs(),
        getAgents(),
        getChartData()
      ]);

      setKpis(kpisData);
      setAgents(agentsData);
      setChartData(chartsData);
    } catch (err) {
      console.error('Erro ao carregar dados do dashboard:', err);
      setError('Erro ao carregar dados. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  // Prepara dados dos KPIs para o componente
  const kpiData = kpis ? [
    {
      title: 'Custo por Lead (CPL)',
      value: kpis.cpl,
      type: 'currency',
      icon: DollarSign,
      color: 'blue',
      trend: 'down',
      trendValue: 5.2,
      subtitle: 'Redução de 5.2% vs. mês anterior'
    },
    {
      title: 'Custo de Aquisição (CAC)',
      value: kpis.cac,
      type: 'currency',
      icon: Target,
      color: 'green',
      trend: 'down',
      trendValue: 8.1,
      subtitle: 'Otimização de 8.1% no período'
    },
    {
      title: 'Custo por Clique (CPC)',
      value: kpis.cpc,
      type: 'currency',
      icon: MousePointer,
      color: 'yellow',
      trend: 'up',
      trendValue: 2.3,
      subtitle: 'Aumento de 2.3% vs. período anterior'
    },
    {
      title: 'Taxa de Conversão',
      value: kpis.conversionRate,
      type: 'percentage',
      icon: TrendingUp,
      color: 'green',
      trend: 'up',
      trendValue: 12.5,
      subtitle: 'Melhoria significativa de 12.5%'
    },
    {
      title: 'Receita por Agente',
      value: kpis.revenuePerAgent,
      type: 'currency',
      icon: Award,
      color: 'blue',
      trend: 'up',
      trendValue: 15.8,
      subtitle: 'Crescimento de 15.8% na produtividade'
    },
    {
      title: 'Leads Ativos',
      value: kpis.activeLeads,
      type: 'number',
      icon: Users,
      color: 'green',
      trend: 'up',
      trendValue: 7.2,
      subtitle: 'Aumento de 7.2% em leads qualificados'
    },
    {
      title: 'Total de Conversas',
      value: kpis.totalConversations,
      type: 'number',
      icon: MessageCircle,
      color: 'blue',
      trend: 'up',
      trendValue: 18.9,
      subtitle: 'Crescimento de 18.9% no engajamento'
    },
    {
      title: 'Conversas Qualificadas',
      value: kpis.qualifiedConversations,
      type: 'number',
      icon: Activity,
      color: 'green',
      trend: 'up',
      trendValue: 22.1,
      subtitle: 'Melhoria de 22.1% na qualificação'
    }
  ] : [];

  if (error) {
    return (
      <div className="container py-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Activity size={32} className="text-red-500" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Erro ao carregar dashboard
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={loadDashboardData}
            className="btn btn-primary"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Dashboard CRM
        </h1>
        <p className="text-gray-600">
          Visão geral do desempenho dos seus agentes de IA
        </p>
      </div>

      {/* KPIs Grid */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">
          Indicadores Principais
        </h2>
        <KPIGrid kpis={kpiData} loading={loading} />
      </div>

      {/* Charts Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">
          Análise de Performance
        </h2>
        <ChartsGrid chartData={chartData} loading={loading} />
      </div>

      {/* Agents Overview */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            Agentes de IA
          </h2>
          <button className="btn btn-primary">
            Ver Todos
          </button>
        </div>
        
        {/* Mostra apenas os primeiros 3 agentes no dashboard */}
        <AgentsGrid 
          agents={agents.slice(0, 3)} 
          loading={loading}
          onViewDetails={(agent) => {
            console.log('Ver detalhes do agente:', agent);
            // Aqui você implementaria a navegação ou modal
          }}
          onToggleStatus={(agent) => {
            console.log('Toggle status do agente:', agent);
            // Aqui você implementaria a mudança de status
          }}
          onSettings={(agent) => {
            console.log('Configurações do agente:', agent);
            // Aqui você implementaria as configurações
          }}
        />
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <div className="card-body text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <MessageCircle size={24} className="text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {loading ? '-' : formatNumber(kpis?.totalConversations || 0)}
            </h3>
            <p className="text-sm text-gray-600">Conversas Hoje</p>
          </div>
        </div>

        <div className="card">
          <div className="card-body text-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <TrendingUp size={24} className="text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {loading ? '-' : formatPercentage(kpis?.conversionRate || 0)}
            </h3>
            <p className="text-sm text-gray-600">Taxa de Conversão</p>
          </div>
        </div>

        <div className="card">
          <div className="card-body text-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Award size={24} className="text-yellow-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {loading ? '-' : formatCurrency(kpis?.revenuePerAgent || 0)}
            </h3>
            <p className="text-sm text-gray-600">Receita por Agente</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

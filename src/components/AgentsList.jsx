import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Plus, 
  Download,
  RefreshCw,
  Users
} from 'lucide-react';
import { AgentsGrid } from './AgentCard';
import ConversationModal from './ConversationModal';
import AgentEditModal from './AgentEditModal';
import { getAgents, getConversations, updateAgentStatus } from '../services/api';

const AgentsList = () => {
  const [agents, setAgents] = useState([]);
  const [filteredAgents, setFilteredAgents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [channelFilter, setChannelFilter] = useState('all');
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [showConversationModal, setShowConversationModal] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadAgents();
  }, []);

  useEffect(() => {
    filterAgents();
  }, [agents, searchTerm, statusFilter, channelFilter]);

  const loadAgents = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getAgents();
      setAgents(data);
    } catch (err) {
      console.error('Erro ao carregar agentes:', err);
      setError('Erro ao carregar agentes. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const filterAgents = () => {
    let filtered = [...agents];

    // Filtro por termo de busca
    if (searchTerm) {
      filtered = filtered.filter(agent =>
        agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        agent.channel.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtro por status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(agent => agent.status === statusFilter);
    }

    // Filtro por canal
    if (channelFilter !== 'all') {
      filtered = filtered.filter(agent => agent.channel === channelFilter);
    }

    setFilteredAgents(filtered);
  };

  const handleViewDetails = async (agent) => {
    try {
      // Busca conversas do agente
      const conversations = await getConversations({ agentId: agent.id });
      if (conversations.length > 0) {
        setSelectedConversation(conversations[0]); // Mostra a primeira conversa
        setShowConversationModal(true);
      } else {
        alert('Nenhuma conversa encontrada para este agente.');
      }
    } catch (err) {
      console.error('Erro ao buscar conversas:', err);
      alert('Erro ao carregar conversas do agente.');
    }
  };

  const handleToggleStatus = async (agent) => {
    try {
      const newStatus = agent.status === 'online' ? 'offline' : 'online';
      await updateAgentStatus(agent.id, newStatus);
      
      // Atualiza o estado local
      setAgents(prevAgents =>
        prevAgents.map(a =>
          a.id === agent.id ? { ...a, status: newStatus } : a
        )
      );
    } catch (err) {
      console.error('Erro ao atualizar status:', err);
      alert('Erro ao atualizar status do agente.');
    }
  };

  const handleSettings = (agent) => {
    console.log('Configurações do agente:', agent);
    // Aqui você implementaria a navegação para configurações
    alert(`Configurações do ${agent.name} - Em desenvolvimento`);
  };

  const handleEdit = (agent) => {
    setSelectedAgent(agent);
    setShowEditModal(true);
  };

  const handleSaveAgent = async (editedAgent) => {
    try {
      // Aqui você faria a chamada para a API para salvar o agente
      // await updateAgent(editedAgent);

      // Atualiza o estado local
      setAgents(prevAgents =>
        prevAgents.map(a =>
          a.id === editedAgent.id ? editedAgent : a
        )
      );

      setShowEditModal(false);
      setSelectedAgent(null);
    } catch (err) {
      console.error('Erro ao salvar agente:', err);
      alert('Erro ao salvar agente.');
    }
  };

  const handleDeleteAgent = async (agentId) => {
    try {
      // Aqui você faria a chamada para a API para deletar o agente
      // await deleteAgent(agentId);

      // Remove do estado local
      setAgents(prevAgents =>
        prevAgents.filter(a => a.id !== agentId)
      );

      setShowEditModal(false);
      setSelectedAgent(null);
    } catch (err) {
      console.error('Erro ao deletar agente:', err);
      alert('Erro ao deletar agente.');
    }
  };

  const getUniqueChannels = () => {
    const channels = [...new Set(agents.map(agent => agent.channel))];
    return channels;
  };

  const exportAgentsData = () => {
    // Implementação básica de export
    const csvContent = [
      ['Nome', 'Canal', 'Status', 'Leads Atendidos', 'Conversas Ativas', 'Taxa de Resposta'],
      ...filteredAgents.map(agent => [
        agent.name,
        agent.channel,
        agent.status,
        agent.leadsAttended,
        agent.activeConversations,
        `${agent.responseRate}%`
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'agentes-crm.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (error) {
    return (
      <div className="container py-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Users size={32} className="text-red-500" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Erro ao carregar agentes
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={loadAgents}
            className="btn btn-primary"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-10">
          <div className="space-y-2">
            <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white gradient-text">
              Agentes de IA
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-base lg:text-lg">
              Gerencie e monitore seus agentes de atendimento automatizado
            </p>
          </div>

          <div className="flex items-center gap-3 mt-6 lg:mt-0">
            <button
              onClick={loadAgents}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-gray-700 dark:text-gray-300 font-medium"
            >
              <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
              Atualizar
            </button>
            <button
              onClick={exportAgentsData}
              className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-gray-700 dark:text-gray-300 font-medium"
            >
              <Download size={16} />
              Exportar
            </button>
            <button className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
              <Plus size={16} />
              Novo Agente
            </button>
          </div>
        </div>

        {/* Filtros */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Busca */}
            <div className="relative">
              <Search size={18} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder="Buscar agentes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
              />
            </div>

            {/* Filtro por Status */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">Todos os Status</option>
              <option value="online">Online</option>
              <option value="offline">Offline</option>
            </select>

            {/* Filtro por Canal */}
            <select
              value={channelFilter}
              onChange={(e) => setChannelFilter(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">Todos os Canais</option>
              {getUniqueChannels().map(channel => (
                <option key={channel} value={channel}>{channel}</option>
              ))}
            </select>

            {/* Botão de limpar filtros */}
            <button
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('all');
                setChannelFilter('all');
              }}
              className="flex items-center justify-center gap-2 px-4 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-xl transition-colors"
            >
              <Filter size={16} />
              Limpar Filtros
            </button>
          </div>
        </div>

      {/* Estatísticas rápidas */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6 rounded-2xl border border-blue-200 dark:border-blue-800 shadow-sm">
          <p className="text-sm text-blue-600 dark:text-blue-400 mb-2 font-medium">Total de Agentes</p>
          <p className="text-3xl font-bold text-blue-900 dark:text-blue-100">{agents.length}</p>
        </div>
        <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-6 rounded-2xl border border-green-200 dark:border-green-800 shadow-sm">
          <p className="text-sm text-green-600 dark:text-green-400 mb-2 font-medium">Online</p>
          <p className="text-3xl font-bold text-green-900 dark:text-green-100">
            {agents.filter(a => a.status === 'online').length}
          </p>
        </div>
        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 p-6 rounded-2xl border border-yellow-200 dark:border-yellow-800 shadow-sm">
          <p className="text-sm text-yellow-600 dark:text-yellow-400 mb-2 font-medium">Conversas Ativas</p>
          <p className="text-3xl font-bold text-yellow-900 dark:text-yellow-100">
            {agents.reduce((sum, a) => sum + a.activeConversations, 0)}
          </p>
        </div>
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-2xl border border-purple-200 dark:border-purple-800 shadow-sm">
          <p className="text-sm text-purple-600 dark:text-purple-400 mb-2 font-medium">Taxa Média</p>
          <p className="text-3xl font-bold text-purple-900 dark:text-purple-100">
            {agents.length > 0
              ? `${(agents.reduce((sum, a) => sum + a.responseRate, 0) / agents.length).toFixed(1)}%`
              : '0%'
            }
          </p>
        </div>
      </div>

      {/* Lista de Agentes */}
      <AgentsGrid
        agents={filteredAgents}
        loading={loading}
        onViewDetails={handleViewDetails}
        onToggleStatus={handleToggleStatus}
        onSettings={handleSettings}
        onEdit={handleEdit}
      />

      {/* Modal de Conversa */}
      <ConversationModal
        conversation={selectedConversation}
        isOpen={showConversationModal}
        onClose={() => {
          setShowConversationModal(false);
          setSelectedConversation(null);
        }}
      />

      {/* Modal de Edição de Agente */}
      <AgentEditModal
        agent={selectedAgent}
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setSelectedAgent(null);
        }}
        onSave={handleSaveAgent}
        onDelete={handleDeleteAgent}
      />
      </div>
    </div>
  );
};

export default AgentsList;

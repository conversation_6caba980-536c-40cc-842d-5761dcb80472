import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Plus, 
  Download,
  RefreshCw,
  Users
} from 'lucide-react';
import { AgentsGrid } from './AgentCard';
import ConversationModal from './ConversationModal';
import { getAgents, getConversations, updateAgentStatus } from '../services/api';

const AgentsList = () => {
  const [agents, setAgents] = useState([]);
  const [filteredAgents, setFilteredAgents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [channelFilter, setChannelFilter] = useState('all');
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [showConversationModal, setShowConversationModal] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadAgents();
  }, []);

  useEffect(() => {
    filterAgents();
  }, [agents, searchTerm, statusFilter, channelFilter]);

  const loadAgents = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getAgents();
      setAgents(data);
    } catch (err) {
      console.error('Erro ao carregar agentes:', err);
      setError('Erro ao carregar agentes. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const filterAgents = () => {
    let filtered = [...agents];

    // Filtro por termo de busca
    if (searchTerm) {
      filtered = filtered.filter(agent =>
        agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        agent.channel.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtro por status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(agent => agent.status === statusFilter);
    }

    // Filtro por canal
    if (channelFilter !== 'all') {
      filtered = filtered.filter(agent => agent.channel === channelFilter);
    }

    setFilteredAgents(filtered);
  };

  const handleViewDetails = async (agent) => {
    try {
      // Busca conversas do agente
      const conversations = await getConversations({ agentId: agent.id });
      if (conversations.length > 0) {
        setSelectedConversation(conversations[0]); // Mostra a primeira conversa
        setShowConversationModal(true);
      } else {
        alert('Nenhuma conversa encontrada para este agente.');
      }
    } catch (err) {
      console.error('Erro ao buscar conversas:', err);
      alert('Erro ao carregar conversas do agente.');
    }
  };

  const handleToggleStatus = async (agent) => {
    try {
      const newStatus = agent.status === 'online' ? 'offline' : 'online';
      await updateAgentStatus(agent.id, newStatus);
      
      // Atualiza o estado local
      setAgents(prevAgents =>
        prevAgents.map(a =>
          a.id === agent.id ? { ...a, status: newStatus } : a
        )
      );
    } catch (err) {
      console.error('Erro ao atualizar status:', err);
      alert('Erro ao atualizar status do agente.');
    }
  };

  const handleSettings = (agent) => {
    console.log('Configurações do agente:', agent);
    // Aqui você implementaria a navegação para configurações
    alert(`Configurações do ${agent.name} - Em desenvolvimento`);
  };

  const getUniqueChannels = () => {
    const channels = [...new Set(agents.map(agent => agent.channel))];
    return channels;
  };

  const exportAgentsData = () => {
    // Implementação básica de export
    const csvContent = [
      ['Nome', 'Canal', 'Status', 'Leads Atendidos', 'Conversas Ativas', 'Taxa de Resposta'],
      ...filteredAgents.map(agent => [
        agent.name,
        agent.channel,
        agent.status,
        agent.leadsAttended,
        agent.activeConversations,
        `${agent.responseRate}%`
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'agentes-crm.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (error) {
    return (
      <div className="container py-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Users size={32} className="text-red-500" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Erro ao carregar agentes
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={loadAgents}
            className="btn btn-primary"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Agentes de IA
          </h1>
          <p className="text-gray-600">
            Gerencie e monitore seus agentes de atendimento automatizado
          </p>
        </div>
        
        <div className="flex gap-3 mt-4 md:mt-0">
          <button 
            onClick={loadAgents}
            disabled={loading}
            className="btn btn-secondary"
          >
            <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
            Atualizar
          </button>
          <button 
            onClick={exportAgentsData}
            className="btn btn-secondary"
          >
            <Download size={16} />
            Exportar
          </button>
          <button className="btn btn-primary">
            <Plus size={16} />
            Novo Agente
          </button>
        </div>
      </div>

      {/* Filtros */}
      <div className="card mb-8">
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Busca */}
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar agentes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Filtro por Status */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Todos os Status</option>
              <option value="online">Online</option>
              <option value="offline">Offline</option>
            </select>

            {/* Filtro por Canal */}
            <select
              value={channelFilter}
              onChange={(e) => setChannelFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Todos os Canais</option>
              {getUniqueChannels().map(channel => (
                <option key={channel} value={channel}>{channel}</option>
              ))}
            </select>

            {/* Botão de limpar filtros */}
            <button 
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('all');
                setChannelFilter('all');
              }}
              className="btn btn-secondary"
            >
              <Filter size={16} />
              Limpar Filtros
            </button>
          </div>
        </div>
      </div>

      {/* Estatísticas rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-blue-50 p-4 rounded-lg">
          <p className="text-sm text-blue-600 mb-1">Total de Agentes</p>
          <p className="text-2xl font-bold text-blue-900">{agents.length}</p>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <p className="text-sm text-green-600 mb-1">Online</p>
          <p className="text-2xl font-bold text-green-900">
            {agents.filter(a => a.status === 'online').length}
          </p>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <p className="text-sm text-yellow-600 mb-1">Conversas Ativas</p>
          <p className="text-2xl font-bold text-yellow-900">
            {agents.reduce((sum, a) => sum + a.activeConversations, 0)}
          </p>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg">
          <p className="text-sm text-purple-600 mb-1">Taxa Média</p>
          <p className="text-2xl font-bold text-purple-900">
            {agents.length > 0 
              ? `${(agents.reduce((sum, a) => sum + a.responseRate, 0) / agents.length).toFixed(1)}%`
              : '0%'
            }
          </p>
        </div>
      </div>

      {/* Lista de Agentes */}
      <AgentsGrid 
        agents={filteredAgents}
        loading={loading}
        onViewDetails={handleViewDetails}
        onToggleStatus={handleToggleStatus}
        onSettings={handleSettings}
      />

      {/* Modal de Conversa */}
      <ConversationModal
        conversation={selectedConversation}
        isOpen={showConversationModal}
        onClose={() => {
          setShowConversationModal(false);
          setSelectedConversation(null);
        }}
      />
    </div>
  );
};

export default AgentsList;

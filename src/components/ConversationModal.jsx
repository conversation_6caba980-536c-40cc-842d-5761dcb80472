import React, { useState } from 'react';
import { 
  X, 
  Phone, 
  Calendar, 
  Star, 
  MessageCircle,
  Clock,
  User,
  Mail,
  ExternalLink
} from 'lucide-react';
import { 
  formatDateTime, 
  formatRelativeTime, 
  formatPhone, 
  getChannelIcon, 
  getStatusColor,
  formatIntent
} from '../utils/formatters';
import { sendWhatsAppMessage, scheduleCall, markAsOpportunity } from '../services/api';

const ConversationModal = ({ conversation, isOpen, onClose }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [whatsappMessage, setWhatsappMessage] = useState('');
  const [callDateTime, setCallDateTime] = useState('');
  const [callNotes, setCallNotes] = useState('');

  if (!isOpen || !conversation) return null;

  const {
    id,
    leadName,
    channel,
    status,
    intent,
    lastMessage,
    lastMessageTime,
    messagesCount,
    phone,
    email,
    messages = []
  } = conversation;

  const statusColor = getStatusColor(status);
  const channelIcon = getChannelIcon(channel);

  const handleSendWhatsApp = async () => {
    if (!whatsappMessage.trim() || !phone) return;

    setIsLoading(true);
    try {
      await sendWhatsAppMessage(phone, whatsappMessage);
      setWhatsappMessage('');
      // Aqui você poderia atualizar a conversa ou mostrar uma notificação
      alert('Mensagem enviada com sucesso!');
    } catch (error) {
      alert('Erro ao enviar mensagem');
    } finally {
      setIsLoading(false);
    }
  };

  const handleScheduleCall = async () => {
    if (!callDateTime) return;

    setIsLoading(true);
    try {
      await scheduleCall(id, callDateTime, callNotes);
      setCallDateTime('');
      setCallNotes('');
      alert('Call agendada com sucesso!');
    } catch (error) {
      alert('Erro ao agendar call');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMarkAsOpportunity = async () => {
    setIsLoading(true);
    try {
      await markAsOpportunity(id);
      alert('Marcado como oportunidade!');
    } catch (error) {
      alert('Erro ao marcar como oportunidade');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-xl">
                {channelIcon}
              </div>
              <div 
                className="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white"
                style={{ backgroundColor: statusColor }}
              ></div>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{leadName}</h2>
              <p className="text-sm text-gray-500">{channel} • {formatIntent(intent)}</p>
            </div>
          </div>
          <button 
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <X size={20} />
          </button>
        </div>

        {/* Informações do Lead */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Informações do Lead</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-3">
              <User size={16} className="text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Nome</p>
                <p className="font-medium">{leadName}</p>
              </div>
            </div>
            
            {phone && (
              <div className="flex items-center gap-3">
                <Phone size={16} className="text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Telefone</p>
                  <p className="font-medium">{formatPhone(phone)}</p>
                </div>
              </div>
            )}
            
            {email && (
              <div className="flex items-center gap-3">
                <Mail size={16} className="text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Email</p>
                  <p className="font-medium">{email}</p>
                </div>
              </div>
            )}
            
            <div className="flex items-center gap-3">
              <MessageCircle size={16} className="text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Total de Mensagens</p>
                <p className="font-medium">{messagesCount}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Histórico de Mensagens */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Histórico de Mensagens</h3>
          <div className="max-h-64 overflow-y-auto space-y-3">
            {messages.map((message) => (
              <div 
                key={message.id}
                className={`flex ${message.sender === 'agent' ? 'justify-end' : 'justify-start'}`}
              >
                <div 
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.sender === 'agent' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className={`text-xs mt-1 ${
                    message.sender === 'agent' ? 'text-blue-100' : 'text-gray-500'
                  }`}>
                    {formatRelativeTime(message.timestamp)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Ações Rápidas */}
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4">Ações Rápidas</h3>
          
          {/* Enviar WhatsApp */}
          {phone && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enviar WhatsApp
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={whatsappMessage}
                  onChange={(e) => setWhatsappMessage(e.target.value)}
                  placeholder="Digite sua mensagem..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button 
                  onClick={handleSendWhatsApp}
                  disabled={isLoading || !whatsappMessage.trim()}
                  className="btn btn-primary"
                >
                  <Phone size={16} />
                  Enviar
                </button>
              </div>
            </div>
          )}

          {/* Agendar Call */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Agendar Call
            </label>
            <div className="space-y-2">
              <input
                type="datetime-local"
                value={callDateTime}
                onChange={(e) => setCallDateTime(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <input
                type="text"
                value={callNotes}
                onChange={(e) => setCallNotes(e.target.value)}
                placeholder="Observações (opcional)"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button 
                onClick={handleScheduleCall}
                disabled={isLoading || !callDateTime}
                className="btn btn-secondary"
              >
                <Calendar size={16} />
                Agendar
              </button>
            </div>
          </div>

          {/* Marcar como Oportunidade */}
          <div className="flex gap-3">
            <button 
              onClick={handleMarkAsOpportunity}
              disabled={isLoading}
              className="btn btn-primary flex-1"
            >
              <Star size={16} />
              Marcar como Oportunidade
            </button>
            
            {phone && (
              <a 
                href={`https://wa.me/${phone.replace(/\D/g, '')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-secondary"
              >
                <ExternalLink size={16} />
                Abrir WhatsApp
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConversationModal;

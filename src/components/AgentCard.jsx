import React from 'react';
import { 
  MessageCircle, 
  Users, 
  TrendingUp, 
  Clock, 
  MoreVertical,
  Eye,
  Settings,
  Power
} from 'lucide-react';
import { 
  formatNumber, 
  formatPercentage, 
  formatRelativeTime, 
  getChannelIcon, 
  getStatusColor 
} from '../utils/formatters';

const AgentCard = ({ 
  agent, 
  onViewDetails, 
  onToggleStatus, 
  onSettings,
  loading = false 
}) => {
  if (loading) {
    return (
      <div className="card">
        <div className="card-body">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gray-200 rounded-lg animate-pulse"></div>
              <div>
                <div className="w-24 h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="w-16 h-3 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
            <div className="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="w-full h-16 bg-gray-200 rounded animate-pulse"></div>
            <div className="w-full h-16 bg-gray-200 rounded animate-pulse"></div>
          </div>
          
          <div className="w-full h-8 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  const {
    id,
    name,
    channel,
    leadsAttended,
    activeConversations,
    responseRate,
    status,
    lastActivity
  } = agent;

  const statusColor = getStatusColor(status);
  const channelIcon = getChannelIcon(channel);

  return (
    <div className="card hover:shadow-md transition-shadow">
      <div className="card-body">
        {/* Header com nome, canal e status */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-xl">
                {channelIcon}
              </div>
              {/* Indicador de status */}
              <div 
                className="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white"
                style={{ backgroundColor: statusColor }}
                title={status === 'online' ? 'Online' : 'Offline'}
              ></div>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900">{name}</h3>
              <p className="text-sm text-gray-500">{channel}</p>
            </div>
          </div>

          {/* Menu de ações */}
          <div className="relative group">
            <button className="p-1 hover:bg-gray-100 rounded">
              <MoreVertical size={16} className="text-gray-400" />
            </button>
            
            {/* Dropdown menu (seria implementado com estado) */}
            <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 opacity-0 group-hover:opacity-100 transition-opacity z-10 min-w-[120px]">
              <button 
                onClick={() => onViewDetails?.(agent)}
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
              >
                <Eye size={14} />
                Ver detalhes
              </button>
              <button 
                onClick={() => onSettings?.(agent)}
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
              >
                <Settings size={14} />
                Configurar
              </button>
              <button 
                onClick={() => onToggleStatus?.(agent)}
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
              >
                <Power size={14} />
                {status === 'online' ? 'Desativar' : 'Ativar'}
              </button>
            </div>
          </div>
        </div>

        {/* Métricas principais */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Users size={16} className="text-gray-500" />
              <span className="text-xs text-gray-500">Leads Atendidos</span>
            </div>
            <div className="text-lg font-semibold text-gray-900">
              {formatNumber(leadsAttended)}
            </div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <MessageCircle size={16} className="text-gray-500" />
              <span className="text-xs text-gray-500">Conversas Ativas</span>
            </div>
            <div className="text-lg font-semibold text-gray-900">
              {formatNumber(activeConversations)}
            </div>
          </div>
        </div>

        {/* Taxa de resposta */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <TrendingUp size={16} className="text-gray-500" />
              <span className="text-sm text-gray-600">Taxa de Resposta</span>
            </div>
            <span className="text-sm font-semibold text-gray-900">
              {formatPercentage(responseRate)}
            </span>
          </div>
          
          {/* Barra de progresso */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="h-2 rounded-full transition-all duration-300"
              style={{ 
                width: `${Math.min(responseRate, 100)}%`,
                backgroundColor: responseRate >= 90 ? 'var(--success)' : 
                                responseRate >= 70 ? 'var(--warning)' : 'var(--danger)'
              }}
            ></div>
          </div>
        </div>

        {/* Última atividade */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2 text-gray-500">
            <Clock size={14} />
            <span>Última atividade</span>
          </div>
          <span className="text-gray-600">
            {formatRelativeTime(lastActivity)}
          </span>
        </div>

        {/* Botão de ação principal */}
        <div className="mt-4 pt-4 border-t border-gray-100">
          <button 
            onClick={() => onViewDetails?.(agent)}
            className="btn btn-primary w-full"
          >
            Ver Conversas
          </button>
        </div>
      </div>
    </div>
  );
};

// Componente para skeleton loading de múltiplos agentes
export const AgentCardSkeleton = ({ count = 3 }) => {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <AgentCard key={index} loading={true} />
      ))}
    </>
  );
};

// Componente para grid de agentes
export const AgentsGrid = ({ 
  agents, 
  loading = false, 
  onViewDetails,
  onToggleStatus,
  onSettings,
  className = '' 
}) => {
  if (loading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
        <AgentCardSkeleton count={6} />
      </div>
    );
  }

  if (!agents || agents.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Users size={32} className="text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Nenhum agente encontrado
        </h3>
        <p className="text-gray-500">
          Configure seus primeiros agentes de IA para começar.
        </p>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {agents.map((agent) => (
        <AgentCard 
          key={agent.id} 
          agent={agent}
          onViewDetails={onViewDetails}
          onToggleStatus={onToggleStatus}
          onSettings={onSettings}
        />
      ))}
    </div>
  );
};

export default AgentCard;

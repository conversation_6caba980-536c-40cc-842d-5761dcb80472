import React from 'react';
import {
  MessageCircle,
  Users,
  TrendingUp,
  Clock,
  MoreVertical,
  Eye,
  Settings,
  Power,
  Edit3
} from 'lucide-react';
import { 
  formatNumber, 
  formatPercentage, 
  formatRelativeTime, 
  getChannelIcon, 
  getStatusColor 
} from '../utils/formatters';

const AgentCard = ({
  agent,
  onViewDetails,
  onToggleStatus,
  onSettings,
  onEdit,
  loading = false
}) => {
  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden animate-pulse">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="w-14 h-14 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
              <div>
                <div className="w-28 h-5 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                <div className="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
            <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="w-full h-20 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
            <div className="w-full h-20 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
          </div>

          <div className="mb-6">
            <div className="w-full h-4 bg-gray-200 dark:bg-gray-700 rounded mb-3"></div>
            <div className="w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
          </div>

          <div className="flex items-center justify-between mb-6">
            <div className="w-24 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>

          <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="w-full h-12 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
          </div>
        </div>
      </div>
    );
  }

  const {
    id,
    name,
    channel,
    leadsAttended,
    activeConversations,
    responseRate,
    status,
    lastActivity
  } = agent;

  const statusColor = getStatusColor(status);
  const channelIcon = getChannelIcon(channel);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100 dark:border-gray-700 overflow-hidden">
      <div className="p-6">
        {/* Header com nome, canal e status */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="relative">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-2xl text-white shadow-lg">
                {channelIcon}
              </div>
              {/* Indicador de status */}
              <div
                className="absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-3 border-white dark:border-gray-800 shadow-sm"
                style={{ backgroundColor: statusColor }}
                title={status === 'online' ? 'Online' : 'Offline'}
              ></div>
            </div>

            <div>
              <h3 className="font-bold text-gray-900 dark:text-white text-lg">{name}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">{channel}</p>
            </div>
          </div>

          {/* Menu de ações */}
          <div className="relative group">
            <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
              <MoreVertical size={18} className="text-gray-400 dark:text-gray-500" />
            </button>
            
            {/* Dropdown menu (seria implementado com estado) */}
            <div className="absolute right-0 top-8 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 opacity-0 group-hover:opacity-100 transition-opacity z-10 min-w-[140px]">
              <button
                onClick={() => onEdit?.(agent)}
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300"
              >
                <Edit3 size={14} />
                Editar
              </button>
              <button
                onClick={() => onViewDetails?.(agent)}
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300"
              >
                <Eye size={14} />
                Ver detalhes
              </button>
              <button
                onClick={() => onSettings?.(agent)}
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300"
              >
                <Settings size={14} />
                Configurar
              </button>
              <button
                onClick={() => onToggleStatus?.(agent)}
                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300"
              >
                <Power size={14} />
                {status === 'online' ? 'Desativar' : 'Ativar'}
              </button>
            </div>
          </div>
        </div>

        {/* Métricas principais */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2 mb-2">
              <Users size={18} className="text-blue-600 dark:text-blue-400" />
              <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">Leads Atendidos</span>
            </div>
            <div className="text-xl font-bold text-blue-900 dark:text-blue-100">
              {formatNumber(leadsAttended)}
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-800">
            <div className="flex items-center gap-2 mb-2">
              <MessageCircle size={18} className="text-green-600 dark:text-green-400" />
              <span className="text-xs text-green-600 dark:text-green-400 font-medium">Conversas Ativas</span>
            </div>
            <div className="text-xl font-bold text-green-900 dark:text-green-100">
              {formatNumber(activeConversations)}
            </div>
          </div>
        </div>

        {/* Taxa de resposta */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <TrendingUp size={18} className="text-purple-600 dark:text-purple-400" />
              <span className="text-sm text-gray-700 dark:text-gray-300 font-medium">Taxa de Resposta</span>
            </div>
            <span className="text-lg font-bold text-purple-900 dark:text-purple-100">
              {formatPercentage(responseRate)}
            </span>
          </div>

          {/* Barra de progresso */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 shadow-inner">
            <div
              className="h-3 rounded-full transition-all duration-500 shadow-sm"
              style={{
                width: `${Math.min(responseRate, 100)}%`,
                background: responseRate >= 90 ? 'linear-gradient(90deg, #10b981, #059669)' :
                           responseRate >= 70 ? 'linear-gradient(90deg, #f59e0b, #d97706)' :
                           'linear-gradient(90deg, #ef4444, #dc2626)'
              }}
            ></div>
          </div>
        </div>

        {/* Última atividade */}
        <div className="flex items-center justify-between text-sm mb-6">
          <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
            <Clock size={16} />
            <span className="font-medium">Última atividade</span>
          </div>
          <span className="text-gray-700 dark:text-gray-300 font-semibold">
            {formatRelativeTime(lastActivity)}
          </span>
        </div>

        {/* Botão de ação principal */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={() => onViewDetails?.(agent)}
            className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            Ver Conversas
          </button>
        </div>
      </div>
    </div>
  );
};

// Componente para skeleton loading de múltiplos agentes
export const AgentCardSkeleton = ({ count = 3 }) => {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <AgentCard key={index} loading={true} />
      ))}
    </>
  );
};

// Componente para grid de agentes
export const AgentsGrid = ({
  agents,
  loading = false,
  onViewDetails,
  onToggleStatus,
  onSettings,
  onEdit,
  className = ''
}) => {
  if (loading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
        <AgentCardSkeleton count={6} />
      </div>
    );
  }

  if (!agents || agents.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Users size={32} className="text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Nenhum agente encontrado
        </h3>
        <p className="text-gray-500">
          Configure seus primeiros agentes de IA para começar.
        </p>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 ${className}`}>
      {agents.map((agent) => (
        <AgentCard
          key={agent.id}
          agent={agent}
          onViewDetails={onViewDetails}
          onToggleStatus={onToggleStatus}
          onSettings={onSettings}
          onEdit={onEdit}
        />
      ))}
    </div>
  );
};

export default AgentCard;

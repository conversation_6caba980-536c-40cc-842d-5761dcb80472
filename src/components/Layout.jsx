import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../utils/cn';

const Layout = ({ children, className, ...props }) => {
  return (
    <div className={cn(
      "min-h-screen bg-gray-50 dark:bg-gray-900",
      className
    )} {...props}>
      <div className="max-w-6xl mx-auto p-6">
        {children}
      </div>
    </div>
  );
};

const Header = ({ title, subtitle, actions, className, ...props }) => {
  return (
    <motion.div 
      className={cn("mb-8", className)}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      {...props}
    >
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
            {title}
          </h1>
          {subtitle && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {subtitle}
            </p>
          )}
        </div>
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>
    </motion.div>
  );
};

const Section = ({ title, icon: Icon, children, className, ...props }) => {
  return (
    <motion.div 
      className={cn("mb-6", className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      {...props}
    >
      {title && (
        <div className="flex items-center gap-2 mb-4">
          {Icon && (
            <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
              <Icon size={14} className="text-blue-600 dark:text-blue-400" />
            </div>
          )}
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            {title}
          </h2>
        </div>
      )}
      {children}
    </motion.div>
  );
};

const Grid = ({ cols = 4, gap = 4, children, className, ...props }) => {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
    6: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
  };

  const gapSize = {
    2: 'gap-2',
    3: 'gap-3',
    4: 'gap-4',
    6: 'gap-6',
    8: 'gap-8'
  };

  return (
    <div 
      className={cn(
        'grid',
        gridCols[cols] || gridCols[4],
        gapSize[gap] || gapSize[4],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

const Card = ({ children, className, hover = false, ...props }) => {
  return (
    <div 
      className={cn(
        "bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4",
        hover && "hover:shadow-md transition-shadow duration-200 cursor-pointer",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

Layout.Header = Header;
Layout.Section = Section;
Layout.Grid = Grid;
Layout.Card = Card;

export default Layout;

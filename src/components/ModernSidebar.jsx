import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BarChart3,
  Calendar,
  Kanban,
  Users,
  FileText,
  Clock,
  DollarSign,
  Package,
  Truck,
  MessageSquare,
  HeadphonesIcon,
  Bot,
  Settings,
  ChevronDown,
  ChevronRight,
  Bell,
  User
} from 'lucide-react';
import { cn } from '../utils/cn';

const ModernSidebar = ({ collapsed, onToggle }) => {
  const [expandedSections, setExpandedSections] = useState({
    principal: true,
    comercial: true,
    producao: true,
    comunicacao: true
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const menuSections = [
    {
      id: 'principal',
      title: 'PRINCIPAL',
      items: [
        { icon: BarChart3, label: 'Dashboard', count: null, active: true },
        { icon: Calendar, label: 'Cronograma', count: 7 },
        { icon: Ka<PERSON>ban, label: 'Kanban', count: 3 }
      ]
    },
    {
      id: 'comercial',
      title: 'COMERCIAL',
      items: [
        { icon: Users, label: 'Leads', count: 14 },
        { icon: FileText, label: 'Propostas', count: 5 },
        { icon: Clock, label: 'Agendamentos', count: 3 },
        { icon: DollarSign, label: 'Medições', count: null },
        { icon: Package, label: 'Vendas Fechadas', count: null }
      ]
    },
    {
      id: 'producao',
      title: 'PRODUÇÃO E ENTREGA',
      items: [
        { icon: Settings, label: 'Projeto Executivo', count: null },
        { icon: Package, label: 'Produção', count: null },
        { icon: Truck, label: 'Entregas', count: 2 },
        { icon: Settings, label: 'Montagens', count: null }
      ]
    },
    {
      id: 'comunicacao',
      title: 'COMUNICAÇÃO',
      items: [
        { icon: MessageSquare, label: 'Chat WhatsApp', count: 8 },
        { icon: HeadphonesIcon, label: 'Chat Interno', count: 5 },
        { icon: HeadphonesIcon, label: 'Suporte', count: 1 },
        { icon: Bot, label: 'IA Assistente', count: 3 }
      ]
    }
  ];

  const MenuItem = ({ item, collapsed }) => (
    <motion.div
      className={cn(
        "flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200",
        item.active 
          ? "bg-blue-600 text-white shadow-lg" 
          : "text-gray-300 hover:bg-gray-700 hover:text-white"
      )}
      whileHover={{ x: collapsed ? 0 : 4 }}
      whileTap={{ scale: 0.98 }}
    >
      <item.icon size={18} className="flex-shrink-0" />
      
      <AnimatePresence>
        {!collapsed && (
          <motion.div
            initial={{ opacity: 0, width: 0 }}
            animate={{ opacity: 1, width: 'auto' }}
            exit={{ opacity: 0, width: 0 }}
            className="flex items-center justify-between flex-1 overflow-hidden"
          >
            <span className="text-sm font-medium truncate">{item.label}</span>
            {item.count && (
              <span className={cn(
                "px-2 py-0.5 rounded-full text-xs font-semibold",
                item.active 
                  ? "bg-white/20 text-white" 
                  : "bg-blue-600 text-white"
              )}>
                {item.count}
              </span>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );

  const SectionHeader = ({ section, collapsed }) => (
    <motion.div
      className="flex items-center justify-between px-3 py-2 cursor-pointer"
      onClick={() => !collapsed && toggleSection(section.id)}
      whileHover={{ x: collapsed ? 0 : 2 }}
    >
      <AnimatePresence>
        {!collapsed && (
          <motion.span
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-xs font-semibold text-gray-400 uppercase tracking-wider"
          >
            {section.title}
          </motion.span>
        )}
      </AnimatePresence>
      
      {!collapsed && (
        <motion.div
          animate={{ rotate: expandedSections[section.id] ? 0 : -90 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronDown size={14} className="text-gray-400" />
        </motion.div>
      )}
    </motion.div>
  );

  return (
    <motion.div
      className="h-screen bg-gray-800 border-r border-gray-700 flex flex-col"
      animate={{ width: collapsed ? 64 : 280 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          
          <AnimatePresence>
            {!collapsed && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="flex-1"
              >
                <h1 className="text-white font-bold text-lg">Planej.AI</h1>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Notification */}
      <AnimatePresence>
        {!collapsed && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mx-4 mt-4"
          >
            <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <Bell size={16} className="text-orange-400" />
                <span className="text-orange-400 text-sm font-medium">
                  8 clientes sem resposta
                </span>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Menu */}
      <div className="flex-1 overflow-y-auto py-4 space-y-6">
        {menuSections.map((section) => (
          <div key={section.id}>
            <SectionHeader section={section} collapsed={collapsed} />
            
            <AnimatePresence>
              {(collapsed || expandedSections[section.id]) && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-1 px-3"
                >
                  {section.items.map((item, index) => (
                    <MenuItem key={index} item={item} collapsed={collapsed} />
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ))}
      </div>

      {/* User Profile */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <User size={16} className="text-white" />
          </div>
          
          <AnimatePresence>
            {!collapsed && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="flex-1"
              >
                <div className="text-white text-sm font-medium">Administrador</div>
                <div className="text-gray-400 text-xs">Administrador</div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
};

export default ModernSidebar;

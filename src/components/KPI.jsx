import React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown } from 'lucide-react';
import { cn } from '../utils/cn';

const KPI = ({ 
  title, 
  value, 
  trend, 
  trendValue, 
  icon: Icon, 
  color = 'blue',
  loading = false,
  className,
  ...props 
}) => {
  const colors = {
    blue: {
      icon: 'text-blue-600 dark:text-blue-400',
      iconBg: 'bg-blue-100 dark:bg-blue-900/20',
      value: 'text-blue-900 dark:text-blue-100'
    },
    green: {
      icon: 'text-green-600 dark:text-green-400',
      iconBg: 'bg-green-100 dark:bg-green-900/20',
      value: 'text-green-900 dark:text-green-100'
    },
    purple: {
      icon: 'text-purple-600 dark:text-purple-400',
      iconBg: 'bg-purple-100 dark:bg-purple-900/20',
      value: 'text-purple-900 dark:text-purple-100'
    },
    orange: {
      icon: 'text-orange-600 dark:text-orange-400',
      iconBg: 'bg-orange-100 dark:bg-orange-900/20',
      value: 'text-orange-900 dark:text-orange-100'
    }
  };

  const colorConfig = colors[color] || colors.blue;
  const TrendIcon = trend === 'up' ? TrendingUp : TrendingDown;

  if (loading) {
    return (
      <div className={cn(
        "bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4",
        className
      )}>
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-3">
            <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            <div className="w-12 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
          <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded mb-1"></div>
          <div className="w-16 h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className={cn(
        "bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow duration-200",
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      {...props}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        {Icon && (
          <div className={cn(
            "w-8 h-8 rounded-lg flex items-center justify-center",
            colorConfig.iconBg
          )}>
            <Icon size={16} className={colorConfig.icon} />
          </div>
        )}
        
        {trend && trendValue && (
          <div className={cn(
            "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
            trend === 'up' 
              ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
              : 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400'
          )}>
            <TrendIcon size={10} />
            {Math.abs(trendValue)}%
          </div>
        )}
      </div>

      {/* Value */}
      <div className={cn(
        "text-2xl font-bold mb-1",
        colorConfig.value
      )}>
        {value}
      </div>

      {/* Title */}
      <div className="text-sm text-gray-600 dark:text-gray-400">
        {title}
      </div>
    </motion.div>
  );
};

export default KPI;

import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Utility function to merge Tailwind CSS classes
 * Combines clsx for conditional classes and tailwind-merge for deduplication
 */
export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

/**
 * Variants for different component states
 */
export const variants = {
  button: {
    primary: 'bg-primary-600 hover:bg-primary-700 text-white shadow-soft hover:shadow-medium',
    secondary: 'bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 shadow-soft hover:shadow-medium',
    success: 'bg-success-600 hover:bg-success-700 text-white shadow-soft hover:shadow-medium',
    warning: 'bg-warning-600 hover:bg-warning-700 text-white shadow-soft hover:shadow-medium',
    danger: 'bg-danger-600 hover:bg-danger-700 text-white shadow-soft hover:shadow-medium',
    ghost: 'hover:bg-gray-100 text-gray-700',
    outline: 'border-2 border-primary-600 text-primary-600 hover:bg-primary-50',
  },
  card: {
    default: 'bg-white border border-gray-200 shadow-soft hover:shadow-medium',
    elevated: 'bg-white border border-gray-200 shadow-medium hover:shadow-large',
    interactive: 'bg-white border border-gray-200 shadow-soft hover:shadow-medium hover:border-primary-300 cursor-pointer',
    gradient: 'bg-gradient-to-br from-white to-gray-50 border border-gray-200 shadow-soft',
  },
  status: {
    online: 'bg-success-500 text-success-50',
    offline: 'bg-gray-400 text-gray-50',
    busy: 'bg-warning-500 text-warning-50',
    away: 'bg-warning-400 text-warning-50',
  },
  badge: {
    primary: 'bg-primary-100 text-primary-800 border border-primary-200',
    success: 'bg-success-100 text-success-800 border border-success-200',
    warning: 'bg-warning-100 text-warning-800 border border-warning-200',
    danger: 'bg-danger-100 text-danger-800 border border-danger-200',
    gray: 'bg-gray-100 text-gray-800 border border-gray-200',
  },
  input: {
    default: 'border-gray-300 focus:border-blue-500 focus:ring-blue-500',
    error: 'border-red-300 focus:border-red-500 focus:ring-red-500',
    success: 'border-green-300 focus:border-green-500 focus:ring-green-500',
  },
};

/**
 * Size variants
 */
export const sizes = {
  button: {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg',
  },
  input: {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  },
  avatar: {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16',
    '2xl': 'w-20 h-20',
  },
};

/**
 * Animation presets
 */
export const animations = {
  fadeIn: 'animate-fade-in',
  slideUp: 'animate-slide-up',
  slideDown: 'animate-slide-down',
  scaleIn: 'animate-scale-in',
  bounceSubtle: 'animate-bounce-subtle',
  pulseSubtle: 'animate-pulse-subtle',
  shimmer: 'animate-shimmer',
};

/**
 * Common transition classes
 */
export const transitions = {
  default: 'transition-all duration-200 ease-in-out',
  fast: 'transition-all duration-150 ease-in-out',
  slow: 'transition-all duration-300 ease-in-out',
  colors: 'transition-colors duration-200 ease-in-out',
  transform: 'transition-transform duration-200 ease-in-out',
  shadow: 'transition-shadow duration-200 ease-in-out',
};

/**
 * Responsive breakpoint helpers
 */
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};

/**
 * Common layout patterns
 */
export const layouts = {
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  containerSmall: 'max-w-4xl mx-auto px-4 sm:px-6 lg:px-8',
  containerLarge: 'max-w-full mx-auto px-4 sm:px-6 lg:px-8',
  flexCenter: 'flex items-center justify-center',
  flexBetween: 'flex items-center justify-between',
  gridAuto: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6',
  gridResponsive: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
};

export default cn;

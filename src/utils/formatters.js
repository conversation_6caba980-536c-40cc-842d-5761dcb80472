import { format, formatDistanceToNow, parseISO, isValid } from 'date-fns';
import { ptBR } from 'date-fns/locale';

/**
 * Formata valores monetários em Real brasileiro
 */
export const formatCurrency = (value, options = {}) => {
  const {
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
    showSymbol = true
  } = options;

  if (value === null || value === undefined || isNaN(value)) {
    return showSymbol ? 'R$ 0,00' : '0,00';
  }

  const formatter = new Intl.NumberFormat('pt-BR', {
    style: showSymbol ? 'currency' : 'decimal',
    currency: 'BRL',
    minimumFractionDigits,
    maximumFractionDigits
  });

  return formatter.format(value);
};

/**
 * Formata números com separadores de milhares
 */
export const formatNumber = (value, options = {}) => {
  const {
    minimumFractionDigits = 0,
    maximumFractionDigits = 0
  } = options;

  if (value === null || value === undefined || isNaN(value)) {
    return '0';
  }

  const formatter = new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits,
    maximumFractionDigits
  });

  return formatter.format(value);
};

/**
 * Formata percentuais
 */
export const formatPercentage = (value, options = {}) => {
  const {
    minimumFractionDigits = 1,
    maximumFractionDigits = 1,
    showSymbol = true
  } = options;

  if (value === null || value === undefined || isNaN(value)) {
    return showSymbol ? '0,0%' : '0,0';
  }

  const formatter = new Intl.NumberFormat('pt-BR', {
    style: showSymbol ? 'percent' : 'decimal',
    minimumFractionDigits,
    maximumFractionDigits
  });

  // Se o valor já está em formato de porcentagem (ex: 12.5 para 12.5%)
  // dividimos por 100 para o formatador de porcentagem
  const normalizedValue = showSymbol ? value / 100 : value;

  return formatter.format(normalizedValue);
};

/**
 * Formata datas em formato brasileiro
 */
export const formatDate = (date, formatString = 'dd/MM/yyyy') => {
  if (!date) return '';

  let dateObj;
  
  if (typeof date === 'string') {
    dateObj = parseISO(date);
  } else {
    dateObj = date;
  }

  if (!isValid(dateObj)) {
    return '';
  }

  return format(dateObj, formatString, { locale: ptBR });
};

/**
 * Formata data e hora em formato brasileiro
 */
export const formatDateTime = (date, formatString = 'dd/MM/yyyy HH:mm') => {
  return formatDate(date, formatString);
};

/**
 * Formata tempo relativo (ex: "há 2 horas")
 */
export const formatRelativeTime = (date) => {
  if (!date) return '';

  let dateObj;
  
  if (typeof date === 'string') {
    dateObj = parseISO(date);
  } else {
    dateObj = date;
  }

  if (!isValid(dateObj)) {
    return '';
  }

  return formatDistanceToNow(dateObj, { 
    addSuffix: true, 
    locale: ptBR 
  });
};

/**
 * Formata apenas a hora
 */
export const formatTime = (date) => {
  return formatDate(date, 'HH:mm');
};

/**
 * Formata número de telefone brasileiro
 */
export const formatPhone = (phone) => {
  if (!phone) return '';

  // Remove todos os caracteres não numéricos
  const cleaned = phone.replace(/\D/g, '');

  // Formata conforme o padrão brasileiro
  if (cleaned.length === 11) {
    // Celular: (11) 99999-9999
    return cleaned.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  } else if (cleaned.length === 10) {
    // Fixo: (11) 9999-9999
    return cleaned.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  } else if (cleaned.length === 13 && cleaned.startsWith('55')) {
    // Com código do país: +55 (11) 99999-9999
    return cleaned.replace(/55(\d{2})(\d{5})(\d{4})/, '+55 ($1) $2-$3');
  }

  return phone; // Retorna original se não conseguir formatar
};

/**
 * Trunca texto com reticências
 */
export const truncateText = (text, maxLength = 50) => {
  if (!text) return '';
  
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength).trim() + '...';
};

/**
 * Capitaliza primeira letra de cada palavra
 */
export const capitalizeWords = (text) => {
  if (!text) return '';
  
  return text
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

/**
 * Formata status com cores
 */
export const getStatusColor = (status) => {
  const statusColors = {
    'online': 'var(--success)',
    'offline': 'var(--gray-400)',
    'quente': 'var(--danger)',
    'morno': 'var(--warning)',
    'frio': 'var(--primary-blue)',
    'ativo': 'var(--success)',
    'inativo': 'var(--gray-400)',
    'pendente': 'var(--warning)',
    'concluído': 'var(--success)',
    'cancelado': 'var(--danger)'
  };

  return statusColors[status?.toLowerCase()] || 'var(--gray-500)';
};

/**
 * Formata canal com ícone
 */
export const getChannelIcon = (channel) => {
  const channelIcons = {
    'WhatsApp': '💬',
    'Instagram': '📷',
    'Facebook': '👥',
    'Site': '🌐',
    'Telegram': '✈️',
    'Email': '📧'
  };

  return channelIcons[channel] || '💬';
};

/**
 * Formata intenção do lead
 */
export const formatIntent = (intent) => {
  const intents = {
    'orçamento': 'Solicitação de Orçamento',
    'suporte': 'Suporte Técnico',
    'agendamento': 'Agendamento',
    'informação': 'Informações Gerais',
    'reclamação': 'Reclamação',
    'elogio': 'Elogio',
    'dúvida': 'Dúvida'
  };

  return intents[intent?.toLowerCase()] || capitalizeWords(intent || '');
};

/**
 * Formata duração em minutos para formato legível
 */
export const formatDuration = (minutes) => {
  if (!minutes || minutes < 1) {
    return '< 1 min';
  }

  if (minutes < 60) {
    return `${Math.round(minutes)} min`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = Math.round(minutes % 60);

  if (remainingMinutes === 0) {
    return `${hours}h`;
  }

  return `${hours}h ${remainingMinutes}min`;
};

/**
 * Formata tamanho de arquivo
 */
export const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

/**
 * Gera iniciais do nome
 */
export const getInitials = (name) => {
  if (!name) return '';

  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
};

/**
 * Valida e formata email
 */
export const formatEmail = (email) => {
  if (!email) return '';
  return email.toLowerCase().trim();
};

/**
 * Formata URL para exibição
 */
export const formatUrl = (url) => {
  if (!url) return '';
  
  // Remove protocolo para exibição mais limpa
  return url.replace(/^https?:\/\//, '').replace(/\/$/, '');
};

export default {
  formatCurrency,
  formatNumber,
  formatPercentage,
  formatDate,
  formatDateTime,
  formatRelativeTime,
  formatTime,
  formatPhone,
  truncateText,
  capitalizeWords,
  getStatusColor,
  getChannelIcon,
  formatIntent,
  formatDuration,
  formatFileSize,
  getInitials,
  formatEmail,
  formatUrl
};

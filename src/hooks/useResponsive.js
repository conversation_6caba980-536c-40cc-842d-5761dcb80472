import { useState, useEffect } from 'react';

/**
 * Hook para detectar breakpoints responsivos
 */
export const useResponsive = () => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  const [breakpoint, setBreakpoint] = useState('lg');

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setWindowSize({ width, height });

      // Define breakpoints baseados no Tailwind CSS
      if (width < 640) {
        setBreakpoint('xs');
      } else if (width < 768) {
        setBreakpoint('sm');
      } else if (width < 1024) {
        setBreakpoint('md');
      } else if (width < 1280) {
        setBreakpoint('lg');
      } else {
        setBreakpoint('xl');
      }
    };

    // Chama uma vez para definir o estado inicial
    handleResize();

    // Adiciona listener para mudanças de tamanho
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Funções helper para verificar breakpoints
  const isMobile = breakpoint === 'xs' || breakpoint === 'sm';
  const isTablet = breakpoint === 'md';
  const isDesktop = breakpoint === 'lg' || breakpoint === 'xl';
  const isSmallScreen = breakpoint === 'xs';
  const isLargeScreen = breakpoint === 'xl';

  // Funções para verificar se está acima ou abaixo de um breakpoint
  const isAbove = (bp) => {
    const breakpoints = { xs: 0, sm: 640, md: 768, lg: 1024, xl: 1280 };
    return windowSize.width >= breakpoints[bp];
  };

  const isBelow = (bp) => {
    const breakpoints = { xs: 640, sm: 768, md: 1024, lg: 1280, xl: Infinity };
    return windowSize.width < breakpoints[bp];
  };

  return {
    windowSize,
    breakpoint,
    isMobile,
    isTablet,
    isDesktop,
    isSmallScreen,
    isLargeScreen,
    isAbove,
    isBelow,
  };
};

/**
 * Hook para detectar orientação do dispositivo
 */
export const useOrientation = () => {
  const [orientation, setOrientation] = useState(
    typeof window !== 'undefined' && window.innerHeight > window.innerWidth 
      ? 'portrait' 
      : 'landscape'
  );

  useEffect(() => {
    const handleOrientationChange = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
    };

    window.addEventListener('resize', handleOrientationChange);
    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      window.removeEventListener('resize', handleOrientationChange);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  return {
    orientation,
    isPortrait: orientation === 'portrait',
    isLandscape: orientation === 'landscape',
  };
};

/**
 * Hook para detectar se é um dispositivo touch
 */
export const useTouch = () => {
  const [isTouch, setIsTouch] = useState(false);

  useEffect(() => {
    const checkTouch = () => {
      setIsTouch(
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0
      );
    };

    checkTouch();
  }, []);

  return isTouch;
};

/**
 * Hook para detectar preferência de tema do sistema
 */
export const useSystemTheme = () => {
  const [theme, setTheme] = useState(
    typeof window !== 'undefined' && window.matchMedia('(prefers-color-scheme: dark)').matches
      ? 'dark'
      : 'light'
  );

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e) => {
      setTheme(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);

    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return {
    theme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
  };
};

/**
 * Hook para detectar se o usuário prefere movimento reduzido
 */
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);

    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

/**
 * Hook combinado que retorna todas as informações responsivas
 */
export const useDeviceInfo = () => {
  const responsive = useResponsive();
  const orientation = useOrientation();
  const isTouch = useTouch();
  const systemTheme = useSystemTheme();
  const prefersReducedMotion = useReducedMotion();

  return {
    ...responsive,
    ...orientation,
    isTouch,
    ...systemTheme,
    prefersReducedMotion,
  };
};

export default useResponsive;

import axios from 'axios';
import { 
  fetchKPIs, 
  fetchAgents, 
  fetchConversations, 
  fetchConversationById, 
  fetchChartData 
} from './mockData.js';

// Configuração base da API
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';
const N8N_WEBHOOK_URL = process.env.REACT_APP_N8N_WEBHOOK_URL || '';

// Instância do axios com configurações padrão
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para adicionar token de autenticação se necessário
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor para tratamento de erros
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    
    if (error.response?.status === 401) {
      // Token expirado ou inválido
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

// Flag para usar dados mock durante desenvolvimento
const USE_MOCK_DATA = process.env.NODE_ENV === 'development' || !N8N_WEBHOOK_URL;

// Serviços de API

/**
 * Busca KPIs do dashboard
 */
export const getKPIs = async (dateRange = {}) => {
  if (USE_MOCK_DATA) {
    return await fetchKPIs();
  }
  
  try {
    const response = await apiClient.get('/api/kpis', {
      params: dateRange
    });
    return response.data;
  } catch (error) {
    console.error('Erro ao buscar KPIs:', error);
    // Fallback para dados mock em caso de erro
    return await fetchKPIs();
  }
};

/**
 * Busca lista de agentes de IA
 */
export const getAgents = async () => {
  if (USE_MOCK_DATA) {
    return await fetchAgents();
  }
  
  try {
    const response = await apiClient.get('/api/agents');
    return response.data;
  } catch (error) {
    console.error('Erro ao buscar agentes:', error);
    return await fetchAgents();
  }
};

/**
 * Busca conversas com filtros opcionais
 */
export const getConversations = async (filters = {}) => {
  if (USE_MOCK_DATA) {
    return await fetchConversations(filters);
  }
  
  try {
    const response = await apiClient.get('/api/conversations', {
      params: filters
    });
    return response.data;
  } catch (error) {
    console.error('Erro ao buscar conversas:', error);
    return await fetchConversations(filters);
  }
};

/**
 * Busca detalhes de uma conversa específica
 */
export const getConversationById = async (id) => {
  if (USE_MOCK_DATA) {
    return await fetchConversationById(id);
  }
  
  try {
    const response = await apiClient.get(`/api/conversations/${id}`);
    return response.data;
  } catch (error) {
    console.error('Erro ao buscar conversa:', error);
    return await fetchConversationById(id);
  }
};

/**
 * Busca dados para gráficos
 */
export const getChartData = async (type = 'all', dateRange = {}) => {
  if (USE_MOCK_DATA) {
    return await fetchChartData();
  }
  
  try {
    const response = await apiClient.get('/api/charts', {
      params: { type, ...dateRange }
    });
    return response.data;
  } catch (error) {
    console.error('Erro ao buscar dados dos gráficos:', error);
    return await fetchChartData();
  }
};

/**
 * Envia mensagem via WhatsApp através do n8n
 */
export const sendWhatsAppMessage = async (phone, message) => {
  if (USE_MOCK_DATA) {
    console.log('Mock: Enviando WhatsApp para', phone, ':', message);
    return { success: true, message: 'Mensagem enviada com sucesso (mock)' };
  }
  
  try {
    const response = await apiClient.post('/api/whatsapp/send', {
      phone,
      message
    });
    return response.data;
  } catch (error) {
    console.error('Erro ao enviar WhatsApp:', error);
    throw error;
  }
};

/**
 * Agenda uma call/reunião
 */
export const scheduleCall = async (leadId, datetime, notes = '') => {
  if (USE_MOCK_DATA) {
    console.log('Mock: Agendando call para lead', leadId, 'em', datetime);
    return { success: true, message: 'Call agendada com sucesso (mock)' };
  }
  
  try {
    const response = await apiClient.post('/api/schedule', {
      leadId,
      datetime,
      notes
    });
    return response.data;
  } catch (error) {
    console.error('Erro ao agendar call:', error);
    throw error;
  }
};

/**
 * Marca conversa como oportunidade
 */
export const markAsOpportunity = async (conversationId, priority = 'medium') => {
  if (USE_MOCK_DATA) {
    console.log('Mock: Marcando conversa', conversationId, 'como oportunidade');
    return { success: true, message: 'Marcado como oportunidade (mock)' };
  }
  
  try {
    const response = await apiClient.patch(`/api/conversations/${conversationId}/opportunity`, {
      priority
    });
    return response.data;
  } catch (error) {
    console.error('Erro ao marcar como oportunidade:', error);
    throw error;
  }
};

/**
 * Webhook para receber dados do n8n
 */
export const sendToN8nWebhook = async (data, webhookType = 'general') => {
  if (!N8N_WEBHOOK_URL) {
    console.log('Mock: Enviando para n8n webhook:', data);
    return { success: true };
  }
  
  try {
    const webhookUrl = `${N8N_WEBHOOK_URL}/${webhookType}`;
    const response = await axios.post(webhookUrl, data);
    return response.data;
  } catch (error) {
    console.error('Erro ao enviar para webhook n8n:', error);
    throw error;
  }
};

/**
 * Atualiza status de um agente
 */
export const updateAgentStatus = async (agentId, status) => {
  if (USE_MOCK_DATA) {
    console.log('Mock: Atualizando status do agente', agentId, 'para', status);
    return { success: true };
  }
  
  try {
    const response = await apiClient.patch(`/api/agents/${agentId}/status`, {
      status
    });
    return response.data;
  } catch (error) {
    console.error('Erro ao atualizar status do agente:', error);
    throw error;
  }
};

/**
 * Busca métricas de performance por período
 */
export const getPerformanceMetrics = async (startDate, endDate, agentId = null) => {
  if (USE_MOCK_DATA) {
    return {
      totalLeads: 150,
      convertedLeads: 23,
      revenue: 15420,
      avgResponseTime: 2.5,
      satisfactionScore: 4.2
    };
  }
  
  try {
    const response = await apiClient.get('/api/metrics/performance', {
      params: { startDate, endDate, agentId }
    });
    return response.data;
  } catch (error) {
    console.error('Erro ao buscar métricas de performance:', error);
    throw error;
  }
};

export default {
  getKPIs,
  getAgents,
  getConversations,
  getConversationById,
  getChartData,
  sendWhatsAppMessage,
  scheduleCall,
  markAsOpportunity,
  sendToN8nWebhook,
  updateAgentStatus,
  getPerformanceMetrics
};

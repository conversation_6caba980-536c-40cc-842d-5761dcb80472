// Dados mock para desenvolvimento do CRM

export const mockKPIs = {
  cpl: 45.50, // Custo por lead
  cac: 180.00, // Custo de aquisição de cliente
  cpc: 2.30, // Custo por clique
  conversionRate: 12.5, // Taxa de conversão em %
  revenuePerAgent: 15420.00, // Receita gerada por agente
  activeLeads: 234, // Número de leads ativos
  totalConversations: 1847, // Total de conversas iniciadas
  qualifiedConversations: 456 // Conversas qualificadas
};

export const mockAgents = [
  {
    id: 1,
    name: "Bot WhatsApp Vendas",
    channel: "WhatsApp",
    leadsAttended: 156,
    activeConversations: 23,
    responseRate: 94.2,
    status: "online",
    lastActivity: "2024-01-15T10:30:00Z"
  },
  {
    id: 2,
    name: "Assistente Instagram",
    channel: "Instagram",
    leadsAttended: 89,
    activeConversations: 12,
    responseRate: 87.5,
    status: "online",
    lastActivity: "2024-01-15T09:45:00Z"
  },
  {
    id: 3,
    name: "Chat Site Principal",
    channel: "Site",
    leadsAttended: 203,
    activeConversations: 31,
    responseRate: 91.8,
    status: "online",
    lastActivity: "2024-01-15T11:15:00Z"
  },
  {
    id: 4,
    name: "Bot Facebook Messenger",
    channel: "Facebook",
    leadsAttended: 67,
    activeConversations: 8,
    responseRate: 83.1,
    status: "offline",
    lastActivity: "2024-01-15T08:20:00Z"
  },
  {
    id: 5,
    name: "Suporte Telegram",
    channel: "Telegram",
    leadsAttended: 34,
    activeConversations: 5,
    responseRate: 96.7,
    status: "online",
    lastActivity: "2024-01-15T11:00:00Z"
  }
];

export const mockConversations = [
  {
    id: 1,
    leadName: "Maria Silva",
    channel: "WhatsApp",
    agentId: 1,
    status: "quente",
    intent: "orçamento",
    lastMessage: "Gostaria de saber o preço do plano premium",
    lastMessageTime: "2024-01-15T11:30:00Z",
    messagesCount: 8,
    phone: "+5511999887766",
    email: "<EMAIL>",
    messages: [
      {
        id: 1,
        sender: "lead",
        content: "Olá, gostaria de informações sobre seus serviços",
        timestamp: "2024-01-15T10:00:00Z"
      },
      {
        id: 2,
        sender: "agent",
        content: "Olá Maria! Fico feliz em ajudar. Que tipo de serviço você está procurando?",
        timestamp: "2024-01-15T10:01:00Z"
      },
      {
        id: 3,
        sender: "lead",
        content: "Preciso de uma solução para automação de vendas",
        timestamp: "2024-01-15T10:05:00Z"
      },
      {
        id: 4,
        sender: "agent",
        content: "Perfeito! Temos várias opções. Você já tem alguma ferramenta atualmente?",
        timestamp: "2024-01-15T10:06:00Z"
      },
      {
        id: 5,
        sender: "lead",
        content: "Não, seria minha primeira vez usando algo assim",
        timestamp: "2024-01-15T10:10:00Z"
      },
      {
        id: 6,
        sender: "agent",
        content: "Entendi! Vou te mostrar nosso plano ideal para iniciantes. Posso enviar um material?",
        timestamp: "2024-01-15T10:11:00Z"
      },
      {
        id: 7,
        sender: "lead",
        content: "Sim, por favor! E gostaria de saber o preço também",
        timestamp: "2024-01-15T10:15:00Z"
      },
      {
        id: 8,
        sender: "lead",
        content: "Gostaria de saber o preço do plano premium",
        timestamp: "2024-01-15T11:30:00Z"
      }
    ]
  },
  {
    id: 2,
    leadName: "João Santos",
    channel: "Instagram",
    agentId: 2,
    status: "morno",
    intent: "suporte",
    lastMessage: "Estou com dificuldades para configurar",
    lastMessageTime: "2024-01-15T10:45:00Z",
    messagesCount: 5,
    phone: "+5511888776655",
    email: "<EMAIL>",
    messages: [
      {
        id: 1,
        sender: "lead",
        content: "Oi, comprei o produto mas estou com dificuldades",
        timestamp: "2024-01-15T09:30:00Z"
      },
      {
        id: 2,
        sender: "agent",
        content: "Olá João! Vou te ajudar. Qual dificuldade específica você está enfrentando?",
        timestamp: "2024-01-15T09:31:00Z"
      },
      {
        id: 3,
        sender: "lead",
        content: "Não consigo fazer a integração com meu sistema",
        timestamp: "2024-01-15T09:35:00Z"
      },
      {
        id: 4,
        sender: "agent",
        content: "Entendo. Que sistema você está usando? Posso te enviar um tutorial específico.",
        timestamp: "2024-01-15T09:36:00Z"
      },
      {
        id: 5,
        sender: "lead",
        content: "Estou com dificuldades para configurar",
        timestamp: "2024-01-15T10:45:00Z"
      }
    ]
  },
  {
    id: 3,
    leadName: "Ana Costa",
    channel: "Site",
    agentId: 3,
    status: "quente",
    intent: "agendamento",
    lastMessage: "Posso agendar uma demonstração?",
    lastMessageTime: "2024-01-15T11:20:00Z",
    messagesCount: 6,
    phone: "+5511777665544",
    email: "<EMAIL>",
    messages: [
      {
        id: 1,
        sender: "lead",
        content: "Olá, vi vocês no Google e gostei do que fazem",
        timestamp: "2024-01-15T10:30:00Z"
      },
      {
        id: 2,
        sender: "agent",
        content: "Olá Ana! Muito obrigado pelo interesse. Como posso ajudá-la?",
        timestamp: "2024-01-15T10:31:00Z"
      },
      {
        id: 3,
        sender: "lead",
        content: "Gostaria de ver uma demonstração do produto",
        timestamp: "2024-01-15T10:35:00Z"
      },
      {
        id: 4,
        sender: "agent",
        content: "Claro! Seria ótimo mostrar como nossa solução pode ajudar sua empresa.",
        timestamp: "2024-01-15T10:36:00Z"
      },
      {
        id: 5,
        sender: "lead",
        content: "Que horários vocês têm disponível esta semana?",
        timestamp: "2024-01-15T11:00:00Z"
      },
      {
        id: 6,
        sender: "lead",
        content: "Posso agendar uma demonstração?",
        timestamp: "2024-01-15T11:20:00Z"
      }
    ]
  },
  {
    id: 4,
    leadName: "Pedro Oliveira",
    channel: "Facebook",
    agentId: 4,
    status: "frio",
    intent: "informação",
    lastMessage: "Obrigado pelas informações",
    lastMessageTime: "2024-01-15T08:30:00Z",
    messagesCount: 3,
    phone: "+5511666554433",
    email: "<EMAIL>",
    messages: [
      {
        id: 1,
        sender: "lead",
        content: "Oi, vi o anúncio de vocês no Facebook",
        timestamp: "2024-01-15T08:00:00Z"
      },
      {
        id: 2,
        sender: "agent",
        content: "Olá Pedro! Que bom que nos encontrou. Em que posso ajudá-lo?",
        timestamp: "2024-01-15T08:01:00Z"
      },
      {
        id: 3,
        sender: "lead",
        content: "Obrigado pelas informações",
        timestamp: "2024-01-15T08:30:00Z"
      }
    ]
  }
];

export const mockChartData = {
  conversionByChannel: [
    { name: 'WhatsApp', value: 35, color: '#25D366' },
    { name: 'Instagram', value: 25, color: '#E4405F' },
    { name: 'Site', value: 30, color: '#3b82f6' },
    { name: 'Facebook', value: 10, color: '#1877F2' }
  ],
  revenueOverTime: [
    { month: 'Jan', revenue: 12000 },
    { month: 'Fev', revenue: 15000 },
    { month: 'Mar', revenue: 18000 },
    { month: 'Abr', revenue: 16000 },
    { month: 'Mai', revenue: 22000 },
    { month: 'Jun', revenue: 25000 }
  ],
  leadsOverTime: [
    { month: 'Jan', leads: 120 },
    { month: 'Fev', leads: 150 },
    { month: 'Mar', leads: 180 },
    { month: 'Abr', leads: 160 },
    { month: 'Mai', leads: 220 },
    { month: 'Jun', leads: 250 }
  ]
};

// Função para simular delay de API
export const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Funções para simular chamadas de API
export const fetchKPIs = async () => {
  await delay(500);
  return mockKPIs;
};

export const fetchAgents = async () => {
  await delay(300);
  return mockAgents;
};

export const fetchConversations = async (filters = {}) => {
  await delay(400);
  let filtered = [...mockConversations];
  
  if (filters.channel) {
    filtered = filtered.filter(conv => conv.channel === filters.channel);
  }
  
  if (filters.status) {
    filtered = filtered.filter(conv => conv.status === filters.status);
  }
  
  if (filters.agentId) {
    filtered = filtered.filter(conv => conv.agentId === filters.agentId);
  }
  
  return filtered;
};

export const fetchConversationById = async (id) => {
  await delay(200);
  return mockConversations.find(conv => conv.id === parseInt(id));
};

export const fetchChartData = async () => {
  await delay(600);
  return mockChartData;
};

import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BarChart3,
  Users,
  MessageSquare,
  Settings,
  Home,
  Menu,
  X,
  Sun,
  Moon
} from 'lucide-react';
import Dashboard from './components/DashboardNew';
import AgentsList from './components/AgentsListNew';
import MobileNavigation from './components/MobileNavigation';
import Button from './components/ui/Button';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import useResponsive from './hooks/useResponsive';
import { cn } from './utils/cn';

// Componente de Sidebar para Desktop
const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation();
  const { theme, toggleTheme } = useTheme();

  const navItems = [
    {
      path: '/',
      icon: Home,
      label: 'Dashboard',
      exact: true
    },
    {
      path: '/agents',
      icon: Users,
      label: 'Agentes de IA'
    },
    {
      path: '/conversations',
      icon: MessageSquare,
      label: 'Conversas'
    },
    {
      path: '/analytics',
      icon: BarChart3,
      label: 'Analytics'
    },
    {
      path: '/settings',
      icon: Settings,
      label: 'Configurações'
    }
  ];

  const isActive = (path, exact = false) => {
    if (exact) {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  return (
    <AnimatePresence>
      {(isOpen || window.innerWidth >= 768) && (
        <motion.div
          className={cn(
            'fixed left-0 top-0 bottom-0 w-72 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 z-50',
            'flex flex-col p-6 glass-effect',
            'md:relative md:translate-x-0',
            !isOpen && 'md:block hidden'
          )}
          initial={{ x: -288 }}
          animate={{ x: 0 }}
          exit={{ x: -288 }}
          transition={{ type: "spring", damping: 25, stiffness: 200 }}
        >
          {/* Header da Sidebar */}
          <div className="flex items-center justify-between mb-8">
            <motion.div
              className="flex items-center gap-3"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center shadow-lg">
                <BarChart3 size={24} className="text-white" />
              </div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white gradient-text">CRM AI</h1>
            </motion.div>

            {/* Botão de fechar para mobile */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="md:hidden"
            >
              <X size={20} />
            </Button>
          </div>

          {/* Navegação */}
          <nav className="flex-1 space-y-2">
            {navItems.map(({ path, icon: Icon, label, exact }, index) => (
              <motion.div
                key={path}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Link
                  to={path}
                  onClick={onClose}
                  className={cn(
                    'flex items-center gap-3 px-4 py-3 rounded-xl font-medium transition-all duration-200',
                    'hover:bg-gray-100 dark:hover:bg-gray-800',
                    isActive(path, exact)
                      ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 shadow-sm'
                      : 'text-gray-700 dark:text-gray-300'
                  )}
                >
                  <Icon size={20} />
                  <span>{label}</span>
                  {isActive(path, exact) && (
                    <motion.div
                      className="ml-auto w-2 h-2 bg-primary-500 rounded-full"
                      layoutId="activeIndicator"
                      transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    />
                  )}
                </Link>
              </motion.div>
            ))}
          </nav>

          {/* Theme Toggle */}
          <div className="mb-6">
            <Button
              variant="ghost"
              onClick={toggleTheme}
              className="w-full justify-start"
              leftIcon={theme === 'dark' ? <Sun size={20} /> : <Moon size={20} />}
            >
              {theme === 'dark' ? 'Modo Claro' : 'Modo Escuro'}
            </Button>
          </div>

          {/* Footer da Sidebar */}
          <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="text-xs text-gray-500 dark:text-gray-400 text-center space-y-1">
              <p className="font-medium">CRM AI v2.0</p>
              <p>Powered by n8n</p>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Componente de Header para Mobile
const MobileHeader = ({ onMenuClick }) => {
  const { theme, toggleTheme } = useTheme();

  return (
    <motion.header
      className="md:hidden bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex items-center justify-between glass-effect"
      initial={{ y: -60 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", damping: 20, stiffness: 300 }}
    >
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center shadow-md">
          <BarChart3 size={20} className="text-white" />
        </div>
        <h1 className="text-xl font-bold text-gray-900 dark:text-white gradient-text">CRM AI</h1>
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleTheme}
        >
          {theme === 'dark' ? <Sun size={18} /> : <Moon size={18} />}
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onMenuClick}
        >
          <Menu size={20} />
        </Button>
      </div>
    </motion.header>
  );
};

// Componente principal da aplicação
const AppContent = () => {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);
  const { isMobile } = useResponsive();

  const closeSidebar = () => setSidebarOpen(false);
  const openSidebar = () => setSidebarOpen(true);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      {/* Sidebar */}
      <Sidebar isOpen={sidebarOpen} onClose={closeSidebar} />

      {/* Overlay para mobile quando sidebar está aberta */}
      <AnimatePresence>
        {isMobile && sidebarOpen && (
          <motion.div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={closeSidebar}
          />
        )}
      </AnimatePresence>

      {/* Conteúdo Principal */}
      <div className={cn(
        'transition-all duration-300',
        'md:ml-72'
      )}>
        {/* Header Mobile */}
        <MobileHeader onMenuClick={openSidebar} />

        {/* Rotas */}
        <main className="min-h-screen">
          <AnimatePresence mode="wait">
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/agents" element={<AgentsList />} />
              <Route path="/conversations" element={
                <motion.div
                  className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Conversas</h1>
                  <p className="text-gray-600 dark:text-gray-400">Em desenvolvimento...</p>
                </motion.div>
              } />
              <Route path="/analytics" element={
                <motion.div
                  className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Analytics</h1>
                  <p className="text-gray-600 dark:text-gray-400">Em desenvolvimento...</p>
                </motion.div>
              } />
              <Route path="/settings" element={
                <motion.div
                  className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Configurações</h1>
                  <p className="text-gray-600 dark:text-gray-400">Em desenvolvimento...</p>
                </motion.div>
              } />
            </Routes>
          </AnimatePresence>
        </main>
      </div>

      {/* Navegação Mobile */}
      <MobileNavigation />
    </div>
  );
};

// Componente App com Router
function App() {
  return (
    <ThemeProvider>
      <Router>
        <AppContent />
      </Router>
    </ThemeProvider>
  );
}

export default App;

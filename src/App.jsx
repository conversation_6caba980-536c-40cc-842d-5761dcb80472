import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import {
  BarChart3,
  Users,
  MessageSquare,
  Settings,
  Home,
  Menu,
  X
} from 'lucide-react';
import Dashboard from './components/Dashboard';
import AgentsList from './components/AgentsList';
import MobileNavigation from './components/MobileNavigation';
import useResponsive from './hooks/useResponsive';

// Componente de Sidebar para Desktop
const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation();

  const navItems = [
    {
      path: '/',
      icon: Home,
      label: 'Dashboard',
      exact: true
    },
    {
      path: '/agents',
      icon: Users,
      label: 'Agentes de IA'
    },
    {
      path: '/conversations',
      icon: MessageSquare,
      label: 'Conversas'
    },
    {
      path: '/analytics',
      icon: BarChart3,
      label: 'Analytics'
    },
    {
      path: '/settings',
      icon: Settings,
      label: 'Configurações'
    }
  ];

  const isActive = (path, exact = false) => {
    if (exact) {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  return (
    <div className={`sidebar ${isOpen ? 'block' : 'hidden'} md:block`}>
      {/* Header da Sidebar */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <BarChart3 size={20} className="text-white" />
          </div>
          <h1 className="text-xl font-bold text-gray-900">CRM AI</h1>
        </div>

        {/* Botão de fechar para mobile */}
        <button
          onClick={onClose}
          className="md:hidden p-1 hover:bg-gray-100 rounded"
        >
          <X size={20} />
        </button>
      </div>

      {/* Navegação */}
      <nav>
        {navItems.map(({ path, icon: Icon, label, exact }) => (
          <Link
            key={path}
            to={path}
            onClick={onClose}
            className={`sidebar-item ${isActive(path, exact) ? 'active' : ''}`}
          >
            <Icon size={20} />
            <span>{label}</span>
          </Link>
        ))}
      </nav>

      {/* Footer da Sidebar */}
      <div className="mt-auto pt-6 border-t border-gray-200">
        <div className="text-xs text-gray-500 text-center">
          <p>CRM AI v1.0</p>
          <p>Powered by n8n</p>
        </div>
      </div>
    </div>
  );
};

// Componente de Header para Mobile
const MobileHeader = ({ onMenuClick }) => {
  return (
    <header className="md:hidden bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
          <BarChart3 size={20} className="text-white" />
        </div>
        <h1 className="text-xl font-bold text-gray-900">CRM AI</h1>
      </div>

      <button
        onClick={onMenuClick}
        className="p-2 hover:bg-gray-100 rounded-lg"
      >
        <Menu size={20} />
      </button>
    </header>
  );
};

// Componente principal da aplicação
const AppContent = () => {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);
  const { isMobile } = useResponsive();

  const closeSidebar = () => setSidebarOpen(false);
  const openSidebar = () => setSidebarOpen(true);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar isOpen={sidebarOpen} onClose={closeSidebar} />

      {/* Overlay para mobile quando sidebar está aberta */}
      {isMobile && sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={closeSidebar}
        />
      )}

      {/* Conteúdo Principal */}
      <div className="main-content">
        {/* Header Mobile */}
        <MobileHeader onMenuClick={openSidebar} />

        {/* Rotas */}
        <main>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/agents" element={<AgentsList />} />
            <Route path="/conversations" element={
              <div className="container py-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">Conversas</h1>
                <p className="text-gray-600">Em desenvolvimento...</p>
              </div>
            } />
            <Route path="/analytics" element={
              <div className="container py-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">Analytics</h1>
                <p className="text-gray-600">Em desenvolvimento...</p>
              </div>
            } />
            <Route path="/settings" element={
              <div className="container py-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">Configurações</h1>
                <p className="text-gray-600">Em desenvolvimento...</p>
              </div>
            } />
          </Routes>
        </main>
      </div>

      {/* Navegação Mobile */}
      <MobileNavigation />
    </div>
  );
};

// Componente App com Router
function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

export default App;

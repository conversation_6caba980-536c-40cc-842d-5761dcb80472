{"name": "crm", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "deploy": "npm run build && gh-pages -d dist"}, "homepage": "https://maycongodoy.github.io/crm-ai-dashboard", "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "axios": "^1.9.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.1", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.8", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "gh-pages": "^6.3.0", "globals": "^16.0.0", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "vite": "^6.3.5"}}
# Integração com n8n

Este documento descreve como integrar o CRM AI com o n8n para automatizar o fluxo de dados dos agentes de IA.

## Configuração Básica

### 1. Webhook de Entrada no n8n

Crie um workflow no n8n com um nó **Webhook** configurado para receber dados do CRM:

```json
{
  "httpMethod": "POST",
  "path": "crm-data",
  "responseMode": "responseNode",
  "options": {}
}
```

### 2. Estrutura de Dados

#### Envio de KPIs
```javascript
// POST /webhook/crm-kpis
{
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "cpl": 45.50,
    "cac": 180.00,
    "cpc": 2.30,
    "conversionRate": 12.5,
    "revenuePerAgent": 15420.00,
    "activeLeads": 234,
    "totalConversations": 1847,
    "qualifiedConversations": 456
  }
}
```

#### Atualização de Agentes
```javascript
// POST /webhook/crm-agents
{
  "timestamp": "2024-01-15T10:30:00Z",
  "agents": [
    {
      "id": 1,
      "name": "Bot WhatsApp Vendas",
      "channel": "WhatsApp",
      "leadsAttended": 156,
      "activeConversations": 23,
      "responseRate": 94.2,
      "status": "online",
      "lastActivity": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### Nova Conversa
```javascript
// POST /webhook/crm-conversation
{
  "timestamp": "2024-01-15T10:30:00Z",
  "conversation": {
    "id": 1,
    "leadName": "Maria Silva",
    "channel": "WhatsApp",
    "agentId": 1,
    "status": "quente",
    "intent": "orçamento",
    "phone": "+5511999887766",
    "email": "<EMAIL>",
    "messages": [
      {
        "id": 1,
        "sender": "lead",
        "content": "Olá, gostaria de informações",
        "timestamp": "2024-01-15T10:00:00Z"
      }
    ]
  }
}
```

## Workflows Recomendados

### 1. Coleta de Dados dos Agentes

```
[Webhook] → [Function] → [Database] → [HTTP Response]
```

**Function Node:**
```javascript
// Processa dados dos agentes
const agents = $input.all();
const processedData = agents.map(agent => ({
  ...agent.json,
  lastUpdate: new Date().toISOString(),
  performance: calculatePerformance(agent.json)
}));

return processedData;
```

### 2. Análise de Conversas

```
[Webhook] → [AI Node] → [Function] → [Database] → [Conditional] → [WhatsApp/Email]
```

**AI Node (OpenAI):**
```javascript
// Analisa intenção da conversa
const prompt = `
Analise a seguinte conversa e determine:
1. Intenção do lead (orçamento, suporte, agendamento, etc.)
2. Classificação (quente, morno, frio)
3. Próxima ação recomendada

Conversa: ${$json.messages.map(m => `${m.sender}: ${m.content}`).join('\n')}
`;
```

### 3. Automação de Respostas

```
[Webhook] → [Function] → [Switch] → [WhatsApp API] → [Database]
```

**Switch Node:**
```javascript
// Roteamento baseado na intenção
switch($json.intent) {
  case 'orçamento':
    return [{ json: { action: 'send_pricing' } }];
  case 'suporte':
    return [{ json: { action: 'create_ticket' } }];
  case 'agendamento':
    return [{ json: { action: 'schedule_call' } }];
  default:
    return [{ json: { action: 'human_handoff' } }];
}
```

## Configuração de Banco de Dados

### PostgreSQL Schema

```sql
-- Tabela de Agentes
CREATE TABLE agents (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  channel VARCHAR(100) NOT NULL,
  leads_attended INTEGER DEFAULT 0,
  active_conversations INTEGER DEFAULT 0,
  response_rate DECIMAL(5,2) DEFAULT 0,
  status VARCHAR(20) DEFAULT 'offline',
  last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de Conversas
CREATE TABLE conversations (
  id SERIAL PRIMARY KEY,
  lead_name VARCHAR(255) NOT NULL,
  channel VARCHAR(100) NOT NULL,
  agent_id INTEGER REFERENCES agents(id),
  status VARCHAR(20) DEFAULT 'novo',
  intent VARCHAR(100),
  phone VARCHAR(20),
  email VARCHAR(255),
  last_message TEXT,
  last_message_time TIMESTAMP,
  messages_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de Mensagens
CREATE TABLE messages (
  id SERIAL PRIMARY KEY,
  conversation_id INTEGER REFERENCES conversations(id),
  sender VARCHAR(20) NOT NULL, -- 'lead' ou 'agent'
  content TEXT NOT NULL,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de KPIs
CREATE TABLE kpis (
  id SERIAL PRIMARY KEY,
  date DATE NOT NULL,
  cpl DECIMAL(10,2),
  cac DECIMAL(10,2),
  cpc DECIMAL(10,2),
  conversion_rate DECIMAL(5,2),
  revenue_per_agent DECIMAL(10,2),
  active_leads INTEGER,
  total_conversations INTEGER,
  qualified_conversations INTEGER,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## APIs de Integração

### WhatsApp Business API

```javascript
// Nó HTTP Request para enviar WhatsApp
{
  "method": "POST",
  "url": "https://graph.facebook.com/v17.0/{{$json.phone_number_id}}/messages",
  "headers": {
    "Authorization": "Bearer {{$json.access_token}}",
    "Content-Type": "application/json"
  },
  "body": {
    "messaging_product": "whatsapp",
    "to": "{{$json.phone}}",
    "type": "text",
    "text": {
      "body": "{{$json.message}}"
    }
  }
}
```

### Google Calendar API

```javascript
// Agendar reunião
{
  "method": "POST",
  "url": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
  "headers": {
    "Authorization": "Bearer {{$json.access_token}}",
    "Content-Type": "application/json"
  },
  "body": {
    "summary": "Call com {{$json.leadName}}",
    "description": "{{$json.notes}}",
    "start": {
      "dateTime": "{{$json.datetime}}",
      "timeZone": "America/Sao_Paulo"
    },
    "end": {
      "dateTime": "{{$json.endDateTime}}",
      "timeZone": "America/Sao_Paulo"
    },
    "attendees": [
      {
        "email": "{{$json.email}}"
      }
    ]
  }
}
```

## Monitoramento e Alertas

### Workflow de Monitoramento

```
[Cron] → [Database Query] → [Function] → [Conditional] → [Slack/Email Alert]
```

**Cron Node:** Execute a cada 15 minutos

**Function Node:**
```javascript
// Verifica métricas críticas
const agents = $input.all();
const alerts = [];

agents.forEach(agent => {
  if (agent.json.response_rate < 80) {
    alerts.push({
      type: 'low_response_rate',
      agent: agent.json.name,
      value: agent.json.response_rate
    });
  }
  
  if (agent.json.status === 'offline' && agent.json.active_conversations > 0) {
    alerts.push({
      type: 'agent_offline_with_conversations',
      agent: agent.json.name,
      conversations: agent.json.active_conversations
    });
  }
});

return alerts;
```

## Segurança

### Autenticação
- Use tokens JWT para autenticar requisições
- Configure CORS adequadamente
- Implemente rate limiting

### Validação de Dados
```javascript
// Function Node para validação
const schema = {
  leadName: { type: 'string', required: true },
  channel: { type: 'string', required: true },
  phone: { type: 'string', pattern: /^\+\d{10,15}$/ },
  email: { type: 'string', pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ }
};

function validateData(data, schema) {
  // Implementar validação
  return { isValid: true, errors: [] };
}
```

## Troubleshooting

### Problemas Comuns

1. **Webhook não recebe dados**
   - Verifique a URL do webhook
   - Confirme que o n8n está acessível
   - Verifique logs de erro

2. **Dados não aparecem no CRM**
   - Verifique formato dos dados
   - Confirme mapeamento de campos
   - Verifique conexão com banco

3. **Performance lenta**
   - Otimize queries do banco
   - Implemente cache
   - Use paginação

### Logs e Debugging

```javascript
// Function Node para logging
console.log('Received data:', JSON.stringify($input.all(), null, 2));

// Log de erro
if (error) {
  console.error('Error processing data:', error.message);
  return [{ json: { error: error.message, timestamp: new Date().toISOString() } }];
}
```

import{j as o,m as $e,T as dc,a as _y,D as By,b as Rm,M as qy,P as Hy,C as fh,R as dh,c as ni,B as Cm,A as Ly,U as Ac,E as Gy,d as mh,e as Yy,S as Us,f as Xy,g as Zy,X as Tc,h as hh,i as Um,k as Qy,l as Vy,n as Ky,o as Jy,p as Ds,q as Ec,r as Wy,s as Fy,t as $y,u as Py,v as Iy,w as eb,F as tb,H as gh,x as yh,y as bh,z as ab}from"./ui-BaXaZAi8.js";import{r as se,u as lb,a as ph,L as xh,B as nb,R as ib,b as sb,c as In}from"./router-Dh5s4hGl.js";import{r as rb,a as ub}from"./vendor-Csw2ODfV.js";import{c as cb,R as zc,P as ob,a as fb,C as db,T as Mc,L as vh,b as Sh,X as jh,Y as mc,d as hc,e as mb}from"./charts-D9Maeeg0.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))u(d);new MutationObserver(d=>{for(const h of d)if(h.type==="childList")for(const g of h.addedNodes)g.tagName==="LINK"&&g.rel==="modulepreload"&&u(g)}).observe(document,{childList:!0,subtree:!0});function c(d){const h={};return d.integrity&&(h.integrity=d.integrity),d.referrerPolicy&&(h.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?h.credentials="include":d.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function u(d){if(d.ep)return;d.ep=!0;const h=c(d);fetch(d.href,h)}})();var lc={exports:{}},ei={},nc={exports:{}},ic={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var km;function hb(){return km||(km=1,function(i){function r(A,B){var C=A.length;A.push(B);e:for(;0<C;){var be=C-1>>>1,pe=A[be];if(0<d(pe,B))A[be]=B,A[C]=pe,C=be;else break e}}function c(A){return A.length===0?null:A[0]}function u(A){if(A.length===0)return null;var B=A[0],C=A.pop();if(C!==B){A[0]=C;e:for(var be=0,pe=A.length,Ye=pe>>>1;be<Ye;){var Ne=2*(be+1)-1,F=A[Ne],ce=Ne+1,tt=A[ce];if(0>d(F,C))ce<pe&&0>d(tt,F)?(A[be]=tt,A[ce]=C,be=ce):(A[be]=F,A[Ne]=C,be=Ne);else if(ce<pe&&0>d(tt,C))A[be]=tt,A[ce]=C,be=ce;else break e}}return B}function d(A,B){var C=A.sortIndex-B.sortIndex;return C!==0?C:A.id-B.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;i.unstable_now=function(){return h.now()}}else{var g=Date,b=g.now();i.unstable_now=function(){return g.now()-b}}var w=[],j=[],v=1,M=null,k=3,Z=!1,U=!1,_=!1,q=!1,$=typeof setTimeout=="function"?setTimeout:null,ne=typeof clearTimeout=="function"?clearTimeout:null,J=typeof setImmediate<"u"?setImmediate:null;function ue(A){for(var B=c(j);B!==null;){if(B.callback===null)u(j);else if(B.startTime<=A)u(j),B.sortIndex=B.expirationTime,r(w,B);else break;B=c(j)}}function re(A){if(_=!1,ue(A),!U)if(c(w)!==null)U=!0,de||(de=!0,qe());else{var B=c(j);B!==null&&X(re,B.startTime-A)}}var de=!1,je=-1,Y=5,Ee=-1;function Mt(){return q?!0:!(i.unstable_now()-Ee<Y)}function he(){if(q=!1,de){var A=i.unstable_now();Ee=A;var B=!0;try{e:{U=!1,_&&(_=!1,ne(je),je=-1),Z=!0;var C=k;try{t:{for(ue(A),M=c(w);M!==null&&!(M.expirationTime>A&&Mt());){var be=M.callback;if(typeof be=="function"){M.callback=null,k=M.priorityLevel;var pe=be(M.expirationTime<=A);if(A=i.unstable_now(),typeof pe=="function"){M.callback=pe,ue(A),B=!0;break t}M===c(w)&&u(w),ue(A)}else u(w);M=c(w)}if(M!==null)B=!0;else{var Ye=c(j);Ye!==null&&X(re,Ye.startTime-A),B=!1}}break e}finally{M=null,k=C,Z=!1}B=void 0}}finally{B?qe():de=!1}}}var qe;if(typeof J=="function")qe=function(){J(he)};else if(typeof MessageChannel<"u"){var Kt=new MessageChannel,lt=Kt.port2;Kt.port1.onmessage=he,qe=function(){lt.postMessage(null)}}else qe=function(){$(he,0)};function X(A,B){je=$(function(){A(i.unstable_now())},B)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(A){A.callback=null},i.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Y=0<A?Math.floor(1e3/A):5},i.unstable_getCurrentPriorityLevel=function(){return k},i.unstable_next=function(A){switch(k){case 1:case 2:case 3:var B=3;break;default:B=k}var C=k;k=B;try{return A()}finally{k=C}},i.unstable_requestPaint=function(){q=!0},i.unstable_runWithPriority=function(A,B){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var C=k;k=A;try{return B()}finally{k=C}},i.unstable_scheduleCallback=function(A,B,C){var be=i.unstable_now();switch(typeof C=="object"&&C!==null?(C=C.delay,C=typeof C=="number"&&0<C?be+C:be):C=be,A){case 1:var pe=-1;break;case 2:pe=250;break;case 5:pe=1073741823;break;case 4:pe=1e4;break;default:pe=5e3}return pe=C+pe,A={id:v++,callback:B,priorityLevel:A,startTime:C,expirationTime:pe,sortIndex:-1},C>be?(A.sortIndex=C,r(j,A),c(w)===null&&A===c(j)&&(_?(ne(je),je=-1):_=!0,X(re,C-be))):(A.sortIndex=pe,r(w,A),U||Z||(U=!0,de||(de=!0,qe()))),A},i.unstable_shouldYield=Mt,i.unstable_wrapCallback=function(A){var B=k;return function(){var C=k;k=B;try{return A.apply(this,arguments)}finally{k=C}}}}(ic)),ic}var _m;function gb(){return _m||(_m=1,nc.exports=hb()),nc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bm;function yb(){if(Bm)return ei;Bm=1;var i=gb(),r=rb(),c=ub();function u(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function h(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function g(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function b(e){if(h(e)!==e)throw Error(u(188))}function w(e){var t=e.alternate;if(!t){if(t=h(e),t===null)throw Error(u(188));return t!==e?null:e}for(var a=e,l=t;;){var n=a.return;if(n===null)break;var s=n.alternate;if(s===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===s.child){for(s=n.child;s;){if(s===a)return b(n),e;if(s===l)return b(n),t;s=s.sibling}throw Error(u(188))}if(a.return!==l.return)a=n,l=s;else{for(var f=!1,m=n.child;m;){if(m===a){f=!0,a=n,l=s;break}if(m===l){f=!0,l=n,a=s;break}m=m.sibling}if(!f){for(m=s.child;m;){if(m===a){f=!0,a=s,l=n;break}if(m===l){f=!0,l=s,a=n;break}m=m.sibling}if(!f)throw Error(u(189))}}if(a.alternate!==l)throw Error(u(190))}if(a.tag!==3)throw Error(u(188));return a.stateNode.current===a?e:t}function j(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=j(e),t!==null)return t;e=e.sibling}return null}var v=Object.assign,M=Symbol.for("react.element"),k=Symbol.for("react.transitional.element"),Z=Symbol.for("react.portal"),U=Symbol.for("react.fragment"),_=Symbol.for("react.strict_mode"),q=Symbol.for("react.profiler"),$=Symbol.for("react.provider"),ne=Symbol.for("react.consumer"),J=Symbol.for("react.context"),ue=Symbol.for("react.forward_ref"),re=Symbol.for("react.suspense"),de=Symbol.for("react.suspense_list"),je=Symbol.for("react.memo"),Y=Symbol.for("react.lazy"),Ee=Symbol.for("react.activity"),Mt=Symbol.for("react.memo_cache_sentinel"),he=Symbol.iterator;function qe(e){return e===null||typeof e!="object"?null:(e=he&&e[he]||e["@@iterator"],typeof e=="function"?e:null)}var Kt=Symbol.for("react.client.reference");function lt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Kt?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case U:return"Fragment";case q:return"Profiler";case _:return"StrictMode";case re:return"Suspense";case de:return"SuspenseList";case Ee:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case Z:return"Portal";case J:return(e.displayName||"Context")+".Provider";case ne:return(e._context.displayName||"Context")+".Consumer";case ue:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case je:return t=e.displayName||null,t!==null?t:lt(e.type)||"Memo";case Y:t=e._payload,e=e._init;try{return lt(e(t))}catch{}}return null}var X=Array.isArray,A=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,B=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,C={pending:!1,data:null,method:null,action:null},be=[],pe=-1;function Ye(e){return{current:e}}function Ne(e){0>pe||(e.current=be[pe],be[pe]=null,pe--)}function F(e,t){pe++,be[pe]=e.current,e.current=t}var ce=Ye(null),tt=Ye(null),Ot=Ye(null),ze=Ye(null);function Ga(e,t){switch(F(Ot,t),F(tt,e),F(ce,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?sm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=sm(t),e=rm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}Ne(ce),F(ce,e)}function Dt(){Ne(ce),Ne(tt),Ne(Ot)}function oa(e){e.memoizedState!==null&&F(ze,e);var t=ce.current,a=rm(t,e.type);t!==a&&(F(tt,e),F(ce,a))}function fa(e){tt.current===e&&(Ne(ce),Ne(tt)),ze.current===e&&(Ne(ze),Jn._currentValue=C)}var da=Object.prototype.hasOwnProperty,Zs=i.unstable_scheduleCallback,Qs=i.unstable_cancelCallback,h0=i.unstable_shouldYield,g0=i.unstable_requestPaint,Bt=i.unstable_now,y0=i.unstable_getCurrentPriorityLevel,qc=i.unstable_ImmediatePriority,Hc=i.unstable_UserBlockingPriority,ui=i.unstable_NormalPriority,b0=i.unstable_LowPriority,Lc=i.unstable_IdlePriority,p0=i.log,x0=i.unstable_setDisableYieldValue,tn=null,ft=null;function ma(e){if(typeof p0=="function"&&x0(e),ft&&typeof ft.setStrictMode=="function")try{ft.setStrictMode(tn,e)}catch{}}var dt=Math.clz32?Math.clz32:j0,v0=Math.log,S0=Math.LN2;function j0(e){return e>>>=0,e===0?32:31-(v0(e)/S0|0)|0}var ci=256,oi=4194304;function Ya(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function fi(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var n=0,s=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var m=l&134217727;return m!==0?(l=m&~s,l!==0?n=Ya(l):(f&=m,f!==0?n=Ya(f):a||(a=m&~e,a!==0&&(n=Ya(a))))):(m=l&~s,m!==0?n=Ya(m):f!==0?n=Ya(f):a||(a=l&~e,a!==0&&(n=Ya(a)))),n===0?0:t!==0&&t!==n&&(t&s)===0&&(s=n&-n,a=t&-t,s>=a||s===32&&(a&4194048)!==0)?t:n}function an(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function N0(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Gc(){var e=ci;return ci<<=1,(ci&4194048)===0&&(ci=256),e}function Yc(){var e=oi;return oi<<=1,(oi&62914560)===0&&(oi=4194304),e}function Vs(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function ln(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function w0(e,t,a,l,n,s){var f=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var m=e.entanglements,y=e.expirationTimes,N=e.hiddenUpdates;for(a=f&~a;0<a;){var O=31-dt(a),R=1<<O;m[O]=0,y[O]=-1;var T=N[O];if(T!==null)for(N[O]=null,O=0;O<T.length;O++){var E=T[O];E!==null&&(E.lane&=-536870913)}a&=~R}l!==0&&Xc(e,l,0),s!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=s&~(f&~t))}function Xc(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-dt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function Zc(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-dt(a),n=1<<l;n&t|e[l]&t&&(e[l]|=t),a&=~n}}function Ks(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Js(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Qc(){var e=B.p;return e!==0?e:(e=window.event,e===void 0?32:Tm(e.type))}function A0(e,t){var a=B.p;try{return B.p=e,t()}finally{B.p=a}}var ha=Math.random().toString(36).slice(2),Pe="__reactFiber$"+ha,nt="__reactProps$"+ha,ol="__reactContainer$"+ha,Ws="__reactEvents$"+ha,T0="__reactListeners$"+ha,E0="__reactHandles$"+ha,Vc="__reactResources$"+ha,nn="__reactMarker$"+ha;function Fs(e){delete e[Pe],delete e[nt],delete e[Ws],delete e[T0],delete e[E0]}function fl(e){var t=e[Pe];if(t)return t;for(var a=e.parentNode;a;){if(t=a[ol]||a[Pe]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=fm(e);e!==null;){if(a=e[Pe])return a;e=fm(e)}return t}e=a,a=e.parentNode}return null}function dl(e){if(e=e[Pe]||e[ol]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function sn(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(u(33))}function ml(e){var t=e[Vc];return t||(t=e[Vc]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Xe(e){e[nn]=!0}var Kc=new Set,Jc={};function Xa(e,t){hl(e,t),hl(e+"Capture",t)}function hl(e,t){for(Jc[e]=t,e=0;e<t.length;e++)Kc.add(t[e])}var z0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Wc={},Fc={};function M0(e){return da.call(Fc,e)?!0:da.call(Wc,e)?!1:z0.test(e)?Fc[e]=!0:(Wc[e]=!0,!1)}function di(e,t,a){if(M0(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function mi(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Jt(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var $s,$c;function gl(e){if($s===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);$s=t&&t[1]||"",$c=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+$s+e+$c}var Ps=!1;function Is(e,t){if(!e||Ps)return"";Ps=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var R=function(){throw Error()};if(Object.defineProperty(R.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(R,[])}catch(E){var T=E}Reflect.construct(e,[],R)}else{try{R.call()}catch(E){T=E}e.call(R.prototype)}}else{try{throw Error()}catch(E){T=E}(R=e())&&typeof R.catch=="function"&&R.catch(function(){})}}catch(E){if(E&&T&&typeof E.stack=="string")return[E.stack,T.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var s=l.DetermineComponentFrameRoot(),f=s[0],m=s[1];if(f&&m){var y=f.split(`
`),N=m.split(`
`);for(n=l=0;l<y.length&&!y[l].includes("DetermineComponentFrameRoot");)l++;for(;n<N.length&&!N[n].includes("DetermineComponentFrameRoot");)n++;if(l===y.length||n===N.length)for(l=y.length-1,n=N.length-1;1<=l&&0<=n&&y[l]!==N[n];)n--;for(;1<=l&&0<=n;l--,n--)if(y[l]!==N[n]){if(l!==1||n!==1)do if(l--,n--,0>n||y[l]!==N[n]){var O=`
`+y[l].replace(" at new "," at ");return e.displayName&&O.includes("<anonymous>")&&(O=O.replace("<anonymous>",e.displayName)),O}while(1<=l&&0<=n);break}}}finally{Ps=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?gl(a):""}function O0(e){switch(e.tag){case 26:case 27:case 5:return gl(e.type);case 16:return gl("Lazy");case 13:return gl("Suspense");case 19:return gl("SuspenseList");case 0:case 15:return Is(e.type,!1);case 11:return Is(e.type.render,!1);case 1:return Is(e.type,!0);case 31:return gl("Activity");default:return""}}function Pc(e){try{var t="";do t+=O0(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function vt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ic(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function D0(e){var t=Ic(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,s=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(f){l=""+f,s.call(this,f)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function hi(e){e._valueTracker||(e._valueTracker=D0(e))}function eo(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=Ic(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function gi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var R0=/[\n"\\]/g;function St(e){return e.replace(R0,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function er(e,t,a,l,n,s,f,m){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+vt(t)):e.value!==""+vt(t)&&(e.value=""+vt(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?tr(e,f,vt(t)):a!=null?tr(e,f,vt(a)):l!=null&&e.removeAttribute("value"),n==null&&s!=null&&(e.defaultChecked=!!s),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.name=""+vt(m):e.removeAttribute("name")}function to(e,t,a,l,n,s,f,m){if(s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"&&(e.type=s),t!=null||a!=null){if(!(s!=="submit"&&s!=="reset"||t!=null))return;a=a!=null?""+vt(a):"",t=t!=null?""+vt(t):a,m||t===e.value||(e.value=t),e.defaultValue=t}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=m?e.checked:!!l,e.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function tr(e,t,a){t==="number"&&gi(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function yl(e,t,a,l){if(e=e.options,t){t={};for(var n=0;n<a.length;n++)t["$"+a[n]]=!0;for(a=0;a<e.length;a++)n=t.hasOwnProperty("$"+e[a].value),e[a].selected!==n&&(e[a].selected=n),n&&l&&(e[a].defaultSelected=!0)}else{for(a=""+vt(a),t=null,n=0;n<e.length;n++){if(e[n].value===a){e[n].selected=!0,l&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function ao(e,t,a){if(t!=null&&(t=""+vt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+vt(a):""}function lo(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(u(92));if(X(l)){if(1<l.length)throw Error(u(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=vt(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function bl(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var C0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function no(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||C0.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function io(e,t,a){if(t!=null&&typeof t!="object")throw Error(u(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var n in t)l=t[n],t.hasOwnProperty(n)&&a[n]!==l&&no(e,n,l)}else for(var s in t)t.hasOwnProperty(s)&&no(e,s,t[s])}function ar(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var U0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),k0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function yi(e){return k0.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var lr=null;function nr(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var pl=null,xl=null;function so(e){var t=dl(e);if(t&&(e=t.stateNode)){var a=e[nt]||null;e:switch(e=t.stateNode,t.type){case"input":if(er(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+St(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var n=l[nt]||null;if(!n)throw Error(u(90));er(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&eo(l)}break e;case"textarea":ao(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&yl(e,!!a.multiple,t,!1)}}}var ir=!1;function ro(e,t,a){if(ir)return e(t,a);ir=!0;try{var l=e(t);return l}finally{if(ir=!1,(pl!==null||xl!==null)&&(ts(),pl&&(t=pl,e=xl,xl=pl=null,so(t),e)))for(t=0;t<e.length;t++)so(e[t])}}function rn(e,t){var a=e.stateNode;if(a===null)return null;var l=a[nt]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(u(231,t,typeof a));return a}var Wt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),sr=!1;if(Wt)try{var un={};Object.defineProperty(un,"passive",{get:function(){sr=!0}}),window.addEventListener("test",un,un),window.removeEventListener("test",un,un)}catch{sr=!1}var ga=null,rr=null,bi=null;function uo(){if(bi)return bi;var e,t=rr,a=t.length,l,n="value"in ga?ga.value:ga.textContent,s=n.length;for(e=0;e<a&&t[e]===n[e];e++);var f=a-e;for(l=1;l<=f&&t[a-l]===n[s-l];l++);return bi=n.slice(e,1<l?1-l:void 0)}function pi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function xi(){return!0}function co(){return!1}function it(e){function t(a,l,n,s,f){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=s,this.target=f,this.currentTarget=null;for(var m in e)e.hasOwnProperty(m)&&(a=e[m],this[m]=a?a(s):s[m]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?xi:co,this.isPropagationStopped=co,this}return v(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=xi)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=xi)},persist:function(){},isPersistent:xi}),t}var Za={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},vi=it(Za),cn=v({},Za,{view:0,detail:0}),_0=it(cn),ur,cr,on,Si=v({},cn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:fr,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==on&&(on&&e.type==="mousemove"?(ur=e.screenX-on.screenX,cr=e.screenY-on.screenY):cr=ur=0,on=e),ur)},movementY:function(e){return"movementY"in e?e.movementY:cr}}),oo=it(Si),B0=v({},Si,{dataTransfer:0}),q0=it(B0),H0=v({},cn,{relatedTarget:0}),or=it(H0),L0=v({},Za,{animationName:0,elapsedTime:0,pseudoElement:0}),G0=it(L0),Y0=v({},Za,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),X0=it(Y0),Z0=v({},Za,{data:0}),fo=it(Z0),Q0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},V0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},K0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function J0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=K0[e])?!!t[e]:!1}function fr(){return J0}var W0=v({},cn,{key:function(e){if(e.key){var t=Q0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=pi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?V0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:fr,charCode:function(e){return e.type==="keypress"?pi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?pi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),F0=it(W0),$0=v({},Si,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),mo=it($0),P0=v({},cn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:fr}),I0=it(P0),eg=v({},Za,{propertyName:0,elapsedTime:0,pseudoElement:0}),tg=it(eg),ag=v({},Si,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),lg=it(ag),ng=v({},Za,{newState:0,oldState:0}),ig=it(ng),sg=[9,13,27,32],dr=Wt&&"CompositionEvent"in window,fn=null;Wt&&"documentMode"in document&&(fn=document.documentMode);var rg=Wt&&"TextEvent"in window&&!fn,ho=Wt&&(!dr||fn&&8<fn&&11>=fn),go=" ",yo=!1;function bo(e,t){switch(e){case"keyup":return sg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function po(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var vl=!1;function ug(e,t){switch(e){case"compositionend":return po(t);case"keypress":return t.which!==32?null:(yo=!0,go);case"textInput":return e=t.data,e===go&&yo?null:e;default:return null}}function cg(e,t){if(vl)return e==="compositionend"||!dr&&bo(e,t)?(e=uo(),bi=rr=ga=null,vl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ho&&t.locale!=="ko"?null:t.data;default:return null}}var og={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function xo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!og[e.type]:t==="textarea"}function vo(e,t,a,l){pl?xl?xl.push(l):xl=[l]:pl=l,t=rs(t,"onChange"),0<t.length&&(a=new vi("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var dn=null,mn=null;function fg(e){tm(e,0)}function ji(e){var t=sn(e);if(eo(t))return e}function So(e,t){if(e==="change")return t}var jo=!1;if(Wt){var mr;if(Wt){var hr="oninput"in document;if(!hr){var No=document.createElement("div");No.setAttribute("oninput","return;"),hr=typeof No.oninput=="function"}mr=hr}else mr=!1;jo=mr&&(!document.documentMode||9<document.documentMode)}function wo(){dn&&(dn.detachEvent("onpropertychange",Ao),mn=dn=null)}function Ao(e){if(e.propertyName==="value"&&ji(mn)){var t=[];vo(t,mn,e,nr(e)),ro(fg,t)}}function dg(e,t,a){e==="focusin"?(wo(),dn=t,mn=a,dn.attachEvent("onpropertychange",Ao)):e==="focusout"&&wo()}function mg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ji(mn)}function hg(e,t){if(e==="click")return ji(t)}function gg(e,t){if(e==="input"||e==="change")return ji(t)}function yg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var mt=typeof Object.is=="function"?Object.is:yg;function hn(e,t){if(mt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!da.call(t,n)||!mt(e[n],t[n]))return!1}return!0}function To(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Eo(e,t){var a=To(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=To(a)}}function zo(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?zo(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Mo(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=gi(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=gi(e.document)}return t}function gr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var bg=Wt&&"documentMode"in document&&11>=document.documentMode,Sl=null,yr=null,gn=null,br=!1;function Oo(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;br||Sl==null||Sl!==gi(l)||(l=Sl,"selectionStart"in l&&gr(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),gn&&hn(gn,l)||(gn=l,l=rs(yr,"onSelect"),0<l.length&&(t=new vi("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=Sl)))}function Qa(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var jl={animationend:Qa("Animation","AnimationEnd"),animationiteration:Qa("Animation","AnimationIteration"),animationstart:Qa("Animation","AnimationStart"),transitionrun:Qa("Transition","TransitionRun"),transitionstart:Qa("Transition","TransitionStart"),transitioncancel:Qa("Transition","TransitionCancel"),transitionend:Qa("Transition","TransitionEnd")},pr={},Do={};Wt&&(Do=document.createElement("div").style,"AnimationEvent"in window||(delete jl.animationend.animation,delete jl.animationiteration.animation,delete jl.animationstart.animation),"TransitionEvent"in window||delete jl.transitionend.transition);function Va(e){if(pr[e])return pr[e];if(!jl[e])return e;var t=jl[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in Do)return pr[e]=t[a];return e}var Ro=Va("animationend"),Co=Va("animationiteration"),Uo=Va("animationstart"),pg=Va("transitionrun"),xg=Va("transitionstart"),vg=Va("transitioncancel"),ko=Va("transitionend"),_o=new Map,xr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");xr.push("scrollEnd");function Rt(e,t){_o.set(e,t),Xa(t,[e])}var Bo=new WeakMap;function jt(e,t){if(typeof e=="object"&&e!==null){var a=Bo.get(e);return a!==void 0?a:(t={value:e,source:t,stack:Pc(t)},Bo.set(e,t),t)}return{value:e,source:t,stack:Pc(t)}}var Nt=[],Nl=0,vr=0;function Ni(){for(var e=Nl,t=vr=Nl=0;t<e;){var a=Nt[t];Nt[t++]=null;var l=Nt[t];Nt[t++]=null;var n=Nt[t];Nt[t++]=null;var s=Nt[t];if(Nt[t++]=null,l!==null&&n!==null){var f=l.pending;f===null?n.next=n:(n.next=f.next,f.next=n),l.pending=n}s!==0&&qo(a,n,s)}}function wi(e,t,a,l){Nt[Nl++]=e,Nt[Nl++]=t,Nt[Nl++]=a,Nt[Nl++]=l,vr|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Sr(e,t,a,l){return wi(e,t,a,l),Ai(e)}function wl(e,t){return wi(e,null,null,t),Ai(e)}function qo(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var n=!1,s=e.return;s!==null;)s.childLanes|=a,l=s.alternate,l!==null&&(l.childLanes|=a),s.tag===22&&(e=s.stateNode,e===null||e._visibility&1||(n=!0)),e=s,s=s.return;return e.tag===3?(s=e.stateNode,n&&t!==null&&(n=31-dt(a),e=s.hiddenUpdates,l=e[n],l===null?e[n]=[t]:l.push(t),t.lane=a|536870912),s):null}function Ai(e){if(50<Ln)throw Ln=0,Eu=null,Error(u(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Al={};function Sg(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ht(e,t,a,l){return new Sg(e,t,a,l)}function jr(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ft(e,t){var a=e.alternate;return a===null?(a=ht(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Ho(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ti(e,t,a,l,n,s){var f=0;if(l=e,typeof e=="function")jr(e)&&(f=1);else if(typeof e=="string")f=Ny(e,a,ce.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case Ee:return e=ht(31,a,t,n),e.elementType=Ee,e.lanes=s,e;case U:return Ka(a.children,n,s,t);case _:f=8,n|=24;break;case q:return e=ht(12,a,t,n|2),e.elementType=q,e.lanes=s,e;case re:return e=ht(13,a,t,n),e.elementType=re,e.lanes=s,e;case de:return e=ht(19,a,t,n),e.elementType=de,e.lanes=s,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case $:case J:f=10;break e;case ne:f=9;break e;case ue:f=11;break e;case je:f=14;break e;case Y:f=16,l=null;break e}f=29,a=Error(u(130,e===null?"null":typeof e,"")),l=null}return t=ht(f,a,t,n),t.elementType=e,t.type=l,t.lanes=s,t}function Ka(e,t,a,l){return e=ht(7,e,l,t),e.lanes=a,e}function Nr(e,t,a){return e=ht(6,e,null,t),e.lanes=a,e}function wr(e,t,a){return t=ht(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Tl=[],El=0,Ei=null,zi=0,wt=[],At=0,Ja=null,$t=1,Pt="";function Wa(e,t){Tl[El++]=zi,Tl[El++]=Ei,Ei=e,zi=t}function Lo(e,t,a){wt[At++]=$t,wt[At++]=Pt,wt[At++]=Ja,Ja=e;var l=$t;e=Pt;var n=32-dt(l)-1;l&=~(1<<n),a+=1;var s=32-dt(t)+n;if(30<s){var f=n-n%5;s=(l&(1<<f)-1).toString(32),l>>=f,n-=f,$t=1<<32-dt(t)+n|a<<n|l,Pt=s+e}else $t=1<<s|a<<n|l,Pt=e}function Ar(e){e.return!==null&&(Wa(e,1),Lo(e,1,0))}function Tr(e){for(;e===Ei;)Ei=Tl[--El],Tl[El]=null,zi=Tl[--El],Tl[El]=null;for(;e===Ja;)Ja=wt[--At],wt[At]=null,Pt=wt[--At],wt[At]=null,$t=wt[--At],wt[At]=null}var at=null,Oe=null,me=!1,Fa=null,qt=!1,Er=Error(u(519));function $a(e){var t=Error(u(418,""));throw pn(jt(t,e)),Er}function Go(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[Pe]=e,t[nt]=l,a){case"dialog":le("cancel",t),le("close",t);break;case"iframe":case"object":case"embed":le("load",t);break;case"video":case"audio":for(a=0;a<Yn.length;a++)le(Yn[a],t);break;case"source":le("error",t);break;case"img":case"image":case"link":le("error",t),le("load",t);break;case"details":le("toggle",t);break;case"input":le("invalid",t),to(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),hi(t);break;case"select":le("invalid",t);break;case"textarea":le("invalid",t),lo(t,l.value,l.defaultValue,l.children),hi(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||im(t.textContent,a)?(l.popover!=null&&(le("beforetoggle",t),le("toggle",t)),l.onScroll!=null&&le("scroll",t),l.onScrollEnd!=null&&le("scrollend",t),l.onClick!=null&&(t.onclick=us),t=!0):t=!1,t||$a(e)}function Yo(e){for(at=e.return;at;)switch(at.tag){case 5:case 13:qt=!1;return;case 27:case 3:qt=!0;return;default:at=at.return}}function yn(e){if(e!==at)return!1;if(!me)return Yo(e),me=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Xu(e.type,e.memoizedProps)),a=!a),a&&Oe&&$a(e),Yo(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(u(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Oe=Ut(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Oe=null}}else t===27?(t=Oe,Da(e.type)?(e=Ku,Ku=null,Oe=e):Oe=t):Oe=at?Ut(e.stateNode.nextSibling):null;return!0}function bn(){Oe=at=null,me=!1}function Xo(){var e=Fa;return e!==null&&(ut===null?ut=e:ut.push.apply(ut,e),Fa=null),e}function pn(e){Fa===null?Fa=[e]:Fa.push(e)}var zr=Ye(null),Pa=null,It=null;function ya(e,t,a){F(zr,t._currentValue),t._currentValue=a}function ea(e){e._currentValue=zr.current,Ne(zr)}function Mr(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function Or(e,t,a,l){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var s=n.dependencies;if(s!==null){var f=n.child;s=s.firstContext;e:for(;s!==null;){var m=s;s=n;for(var y=0;y<t.length;y++)if(m.context===t[y]){s.lanes|=a,m=s.alternate,m!==null&&(m.lanes|=a),Mr(s.return,a,e),l||(f=null);break e}s=m.next}}else if(n.tag===18){if(f=n.return,f===null)throw Error(u(341));f.lanes|=a,s=f.alternate,s!==null&&(s.lanes|=a),Mr(f,a,e),f=null}else f=n.child;if(f!==null)f.return=n;else for(f=n;f!==null;){if(f===e){f=null;break}if(n=f.sibling,n!==null){n.return=f.return,f=n;break}f=f.return}n=f}}function xn(e,t,a,l){e=null;for(var n=t,s=!1;n!==null;){if(!s){if((n.flags&524288)!==0)s=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var f=n.alternate;if(f===null)throw Error(u(387));if(f=f.memoizedProps,f!==null){var m=n.type;mt(n.pendingProps.value,f.value)||(e!==null?e.push(m):e=[m])}}else if(n===ze.current){if(f=n.alternate,f===null)throw Error(u(387));f.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Jn):e=[Jn])}n=n.return}e!==null&&Or(t,e,a,l),t.flags|=262144}function Mi(e){for(e=e.firstContext;e!==null;){if(!mt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ia(e){Pa=e,It=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Ie(e){return Zo(Pa,e)}function Oi(e,t){return Pa===null&&Ia(e),Zo(e,t)}function Zo(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},It===null){if(e===null)throw Error(u(308));It=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else It=It.next=t;return a}var jg=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},Ng=i.unstable_scheduleCallback,wg=i.unstable_NormalPriority,He={$$typeof:J,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Dr(){return{controller:new jg,data:new Map,refCount:0}}function vn(e){e.refCount--,e.refCount===0&&Ng(wg,function(){e.controller.abort()})}var Sn=null,Rr=0,zl=0,Ml=null;function Ag(e,t){if(Sn===null){var a=Sn=[];Rr=0,zl=Uu(),Ml={status:"pending",value:void 0,then:function(l){a.push(l)}}}return Rr++,t.then(Qo,Qo),t}function Qo(){if(--Rr===0&&Sn!==null){Ml!==null&&(Ml.status="fulfilled");var e=Sn;Sn=null,zl=0,Ml=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Tg(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var n=0;n<a.length;n++)(0,a[n])(t)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var Vo=A.S;A.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Ag(e,t),Vo!==null&&Vo(e,t)};var el=Ye(null);function Cr(){var e=el.current;return e!==null?e:Ae.pooledCache}function Di(e,t){t===null?F(el,el.current):F(el,t.pool)}function Ko(){var e=Cr();return e===null?null:{parent:He._currentValue,pool:e}}var jn=Error(u(460)),Jo=Error(u(474)),Ri=Error(u(542)),Ur={then:function(){}};function Wo(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Ci(){}function Fo(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(Ci,Ci),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Po(e),e;default:if(typeof t.status=="string")t.then(Ci,Ci);else{if(e=Ae,e!==null&&100<e.shellSuspendCounter)throw Error(u(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=l}},function(l){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Po(e),e}throw Nn=t,jn}}var Nn=null;function $o(){if(Nn===null)throw Error(u(459));var e=Nn;return Nn=null,e}function Po(e){if(e===jn||e===Ri)throw Error(u(483))}var ba=!1;function kr(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function _r(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function pa(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function xa(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(ge&2)!==0){var n=l.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),l.pending=t,t=Ai(e),qo(e,null,a),t}return wi(e,l,t,a),Ai(e)}function wn(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Zc(e,a)}}function Br(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,s=null;if(a=a.firstBaseUpdate,a!==null){do{var f={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};s===null?n=s=f:s=s.next=f,a=a.next}while(a!==null);s===null?n=s=t:s=s.next=t}else n=s=t;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:s,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var qr=!1;function An(){if(qr){var e=Ml;if(e!==null)throw e}}function Tn(e,t,a,l){qr=!1;var n=e.updateQueue;ba=!1;var s=n.firstBaseUpdate,f=n.lastBaseUpdate,m=n.shared.pending;if(m!==null){n.shared.pending=null;var y=m,N=y.next;y.next=null,f===null?s=N:f.next=N,f=y;var O=e.alternate;O!==null&&(O=O.updateQueue,m=O.lastBaseUpdate,m!==f&&(m===null?O.firstBaseUpdate=N:m.next=N,O.lastBaseUpdate=y))}if(s!==null){var R=n.baseState;f=0,O=N=y=null,m=s;do{var T=m.lane&-536870913,E=T!==m.lane;if(E?(ie&T)===T:(l&T)===T){T!==0&&T===zl&&(qr=!0),O!==null&&(O=O.next={lane:0,tag:m.tag,payload:m.payload,callback:null,next:null});e:{var W=e,V=m;T=t;var Se=a;switch(V.tag){case 1:if(W=V.payload,typeof W=="function"){R=W.call(Se,R,T);break e}R=W;break e;case 3:W.flags=W.flags&-65537|128;case 0:if(W=V.payload,T=typeof W=="function"?W.call(Se,R,T):W,T==null)break e;R=v({},R,T);break e;case 2:ba=!0}}T=m.callback,T!==null&&(e.flags|=64,E&&(e.flags|=8192),E=n.callbacks,E===null?n.callbacks=[T]:E.push(T))}else E={lane:T,tag:m.tag,payload:m.payload,callback:m.callback,next:null},O===null?(N=O=E,y=R):O=O.next=E,f|=T;if(m=m.next,m===null){if(m=n.shared.pending,m===null)break;E=m,m=E.next,E.next=null,n.lastBaseUpdate=E,n.shared.pending=null}}while(!0);O===null&&(y=R),n.baseState=y,n.firstBaseUpdate=N,n.lastBaseUpdate=O,s===null&&(n.shared.lanes=0),Ea|=f,e.lanes=f,e.memoizedState=R}}function Io(e,t){if(typeof e!="function")throw Error(u(191,e));e.call(t)}function ef(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Io(a[e],t)}var Ol=Ye(null),Ui=Ye(0);function tf(e,t){e=ra,F(Ui,e),F(Ol,t),ra=e|t.baseLanes}function Hr(){F(Ui,ra),F(Ol,Ol.current)}function Lr(){ra=Ui.current,Ne(Ol),Ne(Ui)}var va=0,I=null,xe=null,ke=null,ki=!1,Dl=!1,tl=!1,_i=0,En=0,Rl=null,Eg=0;function Re(){throw Error(u(321))}function Gr(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!mt(e[a],t[a]))return!1;return!0}function Yr(e,t,a,l,n,s){return va=s,I=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,A.H=e===null||e.memoizedState===null?Hf:Lf,tl=!1,s=a(l,n),tl=!1,Dl&&(s=lf(t,a,l,n)),af(e),s}function af(e){A.H=Yi;var t=xe!==null&&xe.next!==null;if(va=0,ke=xe=I=null,ki=!1,En=0,Rl=null,t)throw Error(u(300));e===null||Ze||(e=e.dependencies,e!==null&&Mi(e)&&(Ze=!0))}function lf(e,t,a,l){I=e;var n=0;do{if(Dl&&(Rl=null),En=0,Dl=!1,25<=n)throw Error(u(301));if(n+=1,ke=xe=null,e.updateQueue!=null){var s=e.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,s.memoCache!=null&&(s.memoCache.index=0)}A.H=Ug,s=t(a,l)}while(Dl);return s}function zg(){var e=A.H,t=e.useState()[0];return t=typeof t.then=="function"?zn(t):t,e=e.useState()[0],(xe!==null?xe.memoizedState:null)!==e&&(I.flags|=1024),t}function Xr(){var e=_i!==0;return _i=0,e}function Zr(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Qr(e){if(ki){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}ki=!1}va=0,ke=xe=I=null,Dl=!1,En=_i=0,Rl=null}function st(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ke===null?I.memoizedState=ke=e:ke=ke.next=e,ke}function _e(){if(xe===null){var e=I.alternate;e=e!==null?e.memoizedState:null}else e=xe.next;var t=ke===null?I.memoizedState:ke.next;if(t!==null)ke=t,xe=e;else{if(e===null)throw I.alternate===null?Error(u(467)):Error(u(310));xe=e,e={memoizedState:xe.memoizedState,baseState:xe.baseState,baseQueue:xe.baseQueue,queue:xe.queue,next:null},ke===null?I.memoizedState=ke=e:ke=ke.next=e}return ke}function Vr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function zn(e){var t=En;return En+=1,Rl===null&&(Rl=[]),e=Fo(Rl,e,t),t=I,(ke===null?t.memoizedState:ke.next)===null&&(t=t.alternate,A.H=t===null||t.memoizedState===null?Hf:Lf),e}function Bi(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return zn(e);if(e.$$typeof===J)return Ie(e)}throw Error(u(438,String(e)))}function Kr(e){var t=null,a=I.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=I.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Vr(),I.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=Mt;return t.index++,a}function ta(e,t){return typeof t=="function"?t(e):t}function qi(e){var t=_e();return Jr(t,xe,e)}function Jr(e,t,a){var l=e.queue;if(l===null)throw Error(u(311));l.lastRenderedReducer=a;var n=e.baseQueue,s=l.pending;if(s!==null){if(n!==null){var f=n.next;n.next=s.next,s.next=f}t.baseQueue=n=s,l.pending=null}if(s=e.baseState,n===null)e.memoizedState=s;else{t=n.next;var m=f=null,y=null,N=t,O=!1;do{var R=N.lane&-536870913;if(R!==N.lane?(ie&R)===R:(va&R)===R){var T=N.revertLane;if(T===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null}),R===zl&&(O=!0);else if((va&T)===T){N=N.next,T===zl&&(O=!0);continue}else R={lane:0,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},y===null?(m=y=R,f=s):y=y.next=R,I.lanes|=T,Ea|=T;R=N.action,tl&&a(s,R),s=N.hasEagerState?N.eagerState:a(s,R)}else T={lane:R,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},y===null?(m=y=T,f=s):y=y.next=T,I.lanes|=R,Ea|=R;N=N.next}while(N!==null&&N!==t);if(y===null?f=s:y.next=m,!mt(s,e.memoizedState)&&(Ze=!0,O&&(a=Ml,a!==null)))throw a;e.memoizedState=s,e.baseState=f,e.baseQueue=y,l.lastRenderedState=s}return n===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Wr(e){var t=_e(),a=t.queue;if(a===null)throw Error(u(311));a.lastRenderedReducer=e;var l=a.dispatch,n=a.pending,s=t.memoizedState;if(n!==null){a.pending=null;var f=n=n.next;do s=e(s,f.action),f=f.next;while(f!==n);mt(s,t.memoizedState)||(Ze=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),a.lastRenderedState=s}return[s,l]}function nf(e,t,a){var l=I,n=_e(),s=me;if(s){if(a===void 0)throw Error(u(407));a=a()}else a=t();var f=!mt((xe||n).memoizedState,a);f&&(n.memoizedState=a,Ze=!0),n=n.queue;var m=uf.bind(null,l,n,e);if(Mn(2048,8,m,[e]),n.getSnapshot!==t||f||ke!==null&&ke.memoizedState.tag&1){if(l.flags|=2048,Cl(9,Hi(),rf.bind(null,l,n,a,t),null),Ae===null)throw Error(u(349));s||(va&124)!==0||sf(l,t,a)}return a}function sf(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=I.updateQueue,t===null?(t=Vr(),I.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function rf(e,t,a,l){t.value=a,t.getSnapshot=l,cf(t)&&of(e)}function uf(e,t,a){return a(function(){cf(t)&&of(e)})}function cf(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!mt(e,a)}catch{return!0}}function of(e){var t=wl(e,2);t!==null&&xt(t,e,2)}function Fr(e){var t=st();if(typeof e=="function"){var a=e;if(e=a(),tl){ma(!0);try{a()}finally{ma(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ta,lastRenderedState:e},t}function ff(e,t,a,l){return e.baseState=a,Jr(e,xe,typeof l=="function"?l:ta)}function Mg(e,t,a,l,n){if(Gi(e))throw Error(u(485));if(e=t.action,e!==null){var s={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){s.listeners.push(f)}};A.T!==null?a(!0):s.isTransition=!1,l(s),a=t.pending,a===null?(s.next=t.pending=s,df(t,s)):(s.next=a.next,t.pending=a.next=s)}}function df(e,t){var a=t.action,l=t.payload,n=e.state;if(t.isTransition){var s=A.T,f={};A.T=f;try{var m=a(n,l),y=A.S;y!==null&&y(f,m),mf(e,t,m)}catch(N){$r(e,t,N)}finally{A.T=s}}else try{s=a(n,l),mf(e,t,s)}catch(N){$r(e,t,N)}}function mf(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){hf(e,t,l)},function(l){return $r(e,t,l)}):hf(e,t,a)}function hf(e,t,a){t.status="fulfilled",t.value=a,gf(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,df(e,a)))}function $r(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,gf(t),t=t.next;while(t!==l)}e.action=null}function gf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function yf(e,t){return t}function bf(e,t){if(me){var a=Ae.formState;if(a!==null){e:{var l=I;if(me){if(Oe){t:{for(var n=Oe,s=qt;n.nodeType!==8;){if(!s){n=null;break t}if(n=Ut(n.nextSibling),n===null){n=null;break t}}s=n.data,n=s==="F!"||s==="F"?n:null}if(n){Oe=Ut(n.nextSibling),l=n.data==="F!";break e}}$a(l)}l=!1}l&&(t=a[0])}}return a=st(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:yf,lastRenderedState:t},a.queue=l,a=_f.bind(null,I,l),l.dispatch=a,l=Fr(!1),s=au.bind(null,I,!1,l.queue),l=st(),n={state:t,dispatch:null,action:e,pending:null},l.queue=n,a=Mg.bind(null,I,n,s,a),n.dispatch=a,l.memoizedState=e,[t,a,!1]}function pf(e){var t=_e();return xf(t,xe,e)}function xf(e,t,a){if(t=Jr(e,t,yf)[0],e=qi(ta)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=zn(t)}catch(f){throw f===jn?Ri:f}else l=t;t=_e();var n=t.queue,s=n.dispatch;return a!==t.memoizedState&&(I.flags|=2048,Cl(9,Hi(),Og.bind(null,n,a),null)),[l,s,e]}function Og(e,t){e.action=t}function vf(e){var t=_e(),a=xe;if(a!==null)return xf(t,a,e);_e(),t=t.memoizedState,a=_e();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function Cl(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=I.updateQueue,t===null&&(t=Vr(),I.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function Hi(){return{destroy:void 0,resource:void 0}}function Sf(){return _e().memoizedState}function Li(e,t,a,l){var n=st();l=l===void 0?null:l,I.flags|=e,n.memoizedState=Cl(1|t,Hi(),a,l)}function Mn(e,t,a,l){var n=_e();l=l===void 0?null:l;var s=n.memoizedState.inst;xe!==null&&l!==null&&Gr(l,xe.memoizedState.deps)?n.memoizedState=Cl(t,s,a,l):(I.flags|=e,n.memoizedState=Cl(1|t,s,a,l))}function jf(e,t){Li(8390656,8,e,t)}function Nf(e,t){Mn(2048,8,e,t)}function wf(e,t){return Mn(4,2,e,t)}function Af(e,t){return Mn(4,4,e,t)}function Tf(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ef(e,t,a){a=a!=null?a.concat([e]):null,Mn(4,4,Tf.bind(null,t,e),a)}function Pr(){}function zf(e,t){var a=_e();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&Gr(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function Mf(e,t){var a=_e();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&Gr(t,l[1]))return l[0];if(l=e(),tl){ma(!0);try{e()}finally{ma(!1)}}return a.memoizedState=[l,t],l}function Ir(e,t,a){return a===void 0||(va&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=Rd(),I.lanes|=e,Ea|=e,a)}function Of(e,t,a,l){return mt(a,t)?a:Ol.current!==null?(e=Ir(e,a,l),mt(e,t)||(Ze=!0),e):(va&42)===0?(Ze=!0,e.memoizedState=a):(e=Rd(),I.lanes|=e,Ea|=e,t)}function Df(e,t,a,l,n){var s=B.p;B.p=s!==0&&8>s?s:8;var f=A.T,m={};A.T=m,au(e,!1,t,a);try{var y=n(),N=A.S;if(N!==null&&N(m,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var O=Tg(y,l);On(e,t,O,pt(e))}else On(e,t,l,pt(e))}catch(R){On(e,t,{then:function(){},status:"rejected",reason:R},pt())}finally{B.p=s,A.T=f}}function Dg(){}function eu(e,t,a,l){if(e.tag!==5)throw Error(u(476));var n=Rf(e).queue;Df(e,n,t,C,a===null?Dg:function(){return Cf(e),a(l)})}function Rf(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:C,baseState:C,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ta,lastRenderedState:C},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ta,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Cf(e){var t=Rf(e).next.queue;On(e,t,{},pt())}function tu(){return Ie(Jn)}function Uf(){return _e().memoizedState}function kf(){return _e().memoizedState}function Rg(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=pt();e=pa(a);var l=xa(t,e,a);l!==null&&(xt(l,t,a),wn(l,t,a)),t={cache:Dr()},e.payload=t;return}t=t.return}}function Cg(e,t,a){var l=pt();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Gi(e)?Bf(t,a):(a=Sr(e,t,a,l),a!==null&&(xt(a,e,l),qf(a,t,l)))}function _f(e,t,a){var l=pt();On(e,t,a,l)}function On(e,t,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Gi(e))Bf(t,n);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var f=t.lastRenderedState,m=s(f,a);if(n.hasEagerState=!0,n.eagerState=m,mt(m,f))return wi(e,t,n,0),Ae===null&&Ni(),!1}catch{}finally{}if(a=Sr(e,t,n,l),a!==null)return xt(a,e,l),qf(a,t,l),!0}return!1}function au(e,t,a,l){if(l={lane:2,revertLane:Uu(),action:l,hasEagerState:!1,eagerState:null,next:null},Gi(e)){if(t)throw Error(u(479))}else t=Sr(e,a,l,2),t!==null&&xt(t,e,2)}function Gi(e){var t=e.alternate;return e===I||t!==null&&t===I}function Bf(e,t){Dl=ki=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function qf(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Zc(e,a)}}var Yi={readContext:Ie,use:Bi,useCallback:Re,useContext:Re,useEffect:Re,useImperativeHandle:Re,useLayoutEffect:Re,useInsertionEffect:Re,useMemo:Re,useReducer:Re,useRef:Re,useState:Re,useDebugValue:Re,useDeferredValue:Re,useTransition:Re,useSyncExternalStore:Re,useId:Re,useHostTransitionStatus:Re,useFormState:Re,useActionState:Re,useOptimistic:Re,useMemoCache:Re,useCacheRefresh:Re},Hf={readContext:Ie,use:Bi,useCallback:function(e,t){return st().memoizedState=[e,t===void 0?null:t],e},useContext:Ie,useEffect:jf,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Li(4194308,4,Tf.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Li(4194308,4,e,t)},useInsertionEffect:function(e,t){Li(4,2,e,t)},useMemo:function(e,t){var a=st();t=t===void 0?null:t;var l=e();if(tl){ma(!0);try{e()}finally{ma(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=st();if(a!==void 0){var n=a(t);if(tl){ma(!0);try{a(t)}finally{ma(!1)}}}else n=t;return l.memoizedState=l.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},l.queue=e,e=e.dispatch=Cg.bind(null,I,e),[l.memoizedState,e]},useRef:function(e){var t=st();return e={current:e},t.memoizedState=e},useState:function(e){e=Fr(e);var t=e.queue,a=_f.bind(null,I,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:Pr,useDeferredValue:function(e,t){var a=st();return Ir(a,e,t)},useTransition:function(){var e=Fr(!1);return e=Df.bind(null,I,e.queue,!0,!1),st().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=I,n=st();if(me){if(a===void 0)throw Error(u(407));a=a()}else{if(a=t(),Ae===null)throw Error(u(349));(ie&124)!==0||sf(l,t,a)}n.memoizedState=a;var s={value:a,getSnapshot:t};return n.queue=s,jf(uf.bind(null,l,s,e),[e]),l.flags|=2048,Cl(9,Hi(),rf.bind(null,l,s,a,t),null),a},useId:function(){var e=st(),t=Ae.identifierPrefix;if(me){var a=Pt,l=$t;a=(l&~(1<<32-dt(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=_i++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=Eg++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:tu,useFormState:bf,useActionState:bf,useOptimistic:function(e){var t=st();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=au.bind(null,I,!0,a),a.dispatch=t,[e,t]},useMemoCache:Kr,useCacheRefresh:function(){return st().memoizedState=Rg.bind(null,I)}},Lf={readContext:Ie,use:Bi,useCallback:zf,useContext:Ie,useEffect:Nf,useImperativeHandle:Ef,useInsertionEffect:wf,useLayoutEffect:Af,useMemo:Mf,useReducer:qi,useRef:Sf,useState:function(){return qi(ta)},useDebugValue:Pr,useDeferredValue:function(e,t){var a=_e();return Of(a,xe.memoizedState,e,t)},useTransition:function(){var e=qi(ta)[0],t=_e().memoizedState;return[typeof e=="boolean"?e:zn(e),t]},useSyncExternalStore:nf,useId:Uf,useHostTransitionStatus:tu,useFormState:pf,useActionState:pf,useOptimistic:function(e,t){var a=_e();return ff(a,xe,e,t)},useMemoCache:Kr,useCacheRefresh:kf},Ug={readContext:Ie,use:Bi,useCallback:zf,useContext:Ie,useEffect:Nf,useImperativeHandle:Ef,useInsertionEffect:wf,useLayoutEffect:Af,useMemo:Mf,useReducer:Wr,useRef:Sf,useState:function(){return Wr(ta)},useDebugValue:Pr,useDeferredValue:function(e,t){var a=_e();return xe===null?Ir(a,e,t):Of(a,xe.memoizedState,e,t)},useTransition:function(){var e=Wr(ta)[0],t=_e().memoizedState;return[typeof e=="boolean"?e:zn(e),t]},useSyncExternalStore:nf,useId:Uf,useHostTransitionStatus:tu,useFormState:vf,useActionState:vf,useOptimistic:function(e,t){var a=_e();return xe!==null?ff(a,xe,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Kr,useCacheRefresh:kf},Ul=null,Dn=0;function Xi(e){var t=Dn;return Dn+=1,Ul===null&&(Ul=[]),Fo(Ul,e,t)}function Rn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Zi(e,t){throw t.$$typeof===M?Error(u(525)):(e=Object.prototype.toString.call(t),Error(u(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Gf(e){var t=e._init;return t(e._payload)}function Yf(e){function t(x,p){if(e){var S=x.deletions;S===null?(x.deletions=[p],x.flags|=16):S.push(p)}}function a(x,p){if(!e)return null;for(;p!==null;)t(x,p),p=p.sibling;return null}function l(x){for(var p=new Map;x!==null;)x.key!==null?p.set(x.key,x):p.set(x.index,x),x=x.sibling;return p}function n(x,p){return x=Ft(x,p),x.index=0,x.sibling=null,x}function s(x,p,S){return x.index=S,e?(S=x.alternate,S!==null?(S=S.index,S<p?(x.flags|=67108866,p):S):(x.flags|=67108866,p)):(x.flags|=1048576,p)}function f(x){return e&&x.alternate===null&&(x.flags|=67108866),x}function m(x,p,S,D){return p===null||p.tag!==6?(p=Nr(S,x.mode,D),p.return=x,p):(p=n(p,S),p.return=x,p)}function y(x,p,S,D){var H=S.type;return H===U?O(x,p,S.props.children,D,S.key):p!==null&&(p.elementType===H||typeof H=="object"&&H!==null&&H.$$typeof===Y&&Gf(H)===p.type)?(p=n(p,S.props),Rn(p,S),p.return=x,p):(p=Ti(S.type,S.key,S.props,null,x.mode,D),Rn(p,S),p.return=x,p)}function N(x,p,S,D){return p===null||p.tag!==4||p.stateNode.containerInfo!==S.containerInfo||p.stateNode.implementation!==S.implementation?(p=wr(S,x.mode,D),p.return=x,p):(p=n(p,S.children||[]),p.return=x,p)}function O(x,p,S,D,H){return p===null||p.tag!==7?(p=Ka(S,x.mode,D,H),p.return=x,p):(p=n(p,S),p.return=x,p)}function R(x,p,S){if(typeof p=="string"&&p!==""||typeof p=="number"||typeof p=="bigint")return p=Nr(""+p,x.mode,S),p.return=x,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case k:return S=Ti(p.type,p.key,p.props,null,x.mode,S),Rn(S,p),S.return=x,S;case Z:return p=wr(p,x.mode,S),p.return=x,p;case Y:var D=p._init;return p=D(p._payload),R(x,p,S)}if(X(p)||qe(p))return p=Ka(p,x.mode,S,null),p.return=x,p;if(typeof p.then=="function")return R(x,Xi(p),S);if(p.$$typeof===J)return R(x,Oi(x,p),S);Zi(x,p)}return null}function T(x,p,S,D){var H=p!==null?p.key:null;if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return H!==null?null:m(x,p,""+S,D);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case k:return S.key===H?y(x,p,S,D):null;case Z:return S.key===H?N(x,p,S,D):null;case Y:return H=S._init,S=H(S._payload),T(x,p,S,D)}if(X(S)||qe(S))return H!==null?null:O(x,p,S,D,null);if(typeof S.then=="function")return T(x,p,Xi(S),D);if(S.$$typeof===J)return T(x,p,Oi(x,S),D);Zi(x,S)}return null}function E(x,p,S,D,H){if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return x=x.get(S)||null,m(p,x,""+D,H);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case k:return x=x.get(D.key===null?S:D.key)||null,y(p,x,D,H);case Z:return x=x.get(D.key===null?S:D.key)||null,N(p,x,D,H);case Y:var te=D._init;return D=te(D._payload),E(x,p,S,D,H)}if(X(D)||qe(D))return x=x.get(S)||null,O(p,x,D,H,null);if(typeof D.then=="function")return E(x,p,S,Xi(D),H);if(D.$$typeof===J)return E(x,p,S,Oi(p,D),H);Zi(p,D)}return null}function W(x,p,S,D){for(var H=null,te=null,Q=p,K=p=0,Ve=null;Q!==null&&K<S.length;K++){Q.index>K?(Ve=Q,Q=null):Ve=Q.sibling;var oe=T(x,Q,S[K],D);if(oe===null){Q===null&&(Q=Ve);break}e&&Q&&oe.alternate===null&&t(x,Q),p=s(oe,p,K),te===null?H=oe:te.sibling=oe,te=oe,Q=Ve}if(K===S.length)return a(x,Q),me&&Wa(x,K),H;if(Q===null){for(;K<S.length;K++)Q=R(x,S[K],D),Q!==null&&(p=s(Q,p,K),te===null?H=Q:te.sibling=Q,te=Q);return me&&Wa(x,K),H}for(Q=l(Q);K<S.length;K++)Ve=E(Q,x,K,S[K],D),Ve!==null&&(e&&Ve.alternate!==null&&Q.delete(Ve.key===null?K:Ve.key),p=s(Ve,p,K),te===null?H=Ve:te.sibling=Ve,te=Ve);return e&&Q.forEach(function(_a){return t(x,_a)}),me&&Wa(x,K),H}function V(x,p,S,D){if(S==null)throw Error(u(151));for(var H=null,te=null,Q=p,K=p=0,Ve=null,oe=S.next();Q!==null&&!oe.done;K++,oe=S.next()){Q.index>K?(Ve=Q,Q=null):Ve=Q.sibling;var _a=T(x,Q,oe.value,D);if(_a===null){Q===null&&(Q=Ve);break}e&&Q&&_a.alternate===null&&t(x,Q),p=s(_a,p,K),te===null?H=_a:te.sibling=_a,te=_a,Q=Ve}if(oe.done)return a(x,Q),me&&Wa(x,K),H;if(Q===null){for(;!oe.done;K++,oe=S.next())oe=R(x,oe.value,D),oe!==null&&(p=s(oe,p,K),te===null?H=oe:te.sibling=oe,te=oe);return me&&Wa(x,K),H}for(Q=l(Q);!oe.done;K++,oe=S.next())oe=E(Q,x,K,oe.value,D),oe!==null&&(e&&oe.alternate!==null&&Q.delete(oe.key===null?K:oe.key),p=s(oe,p,K),te===null?H=oe:te.sibling=oe,te=oe);return e&&Q.forEach(function(ky){return t(x,ky)}),me&&Wa(x,K),H}function Se(x,p,S,D){if(typeof S=="object"&&S!==null&&S.type===U&&S.key===null&&(S=S.props.children),typeof S=="object"&&S!==null){switch(S.$$typeof){case k:e:{for(var H=S.key;p!==null;){if(p.key===H){if(H=S.type,H===U){if(p.tag===7){a(x,p.sibling),D=n(p,S.props.children),D.return=x,x=D;break e}}else if(p.elementType===H||typeof H=="object"&&H!==null&&H.$$typeof===Y&&Gf(H)===p.type){a(x,p.sibling),D=n(p,S.props),Rn(D,S),D.return=x,x=D;break e}a(x,p);break}else t(x,p);p=p.sibling}S.type===U?(D=Ka(S.props.children,x.mode,D,S.key),D.return=x,x=D):(D=Ti(S.type,S.key,S.props,null,x.mode,D),Rn(D,S),D.return=x,x=D)}return f(x);case Z:e:{for(H=S.key;p!==null;){if(p.key===H)if(p.tag===4&&p.stateNode.containerInfo===S.containerInfo&&p.stateNode.implementation===S.implementation){a(x,p.sibling),D=n(p,S.children||[]),D.return=x,x=D;break e}else{a(x,p);break}else t(x,p);p=p.sibling}D=wr(S,x.mode,D),D.return=x,x=D}return f(x);case Y:return H=S._init,S=H(S._payload),Se(x,p,S,D)}if(X(S))return W(x,p,S,D);if(qe(S)){if(H=qe(S),typeof H!="function")throw Error(u(150));return S=H.call(S),V(x,p,S,D)}if(typeof S.then=="function")return Se(x,p,Xi(S),D);if(S.$$typeof===J)return Se(x,p,Oi(x,S),D);Zi(x,S)}return typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint"?(S=""+S,p!==null&&p.tag===6?(a(x,p.sibling),D=n(p,S),D.return=x,x=D):(a(x,p),D=Nr(S,x.mode,D),D.return=x,x=D),f(x)):a(x,p)}return function(x,p,S,D){try{Dn=0;var H=Se(x,p,S,D);return Ul=null,H}catch(Q){if(Q===jn||Q===Ri)throw Q;var te=ht(29,Q,null,x.mode);return te.lanes=D,te.return=x,te}finally{}}}var kl=Yf(!0),Xf=Yf(!1),Tt=Ye(null),Ht=null;function Sa(e){var t=e.alternate;F(Le,Le.current&1),F(Tt,e),Ht===null&&(t===null||Ol.current!==null||t.memoizedState!==null)&&(Ht=e)}function Zf(e){if(e.tag===22){if(F(Le,Le.current),F(Tt,e),Ht===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Ht=e)}}else ja()}function ja(){F(Le,Le.current),F(Tt,Tt.current)}function aa(e){Ne(Tt),Ht===e&&(Ht=null),Ne(Le)}var Le=Ye(0);function Qi(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Vu(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function lu(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:v({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var nu={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=pt(),n=pa(l);n.payload=t,a!=null&&(n.callback=a),t=xa(e,n,l),t!==null&&(xt(t,e,l),wn(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=pt(),n=pa(l);n.tag=1,n.payload=t,a!=null&&(n.callback=a),t=xa(e,n,l),t!==null&&(xt(t,e,l),wn(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=pt(),l=pa(a);l.tag=2,t!=null&&(l.callback=t),t=xa(e,l,a),t!==null&&(xt(t,e,a),wn(t,e,a))}};function Qf(e,t,a,l,n,s,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,s,f):t.prototype&&t.prototype.isPureReactComponent?!hn(a,l)||!hn(n,s):!0}function Vf(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&nu.enqueueReplaceState(t,t.state,null)}function al(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=v({},a));for(var n in e)a[n]===void 0&&(a[n]=e[n])}return a}var Vi=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Kf(e){Vi(e)}function Jf(e){console.error(e)}function Wf(e){Vi(e)}function Ki(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Ff(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function iu(e,t,a){return a=pa(a),a.tag=3,a.payload={element:null},a.callback=function(){Ki(e,t)},a}function $f(e){return e=pa(e),e.tag=3,e}function Pf(e,t,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var s=l.value;e.payload=function(){return n(s)},e.callback=function(){Ff(t,a,l)}}var f=a.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){Ff(t,a,l),typeof n!="function"&&(za===null?za=new Set([this]):za.add(this));var m=l.stack;this.componentDidCatch(l.value,{componentStack:m!==null?m:""})})}function kg(e,t,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&xn(t,a,n,!0),a=Tt.current,a!==null){switch(a.tag){case 13:return Ht===null?Mu():a.alternate===null&&De===0&&(De=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===Ur?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),Du(e,l,n)),!1;case 22:return a.flags|=65536,l===Ur?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),Du(e,l,n)),!1}throw Error(u(435,a.tag))}return Du(e,l,n),Mu(),!1}if(me)return t=Tt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,l!==Er&&(e=Error(u(422),{cause:l}),pn(jt(e,a)))):(l!==Er&&(t=Error(u(423),{cause:l}),pn(jt(t,a))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,l=jt(l,a),n=iu(e.stateNode,l,n),Br(e,n),De!==4&&(De=2)),!1;var s=Error(u(520),{cause:l});if(s=jt(s,a),Hn===null?Hn=[s]:Hn.push(s),De!==4&&(De=2),t===null)return!0;l=jt(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=n&-n,a.lanes|=e,e=iu(a.stateNode,l,e),Br(a,e),!1;case 1:if(t=a.type,s=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||s!==null&&typeof s.componentDidCatch=="function"&&(za===null||!za.has(s))))return a.flags|=65536,n&=-n,a.lanes|=n,n=$f(n),Pf(n,e,a,l),Br(a,n),!1}a=a.return}while(a!==null);return!1}var If=Error(u(461)),Ze=!1;function Ke(e,t,a,l){t.child=e===null?Xf(t,null,a,l):kl(t,e.child,a,l)}function ed(e,t,a,l,n){a=a.render;var s=t.ref;if("ref"in l){var f={};for(var m in l)m!=="ref"&&(f[m]=l[m])}else f=l;return Ia(t),l=Yr(e,t,a,f,s,n),m=Xr(),e!==null&&!Ze?(Zr(e,t,n),la(e,t,n)):(me&&m&&Ar(t),t.flags|=1,Ke(e,t,l,n),t.child)}function td(e,t,a,l,n){if(e===null){var s=a.type;return typeof s=="function"&&!jr(s)&&s.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=s,ad(e,t,s,l,n)):(e=Ti(a.type,null,l,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!mu(e,n)){var f=s.memoizedProps;if(a=a.compare,a=a!==null?a:hn,a(f,l)&&e.ref===t.ref)return la(e,t,n)}return t.flags|=1,e=Ft(s,l),e.ref=t.ref,e.return=t,t.child=e}function ad(e,t,a,l,n){if(e!==null){var s=e.memoizedProps;if(hn(s,l)&&e.ref===t.ref)if(Ze=!1,t.pendingProps=l=s,mu(e,n))(e.flags&131072)!==0&&(Ze=!0);else return t.lanes=e.lanes,la(e,t,n)}return su(e,t,a,l,n)}function ld(e,t,a){var l=t.pendingProps,n=l.children,s=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=s!==null?s.baseLanes|a:a,e!==null){for(n=t.child=e.child,s=0;n!==null;)s=s|n.lanes|n.childLanes,n=n.sibling;t.childLanes=s&~l}else t.childLanes=0,t.child=null;return nd(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Di(t,s!==null?s.cachePool:null),s!==null?tf(t,s):Hr(),Zf(t);else return t.lanes=t.childLanes=536870912,nd(e,t,s!==null?s.baseLanes|a:a,a)}else s!==null?(Di(t,s.cachePool),tf(t,s),ja(),t.memoizedState=null):(e!==null&&Di(t,null),Hr(),ja());return Ke(e,t,n,a),t.child}function nd(e,t,a,l){var n=Cr();return n=n===null?null:{parent:He._currentValue,pool:n},t.memoizedState={baseLanes:a,cachePool:n},e!==null&&Di(t,null),Hr(),Zf(t),e!==null&&xn(e,t,l,!0),null}function Ji(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(u(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function su(e,t,a,l,n){return Ia(t),a=Yr(e,t,a,l,void 0,n),l=Xr(),e!==null&&!Ze?(Zr(e,t,n),la(e,t,n)):(me&&l&&Ar(t),t.flags|=1,Ke(e,t,a,n),t.child)}function id(e,t,a,l,n,s){return Ia(t),t.updateQueue=null,a=lf(t,l,a,n),af(e),l=Xr(),e!==null&&!Ze?(Zr(e,t,s),la(e,t,s)):(me&&l&&Ar(t),t.flags|=1,Ke(e,t,a,s),t.child)}function sd(e,t,a,l,n){if(Ia(t),t.stateNode===null){var s=Al,f=a.contextType;typeof f=="object"&&f!==null&&(s=Ie(f)),s=new a(l,s),t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,s.updater=nu,t.stateNode=s,s._reactInternals=t,s=t.stateNode,s.props=l,s.state=t.memoizedState,s.refs={},kr(t),f=a.contextType,s.context=typeof f=="object"&&f!==null?Ie(f):Al,s.state=t.memoizedState,f=a.getDerivedStateFromProps,typeof f=="function"&&(lu(t,a,f,l),s.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(f=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),f!==s.state&&nu.enqueueReplaceState(s,s.state,null),Tn(t,l,s,n),An(),s.state=t.memoizedState),typeof s.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){s=t.stateNode;var m=t.memoizedProps,y=al(a,m);s.props=y;var N=s.context,O=a.contextType;f=Al,typeof O=="object"&&O!==null&&(f=Ie(O));var R=a.getDerivedStateFromProps;O=typeof R=="function"||typeof s.getSnapshotBeforeUpdate=="function",m=t.pendingProps!==m,O||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(m||N!==f)&&Vf(t,s,l,f),ba=!1;var T=t.memoizedState;s.state=T,Tn(t,l,s,n),An(),N=t.memoizedState,m||T!==N||ba?(typeof R=="function"&&(lu(t,a,R,l),N=t.memoizedState),(y=ba||Qf(t,a,y,l,T,N,f))?(O||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=N),s.props=l,s.state=N,s.context=f,l=y):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{s=t.stateNode,_r(e,t),f=t.memoizedProps,O=al(a,f),s.props=O,R=t.pendingProps,T=s.context,N=a.contextType,y=Al,typeof N=="object"&&N!==null&&(y=Ie(N)),m=a.getDerivedStateFromProps,(N=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(f!==R||T!==y)&&Vf(t,s,l,y),ba=!1,T=t.memoizedState,s.state=T,Tn(t,l,s,n),An();var E=t.memoizedState;f!==R||T!==E||ba||e!==null&&e.dependencies!==null&&Mi(e.dependencies)?(typeof m=="function"&&(lu(t,a,m,l),E=t.memoizedState),(O=ba||Qf(t,a,O,l,T,E,y)||e!==null&&e.dependencies!==null&&Mi(e.dependencies))?(N||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(l,E,y),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(l,E,y)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||f===e.memoizedProps&&T===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&T===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=E),s.props=l,s.state=E,s.context=y,l=O):(typeof s.componentDidUpdate!="function"||f===e.memoizedProps&&T===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&T===e.memoizedState||(t.flags|=1024),l=!1)}return s=l,Ji(e,t),l=(t.flags&128)!==0,s||l?(s=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:s.render(),t.flags|=1,e!==null&&l?(t.child=kl(t,e.child,null,n),t.child=kl(t,null,a,n)):Ke(e,t,a,n),t.memoizedState=s.state,e=t.child):e=la(e,t,n),e}function rd(e,t,a,l){return bn(),t.flags|=256,Ke(e,t,a,l),t.child}var ru={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function uu(e){return{baseLanes:e,cachePool:Ko()}}function cu(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=Et),e}function ud(e,t,a){var l=t.pendingProps,n=!1,s=(t.flags&128)!==0,f;if((f=s)||(f=e!==null&&e.memoizedState===null?!1:(Le.current&2)!==0),f&&(n=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(me){if(n?Sa(t):ja(),me){var m=Oe,y;if(y=m){e:{for(y=m,m=qt;y.nodeType!==8;){if(!m){m=null;break e}if(y=Ut(y.nextSibling),y===null){m=null;break e}}m=y}m!==null?(t.memoizedState={dehydrated:m,treeContext:Ja!==null?{id:$t,overflow:Pt}:null,retryLane:536870912,hydrationErrors:null},y=ht(18,null,null,0),y.stateNode=m,y.return=t,t.child=y,at=t,Oe=null,y=!0):y=!1}y||$a(t)}if(m=t.memoizedState,m!==null&&(m=m.dehydrated,m!==null))return Vu(m)?t.lanes=32:t.lanes=536870912,null;aa(t)}return m=l.children,l=l.fallback,n?(ja(),n=t.mode,m=Wi({mode:"hidden",children:m},n),l=Ka(l,n,a,null),m.return=t,l.return=t,m.sibling=l,t.child=m,n=t.child,n.memoizedState=uu(a),n.childLanes=cu(e,f,a),t.memoizedState=ru,l):(Sa(t),ou(t,m))}if(y=e.memoizedState,y!==null&&(m=y.dehydrated,m!==null)){if(s)t.flags&256?(Sa(t),t.flags&=-257,t=fu(e,t,a)):t.memoizedState!==null?(ja(),t.child=e.child,t.flags|=128,t=null):(ja(),n=l.fallback,m=t.mode,l=Wi({mode:"visible",children:l.children},m),n=Ka(n,m,a,null),n.flags|=2,l.return=t,n.return=t,l.sibling=n,t.child=l,kl(t,e.child,null,a),l=t.child,l.memoizedState=uu(a),l.childLanes=cu(e,f,a),t.memoizedState=ru,t=n);else if(Sa(t),Vu(m)){if(f=m.nextSibling&&m.nextSibling.dataset,f)var N=f.dgst;f=N,l=Error(u(419)),l.stack="",l.digest=f,pn({value:l,source:null,stack:null}),t=fu(e,t,a)}else if(Ze||xn(e,t,a,!1),f=(a&e.childLanes)!==0,Ze||f){if(f=Ae,f!==null&&(l=a&-a,l=(l&42)!==0?1:Ks(l),l=(l&(f.suspendedLanes|a))!==0?0:l,l!==0&&l!==y.retryLane))throw y.retryLane=l,wl(e,l),xt(f,e,l),If;m.data==="$?"||Mu(),t=fu(e,t,a)}else m.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=y.treeContext,Oe=Ut(m.nextSibling),at=t,me=!0,Fa=null,qt=!1,e!==null&&(wt[At++]=$t,wt[At++]=Pt,wt[At++]=Ja,$t=e.id,Pt=e.overflow,Ja=t),t=ou(t,l.children),t.flags|=4096);return t}return n?(ja(),n=l.fallback,m=t.mode,y=e.child,N=y.sibling,l=Ft(y,{mode:"hidden",children:l.children}),l.subtreeFlags=y.subtreeFlags&65011712,N!==null?n=Ft(N,n):(n=Ka(n,m,a,null),n.flags|=2),n.return=t,l.return=t,l.sibling=n,t.child=l,l=n,n=t.child,m=e.child.memoizedState,m===null?m=uu(a):(y=m.cachePool,y!==null?(N=He._currentValue,y=y.parent!==N?{parent:N,pool:N}:y):y=Ko(),m={baseLanes:m.baseLanes|a,cachePool:y}),n.memoizedState=m,n.childLanes=cu(e,f,a),t.memoizedState=ru,l):(Sa(t),a=e.child,e=a.sibling,a=Ft(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=a,t.memoizedState=null,a)}function ou(e,t){return t=Wi({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Wi(e,t){return e=ht(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function fu(e,t,a){return kl(t,e.child,null,a),e=ou(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function cd(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Mr(e.return,t,a)}function du(e,t,a,l,n){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=l,s.tail=a,s.tailMode=n)}function od(e,t,a){var l=t.pendingProps,n=l.revealOrder,s=l.tail;if(Ke(e,t,l.children,a),l=Le.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&cd(e,a,t);else if(e.tag===19)cd(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(F(Le,l),n){case"forwards":for(a=t.child,n=null;a!==null;)e=a.alternate,e!==null&&Qi(e)===null&&(n=a),a=a.sibling;a=n,a===null?(n=t.child,t.child=null):(n=a.sibling,a.sibling=null),du(t,!1,n,a,s);break;case"backwards":for(a=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Qi(e)===null){t.child=n;break}e=n.sibling,n.sibling=a,a=n,n=e}du(t,!0,a,null,s);break;case"together":du(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function la(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Ea|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(xn(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(u(153));if(t.child!==null){for(e=t.child,a=Ft(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Ft(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function mu(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Mi(e)))}function _g(e,t,a){switch(t.tag){case 3:Ga(t,t.stateNode.containerInfo),ya(t,He,e.memoizedState.cache),bn();break;case 27:case 5:oa(t);break;case 4:Ga(t,t.stateNode.containerInfo);break;case 10:ya(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(Sa(t),t.flags|=128,null):(a&t.child.childLanes)!==0?ud(e,t,a):(Sa(t),e=la(e,t,a),e!==null?e.sibling:null);Sa(t);break;case 19:var n=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(xn(e,t,a,!1),l=(a&t.childLanes)!==0),n){if(l)return od(e,t,a);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),F(Le,Le.current),l)break;return null;case 22:case 23:return t.lanes=0,ld(e,t,a);case 24:ya(t,He,e.memoizedState.cache)}return la(e,t,a)}function fd(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ze=!0;else{if(!mu(e,a)&&(t.flags&128)===0)return Ze=!1,_g(e,t,a);Ze=(e.flags&131072)!==0}else Ze=!1,me&&(t.flags&1048576)!==0&&Lo(t,zi,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,n=l._init;if(l=n(l._payload),t.type=l,typeof l=="function")jr(l)?(e=al(l,e),t.tag=1,t=sd(null,t,l,e,a)):(t.tag=0,t=su(null,t,l,e,a));else{if(l!=null){if(n=l.$$typeof,n===ue){t.tag=11,t=ed(null,t,l,e,a);break e}else if(n===je){t.tag=14,t=td(null,t,l,e,a);break e}}throw t=lt(l)||l,Error(u(306,t,""))}}return t;case 0:return su(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,n=al(l,t.pendingProps),sd(e,t,l,n,a);case 3:e:{if(Ga(t,t.stateNode.containerInfo),e===null)throw Error(u(387));l=t.pendingProps;var s=t.memoizedState;n=s.element,_r(e,t),Tn(t,l,null,a);var f=t.memoizedState;if(l=f.cache,ya(t,He,l),l!==s.cache&&Or(t,[He],a,!0),An(),l=f.element,s.isDehydrated)if(s={element:l,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){t=rd(e,t,l,a);break e}else if(l!==n){n=jt(Error(u(424)),t),pn(n),t=rd(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Oe=Ut(e.firstChild),at=t,me=!0,Fa=null,qt=!0,a=Xf(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(bn(),l===n){t=la(e,t,a);break e}Ke(e,t,l,a)}t=t.child}return t;case 26:return Ji(e,t),e===null?(a=gm(t.type,null,t.pendingProps,null))?t.memoizedState=a:me||(a=t.type,e=t.pendingProps,l=cs(Ot.current).createElement(a),l[Pe]=t,l[nt]=e,We(l,a,e),Xe(l),t.stateNode=l):t.memoizedState=gm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return oa(t),e===null&&me&&(l=t.stateNode=dm(t.type,t.pendingProps,Ot.current),at=t,qt=!0,n=Oe,Da(t.type)?(Ku=n,Oe=Ut(l.firstChild)):Oe=n),Ke(e,t,t.pendingProps.children,a),Ji(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&me&&((n=l=Oe)&&(l=oy(l,t.type,t.pendingProps,qt),l!==null?(t.stateNode=l,at=t,Oe=Ut(l.firstChild),qt=!1,n=!0):n=!1),n||$a(t)),oa(t),n=t.type,s=t.pendingProps,f=e!==null?e.memoizedProps:null,l=s.children,Xu(n,s)?l=null:f!==null&&Xu(n,f)&&(t.flags|=32),t.memoizedState!==null&&(n=Yr(e,t,zg,null,null,a),Jn._currentValue=n),Ji(e,t),Ke(e,t,l,a),t.child;case 6:return e===null&&me&&((e=a=Oe)&&(a=fy(a,t.pendingProps,qt),a!==null?(t.stateNode=a,at=t,Oe=null,e=!0):e=!1),e||$a(t)),null;case 13:return ud(e,t,a);case 4:return Ga(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=kl(t,null,l,a):Ke(e,t,l,a),t.child;case 11:return ed(e,t,t.type,t.pendingProps,a);case 7:return Ke(e,t,t.pendingProps,a),t.child;case 8:return Ke(e,t,t.pendingProps.children,a),t.child;case 12:return Ke(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,ya(t,t.type,l.value),Ke(e,t,l.children,a),t.child;case 9:return n=t.type._context,l=t.pendingProps.children,Ia(t),n=Ie(n),l=l(n),t.flags|=1,Ke(e,t,l,a),t.child;case 14:return td(e,t,t.type,t.pendingProps,a);case 15:return ad(e,t,t.type,t.pendingProps,a);case 19:return od(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=Wi(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Ft(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return ld(e,t,a);case 24:return Ia(t),l=Ie(He),e===null?(n=Cr(),n===null&&(n=Ae,s=Dr(),n.pooledCache=s,s.refCount++,s!==null&&(n.pooledCacheLanes|=a),n=s),t.memoizedState={parent:l,cache:n},kr(t),ya(t,He,n)):((e.lanes&a)!==0&&(_r(e,t),Tn(t,null,null,a),An()),n=e.memoizedState,s=t.memoizedState,n.parent!==l?(n={parent:l,cache:l},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),ya(t,He,l)):(l=s.cache,ya(t,He,l),l!==n.cache&&Or(t,[He],a,!0))),Ke(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(u(156,t.tag))}function na(e){e.flags|=4}function dd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!vm(t)){if(t=Tt.current,t!==null&&((ie&4194048)===ie?Ht!==null:(ie&62914560)!==ie&&(ie&536870912)===0||t!==Ht))throw Nn=Ur,Jo;e.flags|=8192}}function Fi(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Yc():536870912,e.lanes|=t,Hl|=t)}function Cn(e,t){if(!me)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Me(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function Bg(e,t,a){var l=t.pendingProps;switch(Tr(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Me(t),null;case 1:return Me(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),ea(He),Dt(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(yn(t)?na(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Xo())),Me(t),null;case 26:return a=t.memoizedState,e===null?(na(t),a!==null?(Me(t),dd(t,a)):(Me(t),t.flags&=-16777217)):a?a!==e.memoizedState?(na(t),Me(t),dd(t,a)):(Me(t),t.flags&=-16777217):(e.memoizedProps!==l&&na(t),Me(t),t.flags&=-16777217),null;case 27:fa(t),a=Ot.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&na(t);else{if(!l){if(t.stateNode===null)throw Error(u(166));return Me(t),null}e=ce.current,yn(t)?Go(t):(e=dm(n,l,a),t.stateNode=e,na(t))}return Me(t),null;case 5:if(fa(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&na(t);else{if(!l){if(t.stateNode===null)throw Error(u(166));return Me(t),null}if(e=ce.current,yn(t))Go(t);else{switch(n=cs(Ot.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}e[Pe]=t,e[nt]=l;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(We(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&na(t)}}return Me(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&na(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(u(166));if(e=Ot.current,yn(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,n=at,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}e[Pe]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||im(e.nodeValue,a)),e||$a(t)}else e=cs(e).createTextNode(l),e[Pe]=t,t.stateNode=e}return Me(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=yn(t),l!==null&&l.dehydrated!==null){if(e===null){if(!n)throw Error(u(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(u(317));n[Pe]=t}else bn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Me(t),n=!1}else n=Xo(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(aa(t),t):(aa(t),null)}if(aa(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var s=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(s=l.memoizedState.cachePool.pool),s!==n&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),Fi(t,t.updateQueue),Me(t),null;case 4:return Dt(),e===null&&qu(t.stateNode.containerInfo),Me(t),null;case 10:return ea(t.type),Me(t),null;case 19:if(Ne(Le),n=t.memoizedState,n===null)return Me(t),null;if(l=(t.flags&128)!==0,s=n.rendering,s===null)if(l)Cn(n,!1);else{if(De!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(s=Qi(e),s!==null){for(t.flags|=128,Cn(n,!1),e=s.updateQueue,t.updateQueue=e,Fi(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Ho(a,e),a=a.sibling;return F(Le,Le.current&1|2),t.child}e=e.sibling}n.tail!==null&&Bt()>Ii&&(t.flags|=128,l=!0,Cn(n,!1),t.lanes=4194304)}else{if(!l)if(e=Qi(s),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,Fi(t,e),Cn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!s.alternate&&!me)return Me(t),null}else 2*Bt()-n.renderingStartTime>Ii&&a!==536870912&&(t.flags|=128,l=!0,Cn(n,!1),t.lanes=4194304);n.isBackwards?(s.sibling=t.child,t.child=s):(e=n.last,e!==null?e.sibling=s:t.child=s,n.last=s)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=Bt(),t.sibling=null,e=Le.current,F(Le,l?e&1|2:e&1),t):(Me(t),null);case 22:case 23:return aa(t),Lr(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(Me(t),t.subtreeFlags&6&&(t.flags|=8192)):Me(t),a=t.updateQueue,a!==null&&Fi(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&Ne(el),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),ea(He),Me(t),null;case 25:return null;case 30:return null}throw Error(u(156,t.tag))}function qg(e,t){switch(Tr(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ea(He),Dt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return fa(t),null;case 13:if(aa(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(u(340));bn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Ne(Le),null;case 4:return Dt(),null;case 10:return ea(t.type),null;case 22:case 23:return aa(t),Lr(),e!==null&&Ne(el),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return ea(He),null;case 25:return null;default:return null}}function md(e,t){switch(Tr(t),t.tag){case 3:ea(He),Dt();break;case 26:case 27:case 5:fa(t);break;case 4:Dt();break;case 13:aa(t);break;case 19:Ne(Le);break;case 10:ea(t.type);break;case 22:case 23:aa(t),Lr(),e!==null&&Ne(el);break;case 24:ea(He)}}function Un(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&e)===e){l=void 0;var s=a.create,f=a.inst;l=s(),f.destroy=l}a=a.next}while(a!==n)}}catch(m){we(t,t.return,m)}}function Na(e,t,a){try{var l=t.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var s=n.next;l=s;do{if((l.tag&e)===e){var f=l.inst,m=f.destroy;if(m!==void 0){f.destroy=void 0,n=t;var y=a,N=m;try{N()}catch(O){we(n,y,O)}}}l=l.next}while(l!==s)}}catch(O){we(t,t.return,O)}}function hd(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{ef(t,a)}catch(l){we(e,e.return,l)}}}function gd(e,t,a){a.props=al(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){we(e,t,l)}}function kn(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(n){we(e,t,n)}}function Lt(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){we(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){we(e,t,n)}else a.current=null}function yd(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){we(e,e.return,n)}}function hu(e,t,a){try{var l=e.stateNode;iy(l,e.type,a,t),l[nt]=t}catch(n){we(e,e.return,n)}}function bd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Da(e.type)||e.tag===4}function gu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||bd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Da(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function yu(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=us));else if(l!==4&&(l===27&&Da(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(yu(e,t,a),e=e.sibling;e!==null;)yu(e,t,a),e=e.sibling}function $i(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&Da(e.type)&&(a=e.stateNode),e=e.child,e!==null))for($i(e,t,a),e=e.sibling;e!==null;)$i(e,t,a),e=e.sibling}function pd(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);We(t,l,a),t[Pe]=e,t[nt]=a}catch(s){we(e,e.return,s)}}var ia=!1,Ce=!1,bu=!1,xd=typeof WeakSet=="function"?WeakSet:Set,Qe=null;function Hg(e,t){if(e=e.containerInfo,Gu=gs,e=Mo(e),gr(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,s=l.focusNode;l=l.focusOffset;try{a.nodeType,s.nodeType}catch{a=null;break e}var f=0,m=-1,y=-1,N=0,O=0,R=e,T=null;t:for(;;){for(var E;R!==a||n!==0&&R.nodeType!==3||(m=f+n),R!==s||l!==0&&R.nodeType!==3||(y=f+l),R.nodeType===3&&(f+=R.nodeValue.length),(E=R.firstChild)!==null;)T=R,R=E;for(;;){if(R===e)break t;if(T===a&&++N===n&&(m=f),T===s&&++O===l&&(y=f),(E=R.nextSibling)!==null)break;R=T,T=R.parentNode}R=E}a=m===-1||y===-1?null:{start:m,end:y}}else a=null}a=a||{start:0,end:0}}else a=null;for(Yu={focusedElem:e,selectionRange:a},gs=!1,Qe=t;Qe!==null;)if(t=Qe,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Qe=e;else for(;Qe!==null;){switch(t=Qe,s=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&s!==null){e=void 0,a=t,n=s.memoizedProps,s=s.memoizedState,l=a.stateNode;try{var W=al(a.type,n,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(W,s),l.__reactInternalSnapshotBeforeUpdate=e}catch(V){we(a,a.return,V)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Qu(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Qu(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(u(163))}if(e=t.sibling,e!==null){e.return=t.return,Qe=e;break}Qe=t.return}}function vd(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:wa(e,a),l&4&&Un(5,a);break;case 1:if(wa(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(f){we(a,a.return,f)}else{var n=al(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){we(a,a.return,f)}}l&64&&hd(a),l&512&&kn(a,a.return);break;case 3:if(wa(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{ef(e,t)}catch(f){we(a,a.return,f)}}break;case 27:t===null&&l&4&&pd(a);case 26:case 5:wa(e,a),t===null&&l&4&&yd(a),l&512&&kn(a,a.return);break;case 12:wa(e,a);break;case 13:wa(e,a),l&4&&Nd(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Jg.bind(null,a),dy(e,a))));break;case 22:if(l=a.memoizedState!==null||ia,!l){t=t!==null&&t.memoizedState!==null||Ce,n=ia;var s=Ce;ia=l,(Ce=t)&&!s?Aa(e,a,(a.subtreeFlags&8772)!==0):wa(e,a),ia=n,Ce=s}break;case 30:break;default:wa(e,a)}}function Sd(e){var t=e.alternate;t!==null&&(e.alternate=null,Sd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Fs(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Te=null,rt=!1;function sa(e,t,a){for(a=a.child;a!==null;)jd(e,t,a),a=a.sibling}function jd(e,t,a){if(ft&&typeof ft.onCommitFiberUnmount=="function")try{ft.onCommitFiberUnmount(tn,a)}catch{}switch(a.tag){case 26:Ce||Lt(a,t),sa(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ce||Lt(a,t);var l=Te,n=rt;Da(a.type)&&(Te=a.stateNode,rt=!1),sa(e,t,a),Zn(a.stateNode),Te=l,rt=n;break;case 5:Ce||Lt(a,t);case 6:if(l=Te,n=rt,Te=null,sa(e,t,a),Te=l,rt=n,Te!==null)if(rt)try{(Te.nodeType===9?Te.body:Te.nodeName==="HTML"?Te.ownerDocument.body:Te).removeChild(a.stateNode)}catch(s){we(a,t,s)}else try{Te.removeChild(a.stateNode)}catch(s){we(a,t,s)}break;case 18:Te!==null&&(rt?(e=Te,om(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),Pn(e)):om(Te,a.stateNode));break;case 4:l=Te,n=rt,Te=a.stateNode.containerInfo,rt=!0,sa(e,t,a),Te=l,rt=n;break;case 0:case 11:case 14:case 15:Ce||Na(2,a,t),Ce||Na(4,a,t),sa(e,t,a);break;case 1:Ce||(Lt(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&gd(a,t,l)),sa(e,t,a);break;case 21:sa(e,t,a);break;case 22:Ce=(l=Ce)||a.memoizedState!==null,sa(e,t,a),Ce=l;break;default:sa(e,t,a)}}function Nd(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Pn(e)}catch(a){we(t,t.return,a)}}function Lg(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new xd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new xd),t;default:throw Error(u(435,e.tag))}}function pu(e,t){var a=Lg(e);t.forEach(function(l){var n=Wg.bind(null,e,l);a.has(l)||(a.add(l),l.then(n,n))})}function gt(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],s=e,f=t,m=f;e:for(;m!==null;){switch(m.tag){case 27:if(Da(m.type)){Te=m.stateNode,rt=!1;break e}break;case 5:Te=m.stateNode,rt=!1;break e;case 3:case 4:Te=m.stateNode.containerInfo,rt=!0;break e}m=m.return}if(Te===null)throw Error(u(160));jd(s,f,n),Te=null,rt=!1,s=n.alternate,s!==null&&(s.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)wd(t,e),t=t.sibling}var Ct=null;function wd(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:gt(t,e),yt(e),l&4&&(Na(3,e,e.return),Un(3,e),Na(5,e,e.return));break;case 1:gt(t,e),yt(e),l&512&&(Ce||a===null||Lt(a,a.return)),l&64&&ia&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=Ct;if(gt(t,e),yt(e),l&512&&(Ce||a===null||Lt(a,a.return)),l&4){var s=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,n=n.ownerDocument||n;t:switch(l){case"title":s=n.getElementsByTagName("title")[0],(!s||s[nn]||s[Pe]||s.namespaceURI==="http://www.w3.org/2000/svg"||s.hasAttribute("itemprop"))&&(s=n.createElement(l),n.head.insertBefore(s,n.querySelector("head > title"))),We(s,l,a),s[Pe]=e,Xe(s),l=s;break e;case"link":var f=pm("link","href",n).get(l+(a.href||""));if(f){for(var m=0;m<f.length;m++)if(s=f[m],s.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&s.getAttribute("rel")===(a.rel==null?null:a.rel)&&s.getAttribute("title")===(a.title==null?null:a.title)&&s.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){f.splice(m,1);break t}}s=n.createElement(l),We(s,l,a),n.head.appendChild(s);break;case"meta":if(f=pm("meta","content",n).get(l+(a.content||""))){for(m=0;m<f.length;m++)if(s=f[m],s.getAttribute("content")===(a.content==null?null:""+a.content)&&s.getAttribute("name")===(a.name==null?null:a.name)&&s.getAttribute("property")===(a.property==null?null:a.property)&&s.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&s.getAttribute("charset")===(a.charSet==null?null:a.charSet)){f.splice(m,1);break t}}s=n.createElement(l),We(s,l,a),n.head.appendChild(s);break;default:throw Error(u(468,l))}s[Pe]=e,Xe(s),l=s}e.stateNode=l}else xm(n,e.type,e.stateNode);else e.stateNode=bm(n,l,e.memoizedProps);else s!==l?(s===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):s.count--,l===null?xm(n,e.type,e.stateNode):bm(n,l,e.memoizedProps)):l===null&&e.stateNode!==null&&hu(e,e.memoizedProps,a.memoizedProps)}break;case 27:gt(t,e),yt(e),l&512&&(Ce||a===null||Lt(a,a.return)),a!==null&&l&4&&hu(e,e.memoizedProps,a.memoizedProps);break;case 5:if(gt(t,e),yt(e),l&512&&(Ce||a===null||Lt(a,a.return)),e.flags&32){n=e.stateNode;try{bl(n,"")}catch(E){we(e,e.return,E)}}l&4&&e.stateNode!=null&&(n=e.memoizedProps,hu(e,n,a!==null?a.memoizedProps:n)),l&1024&&(bu=!0);break;case 6:if(gt(t,e),yt(e),l&4){if(e.stateNode===null)throw Error(u(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(E){we(e,e.return,E)}}break;case 3:if(ds=null,n=Ct,Ct=os(t.containerInfo),gt(t,e),Ct=n,yt(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{Pn(t.containerInfo)}catch(E){we(e,e.return,E)}bu&&(bu=!1,Ad(e));break;case 4:l=Ct,Ct=os(e.stateNode.containerInfo),gt(t,e),yt(e),Ct=l;break;case 12:gt(t,e),yt(e);break;case 13:gt(t,e),yt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(wu=Bt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,pu(e,l)));break;case 22:n=e.memoizedState!==null;var y=a!==null&&a.memoizedState!==null,N=ia,O=Ce;if(ia=N||n,Ce=O||y,gt(t,e),Ce=O,ia=N,yt(e),l&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(a===null||y||ia||Ce||ll(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){y=a=t;try{if(s=y.stateNode,n)f=s.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{m=y.stateNode;var R=y.memoizedProps.style,T=R!=null&&R.hasOwnProperty("display")?R.display:null;m.style.display=T==null||typeof T=="boolean"?"":(""+T).trim()}}catch(E){we(y,y.return,E)}}}else if(t.tag===6){if(a===null){y=t;try{y.stateNode.nodeValue=n?"":y.memoizedProps}catch(E){we(y,y.return,E)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,pu(e,a))));break;case 19:gt(t,e),yt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,pu(e,l)));break;case 30:break;case 21:break;default:gt(t,e),yt(e)}}function yt(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(bd(l)){a=l;break}l=l.return}if(a==null)throw Error(u(160));switch(a.tag){case 27:var n=a.stateNode,s=gu(e);$i(e,s,n);break;case 5:var f=a.stateNode;a.flags&32&&(bl(f,""),a.flags&=-33);var m=gu(e);$i(e,m,f);break;case 3:case 4:var y=a.stateNode.containerInfo,N=gu(e);yu(e,N,y);break;default:throw Error(u(161))}}catch(O){we(e,e.return,O)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Ad(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Ad(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function wa(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)vd(e,t.alternate,t),t=t.sibling}function ll(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Na(4,t,t.return),ll(t);break;case 1:Lt(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&gd(t,t.return,a),ll(t);break;case 27:Zn(t.stateNode);case 26:case 5:Lt(t,t.return),ll(t);break;case 22:t.memoizedState===null&&ll(t);break;case 30:ll(t);break;default:ll(t)}e=e.sibling}}function Aa(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,n=e,s=t,f=s.flags;switch(s.tag){case 0:case 11:case 15:Aa(n,s,a),Un(4,s);break;case 1:if(Aa(n,s,a),l=s,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(N){we(l,l.return,N)}if(l=s,n=l.updateQueue,n!==null){var m=l.stateNode;try{var y=n.shared.hiddenCallbacks;if(y!==null)for(n.shared.hiddenCallbacks=null,n=0;n<y.length;n++)Io(y[n],m)}catch(N){we(l,l.return,N)}}a&&f&64&&hd(s),kn(s,s.return);break;case 27:pd(s);case 26:case 5:Aa(n,s,a),a&&l===null&&f&4&&yd(s),kn(s,s.return);break;case 12:Aa(n,s,a);break;case 13:Aa(n,s,a),a&&f&4&&Nd(n,s);break;case 22:s.memoizedState===null&&Aa(n,s,a),kn(s,s.return);break;case 30:break;default:Aa(n,s,a)}t=t.sibling}}function xu(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&vn(a))}function vu(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&vn(e))}function Gt(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Td(e,t,a,l),t=t.sibling}function Td(e,t,a,l){var n=t.flags;switch(t.tag){case 0:case 11:case 15:Gt(e,t,a,l),n&2048&&Un(9,t);break;case 1:Gt(e,t,a,l);break;case 3:Gt(e,t,a,l),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&vn(e)));break;case 12:if(n&2048){Gt(e,t,a,l),e=t.stateNode;try{var s=t.memoizedProps,f=s.id,m=s.onPostCommit;typeof m=="function"&&m(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(y){we(t,t.return,y)}}else Gt(e,t,a,l);break;case 13:Gt(e,t,a,l);break;case 23:break;case 22:s=t.stateNode,f=t.alternate,t.memoizedState!==null?s._visibility&2?Gt(e,t,a,l):_n(e,t):s._visibility&2?Gt(e,t,a,l):(s._visibility|=2,_l(e,t,a,l,(t.subtreeFlags&10256)!==0)),n&2048&&xu(f,t);break;case 24:Gt(e,t,a,l),n&2048&&vu(t.alternate,t);break;default:Gt(e,t,a,l)}}function _l(e,t,a,l,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var s=e,f=t,m=a,y=l,N=f.flags;switch(f.tag){case 0:case 11:case 15:_l(s,f,m,y,n),Un(8,f);break;case 23:break;case 22:var O=f.stateNode;f.memoizedState!==null?O._visibility&2?_l(s,f,m,y,n):_n(s,f):(O._visibility|=2,_l(s,f,m,y,n)),n&&N&2048&&xu(f.alternate,f);break;case 24:_l(s,f,m,y,n),n&&N&2048&&vu(f.alternate,f);break;default:_l(s,f,m,y,n)}t=t.sibling}}function _n(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,n=l.flags;switch(l.tag){case 22:_n(a,l),n&2048&&xu(l.alternate,l);break;case 24:_n(a,l),n&2048&&vu(l.alternate,l);break;default:_n(a,l)}t=t.sibling}}var Bn=8192;function Bl(e){if(e.subtreeFlags&Bn)for(e=e.child;e!==null;)Ed(e),e=e.sibling}function Ed(e){switch(e.tag){case 26:Bl(e),e.flags&Bn&&e.memoizedState!==null&&Ay(Ct,e.memoizedState,e.memoizedProps);break;case 5:Bl(e);break;case 3:case 4:var t=Ct;Ct=os(e.stateNode.containerInfo),Bl(e),Ct=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Bn,Bn=16777216,Bl(e),Bn=t):Bl(e));break;default:Bl(e)}}function zd(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function qn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Qe=l,Od(l,e)}zd(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Md(e),e=e.sibling}function Md(e){switch(e.tag){case 0:case 11:case 15:qn(e),e.flags&2048&&Na(9,e,e.return);break;case 3:qn(e);break;case 12:qn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Pi(e)):qn(e);break;default:qn(e)}}function Pi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Qe=l,Od(l,e)}zd(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Na(8,t,t.return),Pi(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,Pi(t));break;default:Pi(t)}e=e.sibling}}function Od(e,t){for(;Qe!==null;){var a=Qe;switch(a.tag){case 0:case 11:case 15:Na(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:vn(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Qe=l;else e:for(a=e;Qe!==null;){l=Qe;var n=l.sibling,s=l.return;if(Sd(l),l===a){Qe=null;break e}if(n!==null){n.return=s,Qe=n;break e}Qe=s}}}var Gg={getCacheForType:function(e){var t=Ie(He),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},Yg=typeof WeakMap=="function"?WeakMap:Map,ge=0,Ae=null,ae=null,ie=0,ye=0,bt=null,Ta=!1,ql=!1,Su=!1,ra=0,De=0,Ea=0,nl=0,ju=0,Et=0,Hl=0,Hn=null,ut=null,Nu=!1,wu=0,Ii=1/0,es=null,za=null,Je=0,Ma=null,Ll=null,Gl=0,Au=0,Tu=null,Dd=null,Ln=0,Eu=null;function pt(){if((ge&2)!==0&&ie!==0)return ie&-ie;if(A.T!==null){var e=zl;return e!==0?e:Uu()}return Qc()}function Rd(){Et===0&&(Et=(ie&536870912)===0||me?Gc():536870912);var e=Tt.current;return e!==null&&(e.flags|=32),Et}function xt(e,t,a){(e===Ae&&(ye===2||ye===9)||e.cancelPendingCommit!==null)&&(Yl(e,0),Oa(e,ie,Et,!1)),ln(e,a),((ge&2)===0||e!==Ae)&&(e===Ae&&((ge&2)===0&&(nl|=a),De===4&&Oa(e,ie,Et,!1)),Yt(e))}function Cd(e,t,a){if((ge&6)!==0)throw Error(u(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||an(e,t),n=l?Qg(e,t):Ou(e,t,!0),s=l;do{if(n===0){ql&&!l&&Oa(e,t,0,!1);break}else{if(a=e.current.alternate,s&&!Xg(a)){n=Ou(e,t,!1),s=!1;continue}if(n===2){if(s=t,e.errorRecoveryDisabledLanes&s)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var m=e;n=Hn;var y=m.current.memoizedState.isDehydrated;if(y&&(Yl(m,f).flags|=256),f=Ou(m,f,!1),f!==2){if(Su&&!y){m.errorRecoveryDisabledLanes|=s,nl|=s,n=4;break e}s=ut,ut=n,s!==null&&(ut===null?ut=s:ut.push.apply(ut,s))}n=f}if(s=!1,n!==2)continue}}if(n===1){Yl(e,0),Oa(e,t,0,!0);break}e:{switch(l=e,s=n,s){case 0:case 1:throw Error(u(345));case 4:if((t&4194048)!==t)break;case 6:Oa(l,t,Et,!Ta);break e;case 2:ut=null;break;case 3:case 5:break;default:throw Error(u(329))}if((t&62914560)===t&&(n=wu+300-Bt(),10<n)){if(Oa(l,t,Et,!Ta),fi(l,0,!0)!==0)break e;l.timeoutHandle=um(Ud.bind(null,l,a,ut,es,Nu,t,Et,nl,Hl,Ta,s,2,-0,0),n);break e}Ud(l,a,ut,es,Nu,t,Et,nl,Hl,Ta,s,0,-0,0)}}break}while(!0);Yt(e)}function Ud(e,t,a,l,n,s,f,m,y,N,O,R,T,E){if(e.timeoutHandle=-1,R=t.subtreeFlags,(R&8192||(R&16785408)===16785408)&&(Kn={stylesheets:null,count:0,unsuspend:wy},Ed(t),R=Ty(),R!==null)){e.cancelPendingCommit=R(Gd.bind(null,e,t,s,a,l,n,f,m,y,O,1,T,E)),Oa(e,s,f,!N);return}Gd(e,t,s,a,l,n,f,m,y)}function Xg(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],s=n.getSnapshot;n=n.value;try{if(!mt(s(),n))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Oa(e,t,a,l){t&=~ju,t&=~nl,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var n=t;0<n;){var s=31-dt(n),f=1<<s;l[s]=-1,n&=~f}a!==0&&Xc(e,a,t)}function ts(){return(ge&6)===0?(Gn(0),!1):!0}function zu(){if(ae!==null){if(ye===0)var e=ae.return;else e=ae,It=Pa=null,Qr(e),Ul=null,Dn=0,e=ae;for(;e!==null;)md(e.alternate,e),e=e.return;ae=null}}function Yl(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,ry(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),zu(),Ae=e,ae=a=Ft(e.current,null),ie=t,ye=0,bt=null,Ta=!1,ql=an(e,t),Su=!1,Hl=Et=ju=nl=Ea=De=0,ut=Hn=null,Nu=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var n=31-dt(l),s=1<<n;t|=e[n],l&=~s}return ra=t,Ni(),a}function kd(e,t){I=null,A.H=Yi,t===jn||t===Ri?(t=$o(),ye=3):t===Jo?(t=$o(),ye=4):ye=t===If?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,bt=t,ae===null&&(De=1,Ki(e,jt(t,e.current)))}function _d(){var e=A.H;return A.H=Yi,e===null?Yi:e}function Bd(){var e=A.A;return A.A=Gg,e}function Mu(){De=4,Ta||(ie&4194048)!==ie&&Tt.current!==null||(ql=!0),(Ea&134217727)===0&&(nl&134217727)===0||Ae===null||Oa(Ae,ie,Et,!1)}function Ou(e,t,a){var l=ge;ge|=2;var n=_d(),s=Bd();(Ae!==e||ie!==t)&&(es=null,Yl(e,t)),t=!1;var f=De;e:do try{if(ye!==0&&ae!==null){var m=ae,y=bt;switch(ye){case 8:zu(),f=6;break e;case 3:case 2:case 9:case 6:Tt.current===null&&(t=!0);var N=ye;if(ye=0,bt=null,Xl(e,m,y,N),a&&ql){f=0;break e}break;default:N=ye,ye=0,bt=null,Xl(e,m,y,N)}}Zg(),f=De;break}catch(O){kd(e,O)}while(!0);return t&&e.shellSuspendCounter++,It=Pa=null,ge=l,A.H=n,A.A=s,ae===null&&(Ae=null,ie=0,Ni()),f}function Zg(){for(;ae!==null;)qd(ae)}function Qg(e,t){var a=ge;ge|=2;var l=_d(),n=Bd();Ae!==e||ie!==t?(es=null,Ii=Bt()+500,Yl(e,t)):ql=an(e,t);e:do try{if(ye!==0&&ae!==null){t=ae;var s=bt;t:switch(ye){case 1:ye=0,bt=null,Xl(e,t,s,1);break;case 2:case 9:if(Wo(s)){ye=0,bt=null,Hd(t);break}t=function(){ye!==2&&ye!==9||Ae!==e||(ye=7),Yt(e)},s.then(t,t);break e;case 3:ye=7;break e;case 4:ye=5;break e;case 7:Wo(s)?(ye=0,bt=null,Hd(t)):(ye=0,bt=null,Xl(e,t,s,7));break;case 5:var f=null;switch(ae.tag){case 26:f=ae.memoizedState;case 5:case 27:var m=ae;if(!f||vm(f)){ye=0,bt=null;var y=m.sibling;if(y!==null)ae=y;else{var N=m.return;N!==null?(ae=N,as(N)):ae=null}break t}}ye=0,bt=null,Xl(e,t,s,5);break;case 6:ye=0,bt=null,Xl(e,t,s,6);break;case 8:zu(),De=6;break e;default:throw Error(u(462))}}Vg();break}catch(O){kd(e,O)}while(!0);return It=Pa=null,A.H=l,A.A=n,ge=a,ae!==null?0:(Ae=null,ie=0,Ni(),De)}function Vg(){for(;ae!==null&&!h0();)qd(ae)}function qd(e){var t=fd(e.alternate,e,ra);e.memoizedProps=e.pendingProps,t===null?as(e):ae=t}function Hd(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=id(a,t,t.pendingProps,t.type,void 0,ie);break;case 11:t=id(a,t,t.pendingProps,t.type.render,t.ref,ie);break;case 5:Qr(t);default:md(a,t),t=ae=Ho(t,ra),t=fd(a,t,ra)}e.memoizedProps=e.pendingProps,t===null?as(e):ae=t}function Xl(e,t,a,l){It=Pa=null,Qr(t),Ul=null,Dn=0;var n=t.return;try{if(kg(e,n,t,a,ie)){De=1,Ki(e,jt(a,e.current)),ae=null;return}}catch(s){if(n!==null)throw ae=n,s;De=1,Ki(e,jt(a,e.current)),ae=null;return}t.flags&32768?(me||l===1?e=!0:ql||(ie&536870912)!==0?e=!1:(Ta=e=!0,(l===2||l===9||l===3||l===6)&&(l=Tt.current,l!==null&&l.tag===13&&(l.flags|=16384))),Ld(t,e)):as(t)}function as(e){var t=e;do{if((t.flags&32768)!==0){Ld(t,Ta);return}e=t.return;var a=Bg(t.alternate,t,ra);if(a!==null){ae=a;return}if(t=t.sibling,t!==null){ae=t;return}ae=t=e}while(t!==null);De===0&&(De=5)}function Ld(e,t){do{var a=qg(e.alternate,e);if(a!==null){a.flags&=32767,ae=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){ae=e;return}ae=e=a}while(e!==null);De=6,ae=null}function Gd(e,t,a,l,n,s,f,m,y){e.cancelPendingCommit=null;do ls();while(Je!==0);if((ge&6)!==0)throw Error(u(327));if(t!==null){if(t===e.current)throw Error(u(177));if(s=t.lanes|t.childLanes,s|=vr,w0(e,a,s,f,m,y),e===Ae&&(ae=Ae=null,ie=0),Ll=t,Ma=e,Gl=a,Au=s,Tu=n,Dd=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Fg(ui,function(){return Vd(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=A.T,A.T=null,n=B.p,B.p=2,f=ge,ge|=4;try{Hg(e,t,a)}finally{ge=f,B.p=n,A.T=l}}Je=1,Yd(),Xd(),Zd()}}function Yd(){if(Je===1){Je=0;var e=Ma,t=Ll,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=A.T,A.T=null;var l=B.p;B.p=2;var n=ge;ge|=4;try{wd(t,e);var s=Yu,f=Mo(e.containerInfo),m=s.focusedElem,y=s.selectionRange;if(f!==m&&m&&m.ownerDocument&&zo(m.ownerDocument.documentElement,m)){if(y!==null&&gr(m)){var N=y.start,O=y.end;if(O===void 0&&(O=N),"selectionStart"in m)m.selectionStart=N,m.selectionEnd=Math.min(O,m.value.length);else{var R=m.ownerDocument||document,T=R&&R.defaultView||window;if(T.getSelection){var E=T.getSelection(),W=m.textContent.length,V=Math.min(y.start,W),Se=y.end===void 0?V:Math.min(y.end,W);!E.extend&&V>Se&&(f=Se,Se=V,V=f);var x=Eo(m,V),p=Eo(m,Se);if(x&&p&&(E.rangeCount!==1||E.anchorNode!==x.node||E.anchorOffset!==x.offset||E.focusNode!==p.node||E.focusOffset!==p.offset)){var S=R.createRange();S.setStart(x.node,x.offset),E.removeAllRanges(),V>Se?(E.addRange(S),E.extend(p.node,p.offset)):(S.setEnd(p.node,p.offset),E.addRange(S))}}}}for(R=[],E=m;E=E.parentNode;)E.nodeType===1&&R.push({element:E,left:E.scrollLeft,top:E.scrollTop});for(typeof m.focus=="function"&&m.focus(),m=0;m<R.length;m++){var D=R[m];D.element.scrollLeft=D.left,D.element.scrollTop=D.top}}gs=!!Gu,Yu=Gu=null}finally{ge=n,B.p=l,A.T=a}}e.current=t,Je=2}}function Xd(){if(Je===2){Je=0;var e=Ma,t=Ll,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=A.T,A.T=null;var l=B.p;B.p=2;var n=ge;ge|=4;try{vd(e,t.alternate,t)}finally{ge=n,B.p=l,A.T=a}}Je=3}}function Zd(){if(Je===4||Je===3){Je=0,g0();var e=Ma,t=Ll,a=Gl,l=Dd;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Je=5:(Je=0,Ll=Ma=null,Qd(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(za=null),Js(a),t=t.stateNode,ft&&typeof ft.onCommitFiberRoot=="function")try{ft.onCommitFiberRoot(tn,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=A.T,n=B.p,B.p=2,A.T=null;try{for(var s=e.onRecoverableError,f=0;f<l.length;f++){var m=l[f];s(m.value,{componentStack:m.stack})}}finally{A.T=t,B.p=n}}(Gl&3)!==0&&ls(),Yt(e),n=e.pendingLanes,(a&4194090)!==0&&(n&42)!==0?e===Eu?Ln++:(Ln=0,Eu=e):Ln=0,Gn(0)}}function Qd(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,vn(t)))}function ls(e){return Yd(),Xd(),Zd(),Vd()}function Vd(){if(Je!==5)return!1;var e=Ma,t=Au;Au=0;var a=Js(Gl),l=A.T,n=B.p;try{B.p=32>a?32:a,A.T=null,a=Tu,Tu=null;var s=Ma,f=Gl;if(Je=0,Ll=Ma=null,Gl=0,(ge&6)!==0)throw Error(u(331));var m=ge;if(ge|=4,Md(s.current),Td(s,s.current,f,a),ge=m,Gn(0,!1),ft&&typeof ft.onPostCommitFiberRoot=="function")try{ft.onPostCommitFiberRoot(tn,s)}catch{}return!0}finally{B.p=n,A.T=l,Qd(e,t)}}function Kd(e,t,a){t=jt(a,t),t=iu(e.stateNode,t,2),e=xa(e,t,2),e!==null&&(ln(e,2),Yt(e))}function we(e,t,a){if(e.tag===3)Kd(e,e,a);else for(;t!==null;){if(t.tag===3){Kd(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(za===null||!za.has(l))){e=jt(a,e),a=$f(2),l=xa(t,a,2),l!==null&&(Pf(a,l,t,e),ln(l,2),Yt(l));break}}t=t.return}}function Du(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new Yg;var n=new Set;l.set(t,n)}else n=l.get(t),n===void 0&&(n=new Set,l.set(t,n));n.has(a)||(Su=!0,n.add(a),e=Kg.bind(null,e,t,a),t.then(e,e))}function Kg(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Ae===e&&(ie&a)===a&&(De===4||De===3&&(ie&62914560)===ie&&300>Bt()-wu?(ge&2)===0&&Yl(e,0):ju|=a,Hl===ie&&(Hl=0)),Yt(e)}function Jd(e,t){t===0&&(t=Yc()),e=wl(e,t),e!==null&&(ln(e,t),Yt(e))}function Jg(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Jd(e,a)}function Wg(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,n=e.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(u(314))}l!==null&&l.delete(t),Jd(e,a)}function Fg(e,t){return Zs(e,t)}var ns=null,Zl=null,Ru=!1,is=!1,Cu=!1,il=0;function Yt(e){e!==Zl&&e.next===null&&(Zl===null?ns=Zl=e:Zl=Zl.next=e),is=!0,Ru||(Ru=!0,Pg())}function Gn(e,t){if(!Cu&&is){Cu=!0;do for(var a=!1,l=ns;l!==null;){if(e!==0){var n=l.pendingLanes;if(n===0)var s=0;else{var f=l.suspendedLanes,m=l.pingedLanes;s=(1<<31-dt(42|e)+1)-1,s&=n&~(f&~m),s=s&201326741?s&201326741|1:s?s|2:0}s!==0&&(a=!0,Pd(l,s))}else s=ie,s=fi(l,l===Ae?s:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(s&3)===0||an(l,s)||(a=!0,Pd(l,s));l=l.next}while(a);Cu=!1}}function $g(){Wd()}function Wd(){is=Ru=!1;var e=0;il!==0&&(sy()&&(e=il),il=0);for(var t=Bt(),a=null,l=ns;l!==null;){var n=l.next,s=Fd(l,t);s===0?(l.next=null,a===null?ns=n:a.next=n,n===null&&(Zl=a)):(a=l,(e!==0||(s&3)!==0)&&(is=!0)),l=n}Gn(e)}function Fd(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,n=e.expirationTimes,s=e.pendingLanes&-62914561;0<s;){var f=31-dt(s),m=1<<f,y=n[f];y===-1?((m&a)===0||(m&l)!==0)&&(n[f]=N0(m,t)):y<=t&&(e.expiredLanes|=m),s&=~m}if(t=Ae,a=ie,a=fi(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(ye===2||ye===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&Qs(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||an(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&Qs(l),Js(a)){case 2:case 8:a=Hc;break;case 32:a=ui;break;case 268435456:a=Lc;break;default:a=ui}return l=$d.bind(null,e),a=Zs(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&Qs(l),e.callbackPriority=2,e.callbackNode=null,2}function $d(e,t){if(Je!==0&&Je!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(ls()&&e.callbackNode!==a)return null;var l=ie;return l=fi(e,e===Ae?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Cd(e,l,t),Fd(e,Bt()),e.callbackNode!=null&&e.callbackNode===a?$d.bind(null,e):null)}function Pd(e,t){if(ls())return null;Cd(e,t,!0)}function Pg(){uy(function(){(ge&6)!==0?Zs(qc,$g):Wd()})}function Uu(){return il===0&&(il=Gc()),il}function Id(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:yi(""+e)}function em(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function Ig(e,t,a,l,n){if(t==="submit"&&a&&a.stateNode===n){var s=Id((n[nt]||null).action),f=l.submitter;f&&(t=(t=f[nt]||null)?Id(t.formAction):f.getAttribute("formAction"),t!==null&&(s=t,f=null));var m=new vi("action","action",null,l,n);e.push({event:m,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(il!==0){var y=f?em(n,f):new FormData(n);eu(a,{pending:!0,data:y,method:n.method,action:s},null,y)}}else typeof s=="function"&&(m.preventDefault(),y=f?em(n,f):new FormData(n),eu(a,{pending:!0,data:y,method:n.method,action:s},s,y))},currentTarget:n}]})}}for(var ku=0;ku<xr.length;ku++){var _u=xr[ku],ey=_u.toLowerCase(),ty=_u[0].toUpperCase()+_u.slice(1);Rt(ey,"on"+ty)}Rt(Ro,"onAnimationEnd"),Rt(Co,"onAnimationIteration"),Rt(Uo,"onAnimationStart"),Rt("dblclick","onDoubleClick"),Rt("focusin","onFocus"),Rt("focusout","onBlur"),Rt(pg,"onTransitionRun"),Rt(xg,"onTransitionStart"),Rt(vg,"onTransitionCancel"),Rt(ko,"onTransitionEnd"),hl("onMouseEnter",["mouseout","mouseover"]),hl("onMouseLeave",["mouseout","mouseover"]),hl("onPointerEnter",["pointerout","pointerover"]),hl("onPointerLeave",["pointerout","pointerover"]),Xa("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Xa("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Xa("onBeforeInput",["compositionend","keypress","textInput","paste"]),Xa("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Xa("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Xa("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Yn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ay=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Yn));function tm(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],n=l.event;l=l.listeners;e:{var s=void 0;if(t)for(var f=l.length-1;0<=f;f--){var m=l[f],y=m.instance,N=m.currentTarget;if(m=m.listener,y!==s&&n.isPropagationStopped())break e;s=m,n.currentTarget=N;try{s(n)}catch(O){Vi(O)}n.currentTarget=null,s=y}else for(f=0;f<l.length;f++){if(m=l[f],y=m.instance,N=m.currentTarget,m=m.listener,y!==s&&n.isPropagationStopped())break e;s=m,n.currentTarget=N;try{s(n)}catch(O){Vi(O)}n.currentTarget=null,s=y}}}}function le(e,t){var a=t[Ws];a===void 0&&(a=t[Ws]=new Set);var l=e+"__bubble";a.has(l)||(am(t,e,2,!1),a.add(l))}function Bu(e,t,a){var l=0;t&&(l|=4),am(a,e,l,t)}var ss="_reactListening"+Math.random().toString(36).slice(2);function qu(e){if(!e[ss]){e[ss]=!0,Kc.forEach(function(a){a!=="selectionchange"&&(ay.has(a)||Bu(a,!1,e),Bu(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ss]||(t[ss]=!0,Bu("selectionchange",!1,t))}}function am(e,t,a,l){switch(Tm(t)){case 2:var n=My;break;case 8:n=Oy;break;default:n=Pu}a=n.bind(null,t,a,e),n=void 0,!sr||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),l?n!==void 0?e.addEventListener(t,a,{capture:!0,passive:n}):e.addEventListener(t,a,!0):n!==void 0?e.addEventListener(t,a,{passive:n}):e.addEventListener(t,a,!1)}function Hu(e,t,a,l,n){var s=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var m=l.stateNode.containerInfo;if(m===n)break;if(f===4)for(f=l.return;f!==null;){var y=f.tag;if((y===3||y===4)&&f.stateNode.containerInfo===n)return;f=f.return}for(;m!==null;){if(f=fl(m),f===null)return;if(y=f.tag,y===5||y===6||y===26||y===27){l=s=f;continue e}m=m.parentNode}}l=l.return}ro(function(){var N=s,O=nr(a),R=[];e:{var T=_o.get(e);if(T!==void 0){var E=vi,W=e;switch(e){case"keypress":if(pi(a)===0)break e;case"keydown":case"keyup":E=F0;break;case"focusin":W="focus",E=or;break;case"focusout":W="blur",E=or;break;case"beforeblur":case"afterblur":E=or;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":E=oo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":E=q0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":E=I0;break;case Ro:case Co:case Uo:E=G0;break;case ko:E=tg;break;case"scroll":case"scrollend":E=_0;break;case"wheel":E=lg;break;case"copy":case"cut":case"paste":E=X0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":E=mo;break;case"toggle":case"beforetoggle":E=ig}var V=(t&4)!==0,Se=!V&&(e==="scroll"||e==="scrollend"),x=V?T!==null?T+"Capture":null:T;V=[];for(var p=N,S;p!==null;){var D=p;if(S=D.stateNode,D=D.tag,D!==5&&D!==26&&D!==27||S===null||x===null||(D=rn(p,x),D!=null&&V.push(Xn(p,D,S))),Se)break;p=p.return}0<V.length&&(T=new E(T,W,null,a,O),R.push({event:T,listeners:V}))}}if((t&7)===0){e:{if(T=e==="mouseover"||e==="pointerover",E=e==="mouseout"||e==="pointerout",T&&a!==lr&&(W=a.relatedTarget||a.fromElement)&&(fl(W)||W[ol]))break e;if((E||T)&&(T=O.window===O?O:(T=O.ownerDocument)?T.defaultView||T.parentWindow:window,E?(W=a.relatedTarget||a.toElement,E=N,W=W?fl(W):null,W!==null&&(Se=h(W),V=W.tag,W!==Se||V!==5&&V!==27&&V!==6)&&(W=null)):(E=null,W=N),E!==W)){if(V=oo,D="onMouseLeave",x="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(V=mo,D="onPointerLeave",x="onPointerEnter",p="pointer"),Se=E==null?T:sn(E),S=W==null?T:sn(W),T=new V(D,p+"leave",E,a,O),T.target=Se,T.relatedTarget=S,D=null,fl(O)===N&&(V=new V(x,p+"enter",W,a,O),V.target=S,V.relatedTarget=Se,D=V),Se=D,E&&W)t:{for(V=E,x=W,p=0,S=V;S;S=Ql(S))p++;for(S=0,D=x;D;D=Ql(D))S++;for(;0<p-S;)V=Ql(V),p--;for(;0<S-p;)x=Ql(x),S--;for(;p--;){if(V===x||x!==null&&V===x.alternate)break t;V=Ql(V),x=Ql(x)}V=null}else V=null;E!==null&&lm(R,T,E,V,!1),W!==null&&Se!==null&&lm(R,Se,W,V,!0)}}e:{if(T=N?sn(N):window,E=T.nodeName&&T.nodeName.toLowerCase(),E==="select"||E==="input"&&T.type==="file")var H=So;else if(xo(T))if(jo)H=gg;else{H=mg;var te=dg}else E=T.nodeName,!E||E.toLowerCase()!=="input"||T.type!=="checkbox"&&T.type!=="radio"?N&&ar(N.elementType)&&(H=So):H=hg;if(H&&(H=H(e,N))){vo(R,H,a,O);break e}te&&te(e,T,N),e==="focusout"&&N&&T.type==="number"&&N.memoizedProps.value!=null&&tr(T,"number",T.value)}switch(te=N?sn(N):window,e){case"focusin":(xo(te)||te.contentEditable==="true")&&(Sl=te,yr=N,gn=null);break;case"focusout":gn=yr=Sl=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,Oo(R,a,O);break;case"selectionchange":if(bg)break;case"keydown":case"keyup":Oo(R,a,O)}var Q;if(dr)e:{switch(e){case"compositionstart":var K="onCompositionStart";break e;case"compositionend":K="onCompositionEnd";break e;case"compositionupdate":K="onCompositionUpdate";break e}K=void 0}else vl?bo(e,a)&&(K="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(K="onCompositionStart");K&&(ho&&a.locale!=="ko"&&(vl||K!=="onCompositionStart"?K==="onCompositionEnd"&&vl&&(Q=uo()):(ga=O,rr="value"in ga?ga.value:ga.textContent,vl=!0)),te=rs(N,K),0<te.length&&(K=new fo(K,e,null,a,O),R.push({event:K,listeners:te}),Q?K.data=Q:(Q=po(a),Q!==null&&(K.data=Q)))),(Q=rg?ug(e,a):cg(e,a))&&(K=rs(N,"onBeforeInput"),0<K.length&&(te=new fo("onBeforeInput","beforeinput",null,a,O),R.push({event:te,listeners:K}),te.data=Q)),Ig(R,e,N,a,O)}tm(R,t)})}function Xn(e,t,a){return{instance:e,listener:t,currentTarget:a}}function rs(e,t){for(var a=t+"Capture",l=[];e!==null;){var n=e,s=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||s===null||(n=rn(e,a),n!=null&&l.unshift(Xn(e,n,s)),n=rn(e,t),n!=null&&l.push(Xn(e,n,s))),e.tag===3)return l;e=e.return}return[]}function Ql(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function lm(e,t,a,l,n){for(var s=t._reactName,f=[];a!==null&&a!==l;){var m=a,y=m.alternate,N=m.stateNode;if(m=m.tag,y!==null&&y===l)break;m!==5&&m!==26&&m!==27||N===null||(y=N,n?(N=rn(a,s),N!=null&&f.unshift(Xn(a,N,y))):n||(N=rn(a,s),N!=null&&f.push(Xn(a,N,y)))),a=a.return}f.length!==0&&e.push({event:t,listeners:f})}var ly=/\r\n?/g,ny=/\u0000|\uFFFD/g;function nm(e){return(typeof e=="string"?e:""+e).replace(ly,`
`).replace(ny,"")}function im(e,t){return t=nm(t),nm(e)===t}function us(){}function ve(e,t,a,l,n,s){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||bl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&bl(e,""+l);break;case"className":mi(e,"class",l);break;case"tabIndex":mi(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":mi(e,a,l);break;case"style":io(e,l,s);break;case"data":if(t!=="object"){mi(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=yi(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof s=="function"&&(a==="formAction"?(t!=="input"&&ve(e,t,"name",n.name,n,null),ve(e,t,"formEncType",n.formEncType,n,null),ve(e,t,"formMethod",n.formMethod,n,null),ve(e,t,"formTarget",n.formTarget,n,null)):(ve(e,t,"encType",n.encType,n,null),ve(e,t,"method",n.method,n,null),ve(e,t,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=yi(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=us);break;case"onScroll":l!=null&&le("scroll",e);break;case"onScrollEnd":l!=null&&le("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(u(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=yi(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":le("beforetoggle",e),le("toggle",e),di(e,"popover",l);break;case"xlinkActuate":Jt(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Jt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Jt(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Jt(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Jt(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Jt(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Jt(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Jt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Jt(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":di(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=U0.get(a)||a,di(e,a,l))}}function Lu(e,t,a,l,n,s){switch(a){case"style":io(e,l,s);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(u(60));e.innerHTML=a}}break;case"children":typeof l=="string"?bl(e,l):(typeof l=="number"||typeof l=="bigint")&&bl(e,""+l);break;case"onScroll":l!=null&&le("scroll",e);break;case"onScrollEnd":l!=null&&le("scrollend",e);break;case"onClick":l!=null&&(e.onclick=us);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Jc.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),t=a.slice(2,n?a.length-7:void 0),s=e[nt]||null,s=s!=null?s[a]:null,typeof s=="function"&&e.removeEventListener(t,s,n),typeof l=="function")){typeof s!="function"&&s!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,n);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):di(e,a,l)}}}function We(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":le("error",e),le("load",e);var l=!1,n=!1,s;for(s in a)if(a.hasOwnProperty(s)){var f=a[s];if(f!=null)switch(s){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:ve(e,t,s,f,a,null)}}n&&ve(e,t,"srcSet",a.srcSet,a,null),l&&ve(e,t,"src",a.src,a,null);return;case"input":le("invalid",e);var m=s=f=n=null,y=null,N=null;for(l in a)if(a.hasOwnProperty(l)){var O=a[l];if(O!=null)switch(l){case"name":n=O;break;case"type":f=O;break;case"checked":y=O;break;case"defaultChecked":N=O;break;case"value":s=O;break;case"defaultValue":m=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(u(137,t));break;default:ve(e,t,l,O,a,null)}}to(e,s,m,y,N,f,n,!1),hi(e);return;case"select":le("invalid",e),l=f=s=null;for(n in a)if(a.hasOwnProperty(n)&&(m=a[n],m!=null))switch(n){case"value":s=m;break;case"defaultValue":f=m;break;case"multiple":l=m;default:ve(e,t,n,m,a,null)}t=s,a=f,e.multiple=!!l,t!=null?yl(e,!!l,t,!1):a!=null&&yl(e,!!l,a,!0);return;case"textarea":le("invalid",e),s=n=l=null;for(f in a)if(a.hasOwnProperty(f)&&(m=a[f],m!=null))switch(f){case"value":l=m;break;case"defaultValue":n=m;break;case"children":s=m;break;case"dangerouslySetInnerHTML":if(m!=null)throw Error(u(91));break;default:ve(e,t,f,m,a,null)}lo(e,l,n,s),hi(e);return;case"option":for(y in a)if(a.hasOwnProperty(y)&&(l=a[y],l!=null))switch(y){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:ve(e,t,y,l,a,null)}return;case"dialog":le("beforetoggle",e),le("toggle",e),le("cancel",e),le("close",e);break;case"iframe":case"object":le("load",e);break;case"video":case"audio":for(l=0;l<Yn.length;l++)le(Yn[l],e);break;case"image":le("error",e),le("load",e);break;case"details":le("toggle",e);break;case"embed":case"source":case"link":le("error",e),le("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(N in a)if(a.hasOwnProperty(N)&&(l=a[N],l!=null))switch(N){case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:ve(e,t,N,l,a,null)}return;default:if(ar(t)){for(O in a)a.hasOwnProperty(O)&&(l=a[O],l!==void 0&&Lu(e,t,O,l,a,void 0));return}}for(m in a)a.hasOwnProperty(m)&&(l=a[m],l!=null&&ve(e,t,m,l,a,null))}function iy(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,s=null,f=null,m=null,y=null,N=null,O=null;for(E in a){var R=a[E];if(a.hasOwnProperty(E)&&R!=null)switch(E){case"checked":break;case"value":break;case"defaultValue":y=R;default:l.hasOwnProperty(E)||ve(e,t,E,null,l,R)}}for(var T in l){var E=l[T];if(R=a[T],l.hasOwnProperty(T)&&(E!=null||R!=null))switch(T){case"type":s=E;break;case"name":n=E;break;case"checked":N=E;break;case"defaultChecked":O=E;break;case"value":f=E;break;case"defaultValue":m=E;break;case"children":case"dangerouslySetInnerHTML":if(E!=null)throw Error(u(137,t));break;default:E!==R&&ve(e,t,T,E,l,R)}}er(e,f,m,y,N,O,s,n);return;case"select":E=f=m=T=null;for(s in a)if(y=a[s],a.hasOwnProperty(s)&&y!=null)switch(s){case"value":break;case"multiple":E=y;default:l.hasOwnProperty(s)||ve(e,t,s,null,l,y)}for(n in l)if(s=l[n],y=a[n],l.hasOwnProperty(n)&&(s!=null||y!=null))switch(n){case"value":T=s;break;case"defaultValue":m=s;break;case"multiple":f=s;default:s!==y&&ve(e,t,n,s,l,y)}t=m,a=f,l=E,T!=null?yl(e,!!a,T,!1):!!l!=!!a&&(t!=null?yl(e,!!a,t,!0):yl(e,!!a,a?[]:"",!1));return;case"textarea":E=T=null;for(m in a)if(n=a[m],a.hasOwnProperty(m)&&n!=null&&!l.hasOwnProperty(m))switch(m){case"value":break;case"children":break;default:ve(e,t,m,null,l,n)}for(f in l)if(n=l[f],s=a[f],l.hasOwnProperty(f)&&(n!=null||s!=null))switch(f){case"value":T=n;break;case"defaultValue":E=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(u(91));break;default:n!==s&&ve(e,t,f,n,l,s)}ao(e,T,E);return;case"option":for(var W in a)if(T=a[W],a.hasOwnProperty(W)&&T!=null&&!l.hasOwnProperty(W))switch(W){case"selected":e.selected=!1;break;default:ve(e,t,W,null,l,T)}for(y in l)if(T=l[y],E=a[y],l.hasOwnProperty(y)&&T!==E&&(T!=null||E!=null))switch(y){case"selected":e.selected=T&&typeof T!="function"&&typeof T!="symbol";break;default:ve(e,t,y,T,l,E)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var V in a)T=a[V],a.hasOwnProperty(V)&&T!=null&&!l.hasOwnProperty(V)&&ve(e,t,V,null,l,T);for(N in l)if(T=l[N],E=a[N],l.hasOwnProperty(N)&&T!==E&&(T!=null||E!=null))switch(N){case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(u(137,t));break;default:ve(e,t,N,T,l,E)}return;default:if(ar(t)){for(var Se in a)T=a[Se],a.hasOwnProperty(Se)&&T!==void 0&&!l.hasOwnProperty(Se)&&Lu(e,t,Se,void 0,l,T);for(O in l)T=l[O],E=a[O],!l.hasOwnProperty(O)||T===E||T===void 0&&E===void 0||Lu(e,t,O,T,l,E);return}}for(var x in a)T=a[x],a.hasOwnProperty(x)&&T!=null&&!l.hasOwnProperty(x)&&ve(e,t,x,null,l,T);for(R in l)T=l[R],E=a[R],!l.hasOwnProperty(R)||T===E||T==null&&E==null||ve(e,t,R,T,l,E)}var Gu=null,Yu=null;function cs(e){return e.nodeType===9?e:e.ownerDocument}function sm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function rm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Xu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Zu=null;function sy(){var e=window.event;return e&&e.type==="popstate"?e===Zu?!1:(Zu=e,!0):(Zu=null,!1)}var um=typeof setTimeout=="function"?setTimeout:void 0,ry=typeof clearTimeout=="function"?clearTimeout:void 0,cm=typeof Promise=="function"?Promise:void 0,uy=typeof queueMicrotask=="function"?queueMicrotask:typeof cm<"u"?function(e){return cm.resolve(null).then(e).catch(cy)}:um;function cy(e){setTimeout(function(){throw e})}function Da(e){return e==="head"}function om(e,t){var a=t,l=0,n=0;do{var s=a.nextSibling;if(e.removeChild(a),s&&s.nodeType===8)if(a=s.data,a==="/$"){if(0<l&&8>l){a=l;var f=e.ownerDocument;if(a&1&&Zn(f.documentElement),a&2&&Zn(f.body),a&4)for(a=f.head,Zn(a),f=a.firstChild;f;){var m=f.nextSibling,y=f.nodeName;f[nn]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&f.rel.toLowerCase()==="stylesheet"||a.removeChild(f),f=m}}if(n===0){e.removeChild(s),Pn(t);return}n--}else a==="$"||a==="$?"||a==="$!"?n++:l=a.charCodeAt(0)-48;else l=0;a=s}while(a);Pn(t)}function Qu(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Qu(a),Fs(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function oy(e,t,a,l){for(;e.nodeType===1;){var n=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[nn])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(s=e.getAttribute("rel"),s==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(s!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(s=e.getAttribute("src"),(s!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&s&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var s=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===s)return e}else return e;if(e=Ut(e.nextSibling),e===null)break}return null}function fy(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Ut(e.nextSibling),e===null))return null;return e}function Vu(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function dy(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Ut(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Ku=null;function fm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function dm(e,t,a){switch(t=cs(a),e){case"html":if(e=t.documentElement,!e)throw Error(u(452));return e;case"head":if(e=t.head,!e)throw Error(u(453));return e;case"body":if(e=t.body,!e)throw Error(u(454));return e;default:throw Error(u(451))}}function Zn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Fs(e)}var zt=new Map,mm=new Set;function os(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ua=B.d;B.d={f:my,r:hy,D:gy,C:yy,L:by,m:py,X:vy,S:xy,M:Sy};function my(){var e=ua.f(),t=ts();return e||t}function hy(e){var t=dl(e);t!==null&&t.tag===5&&t.type==="form"?Cf(t):ua.r(e)}var Vl=typeof document>"u"?null:document;function hm(e,t,a){var l=Vl;if(l&&typeof t=="string"&&t){var n=St(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),mm.has(n)||(mm.add(n),e={rel:e,crossOrigin:a,href:t},l.querySelector(n)===null&&(t=l.createElement("link"),We(t,"link",e),Xe(t),l.head.appendChild(t)))}}function gy(e){ua.D(e),hm("dns-prefetch",e,null)}function yy(e,t){ua.C(e,t),hm("preconnect",e,t)}function by(e,t,a){ua.L(e,t,a);var l=Vl;if(l&&e&&t){var n='link[rel="preload"][as="'+St(t)+'"]';t==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+St(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+St(a.imageSizes)+'"]')):n+='[href="'+St(e)+'"]';var s=n;switch(t){case"style":s=Kl(e);break;case"script":s=Jl(e)}zt.has(s)||(e=v({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),zt.set(s,e),l.querySelector(n)!==null||t==="style"&&l.querySelector(Qn(s))||t==="script"&&l.querySelector(Vn(s))||(t=l.createElement("link"),We(t,"link",e),Xe(t),l.head.appendChild(t)))}}function py(e,t){ua.m(e,t);var a=Vl;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+St(l)+'"][href="'+St(e)+'"]',s=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":s=Jl(e)}if(!zt.has(s)&&(e=v({rel:"modulepreload",href:e},t),zt.set(s,e),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Vn(s)))return}l=a.createElement("link"),We(l,"link",e),Xe(l),a.head.appendChild(l)}}}function xy(e,t,a){ua.S(e,t,a);var l=Vl;if(l&&e){var n=ml(l).hoistableStyles,s=Kl(e);t=t||"default";var f=n.get(s);if(!f){var m={loading:0,preload:null};if(f=l.querySelector(Qn(s)))m.loading=5;else{e=v({rel:"stylesheet",href:e,"data-precedence":t},a),(a=zt.get(s))&&Ju(e,a);var y=f=l.createElement("link");Xe(y),We(y,"link",e),y._p=new Promise(function(N,O){y.onload=N,y.onerror=O}),y.addEventListener("load",function(){m.loading|=1}),y.addEventListener("error",function(){m.loading|=2}),m.loading|=4,fs(f,t,l)}f={type:"stylesheet",instance:f,count:1,state:m},n.set(s,f)}}}function vy(e,t){ua.X(e,t);var a=Vl;if(a&&e){var l=ml(a).hoistableScripts,n=Jl(e),s=l.get(n);s||(s=a.querySelector(Vn(n)),s||(e=v({src:e,async:!0},t),(t=zt.get(n))&&Wu(e,t),s=a.createElement("script"),Xe(s),We(s,"link",e),a.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},l.set(n,s))}}function Sy(e,t){ua.M(e,t);var a=Vl;if(a&&e){var l=ml(a).hoistableScripts,n=Jl(e),s=l.get(n);s||(s=a.querySelector(Vn(n)),s||(e=v({src:e,async:!0,type:"module"},t),(t=zt.get(n))&&Wu(e,t),s=a.createElement("script"),Xe(s),We(s,"link",e),a.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},l.set(n,s))}}function gm(e,t,a,l){var n=(n=Ot.current)?os(n):null;if(!n)throw Error(u(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Kl(a.href),a=ml(n).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Kl(a.href);var s=ml(n).hoistableStyles,f=s.get(e);if(f||(n=n.ownerDocument||n,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},s.set(e,f),(s=n.querySelector(Qn(e)))&&!s._p&&(f.instance=s,f.state.loading=5),zt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},zt.set(e,a),s||jy(n,e,a,f.state))),t&&l===null)throw Error(u(528,""));return f}if(t&&l!==null)throw Error(u(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Jl(a),a=ml(n).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(u(444,e))}}function Kl(e){return'href="'+St(e)+'"'}function Qn(e){return'link[rel="stylesheet"]['+e+"]"}function ym(e){return v({},e,{"data-precedence":e.precedence,precedence:null})}function jy(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),We(t,"link",a),Xe(t),e.head.appendChild(t))}function Jl(e){return'[src="'+St(e)+'"]'}function Vn(e){return"script[async]"+e}function bm(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+St(a.href)+'"]');if(l)return t.instance=l,Xe(l),l;var n=v({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Xe(l),We(l,"style",n),fs(l,a.precedence,e),t.instance=l;case"stylesheet":n=Kl(a.href);var s=e.querySelector(Qn(n));if(s)return t.state.loading|=4,t.instance=s,Xe(s),s;l=ym(a),(n=zt.get(n))&&Ju(l,n),s=(e.ownerDocument||e).createElement("link"),Xe(s);var f=s;return f._p=new Promise(function(m,y){f.onload=m,f.onerror=y}),We(s,"link",l),t.state.loading|=4,fs(s,a.precedence,e),t.instance=s;case"script":return s=Jl(a.src),(n=e.querySelector(Vn(s)))?(t.instance=n,Xe(n),n):(l=a,(n=zt.get(s))&&(l=v({},a),Wu(l,n)),e=e.ownerDocument||e,n=e.createElement("script"),Xe(n),We(n,"link",l),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(u(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,fs(l,a.precedence,e));return t.instance}function fs(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,s=n,f=0;f<l.length;f++){var m=l[f];if(m.dataset.precedence===t)s=m;else if(s!==n)break}s?s.parentNode.insertBefore(e,s.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Ju(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Wu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var ds=null;function pm(e,t,a){if(ds===null){var l=new Map,n=ds=new Map;n.set(a,l)}else n=ds,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),n=0;n<a.length;n++){var s=a[n];if(!(s[nn]||s[Pe]||e==="link"&&s.getAttribute("rel")==="stylesheet")&&s.namespaceURI!=="http://www.w3.org/2000/svg"){var f=s.getAttribute(t)||"";f=e+f;var m=l.get(f);m?m.push(s):l.set(f,[s])}}return l}function xm(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function Ny(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function vm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Kn=null;function wy(){}function Ay(e,t,a){if(Kn===null)throw Error(u(475));var l=Kn;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=Kl(a.href),s=e.querySelector(Qn(n));if(s){e=s._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=ms.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=s,Xe(s);return}s=e.ownerDocument||e,a=ym(a),(n=zt.get(n))&&Ju(a,n),s=s.createElement("link"),Xe(s);var f=s;f._p=new Promise(function(m,y){f.onload=m,f.onerror=y}),We(s,"link",a),t.instance=s}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=ms.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function Ty(){if(Kn===null)throw Error(u(475));var e=Kn;return e.stylesheets&&e.count===0&&Fu(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Fu(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function ms(){if(this.count--,this.count===0){if(this.stylesheets)Fu(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var hs=null;function Fu(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,hs=new Map,t.forEach(Ey,e),hs=null,ms.call(e))}function Ey(e,t){if(!(t.state.loading&4)){var a=hs.get(e);if(a)var l=a.get(null);else{a=new Map,hs.set(e,a);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),s=0;s<n.length;s++){var f=n[s];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(a.set(f.dataset.precedence,f),l=f)}l&&a.set(null,l)}n=t.instance,f=n.getAttribute("data-precedence"),s=a.get(f)||l,s===l&&a.set(null,n),a.set(f,n),this.count++,l=ms.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),s?s.parentNode.insertBefore(n,s.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var Jn={$$typeof:J,Provider:null,Consumer:null,_currentValue:C,_currentValue2:C,_threadCount:0};function zy(e,t,a,l,n,s,f,m){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Vs(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Vs(0),this.hiddenUpdates=Vs(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=s,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=m,this.incompleteTransitions=new Map}function Sm(e,t,a,l,n,s,f,m,y,N,O,R){return e=new zy(e,t,a,f,m,y,N,R),t=1,s===!0&&(t|=24),s=ht(3,null,null,t),e.current=s,s.stateNode=e,t=Dr(),t.refCount++,e.pooledCache=t,t.refCount++,s.memoizedState={element:l,isDehydrated:a,cache:t},kr(s),e}function jm(e){return e?(e=Al,e):Al}function Nm(e,t,a,l,n,s){n=jm(n),l.context===null?l.context=n:l.pendingContext=n,l=pa(t),l.payload={element:a},s=s===void 0?null:s,s!==null&&(l.callback=s),a=xa(e,l,t),a!==null&&(xt(a,e,t),wn(a,e,t))}function wm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function $u(e,t){wm(e,t),(e=e.alternate)&&wm(e,t)}function Am(e){if(e.tag===13){var t=wl(e,67108864);t!==null&&xt(t,e,67108864),$u(e,67108864)}}var gs=!0;function My(e,t,a,l){var n=A.T;A.T=null;var s=B.p;try{B.p=2,Pu(e,t,a,l)}finally{B.p=s,A.T=n}}function Oy(e,t,a,l){var n=A.T;A.T=null;var s=B.p;try{B.p=8,Pu(e,t,a,l)}finally{B.p=s,A.T=n}}function Pu(e,t,a,l){if(gs){var n=Iu(l);if(n===null)Hu(e,t,l,ys,a),Em(e,l);else if(Ry(n,e,t,a,l))l.stopPropagation();else if(Em(e,l),t&4&&-1<Dy.indexOf(e)){for(;n!==null;){var s=dl(n);if(s!==null)switch(s.tag){case 3:if(s=s.stateNode,s.current.memoizedState.isDehydrated){var f=Ya(s.pendingLanes);if(f!==0){var m=s;for(m.pendingLanes|=2,m.entangledLanes|=2;f;){var y=1<<31-dt(f);m.entanglements[1]|=y,f&=~y}Yt(s),(ge&6)===0&&(Ii=Bt()+500,Gn(0))}}break;case 13:m=wl(s,2),m!==null&&xt(m,s,2),ts(),$u(s,2)}if(s=Iu(l),s===null&&Hu(e,t,l,ys,a),s===n)break;n=s}n!==null&&l.stopPropagation()}else Hu(e,t,l,null,a)}}function Iu(e){return e=nr(e),ec(e)}var ys=null;function ec(e){if(ys=null,e=fl(e),e!==null){var t=h(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=g(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return ys=e,null}function Tm(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(y0()){case qc:return 2;case Hc:return 8;case ui:case b0:return 32;case Lc:return 268435456;default:return 32}default:return 32}}var tc=!1,Ra=null,Ca=null,Ua=null,Wn=new Map,Fn=new Map,ka=[],Dy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Em(e,t){switch(e){case"focusin":case"focusout":Ra=null;break;case"dragenter":case"dragleave":Ca=null;break;case"mouseover":case"mouseout":Ua=null;break;case"pointerover":case"pointerout":Wn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Fn.delete(t.pointerId)}}function $n(e,t,a,l,n,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:s,targetContainers:[n]},t!==null&&(t=dl(t),t!==null&&Am(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function Ry(e,t,a,l,n){switch(t){case"focusin":return Ra=$n(Ra,e,t,a,l,n),!0;case"dragenter":return Ca=$n(Ca,e,t,a,l,n),!0;case"mouseover":return Ua=$n(Ua,e,t,a,l,n),!0;case"pointerover":var s=n.pointerId;return Wn.set(s,$n(Wn.get(s)||null,e,t,a,l,n)),!0;case"gotpointercapture":return s=n.pointerId,Fn.set(s,$n(Fn.get(s)||null,e,t,a,l,n)),!0}return!1}function zm(e){var t=fl(e.target);if(t!==null){var a=h(t);if(a!==null){if(t=a.tag,t===13){if(t=g(a),t!==null){e.blockedOn=t,A0(e.priority,function(){if(a.tag===13){var l=pt();l=Ks(l);var n=wl(a,l);n!==null&&xt(n,a,l),$u(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function bs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=Iu(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);lr=l,a.target.dispatchEvent(l),lr=null}else return t=dl(a),t!==null&&Am(t),e.blockedOn=a,!1;t.shift()}return!0}function Mm(e,t,a){bs(e)&&a.delete(t)}function Cy(){tc=!1,Ra!==null&&bs(Ra)&&(Ra=null),Ca!==null&&bs(Ca)&&(Ca=null),Ua!==null&&bs(Ua)&&(Ua=null),Wn.forEach(Mm),Fn.forEach(Mm)}function ps(e,t){e.blockedOn===t&&(e.blockedOn=null,tc||(tc=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Cy)))}var xs=null;function Om(e){xs!==e&&(xs=e,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){xs===e&&(xs=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],n=e[t+2];if(typeof l!="function"){if(ec(l||a)===null)continue;break}var s=dl(a);s!==null&&(e.splice(t,3),t-=3,eu(s,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function Pn(e){function t(y){return ps(y,e)}Ra!==null&&ps(Ra,e),Ca!==null&&ps(Ca,e),Ua!==null&&ps(Ua,e),Wn.forEach(t),Fn.forEach(t);for(var a=0;a<ka.length;a++){var l=ka[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<ka.length&&(a=ka[0],a.blockedOn===null);)zm(a),a.blockedOn===null&&ka.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],s=a[l+1],f=n[nt]||null;if(typeof s=="function")f||Om(a);else if(f){var m=null;if(s&&s.hasAttribute("formAction")){if(n=s,f=s[nt]||null)m=f.formAction;else if(ec(n)!==null)continue}else m=f.action;typeof m=="function"?a[l+1]=m:(a.splice(l,3),l-=3),Om(a)}}}function ac(e){this._internalRoot=e}vs.prototype.render=ac.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(u(409));var a=t.current,l=pt();Nm(a,l,e,t,null,null)},vs.prototype.unmount=ac.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Nm(e.current,2,null,e,null,null),ts(),t[ol]=null}};function vs(e){this._internalRoot=e}vs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Qc();e={blockedOn:null,target:e,priority:t};for(var a=0;a<ka.length&&t!==0&&t<ka[a].priority;a++);ka.splice(a,0,e),a===0&&zm(e)}};var Dm=r.version;if(Dm!=="19.1.0")throw Error(u(527,Dm,"19.1.0"));B.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(u(188)):(e=Object.keys(e).join(","),Error(u(268,e)));return e=w(t),e=e!==null?j(e):null,e=e===null?null:e.stateNode,e};var Uy={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:A,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ss=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ss.isDisabled&&Ss.supportsFiber)try{tn=Ss.inject(Uy),ft=Ss}catch{}}return ei.createRoot=function(e,t){if(!d(e))throw Error(u(299));var a=!1,l="",n=Kf,s=Jf,f=Wf,m=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(s=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(m=t.unstable_transitionCallbacks)),t=Sm(e,1,!1,null,null,a,l,n,s,f,m,null),e[ol]=t.current,qu(e),new ac(t)},ei.hydrateRoot=function(e,t,a){if(!d(e))throw Error(u(299));var l=!1,n="",s=Kf,f=Jf,m=Wf,y=null,N=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(s=a.onUncaughtError),a.onCaughtError!==void 0&&(f=a.onCaughtError),a.onRecoverableError!==void 0&&(m=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(y=a.unstable_transitionCallbacks),a.formState!==void 0&&(N=a.formState)),t=Sm(e,1,!0,t,a??null,l,n,s,f,m,y,N),t.context=jm(null),a=t.current,l=pt(),l=Ks(l),n=pa(l),n.callback=null,xa(a,n,l),a=l,t.current.lanes=a,ln(t,a),Yt(t),e[ol]=t.current,qu(e),new vs(t)},ei.version="19.1.0",ei}var qm;function bb(){if(qm)return lc.exports;qm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(r){console.error(r)}}return i(),lc.exports=yb(),lc.exports}var pb=bb();const Oc="-",xb=i=>{const r=Sb(i),{conflictingClassGroups:c,conflictingClassGroupModifiers:u}=i;return{getClassGroupId:g=>{const b=g.split(Oc);return b[0]===""&&b.length!==1&&b.shift(),Nh(b,r)||vb(g)},getConflictingClassGroupIds:(g,b)=>{const w=c[g]||[];return b&&u[g]?[...w,...u[g]]:w}}},Nh=(i,r)=>{var g;if(i.length===0)return r.classGroupId;const c=i[0],u=r.nextPart.get(c),d=u?Nh(i.slice(1),u):void 0;if(d)return d;if(r.validators.length===0)return;const h=i.join(Oc);return(g=r.validators.find(({validator:b})=>b(h)))==null?void 0:g.classGroupId},Hm=/^\[(.+)\]$/,vb=i=>{if(Hm.test(i)){const r=Hm.exec(i)[1],c=r==null?void 0:r.substring(0,r.indexOf(":"));if(c)return"arbitrary.."+c}},Sb=i=>{const{theme:r,classGroups:c}=i,u={nextPart:new Map,validators:[]};for(const d in c)gc(c[d],u,d,r);return u},gc=(i,r,c,u)=>{i.forEach(d=>{if(typeof d=="string"){const h=d===""?r:Lm(r,d);h.classGroupId=c;return}if(typeof d=="function"){if(jb(d)){gc(d(u),r,c,u);return}r.validators.push({validator:d,classGroupId:c});return}Object.entries(d).forEach(([h,g])=>{gc(g,Lm(r,h),c,u)})})},Lm=(i,r)=>{let c=i;return r.split(Oc).forEach(u=>{c.nextPart.has(u)||c.nextPart.set(u,{nextPart:new Map,validators:[]}),c=c.nextPart.get(u)}),c},jb=i=>i.isThemeGetter,Nb=i=>{if(i<1)return{get:()=>{},set:()=>{}};let r=0,c=new Map,u=new Map;const d=(h,g)=>{c.set(h,g),r++,r>i&&(r=0,u=c,c=new Map)};return{get(h){let g=c.get(h);if(g!==void 0)return g;if((g=u.get(h))!==void 0)return d(h,g),g},set(h,g){c.has(h)?c.set(h,g):d(h,g)}}},yc="!",bc=":",wb=bc.length,Ab=i=>{const{prefix:r,experimentalParseClassName:c}=i;let u=d=>{const h=[];let g=0,b=0,w=0,j;for(let U=0;U<d.length;U++){let _=d[U];if(g===0&&b===0){if(_===bc){h.push(d.slice(w,U)),w=U+wb;continue}if(_==="/"){j=U;continue}}_==="["?g++:_==="]"?g--:_==="("?b++:_===")"&&b--}const v=h.length===0?d:d.substring(w),M=Tb(v),k=M!==v,Z=j&&j>w?j-w:void 0;return{modifiers:h,hasImportantModifier:k,baseClassName:M,maybePostfixModifierPosition:Z}};if(r){const d=r+bc,h=u;u=g=>g.startsWith(d)?h(g.substring(d.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:g,maybePostfixModifierPosition:void 0}}if(c){const d=u;u=h=>c({className:h,parseClassName:d})}return u},Tb=i=>i.endsWith(yc)?i.substring(0,i.length-1):i.startsWith(yc)?i.substring(1):i,Eb=i=>{const r=Object.fromEntries(i.orderSensitiveModifiers.map(u=>[u,!0]));return u=>{if(u.length<=1)return u;const d=[];let h=[];return u.forEach(g=>{g[0]==="["||r[g]?(d.push(...h.sort(),g),h=[]):h.push(g)}),d.push(...h.sort()),d}},zb=i=>({cache:Nb(i.cacheSize),parseClassName:Ab(i),sortModifiers:Eb(i),...xb(i)}),Mb=/\s+/,Ob=(i,r)=>{const{parseClassName:c,getClassGroupId:u,getConflictingClassGroupIds:d,sortModifiers:h}=r,g=[],b=i.trim().split(Mb);let w="";for(let j=b.length-1;j>=0;j-=1){const v=b[j],{isExternal:M,modifiers:k,hasImportantModifier:Z,baseClassName:U,maybePostfixModifierPosition:_}=c(v);if(M){w=v+(w.length>0?" "+w:w);continue}let q=!!_,$=u(q?U.substring(0,_):U);if(!$){if(!q){w=v+(w.length>0?" "+w:w);continue}if($=u(U),!$){w=v+(w.length>0?" "+w:w);continue}q=!1}const ne=h(k).join(":"),J=Z?ne+yc:ne,ue=J+$;if(g.includes(ue))continue;g.push(ue);const re=d($,q);for(let de=0;de<re.length;++de){const je=re[de];g.push(J+je)}w=v+(w.length>0?" "+w:w)}return w};function Db(){let i=0,r,c,u="";for(;i<arguments.length;)(r=arguments[i++])&&(c=wh(r))&&(u&&(u+=" "),u+=c);return u}const wh=i=>{if(typeof i=="string")return i;let r,c="";for(let u=0;u<i.length;u++)i[u]&&(r=wh(i[u]))&&(c&&(c+=" "),c+=r);return c};function Rb(i,...r){let c,u,d,h=g;function g(w){const j=r.reduce((v,M)=>M(v),i());return c=zb(j),u=c.cache.get,d=c.cache.set,h=b,b(w)}function b(w){const j=u(w);if(j)return j;const v=Ob(w,c);return d(w,v),v}return function(){return h(Db.apply(null,arguments))}}const Ge=i=>{const r=c=>c[i]||[];return r.isThemeGetter=!0,r},Ah=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Th=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Cb=/^\d+\/\d+$/,Ub=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,kb=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,_b=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Bb=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,qb=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Wl=i=>Cb.test(i),ee=i=>!!i&&!Number.isNaN(Number(i)),Ba=i=>!!i&&Number.isInteger(Number(i)),sc=i=>i.endsWith("%")&&ee(i.slice(0,-1)),ca=i=>Ub.test(i),Hb=()=>!0,Lb=i=>kb.test(i)&&!_b.test(i),Eh=()=>!1,Gb=i=>Bb.test(i),Yb=i=>qb.test(i),Xb=i=>!L(i)&&!G(i),Zb=i=>$l(i,Oh,Eh),L=i=>Ah.test(i),sl=i=>$l(i,Dh,Lb),rc=i=>$l(i,Wb,ee),Gm=i=>$l(i,zh,Eh),Qb=i=>$l(i,Mh,Yb),js=i=>$l(i,Rh,Gb),G=i=>Th.test(i),ti=i=>Pl(i,Dh),Vb=i=>Pl(i,Fb),Ym=i=>Pl(i,zh),Kb=i=>Pl(i,Oh),Jb=i=>Pl(i,Mh),Ns=i=>Pl(i,Rh,!0),$l=(i,r,c)=>{const u=Ah.exec(i);return u?u[1]?r(u[1]):c(u[2]):!1},Pl=(i,r,c=!1)=>{const u=Th.exec(i);return u?u[1]?r(u[1]):c:!1},zh=i=>i==="position"||i==="percentage",Mh=i=>i==="image"||i==="url",Oh=i=>i==="length"||i==="size"||i==="bg-size",Dh=i=>i==="length",Wb=i=>i==="number",Fb=i=>i==="family-name",Rh=i=>i==="shadow",$b=()=>{const i=Ge("color"),r=Ge("font"),c=Ge("text"),u=Ge("font-weight"),d=Ge("tracking"),h=Ge("leading"),g=Ge("breakpoint"),b=Ge("container"),w=Ge("spacing"),j=Ge("radius"),v=Ge("shadow"),M=Ge("inset-shadow"),k=Ge("text-shadow"),Z=Ge("drop-shadow"),U=Ge("blur"),_=Ge("perspective"),q=Ge("aspect"),$=Ge("ease"),ne=Ge("animate"),J=()=>["auto","avoid","all","avoid-page","page","left","right","column"],ue=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],re=()=>[...ue(),G,L],de=()=>["auto","hidden","clip","visible","scroll"],je=()=>["auto","contain","none"],Y=()=>[G,L,w],Ee=()=>[Wl,"full","auto",...Y()],Mt=()=>[Ba,"none","subgrid",G,L],he=()=>["auto",{span:["full",Ba,G,L]},Ba,G,L],qe=()=>[Ba,"auto",G,L],Kt=()=>["auto","min","max","fr",G,L],lt=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],X=()=>["start","end","center","stretch","center-safe","end-safe"],A=()=>["auto",...Y()],B=()=>[Wl,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...Y()],C=()=>[i,G,L],be=()=>[...ue(),Ym,Gm,{position:[G,L]}],pe=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Ye=()=>["auto","cover","contain",Kb,Zb,{size:[G,L]}],Ne=()=>[sc,ti,sl],F=()=>["","none","full",j,G,L],ce=()=>["",ee,ti,sl],tt=()=>["solid","dashed","dotted","double"],Ot=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ze=()=>[ee,sc,Ym,Gm],Ga=()=>["","none",U,G,L],Dt=()=>["none",ee,G,L],oa=()=>["none",ee,G,L],fa=()=>[ee,G,L],da=()=>[Wl,"full",...Y()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[ca],breakpoint:[ca],color:[Hb],container:[ca],"drop-shadow":[ca],ease:["in","out","in-out"],font:[Xb],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[ca],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[ca],shadow:[ca],spacing:["px",ee],text:[ca],"text-shadow":[ca],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Wl,L,G,q]}],container:["container"],columns:[{columns:[ee,L,G,b]}],"break-after":[{"break-after":J()}],"break-before":[{"break-before":J()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:re()}],overflow:[{overflow:de()}],"overflow-x":[{"overflow-x":de()}],"overflow-y":[{"overflow-y":de()}],overscroll:[{overscroll:je()}],"overscroll-x":[{"overscroll-x":je()}],"overscroll-y":[{"overscroll-y":je()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Ee()}],"inset-x":[{"inset-x":Ee()}],"inset-y":[{"inset-y":Ee()}],start:[{start:Ee()}],end:[{end:Ee()}],top:[{top:Ee()}],right:[{right:Ee()}],bottom:[{bottom:Ee()}],left:[{left:Ee()}],visibility:["visible","invisible","collapse"],z:[{z:[Ba,"auto",G,L]}],basis:[{basis:[Wl,"full","auto",b,...Y()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ee,Wl,"auto","initial","none",L]}],grow:[{grow:["",ee,G,L]}],shrink:[{shrink:["",ee,G,L]}],order:[{order:[Ba,"first","last","none",G,L]}],"grid-cols":[{"grid-cols":Mt()}],"col-start-end":[{col:he()}],"col-start":[{"col-start":qe()}],"col-end":[{"col-end":qe()}],"grid-rows":[{"grid-rows":Mt()}],"row-start-end":[{row:he()}],"row-start":[{"row-start":qe()}],"row-end":[{"row-end":qe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Kt()}],"auto-rows":[{"auto-rows":Kt()}],gap:[{gap:Y()}],"gap-x":[{"gap-x":Y()}],"gap-y":[{"gap-y":Y()}],"justify-content":[{justify:[...lt(),"normal"]}],"justify-items":[{"justify-items":[...X(),"normal"]}],"justify-self":[{"justify-self":["auto",...X()]}],"align-content":[{content:["normal",...lt()]}],"align-items":[{items:[...X(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...X(),{baseline:["","last"]}]}],"place-content":[{"place-content":lt()}],"place-items":[{"place-items":[...X(),"baseline"]}],"place-self":[{"place-self":["auto",...X()]}],p:[{p:Y()}],px:[{px:Y()}],py:[{py:Y()}],ps:[{ps:Y()}],pe:[{pe:Y()}],pt:[{pt:Y()}],pr:[{pr:Y()}],pb:[{pb:Y()}],pl:[{pl:Y()}],m:[{m:A()}],mx:[{mx:A()}],my:[{my:A()}],ms:[{ms:A()}],me:[{me:A()}],mt:[{mt:A()}],mr:[{mr:A()}],mb:[{mb:A()}],ml:[{ml:A()}],"space-x":[{"space-x":Y()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":Y()}],"space-y-reverse":["space-y-reverse"],size:[{size:B()}],w:[{w:[b,"screen",...B()]}],"min-w":[{"min-w":[b,"screen","none",...B()]}],"max-w":[{"max-w":[b,"screen","none","prose",{screen:[g]},...B()]}],h:[{h:["screen","lh",...B()]}],"min-h":[{"min-h":["screen","lh","none",...B()]}],"max-h":[{"max-h":["screen","lh",...B()]}],"font-size":[{text:["base",c,ti,sl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[u,G,rc]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",sc,L]}],"font-family":[{font:[Vb,L,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[d,G,L]}],"line-clamp":[{"line-clamp":[ee,"none",G,rc]}],leading:[{leading:[h,...Y()]}],"list-image":[{"list-image":["none",G,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",G,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:C()}],"text-color":[{text:C()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tt(),"wavy"]}],"text-decoration-thickness":[{decoration:[ee,"from-font","auto",G,sl]}],"text-decoration-color":[{decoration:C()}],"underline-offset":[{"underline-offset":[ee,"auto",G,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Y()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",G,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",G,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:be()}],"bg-repeat":[{bg:pe()}],"bg-size":[{bg:Ye()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Ba,G,L],radial:["",G,L],conic:[Ba,G,L]},Jb,Qb]}],"bg-color":[{bg:C()}],"gradient-from-pos":[{from:Ne()}],"gradient-via-pos":[{via:Ne()}],"gradient-to-pos":[{to:Ne()}],"gradient-from":[{from:C()}],"gradient-via":[{via:C()}],"gradient-to":[{to:C()}],rounded:[{rounded:F()}],"rounded-s":[{"rounded-s":F()}],"rounded-e":[{"rounded-e":F()}],"rounded-t":[{"rounded-t":F()}],"rounded-r":[{"rounded-r":F()}],"rounded-b":[{"rounded-b":F()}],"rounded-l":[{"rounded-l":F()}],"rounded-ss":[{"rounded-ss":F()}],"rounded-se":[{"rounded-se":F()}],"rounded-ee":[{"rounded-ee":F()}],"rounded-es":[{"rounded-es":F()}],"rounded-tl":[{"rounded-tl":F()}],"rounded-tr":[{"rounded-tr":F()}],"rounded-br":[{"rounded-br":F()}],"rounded-bl":[{"rounded-bl":F()}],"border-w":[{border:ce()}],"border-w-x":[{"border-x":ce()}],"border-w-y":[{"border-y":ce()}],"border-w-s":[{"border-s":ce()}],"border-w-e":[{"border-e":ce()}],"border-w-t":[{"border-t":ce()}],"border-w-r":[{"border-r":ce()}],"border-w-b":[{"border-b":ce()}],"border-w-l":[{"border-l":ce()}],"divide-x":[{"divide-x":ce()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ce()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tt(),"hidden","none"]}],"divide-style":[{divide:[...tt(),"hidden","none"]}],"border-color":[{border:C()}],"border-color-x":[{"border-x":C()}],"border-color-y":[{"border-y":C()}],"border-color-s":[{"border-s":C()}],"border-color-e":[{"border-e":C()}],"border-color-t":[{"border-t":C()}],"border-color-r":[{"border-r":C()}],"border-color-b":[{"border-b":C()}],"border-color-l":[{"border-l":C()}],"divide-color":[{divide:C()}],"outline-style":[{outline:[...tt(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ee,G,L]}],"outline-w":[{outline:["",ee,ti,sl]}],"outline-color":[{outline:C()}],shadow:[{shadow:["","none",v,Ns,js]}],"shadow-color":[{shadow:C()}],"inset-shadow":[{"inset-shadow":["none",M,Ns,js]}],"inset-shadow-color":[{"inset-shadow":C()}],"ring-w":[{ring:ce()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:C()}],"ring-offset-w":[{"ring-offset":[ee,sl]}],"ring-offset-color":[{"ring-offset":C()}],"inset-ring-w":[{"inset-ring":ce()}],"inset-ring-color":[{"inset-ring":C()}],"text-shadow":[{"text-shadow":["none",k,Ns,js]}],"text-shadow-color":[{"text-shadow":C()}],opacity:[{opacity:[ee,G,L]}],"mix-blend":[{"mix-blend":[...Ot(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Ot()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ee]}],"mask-image-linear-from-pos":[{"mask-linear-from":ze()}],"mask-image-linear-to-pos":[{"mask-linear-to":ze()}],"mask-image-linear-from-color":[{"mask-linear-from":C()}],"mask-image-linear-to-color":[{"mask-linear-to":C()}],"mask-image-t-from-pos":[{"mask-t-from":ze()}],"mask-image-t-to-pos":[{"mask-t-to":ze()}],"mask-image-t-from-color":[{"mask-t-from":C()}],"mask-image-t-to-color":[{"mask-t-to":C()}],"mask-image-r-from-pos":[{"mask-r-from":ze()}],"mask-image-r-to-pos":[{"mask-r-to":ze()}],"mask-image-r-from-color":[{"mask-r-from":C()}],"mask-image-r-to-color":[{"mask-r-to":C()}],"mask-image-b-from-pos":[{"mask-b-from":ze()}],"mask-image-b-to-pos":[{"mask-b-to":ze()}],"mask-image-b-from-color":[{"mask-b-from":C()}],"mask-image-b-to-color":[{"mask-b-to":C()}],"mask-image-l-from-pos":[{"mask-l-from":ze()}],"mask-image-l-to-pos":[{"mask-l-to":ze()}],"mask-image-l-from-color":[{"mask-l-from":C()}],"mask-image-l-to-color":[{"mask-l-to":C()}],"mask-image-x-from-pos":[{"mask-x-from":ze()}],"mask-image-x-to-pos":[{"mask-x-to":ze()}],"mask-image-x-from-color":[{"mask-x-from":C()}],"mask-image-x-to-color":[{"mask-x-to":C()}],"mask-image-y-from-pos":[{"mask-y-from":ze()}],"mask-image-y-to-pos":[{"mask-y-to":ze()}],"mask-image-y-from-color":[{"mask-y-from":C()}],"mask-image-y-to-color":[{"mask-y-to":C()}],"mask-image-radial":[{"mask-radial":[G,L]}],"mask-image-radial-from-pos":[{"mask-radial-from":ze()}],"mask-image-radial-to-pos":[{"mask-radial-to":ze()}],"mask-image-radial-from-color":[{"mask-radial-from":C()}],"mask-image-radial-to-color":[{"mask-radial-to":C()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":ue()}],"mask-image-conic-pos":[{"mask-conic":[ee]}],"mask-image-conic-from-pos":[{"mask-conic-from":ze()}],"mask-image-conic-to-pos":[{"mask-conic-to":ze()}],"mask-image-conic-from-color":[{"mask-conic-from":C()}],"mask-image-conic-to-color":[{"mask-conic-to":C()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:be()}],"mask-repeat":[{mask:pe()}],"mask-size":[{mask:Ye()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",G,L]}],filter:[{filter:["","none",G,L]}],blur:[{blur:Ga()}],brightness:[{brightness:[ee,G,L]}],contrast:[{contrast:[ee,G,L]}],"drop-shadow":[{"drop-shadow":["","none",Z,Ns,js]}],"drop-shadow-color":[{"drop-shadow":C()}],grayscale:[{grayscale:["",ee,G,L]}],"hue-rotate":[{"hue-rotate":[ee,G,L]}],invert:[{invert:["",ee,G,L]}],saturate:[{saturate:[ee,G,L]}],sepia:[{sepia:["",ee,G,L]}],"backdrop-filter":[{"backdrop-filter":["","none",G,L]}],"backdrop-blur":[{"backdrop-blur":Ga()}],"backdrop-brightness":[{"backdrop-brightness":[ee,G,L]}],"backdrop-contrast":[{"backdrop-contrast":[ee,G,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ee,G,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ee,G,L]}],"backdrop-invert":[{"backdrop-invert":["",ee,G,L]}],"backdrop-opacity":[{"backdrop-opacity":[ee,G,L]}],"backdrop-saturate":[{"backdrop-saturate":[ee,G,L]}],"backdrop-sepia":[{"backdrop-sepia":["",ee,G,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":Y()}],"border-spacing-x":[{"border-spacing-x":Y()}],"border-spacing-y":[{"border-spacing-y":Y()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",G,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ee,"initial",G,L]}],ease:[{ease:["linear","initial",$,G,L]}],delay:[{delay:[ee,G,L]}],animate:[{animate:["none",ne,G,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[_,G,L]}],"perspective-origin":[{"perspective-origin":re()}],rotate:[{rotate:Dt()}],"rotate-x":[{"rotate-x":Dt()}],"rotate-y":[{"rotate-y":Dt()}],"rotate-z":[{"rotate-z":Dt()}],scale:[{scale:oa()}],"scale-x":[{"scale-x":oa()}],"scale-y":[{"scale-y":oa()}],"scale-z":[{"scale-z":oa()}],"scale-3d":["scale-3d"],skew:[{skew:fa()}],"skew-x":[{"skew-x":fa()}],"skew-y":[{"skew-y":fa()}],transform:[{transform:[G,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:re()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:da()}],"translate-x":[{"translate-x":da()}],"translate-y":[{"translate-y":da()}],"translate-z":[{"translate-z":da()}],"translate-none":["translate-none"],accent:[{accent:C()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:C()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",G,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Y()}],"scroll-mx":[{"scroll-mx":Y()}],"scroll-my":[{"scroll-my":Y()}],"scroll-ms":[{"scroll-ms":Y()}],"scroll-me":[{"scroll-me":Y()}],"scroll-mt":[{"scroll-mt":Y()}],"scroll-mr":[{"scroll-mr":Y()}],"scroll-mb":[{"scroll-mb":Y()}],"scroll-ml":[{"scroll-ml":Y()}],"scroll-p":[{"scroll-p":Y()}],"scroll-px":[{"scroll-px":Y()}],"scroll-py":[{"scroll-py":Y()}],"scroll-ps":[{"scroll-ps":Y()}],"scroll-pe":[{"scroll-pe":Y()}],"scroll-pt":[{"scroll-pt":Y()}],"scroll-pr":[{"scroll-pr":Y()}],"scroll-pb":[{"scroll-pb":Y()}],"scroll-pl":[{"scroll-pl":Y()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",G,L]}],fill:[{fill:["none",...C()]}],"stroke-w":[{stroke:[ee,ti,sl,rc]}],stroke:[{stroke:["none",...C()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Pb=Rb($b);function Fe(...i){return Pb(cb(i))}const Dc={button:{primary:"bg-primary-600 hover:bg-primary-700 text-white shadow-soft hover:shadow-medium",secondary:"bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 shadow-soft hover:shadow-medium",success:"bg-success-600 hover:bg-success-700 text-white shadow-soft hover:shadow-medium",warning:"bg-warning-600 hover:bg-warning-700 text-white shadow-soft hover:shadow-medium",danger:"bg-danger-600 hover:bg-danger-700 text-white shadow-soft hover:shadow-medium",ghost:"hover:bg-gray-100 text-gray-700",outline:"border-2 border-primary-600 text-primary-600 hover:bg-primary-50"},card:{default:"bg-white border border-gray-200 shadow-soft hover:shadow-medium",elevated:"bg-white border border-gray-200 shadow-medium hover:shadow-large",interactive:"bg-white border border-gray-200 shadow-soft hover:shadow-medium hover:border-primary-300 cursor-pointer",gradient:"bg-gradient-to-br from-white to-gray-50 border border-gray-200 shadow-soft"},badge:{primary:"bg-primary-100 text-primary-800 border border-primary-200",success:"bg-success-100 text-success-800 border border-success-200",warning:"bg-warning-100 text-warning-800 border border-warning-200",danger:"bg-danger-100 text-danger-800 border border-danger-200",gray:"bg-gray-100 text-gray-800 border border-gray-200"}},Ib={button:{xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base",xl:"px-8 py-4 text-lg"}},Rc={default:"transition-all duration-200 ease-in-out"},fe=({children:i,className:r,...c})=>o.jsx("div",{className:Fe("min-h-screen bg-gray-50 dark:bg-gray-900",r),...c,children:o.jsx("div",{className:"max-w-6xl mx-auto p-6",children:i})}),ep=({title:i,subtitle:r,actions:c,className:u,...d})=>o.jsx($e.div,{className:Fe("mb-8",u),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.3},...d,children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{children:[o.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-1",children:i}),r&&o.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r})]}),c&&o.jsx("div",{className:"flex items-center gap-2",children:c})]})}),tp=({title:i,icon:r,children:c,className:u,...d})=>o.jsxs($e.div,{className:Fe("mb-6",u),initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},...d,children:[i&&o.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[r&&o.jsx("div",{className:"w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center",children:o.jsx(r,{size:14,className:"text-blue-600 dark:text-blue-400"})}),o.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:i})]}),c]}),ap=({cols:i=4,gap:r=4,children:c,className:u,...d})=>{const h={1:"grid-cols-1",2:"grid-cols-1 sm:grid-cols-2",3:"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 sm:grid-cols-2 lg:grid-cols-4",5:"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5",6:"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6"},g={2:"gap-2",3:"gap-3",4:"gap-4",6:"gap-6",8:"gap-8"};return o.jsx("div",{className:Fe("grid",h[i]||h[4],g[r]||g[4],u),...d,children:c})},lp=({children:i,className:r,hover:c=!1,...u})=>o.jsx("div",{className:Fe("bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4",c&&"hover:shadow-md transition-shadow duration-200 cursor-pointer",r),...u,children:i});fe.Header=ep;fe.Section=tp;fe.Grid=ap;fe.Card=lp;const np=({title:i,value:r,trend:c,trendValue:u,icon:d,color:h="blue",loading:g=!1,className:b,...w})=>{const j={blue:{icon:"text-blue-600 dark:text-blue-400",iconBg:"bg-blue-100 dark:bg-blue-900/20",value:"text-blue-900 dark:text-blue-100"},green:{icon:"text-green-600 dark:text-green-400",iconBg:"bg-green-100 dark:bg-green-900/20",value:"text-green-900 dark:text-green-100"},purple:{icon:"text-purple-600 dark:text-purple-400",iconBg:"bg-purple-100 dark:bg-purple-900/20",value:"text-purple-900 dark:text-purple-100"},orange:{icon:"text-orange-600 dark:text-orange-400",iconBg:"bg-orange-100 dark:bg-orange-900/20",value:"text-orange-900 dark:text-orange-100"}},v=j[h]||j.blue,M=c==="up"?dc:_y;return g?o.jsx("div",{className:Fe("bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4",b),children:o.jsxs("div",{className:"animate-pulse",children:[o.jsxs("div",{className:"flex items-center justify-between mb-3",children:[o.jsx("div",{className:"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg"}),o.jsx("div",{className:"w-12 h-4 bg-gray-200 dark:bg-gray-700 rounded"})]}),o.jsx("div",{className:"w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded mb-1"}),o.jsx("div",{className:"w-16 h-3 bg-gray-200 dark:bg-gray-700 rounded"})]})}):o.jsxs($e.div,{className:Fe("bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow duration-200",b),initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},...w,children:[o.jsxs("div",{className:"flex items-center justify-between mb-3",children:[d&&o.jsx("div",{className:Fe("w-8 h-8 rounded-lg flex items-center justify-center",v.iconBg),children:o.jsx(d,{size:16,className:v.icon})}),c&&u&&o.jsxs("div",{className:Fe("flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",c==="up"?"bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400":"bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400"),children:[o.jsx(M,{size:10}),Math.abs(u),"%"]})]}),o.jsx("div",{className:Fe("text-2xl font-bold mb-1",v.value),children:r}),o.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:i})]})},Ch=6e4,Uh=36e5,ws=43200,Xm=1440,Zm=Symbol.for("constructDateFrom");function ks(i,r){return typeof i=="function"?i(r):i&&typeof i=="object"&&Zm in i?i[Zm](r):i instanceof Date?new i.constructor(r):new Date(r)}function kt(i,r){return ks(i,i)}let ip={};function sp(){return ip}function Qm(i){const r=kt(i),c=new Date(Date.UTC(r.getFullYear(),r.getMonth(),r.getDate(),r.getHours(),r.getMinutes(),r.getSeconds(),r.getMilliseconds()));return c.setUTCFullYear(r.getFullYear()),+i-+c}function Cc(i,...r){const c=ks.bind(null,i||r.find(u=>typeof u=="object"));return r.map(c)}function Ts(i,r){const c=+kt(i)-+kt(r);return c<0?-1:c>0?1:c}function rp(i){return ks(i,Date.now())}function up(i){return i instanceof Date||typeof i=="object"&&Object.prototype.toString.call(i)==="[object Date]"}function cp(i){return!(!up(i)&&typeof i!="number"||isNaN(+kt(i)))}function op(i,r,c){const[u,d]=Cc(c==null?void 0:c.in,i,r),h=u.getFullYear()-d.getFullYear(),g=u.getMonth()-d.getMonth();return h*12+g}function fp(i){return r=>{const u=(i?Math[i]:Math.trunc)(r);return u===0?0:u}}function dp(i,r){return+kt(i)-+kt(r)}function mp(i,r){const c=kt(i);return c.setHours(23,59,59,999),c}function hp(i,r){const c=kt(i),u=c.getMonth();return c.setFullYear(c.getFullYear(),u+1,0),c.setHours(23,59,59,999),c}function gp(i,r){const c=kt(i);return+mp(c)==+hp(c)}function yp(i,r,c){const[u,d,h]=Cc(c==null?void 0:c.in,i,i,r),g=Ts(d,h),b=Math.abs(op(d,h));if(b<1)return 0;d.getMonth()===1&&d.getDate()>27&&d.setDate(30),d.setMonth(d.getMonth()-g*b);let w=Ts(d,h)===-g;gp(u)&&b===1&&Ts(u,h)===1&&(w=!1);const j=g*(b-+w);return j===0?0:j}function bp(i,r,c){const u=dp(i,r)/1e3;return fp(c==null?void 0:c.roundingMethod)(u)}const pp={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},xp=(i,r,c)=>{let u;const d=pp[i];return typeof d=="string"?u=d:r===1?u=d.one:u=d.other.replace("{{count}}",r.toString()),c!=null&&c.addSuffix?c.comparison&&c.comparison>0?"in "+u:u+" ago":u};function Fl(i){return(r={})=>{const c=r.width?String(r.width):i.defaultWidth;return i.formats[c]||i.formats[i.defaultWidth]}}const vp={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Sp={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},jp={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Np={date:Fl({formats:vp,defaultWidth:"full"}),time:Fl({formats:Sp,defaultWidth:"full"}),dateTime:Fl({formats:jp,defaultWidth:"full"})},wp={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Ap=(i,r,c,u)=>wp[i];function Zt(i){return(r,c)=>{const u=c!=null&&c.context?String(c.context):"standalone";let d;if(u==="formatting"&&i.formattingValues){const g=i.defaultFormattingWidth||i.defaultWidth,b=c!=null&&c.width?String(c.width):g;d=i.formattingValues[b]||i.formattingValues[g]}else{const g=i.defaultWidth,b=c!=null&&c.width?String(c.width):i.defaultWidth;d=i.values[b]||i.values[g]}const h=i.argumentCallback?i.argumentCallback(r):r;return d[h]}}const Tp={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Ep={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},zp={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Mp={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Op={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Dp={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Rp=(i,r)=>{const c=Number(i),u=c%100;if(u>20||u<10)switch(u%10){case 1:return c+"st";case 2:return c+"nd";case 3:return c+"rd"}return c+"th"},Cp={ordinalNumber:Rp,era:Zt({values:Tp,defaultWidth:"wide"}),quarter:Zt({values:Ep,defaultWidth:"wide",argumentCallback:i=>i-1}),month:Zt({values:zp,defaultWidth:"wide"}),day:Zt({values:Mp,defaultWidth:"wide"}),dayPeriod:Zt({values:Op,defaultWidth:"wide",formattingValues:Dp,defaultFormattingWidth:"wide"})};function Qt(i){return(r,c={})=>{const u=c.width,d=u&&i.matchPatterns[u]||i.matchPatterns[i.defaultMatchWidth],h=r.match(d);if(!h)return null;const g=h[0],b=u&&i.parsePatterns[u]||i.parsePatterns[i.defaultParseWidth],w=Array.isArray(b)?kp(b,M=>M.test(g)):Up(b,M=>M.test(g));let j;j=i.valueCallback?i.valueCallback(w):w,j=c.valueCallback?c.valueCallback(j):j;const v=r.slice(g.length);return{value:j,rest:v}}}function Up(i,r){for(const c in i)if(Object.prototype.hasOwnProperty.call(i,c)&&r(i[c]))return c}function kp(i,r){for(let c=0;c<i.length;c++)if(r(i[c]))return c}function kh(i){return(r,c={})=>{const u=r.match(i.matchPattern);if(!u)return null;const d=u[0],h=r.match(i.parsePattern);if(!h)return null;let g=i.valueCallback?i.valueCallback(h[0]):h[0];g=c.valueCallback?c.valueCallback(g):g;const b=r.slice(d.length);return{value:g,rest:b}}}const _p=/^(\d+)(th|st|nd|rd)?/i,Bp=/\d+/i,qp={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Hp={any:[/^b/i,/^(a|c)/i]},Lp={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Gp={any:[/1/i,/2/i,/3/i,/4/i]},Yp={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Xp={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Zp={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Qp={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Vp={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Kp={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Jp={ordinalNumber:kh({matchPattern:_p,parsePattern:Bp,valueCallback:i=>parseInt(i,10)}),era:Qt({matchPatterns:qp,defaultMatchWidth:"wide",parsePatterns:Hp,defaultParseWidth:"any"}),quarter:Qt({matchPatterns:Lp,defaultMatchWidth:"wide",parsePatterns:Gp,defaultParseWidth:"any",valueCallback:i=>i+1}),month:Qt({matchPatterns:Yp,defaultMatchWidth:"wide",parsePatterns:Xp,defaultParseWidth:"any"}),day:Qt({matchPatterns:Zp,defaultMatchWidth:"wide",parsePatterns:Qp,defaultParseWidth:"any"}),dayPeriod:Qt({matchPatterns:Vp,defaultMatchWidth:"any",parsePatterns:Kp,defaultParseWidth:"any"})},Wp={code:"en-US",formatDistance:xp,formatLong:Np,formatRelative:Ap,localize:Cp,match:Jp,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Fp(i,r,c){const u=sp(),d=(c==null?void 0:c.locale)??u.locale??Wp,h=2520,g=Ts(i,r);if(isNaN(g))throw new RangeError("Invalid time value");const b=Object.assign({},c,{addSuffix:c==null?void 0:c.addSuffix,comparison:g}),[w,j]=Cc(c==null?void 0:c.in,...g>0?[r,i]:[i,r]),v=bp(j,w),M=(Qm(j)-Qm(w))/1e3,k=Math.round((v-M)/60);let Z;if(k<2)return c!=null&&c.includeSeconds?v<5?d.formatDistance("lessThanXSeconds",5,b):v<10?d.formatDistance("lessThanXSeconds",10,b):v<20?d.formatDistance("lessThanXSeconds",20,b):v<40?d.formatDistance("halfAMinute",0,b):v<60?d.formatDistance("lessThanXMinutes",1,b):d.formatDistance("xMinutes",1,b):k===0?d.formatDistance("lessThanXMinutes",1,b):d.formatDistance("xMinutes",k,b);if(k<45)return d.formatDistance("xMinutes",k,b);if(k<90)return d.formatDistance("aboutXHours",1,b);if(k<Xm){const U=Math.round(k/60);return d.formatDistance("aboutXHours",U,b)}else{if(k<h)return d.formatDistance("xDays",1,b);if(k<ws){const U=Math.round(k/Xm);return d.formatDistance("xDays",U,b)}else if(k<ws*2)return Z=Math.round(k/ws),d.formatDistance("aboutXMonths",Z,b)}if(Z=yp(j,w),Z<12){const U=Math.round(k/ws);return d.formatDistance("xMonths",U,b)}else{const U=Z%12,_=Math.trunc(Z/12);return U<3?d.formatDistance("aboutXYears",_,b):U<9?d.formatDistance("overXYears",_,b):d.formatDistance("almostXYears",_+1,b)}}function $p(i,r){return Fp(i,rp(i),r)}function Pp(i,r){const c=()=>ks(r==null?void 0:r.in,NaN),d=ax(i);let h;if(d.date){const j=lx(d.date,2);h=nx(j.restDateString,j.year)}if(!h||isNaN(+h))return c();const g=+h;let b=0,w;if(d.time&&(b=ix(d.time),isNaN(b)))return c();if(d.timezone){if(w=sx(d.timezone),isNaN(w))return c()}else{const j=new Date(g+b),v=kt(0);return v.setFullYear(j.getUTCFullYear(),j.getUTCMonth(),j.getUTCDate()),v.setHours(j.getUTCHours(),j.getUTCMinutes(),j.getUTCSeconds(),j.getUTCMilliseconds()),v}return kt(g+b+w)}const As={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Ip=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,ex=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,tx=/^([+-])(\d{2})(?::?(\d{2}))?$/;function ax(i){const r={},c=i.split(As.dateTimeDelimiter);let u;if(c.length>2)return r;if(/:/.test(c[0])?u=c[0]:(r.date=c[0],u=c[1],As.timeZoneDelimiter.test(r.date)&&(r.date=i.split(As.timeZoneDelimiter)[0],u=i.substr(r.date.length,i.length))),u){const d=As.timezone.exec(u);d?(r.time=u.replace(d[1],""),r.timezone=d[1]):r.time=u}return r}function lx(i,r){const c=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+r)+"})|(\\d{2}|[+-]\\d{"+(2+r)+"})$)"),u=i.match(c);if(!u)return{year:NaN,restDateString:""};const d=u[1]?parseInt(u[1]):null,h=u[2]?parseInt(u[2]):null;return{year:h===null?d:h*100,restDateString:i.slice((u[1]||u[2]).length)}}function nx(i,r){if(r===null)return new Date(NaN);const c=i.match(Ip);if(!c)return new Date(NaN);const u=!!c[4],d=ai(c[1]),h=ai(c[2])-1,g=ai(c[3]),b=ai(c[4]),w=ai(c[5])-1;if(u)return fx(r,b,w)?rx(r,b,w):new Date(NaN);{const j=new Date(0);return!cx(r,h,g)||!ox(r,d)?new Date(NaN):(j.setUTCFullYear(r,h,Math.max(d,g)),j)}}function ai(i){return i?parseInt(i):1}function ix(i){const r=i.match(ex);if(!r)return NaN;const c=uc(r[1]),u=uc(r[2]),d=uc(r[3]);return dx(c,u,d)?c*Uh+u*Ch+d*1e3:NaN}function uc(i){return i&&parseFloat(i.replace(",","."))||0}function sx(i){if(i==="Z")return 0;const r=i.match(tx);if(!r)return 0;const c=r[1]==="+"?-1:1,u=parseInt(r[2]),d=r[3]&&parseInt(r[3])||0;return mx(u,d)?c*(u*Uh+d*Ch):NaN}function rx(i,r,c){const u=new Date(0);u.setUTCFullYear(i,0,4);const d=u.getUTCDay()||7,h=(r-1)*7+c+1-d;return u.setUTCDate(u.getUTCDate()+h),u}const ux=[31,null,31,30,31,30,31,31,30,31,30,31];function _h(i){return i%400===0||i%4===0&&i%100!==0}function cx(i,r,c){return r>=0&&r<=11&&c>=1&&c<=(ux[r]||(_h(i)?29:28))}function ox(i,r){return r>=1&&r<=(_h(i)?366:365)}function fx(i,r,c){return r>=1&&r<=53&&c>=0&&c<=6}function dx(i,r,c){return i===24?r===0&&c===0:c>=0&&c<60&&r>=0&&r<60&&i>=0&&i<25}function mx(i,r){return r>=0&&r<=59}const hx={lessThanXSeconds:{one:"menos de um segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"meio minuto",lessThanXMinutes:{one:"menos de um minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"cerca de 1 hora",other:"cerca de {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 dia",other:"{{count}} dias"},aboutXWeeks:{one:"cerca de 1 semana",other:"cerca de {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"cerca de 1 mês",other:"cerca de {{count}} meses"},xMonths:{one:"1 mês",other:"{{count}} meses"},aboutXYears:{one:"cerca de 1 ano",other:"cerca de {{count}} anos"},xYears:{one:"1 ano",other:"{{count}} anos"},overXYears:{one:"mais de 1 ano",other:"mais de {{count}} anos"},almostXYears:{one:"quase 1 ano",other:"quase {{count}} anos"}},gx=(i,r,c)=>{let u;const d=hx[i];return typeof d=="string"?u=d:r===1?u=d.one:u=d.other.replace("{{count}}",String(r)),c!=null&&c.addSuffix?c.comparison&&c.comparison>0?"em "+u:"há "+u:u},yx={full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"d MMM y",short:"dd/MM/yyyy"},bx={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},px={full:"{{date}} 'às' {{time}}",long:"{{date}} 'às' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},xx={date:Fl({formats:yx,defaultWidth:"full"}),time:Fl({formats:bx,defaultWidth:"full"}),dateTime:Fl({formats:px,defaultWidth:"full"})},vx={lastWeek:i=>{const r=i.getDay();return"'"+(r===0||r===6?"último":"última")+"' eeee 'às' p"},yesterday:"'ontem às' p",today:"'hoje às' p",tomorrow:"'amanhã às' p",nextWeek:"eeee 'às' p",other:"P"},Sx=(i,r,c,u)=>{const d=vx[i];return typeof d=="function"?d(r):d},jx={narrow:["AC","DC"],abbreviated:["AC","DC"],wide:["antes de cristo","depois de cristo"]},Nx={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1º trimestre","2º trimestre","3º trimestre","4º trimestre"]},wx={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez"],wide:["janeiro","fevereiro","março","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro"]},Ax={narrow:["D","S","T","Q","Q","S","S"],short:["dom","seg","ter","qua","qui","sex","sab"],abbreviated:["domingo","segunda","terça","quarta","quinta","sexta","sábado"],wide:["domingo","segunda-feira","terça-feira","quarta-feira","quinta-feira","sexta-feira","sábado"]},Tx={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"manhã",afternoon:"tarde",evening:"tarde",night:"noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manhã",afternoon:"tarde",evening:"tarde",night:"noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"manhã",afternoon:"tarde",evening:"tarde",night:"noite"}},Ex={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"da manhã",afternoon:"da tarde",evening:"da tarde",night:"da noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manhã",afternoon:"da tarde",evening:"da tarde",night:"da noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"da manhã",afternoon:"da tarde",evening:"da tarde",night:"da noite"}},zx=(i,r)=>{const c=Number(i);return(r==null?void 0:r.unit)==="week"?c+"ª":c+"º"},Mx={ordinalNumber:zx,era:Zt({values:jx,defaultWidth:"wide"}),quarter:Zt({values:Nx,defaultWidth:"wide",argumentCallback:i=>i-1}),month:Zt({values:wx,defaultWidth:"wide"}),day:Zt({values:Ax,defaultWidth:"wide"}),dayPeriod:Zt({values:Tx,defaultWidth:"wide",formattingValues:Ex,defaultFormattingWidth:"wide"})},Ox=/^(\d+)[ºªo]?/i,Dx=/\d+/i,Rx={narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|d\.?\s?c\.?)/i,wide:/^(antes de cristo|depois de cristo)/i},Cx={any:[/^ac/i,/^dc/i],wide:[/^antes de cristo/i,/^depois de cristo/i]},Ux={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º)? trimestre/i},kx={any:[/1/i,/2/i,/3/i,/4/i]},_x={narrow:/^[jfmajsond]/i,abbreviated:/^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,wide:/^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i},Bx={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^fev/i,/^mar/i,/^abr/i,/^mai/i,/^jun/i,/^jul/i,/^ago/i,/^set/i,/^out/i,/^nov/i,/^dez/i]},qx={narrow:/^(dom|[23456]ª?|s[aá]b)/i,short:/^(dom|[23456]ª?|s[aá]b)/i,abbreviated:/^(dom|seg|ter|qua|qui|sex|s[aá]b)/i,wide:/^(domingo|(segunda|ter[cç]a|quarta|quinta|sexta)([- ]feira)?|s[aá]bado)/i},Hx={short:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],narrow:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],any:[/^d/i,/^seg/i,/^t/i,/^qua/i,/^qui/i,/^sex/i,/^s[aá]b/i]},Lx={narrow:/^(a|p|mn|md|(da) (manhã|tarde|noite))/i,any:/^([ap]\.?\s?m\.?|meia[-\s]noite|meio[-\s]dia|(da) (manhã|tarde|noite))/i},Gx={any:{am:/^a/i,pm:/^p/i,midnight:/^mn|^meia[-\s]noite/i,noon:/^md|^meio[-\s]dia/i,morning:/manhã/i,afternoon:/tarde/i,evening:/tarde/i,night:/noite/i}},Yx={ordinalNumber:kh({matchPattern:Ox,parsePattern:Dx,valueCallback:i=>parseInt(i,10)}),era:Qt({matchPatterns:Rx,defaultMatchWidth:"wide",parsePatterns:Cx,defaultParseWidth:"any"}),quarter:Qt({matchPatterns:Ux,defaultMatchWidth:"wide",parsePatterns:kx,defaultParseWidth:"any",valueCallback:i=>i+1}),month:Qt({matchPatterns:_x,defaultMatchWidth:"wide",parsePatterns:Bx,defaultParseWidth:"any"}),day:Qt({matchPatterns:qx,defaultMatchWidth:"wide",parsePatterns:Hx,defaultParseWidth:"any"}),dayPeriod:Qt({matchPatterns:Lx,defaultMatchWidth:"any",parsePatterns:Gx,defaultParseWidth:"any"})},Xx={code:"pt-BR",formatDistance:gx,formatLong:xx,formatRelative:Sx,localize:Mx,match:Yx,options:{weekStartsOn:0,firstWeekContainsDate:1}},qa=(i,r={})=>{const{minimumFractionDigits:c=2,maximumFractionDigits:u=2,showSymbol:d=!0}=r;return i==null||isNaN(i)?d?"R$ 0,00":"0,00":new Intl.NumberFormat("pt-BR",{style:d?"currency":"decimal",currency:"BRL",minimumFractionDigits:c,maximumFractionDigits:u}).format(i)},Ha=(i,r={})=>{const{minimumFractionDigits:c=0,maximumFractionDigits:u=0}=r;return i==null||isNaN(i)?"0":new Intl.NumberFormat("pt-BR",{minimumFractionDigits:c,maximumFractionDigits:u}).format(i)},pc=(i,r={})=>{const{minimumFractionDigits:c=1,maximumFractionDigits:u=1,showSymbol:d=!0}=r;if(i==null||isNaN(i))return d?"0,0%":"0,0";const h=new Intl.NumberFormat("pt-BR",{style:d?"percent":"decimal",minimumFractionDigits:c,maximumFractionDigits:u}),g=d?i/100:i;return h.format(g)},Bh=i=>{if(!i)return"";let r;return typeof i=="string"?r=Pp(i):r=i,cp(r)?$p(r,{addSuffix:!0,locale:Xx}):""},Zx=i=>{if(!i)return"";const r=i.replace(/\D/g,"");return r.length===11?r.replace(/(\d{2})(\d{5})(\d{4})/,"($1) $2-$3"):r.length===10?r.replace(/(\d{2})(\d{4})(\d{4})/,"($1) $2-$3"):r.length===13&&r.startsWith("55")?r.replace(/55(\d{2})(\d{5})(\d{4})/,"+55 ($1) $2-$3"):i},Qx=i=>i?i.toLowerCase().split(" ").map(r=>r.charAt(0).toUpperCase()+r.slice(1)).join(" "):"",Vx=i=>({online:"var(--success)",offline:"var(--gray-400)",quente:"var(--danger)",morno:"var(--warning)",frio:"var(--primary-blue)",ativo:"var(--success)",inativo:"var(--gray-400)",pendente:"var(--warning)",concluído:"var(--success)",cancelado:"var(--danger)"})[i==null?void 0:i.toLowerCase()]||"var(--gray-500)",Uc=i=>({WhatsApp:"💬",Instagram:"📷",Facebook:"👥",Site:"🌐",Telegram:"✈️",Email:"📧"})[i]||"💬",Kx=i=>({orçamento:"Solicitação de Orçamento",suporte:"Suporte Técnico",agendamento:"Agendamento",informação:"Informações Gerais",reclamação:"Reclamação",elogio:"Elogio",dúvida:"Dúvida"})[i==null?void 0:i.toLowerCase()]||Qx(i||""),Be=se.forwardRef(({children:i,variant:r="primary",size:c="md",disabled:u=!1,loading:d=!1,leftIcon:h,rightIcon:g,fullWidth:b=!1,animate:w=!0,className:j,onClick:v,...M},k)=>{const Z=Fe("inline-flex items-center justify-center gap-2 font-medium rounded-xl","focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500","disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none",Rc.default,Dc.button[r],Ib.button[c],b&&"w-full",j),U=o.jsxs(o.Fragment,{children:[d&&o.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[o.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),o.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!d&&h&&o.jsx("span",{className:"flex-shrink-0",children:h}),o.jsx("span",{children:i}),!d&&g&&o.jsx("span",{className:"flex-shrink-0",children:g})]}),_=q=>{u||d||v==null||v(q)};return w?o.jsx($e.button,{ref:k,className:Z,onClick:_,disabled:u||d,whileHover:{scale:u||d?1:1.02},whileTap:{scale:u||d?1:.98},transition:{type:"spring",stiffness:400,damping:17},...M,children:U}):o.jsx("button",{ref:k,className:Z,onClick:_,disabled:u||d,...M,children:U})});Be.displayName="Button";const qh=({active:i,payload:r,label:c,type:u="default"})=>i&&r&&r.length?o.jsxs("div",{className:"bg-white p-3 border border-gray-200 rounded-lg shadow-lg",children:[c&&o.jsx("p",{className:"text-sm font-medium text-gray-900 mb-1",children:c}),r.map((d,h)=>o.jsxs("p",{className:"text-sm",style:{color:d.color},children:[`${d.name}: `,u==="currency"&&qa(d.value),u==="number"&&Ha(d.value),u==="percentage"&&`${d.value}%`,u==="default"&&d.value]},h))]}):null,Jx=({data:i,loading:r=!1})=>{if(r)return o.jsxs("div",{className:"card",children:[o.jsx("div",{className:"card-header",children:o.jsx("h3",{className:"text-lg font-semibold",children:"Conversões por Canal"})}),o.jsx("div",{className:"card-body",children:o.jsx("div",{className:"h-64 bg-gray-100 rounded-lg animate-pulse"})})]});const c=["#3b82f6","#10b981","#f59e0b","#ef4444","#8b5cf6"];return o.jsxs("div",{className:"card",children:[o.jsx("div",{className:"card-header",children:o.jsx("h3",{className:"text-lg font-semibold",children:"Conversões por Canal"})}),o.jsxs("div",{className:"card-body",children:[o.jsx(zc,{width:"100%",height:300,children:o.jsxs(ob,{children:[o.jsx(fb,{data:i,cx:"50%",cy:"50%",labelLine:!1,label:({name:u,percent:d})=>`${u} ${(d*100).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:i.map((u,d)=>o.jsx(db,{fill:u.color||c[d%c.length]},`cell-${d}`))}),o.jsx(Mc,{content:o.jsx(qh,{type:"percentage"})})]})}),o.jsx("div",{className:"mt-4 grid grid-cols-2 gap-2",children:i.map((u,d)=>o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:u.color||c[d%c.length]}}),o.jsx("span",{className:"text-sm text-gray-600",children:u.name})]},u.name))})]})]})},Wx=({data:i,loading:r=!1})=>r?o.jsxs("div",{className:"card",children:[o.jsx("div",{className:"card-header",children:o.jsx("h3",{className:"text-lg font-semibold",children:"Receita ao Longo do Tempo"})}),o.jsx("div",{className:"card-body",children:o.jsx("div",{className:"h-64 bg-gray-100 rounded-lg animate-pulse"})})]}):o.jsxs("div",{className:"card",children:[o.jsx("div",{className:"card-header",children:o.jsx("h3",{className:"text-lg font-semibold",children:"Receita ao Longo do Tempo"})}),o.jsx("div",{className:"card-body",children:o.jsx(zc,{width:"100%",height:300,children:o.jsxs(vh,{data:i,children:[o.jsx(Sh,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),o.jsx(jh,{dataKey:"month",stroke:"#64748b",fontSize:12}),o.jsx(mc,{stroke:"#64748b",fontSize:12,tickFormatter:c=>qa(c,{showSymbol:!1})}),o.jsx(Mc,{content:o.jsx(qh,{type:"currency"})}),o.jsx(hc,{type:"monotone",dataKey:"revenue",stroke:"#3b82f6",strokeWidth:3,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2}})]})})})]}),Fx=({revenueData:i,leadsData:r,loading:c=!1})=>{if(c)return o.jsxs("div",{className:"card",children:[o.jsx("div",{className:"card-header",children:o.jsx("h3",{className:"text-lg font-semibold",children:"Performance Geral"})}),o.jsx("div",{className:"card-body",children:o.jsx("div",{className:"h-64 bg-gray-100 rounded-lg animate-pulse"})})]});const u=i.map((d,h)=>{var g;return{month:d.month,revenue:d.revenue,leads:((g=r[h])==null?void 0:g.leads)||0}});return o.jsxs("div",{className:"card",children:[o.jsx("div",{className:"card-header",children:o.jsx("h3",{className:"text-lg font-semibold",children:"Performance Geral"})}),o.jsx("div",{className:"card-body",children:o.jsx(zc,{width:"100%",height:300,children:o.jsxs(vh,{data:u,children:[o.jsx(Sh,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),o.jsx(jh,{dataKey:"month",stroke:"#64748b",fontSize:12}),o.jsx(mc,{yAxisId:"left",stroke:"#64748b",fontSize:12,tickFormatter:d=>qa(d,{showSymbol:!1})}),o.jsx(mc,{yAxisId:"right",orientation:"right",stroke:"#64748b",fontSize:12}),o.jsx(Mc,{content:({active:d,payload:h,label:g})=>{var b,w;return d&&h&&h.length?o.jsxs("div",{className:"bg-white p-3 border border-gray-200 rounded-lg shadow-lg",children:[o.jsx("p",{className:"text-sm font-medium text-gray-900 mb-1",children:g}),o.jsxs("p",{className:"text-sm",style:{color:"#3b82f6"},children:["Receita: ",qa((b=h[0])==null?void 0:b.value)]}),o.jsxs("p",{className:"text-sm",style:{color:"#10b981"},children:["Leads: ",Ha((w=h[1])==null?void 0:w.value)]})]}):null}}),o.jsx(mb,{}),o.jsx(hc,{yAxisId:"left",type:"monotone",dataKey:"revenue",stroke:"#3b82f6",strokeWidth:3,name:"Receita",dot:{fill:"#3b82f6",strokeWidth:2,r:4}}),o.jsx(hc,{yAxisId:"right",type:"monotone",dataKey:"leads",stroke:"#10b981",strokeWidth:3,name:"Leads",dot:{fill:"#10b981",strokeWidth:2,r:4}})]})})})]})},$x=({chartData:i,loading:r=!1})=>o.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[o.jsx(Jx,{data:(i==null?void 0:i.conversionByChannel)||[],loading:r}),o.jsx(Wx,{data:(i==null?void 0:i.revenueOverTime)||[],loading:r}),o.jsx("div",{className:"lg:col-span-2",children:o.jsx(Fx,{revenueData:(i==null?void 0:i.revenueOverTime)||[],leadsData:(i==null?void 0:i.leadsOverTime)||[],loading:r})})]});function Hh(i,r){return function(){return i.apply(r,arguments)}}const{toString:Px}=Object.prototype,{getPrototypeOf:kc}=Object,{iterator:_s,toStringTag:Lh}=Symbol,Bs=(i=>r=>{const c=Px.call(r);return i[c]||(i[c]=c.slice(8,-1).toLowerCase())})(Object.create(null)),_t=i=>(i=i.toLowerCase(),r=>Bs(r)===i),qs=i=>r=>typeof r===i,{isArray:Il}=Array,ii=qs("undefined");function Ix(i){return i!==null&&!ii(i)&&i.constructor!==null&&!ii(i.constructor)&&ct(i.constructor.isBuffer)&&i.constructor.isBuffer(i)}const Gh=_t("ArrayBuffer");function ev(i){let r;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?r=ArrayBuffer.isView(i):r=i&&i.buffer&&Gh(i.buffer),r}const tv=qs("string"),ct=qs("function"),Yh=qs("number"),Hs=i=>i!==null&&typeof i=="object",av=i=>i===!0||i===!1,Es=i=>{if(Bs(i)!=="object")return!1;const r=kc(i);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Lh in i)&&!(_s in i)},lv=_t("Date"),nv=_t("File"),iv=_t("Blob"),sv=_t("FileList"),rv=i=>Hs(i)&&ct(i.pipe),uv=i=>{let r;return i&&(typeof FormData=="function"&&i instanceof FormData||ct(i.append)&&((r=Bs(i))==="formdata"||r==="object"&&ct(i.toString)&&i.toString()==="[object FormData]"))},cv=_t("URLSearchParams"),[ov,fv,dv,mv]=["ReadableStream","Request","Response","Headers"].map(_t),hv=i=>i.trim?i.trim():i.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function si(i,r,{allOwnKeys:c=!1}={}){if(i===null||typeof i>"u")return;let u,d;if(typeof i!="object"&&(i=[i]),Il(i))for(u=0,d=i.length;u<d;u++)r.call(null,i[u],u,i);else{const h=c?Object.getOwnPropertyNames(i):Object.keys(i),g=h.length;let b;for(u=0;u<g;u++)b=h[u],r.call(null,i[b],b,i)}}function Xh(i,r){r=r.toLowerCase();const c=Object.keys(i);let u=c.length,d;for(;u-- >0;)if(d=c[u],r===d.toLowerCase())return d;return null}const rl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Zh=i=>!ii(i)&&i!==rl;function xc(){const{caseless:i}=Zh(this)&&this||{},r={},c=(u,d)=>{const h=i&&Xh(r,d)||d;Es(r[h])&&Es(u)?r[h]=xc(r[h],u):Es(u)?r[h]=xc({},u):Il(u)?r[h]=u.slice():r[h]=u};for(let u=0,d=arguments.length;u<d;u++)arguments[u]&&si(arguments[u],c);return r}const gv=(i,r,c,{allOwnKeys:u}={})=>(si(r,(d,h)=>{c&&ct(d)?i[h]=Hh(d,c):i[h]=d},{allOwnKeys:u}),i),yv=i=>(i.charCodeAt(0)===65279&&(i=i.slice(1)),i),bv=(i,r,c,u)=>{i.prototype=Object.create(r.prototype,u),i.prototype.constructor=i,Object.defineProperty(i,"super",{value:r.prototype}),c&&Object.assign(i.prototype,c)},pv=(i,r,c,u)=>{let d,h,g;const b={};if(r=r||{},i==null)return r;do{for(d=Object.getOwnPropertyNames(i),h=d.length;h-- >0;)g=d[h],(!u||u(g,i,r))&&!b[g]&&(r[g]=i[g],b[g]=!0);i=c!==!1&&kc(i)}while(i&&(!c||c(i,r))&&i!==Object.prototype);return r},xv=(i,r,c)=>{i=String(i),(c===void 0||c>i.length)&&(c=i.length),c-=r.length;const u=i.indexOf(r,c);return u!==-1&&u===c},vv=i=>{if(!i)return null;if(Il(i))return i;let r=i.length;if(!Yh(r))return null;const c=new Array(r);for(;r-- >0;)c[r]=i[r];return c},Sv=(i=>r=>i&&r instanceof i)(typeof Uint8Array<"u"&&kc(Uint8Array)),jv=(i,r)=>{const u=(i&&i[_s]).call(i);let d;for(;(d=u.next())&&!d.done;){const h=d.value;r.call(i,h[0],h[1])}},Nv=(i,r)=>{let c;const u=[];for(;(c=i.exec(r))!==null;)u.push(c);return u},wv=_t("HTMLFormElement"),Av=i=>i.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(c,u,d){return u.toUpperCase()+d}),Vm=(({hasOwnProperty:i})=>(r,c)=>i.call(r,c))(Object.prototype),Tv=_t("RegExp"),Qh=(i,r)=>{const c=Object.getOwnPropertyDescriptors(i),u={};si(c,(d,h)=>{let g;(g=r(d,h,i))!==!1&&(u[h]=g||d)}),Object.defineProperties(i,u)},Ev=i=>{Qh(i,(r,c)=>{if(ct(i)&&["arguments","caller","callee"].indexOf(c)!==-1)return!1;const u=i[c];if(ct(u)){if(r.enumerable=!1,"writable"in r){r.writable=!1;return}r.set||(r.set=()=>{throw Error("Can not rewrite read-only method '"+c+"'")})}})},zv=(i,r)=>{const c={},u=d=>{d.forEach(h=>{c[h]=!0})};return Il(i)?u(i):u(String(i).split(r)),c},Mv=()=>{},Ov=(i,r)=>i!=null&&Number.isFinite(i=+i)?i:r;function Dv(i){return!!(i&&ct(i.append)&&i[Lh]==="FormData"&&i[_s])}const Rv=i=>{const r=new Array(10),c=(u,d)=>{if(Hs(u)){if(r.indexOf(u)>=0)return;if(!("toJSON"in u)){r[d]=u;const h=Il(u)?[]:{};return si(u,(g,b)=>{const w=c(g,d+1);!ii(w)&&(h[b]=w)}),r[d]=void 0,h}}return u};return c(i,0)},Cv=_t("AsyncFunction"),Uv=i=>i&&(Hs(i)||ct(i))&&ct(i.then)&&ct(i.catch),Vh=((i,r)=>i?setImmediate:r?((c,u)=>(rl.addEventListener("message",({source:d,data:h})=>{d===rl&&h===c&&u.length&&u.shift()()},!1),d=>{u.push(d),rl.postMessage(c,"*")}))(`axios@${Math.random()}`,[]):c=>setTimeout(c))(typeof setImmediate=="function",ct(rl.postMessage)),kv=typeof queueMicrotask<"u"?queueMicrotask.bind(rl):typeof process<"u"&&process.nextTick||Vh,_v=i=>i!=null&&ct(i[_s]),z={isArray:Il,isArrayBuffer:Gh,isBuffer:Ix,isFormData:uv,isArrayBufferView:ev,isString:tv,isNumber:Yh,isBoolean:av,isObject:Hs,isPlainObject:Es,isReadableStream:ov,isRequest:fv,isResponse:dv,isHeaders:mv,isUndefined:ii,isDate:lv,isFile:nv,isBlob:iv,isRegExp:Tv,isFunction:ct,isStream:rv,isURLSearchParams:cv,isTypedArray:Sv,isFileList:sv,forEach:si,merge:xc,extend:gv,trim:hv,stripBOM:yv,inherits:bv,toFlatObject:pv,kindOf:Bs,kindOfTest:_t,endsWith:xv,toArray:vv,forEachEntry:jv,matchAll:Nv,isHTMLForm:wv,hasOwnProperty:Vm,hasOwnProp:Vm,reduceDescriptors:Qh,freezeMethods:Ev,toObjectSet:zv,toCamelCase:Av,noop:Mv,toFiniteNumber:Ov,findKey:Xh,global:rl,isContextDefined:Zh,isSpecCompliantForm:Dv,toJSONObject:Rv,isAsyncFn:Cv,isThenable:Uv,setImmediate:Vh,asap:kv,isIterable:_v};function P(i,r,c,u,d){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=i,this.name="AxiosError",r&&(this.code=r),c&&(this.config=c),u&&(this.request=u),d&&(this.response=d,this.status=d.status?d.status:null)}z.inherits(P,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:z.toJSONObject(this.config),code:this.code,status:this.status}}});const Kh=P.prototype,Jh={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(i=>{Jh[i]={value:i}});Object.defineProperties(P,Jh);Object.defineProperty(Kh,"isAxiosError",{value:!0});P.from=(i,r,c,u,d,h)=>{const g=Object.create(Kh);return z.toFlatObject(i,g,function(w){return w!==Error.prototype},b=>b!=="isAxiosError"),P.call(g,i.message,r,c,u,d),g.cause=i,g.name=i.name,h&&Object.assign(g,h),g};const Bv=null;function vc(i){return z.isPlainObject(i)||z.isArray(i)}function Wh(i){return z.endsWith(i,"[]")?i.slice(0,-2):i}function Km(i,r,c){return i?i.concat(r).map(function(d,h){return d=Wh(d),!c&&h?"["+d+"]":d}).join(c?".":""):r}function qv(i){return z.isArray(i)&&!i.some(vc)}const Hv=z.toFlatObject(z,{},null,function(r){return/^is[A-Z]/.test(r)});function Ls(i,r,c){if(!z.isObject(i))throw new TypeError("target must be an object");r=r||new FormData,c=z.toFlatObject(c,{metaTokens:!0,dots:!1,indexes:!1},!1,function(_,q){return!z.isUndefined(q[_])});const u=c.metaTokens,d=c.visitor||v,h=c.dots,g=c.indexes,w=(c.Blob||typeof Blob<"u"&&Blob)&&z.isSpecCompliantForm(r);if(!z.isFunction(d))throw new TypeError("visitor must be a function");function j(U){if(U===null)return"";if(z.isDate(U))return U.toISOString();if(!w&&z.isBlob(U))throw new P("Blob is not supported. Use a Buffer instead.");return z.isArrayBuffer(U)||z.isTypedArray(U)?w&&typeof Blob=="function"?new Blob([U]):Buffer.from(U):U}function v(U,_,q){let $=U;if(U&&!q&&typeof U=="object"){if(z.endsWith(_,"{}"))_=u?_:_.slice(0,-2),U=JSON.stringify(U);else if(z.isArray(U)&&qv(U)||(z.isFileList(U)||z.endsWith(_,"[]"))&&($=z.toArray(U)))return _=Wh(_),$.forEach(function(J,ue){!(z.isUndefined(J)||J===null)&&r.append(g===!0?Km([_],ue,h):g===null?_:_+"[]",j(J))}),!1}return vc(U)?!0:(r.append(Km(q,_,h),j(U)),!1)}const M=[],k=Object.assign(Hv,{defaultVisitor:v,convertValue:j,isVisitable:vc});function Z(U,_){if(!z.isUndefined(U)){if(M.indexOf(U)!==-1)throw Error("Circular reference detected in "+_.join("."));M.push(U),z.forEach(U,function($,ne){(!(z.isUndefined($)||$===null)&&d.call(r,$,z.isString(ne)?ne.trim():ne,_,k))===!0&&Z($,_?_.concat(ne):[ne])}),M.pop()}}if(!z.isObject(i))throw new TypeError("data must be an object");return Z(i),r}function Jm(i){const r={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(i).replace(/[!'()~]|%20|%00/g,function(u){return r[u]})}function _c(i,r){this._pairs=[],i&&Ls(i,this,r)}const Fh=_c.prototype;Fh.append=function(r,c){this._pairs.push([r,c])};Fh.toString=function(r){const c=r?function(u){return r.call(this,u,Jm)}:Jm;return this._pairs.map(function(d){return c(d[0])+"="+c(d[1])},"").join("&")};function Lv(i){return encodeURIComponent(i).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function $h(i,r,c){if(!r)return i;const u=c&&c.encode||Lv;z.isFunction(c)&&(c={serialize:c});const d=c&&c.serialize;let h;if(d?h=d(r,c):h=z.isURLSearchParams(r)?r.toString():new _c(r,c).toString(u),h){const g=i.indexOf("#");g!==-1&&(i=i.slice(0,g)),i+=(i.indexOf("?")===-1?"?":"&")+h}return i}class Wm{constructor(){this.handlers=[]}use(r,c,u){return this.handlers.push({fulfilled:r,rejected:c,synchronous:u?u.synchronous:!1,runWhen:u?u.runWhen:null}),this.handlers.length-1}eject(r){this.handlers[r]&&(this.handlers[r]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(r){z.forEach(this.handlers,function(u){u!==null&&r(u)})}}const Ph={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Gv=typeof URLSearchParams<"u"?URLSearchParams:_c,Yv=typeof FormData<"u"?FormData:null,Xv=typeof Blob<"u"?Blob:null,Zv={isBrowser:!0,classes:{URLSearchParams:Gv,FormData:Yv,Blob:Xv},protocols:["http","https","file","blob","url","data"]},Bc=typeof window<"u"&&typeof document<"u",Sc=typeof navigator=="object"&&navigator||void 0,Qv=Bc&&(!Sc||["ReactNative","NativeScript","NS"].indexOf(Sc.product)<0),Vv=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Kv=Bc&&window.location.href||"http://localhost",Jv=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Bc,hasStandardBrowserEnv:Qv,hasStandardBrowserWebWorkerEnv:Vv,navigator:Sc,origin:Kv},Symbol.toStringTag,{value:"Module"})),et={...Jv,...Zv};function Wv(i,r){return Ls(i,new et.classes.URLSearchParams,Object.assign({visitor:function(c,u,d,h){return et.isNode&&z.isBuffer(c)?(this.append(u,c.toString("base64")),!1):h.defaultVisitor.apply(this,arguments)}},r))}function Fv(i){return z.matchAll(/\w+|\[(\w*)]/g,i).map(r=>r[0]==="[]"?"":r[1]||r[0])}function $v(i){const r={},c=Object.keys(i);let u;const d=c.length;let h;for(u=0;u<d;u++)h=c[u],r[h]=i[h];return r}function Ih(i){function r(c,u,d,h){let g=c[h++];if(g==="__proto__")return!0;const b=Number.isFinite(+g),w=h>=c.length;return g=!g&&z.isArray(d)?d.length:g,w?(z.hasOwnProp(d,g)?d[g]=[d[g],u]:d[g]=u,!b):((!d[g]||!z.isObject(d[g]))&&(d[g]=[]),r(c,u,d[g],h)&&z.isArray(d[g])&&(d[g]=$v(d[g])),!b)}if(z.isFormData(i)&&z.isFunction(i.entries)){const c={};return z.forEachEntry(i,(u,d)=>{r(Fv(u),d,c,0)}),c}return null}function Pv(i,r,c){if(z.isString(i))try{return(r||JSON.parse)(i),z.trim(i)}catch(u){if(u.name!=="SyntaxError")throw u}return(c||JSON.stringify)(i)}const ri={transitional:Ph,adapter:["xhr","http","fetch"],transformRequest:[function(r,c){const u=c.getContentType()||"",d=u.indexOf("application/json")>-1,h=z.isObject(r);if(h&&z.isHTMLForm(r)&&(r=new FormData(r)),z.isFormData(r))return d?JSON.stringify(Ih(r)):r;if(z.isArrayBuffer(r)||z.isBuffer(r)||z.isStream(r)||z.isFile(r)||z.isBlob(r)||z.isReadableStream(r))return r;if(z.isArrayBufferView(r))return r.buffer;if(z.isURLSearchParams(r))return c.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),r.toString();let b;if(h){if(u.indexOf("application/x-www-form-urlencoded")>-1)return Wv(r,this.formSerializer).toString();if((b=z.isFileList(r))||u.indexOf("multipart/form-data")>-1){const w=this.env&&this.env.FormData;return Ls(b?{"files[]":r}:r,w&&new w,this.formSerializer)}}return h||d?(c.setContentType("application/json",!1),Pv(r)):r}],transformResponse:[function(r){const c=this.transitional||ri.transitional,u=c&&c.forcedJSONParsing,d=this.responseType==="json";if(z.isResponse(r)||z.isReadableStream(r))return r;if(r&&z.isString(r)&&(u&&!this.responseType||d)){const g=!(c&&c.silentJSONParsing)&&d;try{return JSON.parse(r)}catch(b){if(g)throw b.name==="SyntaxError"?P.from(b,P.ERR_BAD_RESPONSE,this,null,this.response):b}}return r}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:et.classes.FormData,Blob:et.classes.Blob},validateStatus:function(r){return r>=200&&r<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};z.forEach(["delete","get","head","post","put","patch"],i=>{ri.headers[i]={}});const Iv=z.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),e1=i=>{const r={};let c,u,d;return i&&i.split(`
`).forEach(function(g){d=g.indexOf(":"),c=g.substring(0,d).trim().toLowerCase(),u=g.substring(d+1).trim(),!(!c||r[c]&&Iv[c])&&(c==="set-cookie"?r[c]?r[c].push(u):r[c]=[u]:r[c]=r[c]?r[c]+", "+u:u)}),r},Fm=Symbol("internals");function li(i){return i&&String(i).trim().toLowerCase()}function zs(i){return i===!1||i==null?i:z.isArray(i)?i.map(zs):String(i)}function t1(i){const r=Object.create(null),c=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let u;for(;u=c.exec(i);)r[u[1]]=u[2];return r}const a1=i=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(i.trim());function cc(i,r,c,u,d){if(z.isFunction(u))return u.call(this,r,c);if(d&&(r=c),!!z.isString(r)){if(z.isString(u))return r.indexOf(u)!==-1;if(z.isRegExp(u))return u.test(r)}}function l1(i){return i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(r,c,u)=>c.toUpperCase()+u)}function n1(i,r){const c=z.toCamelCase(" "+r);["get","set","has"].forEach(u=>{Object.defineProperty(i,u+c,{value:function(d,h,g){return this[u].call(this,r,d,h,g)},configurable:!0})})}let ot=class{constructor(r){r&&this.set(r)}set(r,c,u){const d=this;function h(b,w,j){const v=li(w);if(!v)throw new Error("header name must be a non-empty string");const M=z.findKey(d,v);(!M||d[M]===void 0||j===!0||j===void 0&&d[M]!==!1)&&(d[M||w]=zs(b))}const g=(b,w)=>z.forEach(b,(j,v)=>h(j,v,w));if(z.isPlainObject(r)||r instanceof this.constructor)g(r,c);else if(z.isString(r)&&(r=r.trim())&&!a1(r))g(e1(r),c);else if(z.isObject(r)&&z.isIterable(r)){let b={},w,j;for(const v of r){if(!z.isArray(v))throw TypeError("Object iterator must return a key-value pair");b[j=v[0]]=(w=b[j])?z.isArray(w)?[...w,v[1]]:[w,v[1]]:v[1]}g(b,c)}else r!=null&&h(c,r,u);return this}get(r,c){if(r=li(r),r){const u=z.findKey(this,r);if(u){const d=this[u];if(!c)return d;if(c===!0)return t1(d);if(z.isFunction(c))return c.call(this,d,u);if(z.isRegExp(c))return c.exec(d);throw new TypeError("parser must be boolean|regexp|function")}}}has(r,c){if(r=li(r),r){const u=z.findKey(this,r);return!!(u&&this[u]!==void 0&&(!c||cc(this,this[u],u,c)))}return!1}delete(r,c){const u=this;let d=!1;function h(g){if(g=li(g),g){const b=z.findKey(u,g);b&&(!c||cc(u,u[b],b,c))&&(delete u[b],d=!0)}}return z.isArray(r)?r.forEach(h):h(r),d}clear(r){const c=Object.keys(this);let u=c.length,d=!1;for(;u--;){const h=c[u];(!r||cc(this,this[h],h,r,!0))&&(delete this[h],d=!0)}return d}normalize(r){const c=this,u={};return z.forEach(this,(d,h)=>{const g=z.findKey(u,h);if(g){c[g]=zs(d),delete c[h];return}const b=r?l1(h):String(h).trim();b!==h&&delete c[h],c[b]=zs(d),u[b]=!0}),this}concat(...r){return this.constructor.concat(this,...r)}toJSON(r){const c=Object.create(null);return z.forEach(this,(u,d)=>{u!=null&&u!==!1&&(c[d]=r&&z.isArray(u)?u.join(", "):u)}),c}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([r,c])=>r+": "+c).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(r){return r instanceof this?r:new this(r)}static concat(r,...c){const u=new this(r);return c.forEach(d=>u.set(d)),u}static accessor(r){const u=(this[Fm]=this[Fm]={accessors:{}}).accessors,d=this.prototype;function h(g){const b=li(g);u[b]||(n1(d,g),u[b]=!0)}return z.isArray(r)?r.forEach(h):h(r),this}};ot.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);z.reduceDescriptors(ot.prototype,({value:i},r)=>{let c=r[0].toUpperCase()+r.slice(1);return{get:()=>i,set(u){this[c]=u}}});z.freezeMethods(ot);function oc(i,r){const c=this||ri,u=r||c,d=ot.from(u.headers);let h=u.data;return z.forEach(i,function(b){h=b.call(c,h,d.normalize(),r?r.status:void 0)}),d.normalize(),h}function e0(i){return!!(i&&i.__CANCEL__)}function en(i,r,c){P.call(this,i??"canceled",P.ERR_CANCELED,r,c),this.name="CanceledError"}z.inherits(en,P,{__CANCEL__:!0});function t0(i,r,c){const u=c.config.validateStatus;!c.status||!u||u(c.status)?i(c):r(new P("Request failed with status code "+c.status,[P.ERR_BAD_REQUEST,P.ERR_BAD_RESPONSE][Math.floor(c.status/100)-4],c.config,c.request,c))}function i1(i){const r=/^([-+\w]{1,25})(:?\/\/|:)/.exec(i);return r&&r[1]||""}function s1(i,r){i=i||10;const c=new Array(i),u=new Array(i);let d=0,h=0,g;return r=r!==void 0?r:1e3,function(w){const j=Date.now(),v=u[h];g||(g=j),c[d]=w,u[d]=j;let M=h,k=0;for(;M!==d;)k+=c[M++],M=M%i;if(d=(d+1)%i,d===h&&(h=(h+1)%i),j-g<r)return;const Z=v&&j-v;return Z?Math.round(k*1e3/Z):void 0}}function r1(i,r){let c=0,u=1e3/r,d,h;const g=(j,v=Date.now())=>{c=v,d=null,h&&(clearTimeout(h),h=null),i.apply(null,j)};return[(...j)=>{const v=Date.now(),M=v-c;M>=u?g(j,v):(d=j,h||(h=setTimeout(()=>{h=null,g(d)},u-M)))},()=>d&&g(d)]}const Rs=(i,r,c=3)=>{let u=0;const d=s1(50,250);return r1(h=>{const g=h.loaded,b=h.lengthComputable?h.total:void 0,w=g-u,j=d(w),v=g<=b;u=g;const M={loaded:g,total:b,progress:b?g/b:void 0,bytes:w,rate:j||void 0,estimated:j&&b&&v?(b-g)/j:void 0,event:h,lengthComputable:b!=null,[r?"download":"upload"]:!0};i(M)},c)},$m=(i,r)=>{const c=i!=null;return[u=>r[0]({lengthComputable:c,total:i,loaded:u}),r[1]]},Pm=i=>(...r)=>z.asap(()=>i(...r)),u1=et.hasStandardBrowserEnv?((i,r)=>c=>(c=new URL(c,et.origin),i.protocol===c.protocol&&i.host===c.host&&(r||i.port===c.port)))(new URL(et.origin),et.navigator&&/(msie|trident)/i.test(et.navigator.userAgent)):()=>!0,c1=et.hasStandardBrowserEnv?{write(i,r,c,u,d,h){const g=[i+"="+encodeURIComponent(r)];z.isNumber(c)&&g.push("expires="+new Date(c).toGMTString()),z.isString(u)&&g.push("path="+u),z.isString(d)&&g.push("domain="+d),h===!0&&g.push("secure"),document.cookie=g.join("; ")},read(i){const r=document.cookie.match(new RegExp("(^|;\\s*)("+i+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove(i){this.write(i,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function o1(i){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)}function f1(i,r){return r?i.replace(/\/?\/$/,"")+"/"+r.replace(/^\/+/,""):i}function a0(i,r,c){let u=!o1(r);return i&&(u||c==!1)?f1(i,r):r}const Im=i=>i instanceof ot?{...i}:i;function cl(i,r){r=r||{};const c={};function u(j,v,M,k){return z.isPlainObject(j)&&z.isPlainObject(v)?z.merge.call({caseless:k},j,v):z.isPlainObject(v)?z.merge({},v):z.isArray(v)?v.slice():v}function d(j,v,M,k){if(z.isUndefined(v)){if(!z.isUndefined(j))return u(void 0,j,M,k)}else return u(j,v,M,k)}function h(j,v){if(!z.isUndefined(v))return u(void 0,v)}function g(j,v){if(z.isUndefined(v)){if(!z.isUndefined(j))return u(void 0,j)}else return u(void 0,v)}function b(j,v,M){if(M in r)return u(j,v);if(M in i)return u(void 0,j)}const w={url:h,method:h,data:h,baseURL:g,transformRequest:g,transformResponse:g,paramsSerializer:g,timeout:g,timeoutMessage:g,withCredentials:g,withXSRFToken:g,adapter:g,responseType:g,xsrfCookieName:g,xsrfHeaderName:g,onUploadProgress:g,onDownloadProgress:g,decompress:g,maxContentLength:g,maxBodyLength:g,beforeRedirect:g,transport:g,httpAgent:g,httpsAgent:g,cancelToken:g,socketPath:g,responseEncoding:g,validateStatus:b,headers:(j,v,M)=>d(Im(j),Im(v),M,!0)};return z.forEach(Object.keys(Object.assign({},i,r)),function(v){const M=w[v]||d,k=M(i[v],r[v],v);z.isUndefined(k)&&M!==b||(c[v]=k)}),c}const l0=i=>{const r=cl({},i);let{data:c,withXSRFToken:u,xsrfHeaderName:d,xsrfCookieName:h,headers:g,auth:b}=r;r.headers=g=ot.from(g),r.url=$h(a0(r.baseURL,r.url,r.allowAbsoluteUrls),i.params,i.paramsSerializer),b&&g.set("Authorization","Basic "+btoa((b.username||"")+":"+(b.password?unescape(encodeURIComponent(b.password)):"")));let w;if(z.isFormData(c)){if(et.hasStandardBrowserEnv||et.hasStandardBrowserWebWorkerEnv)g.setContentType(void 0);else if((w=g.getContentType())!==!1){const[j,...v]=w?w.split(";").map(M=>M.trim()).filter(Boolean):[];g.setContentType([j||"multipart/form-data",...v].join("; "))}}if(et.hasStandardBrowserEnv&&(u&&z.isFunction(u)&&(u=u(r)),u||u!==!1&&u1(r.url))){const j=d&&h&&c1.read(h);j&&g.set(d,j)}return r},d1=typeof XMLHttpRequest<"u",m1=d1&&function(i){return new Promise(function(c,u){const d=l0(i);let h=d.data;const g=ot.from(d.headers).normalize();let{responseType:b,onUploadProgress:w,onDownloadProgress:j}=d,v,M,k,Z,U;function _(){Z&&Z(),U&&U(),d.cancelToken&&d.cancelToken.unsubscribe(v),d.signal&&d.signal.removeEventListener("abort",v)}let q=new XMLHttpRequest;q.open(d.method.toUpperCase(),d.url,!0),q.timeout=d.timeout;function $(){if(!q)return;const J=ot.from("getAllResponseHeaders"in q&&q.getAllResponseHeaders()),re={data:!b||b==="text"||b==="json"?q.responseText:q.response,status:q.status,statusText:q.statusText,headers:J,config:i,request:q};t0(function(je){c(je),_()},function(je){u(je),_()},re),q=null}"onloadend"in q?q.onloadend=$:q.onreadystatechange=function(){!q||q.readyState!==4||q.status===0&&!(q.responseURL&&q.responseURL.indexOf("file:")===0)||setTimeout($)},q.onabort=function(){q&&(u(new P("Request aborted",P.ECONNABORTED,i,q)),q=null)},q.onerror=function(){u(new P("Network Error",P.ERR_NETWORK,i,q)),q=null},q.ontimeout=function(){let ue=d.timeout?"timeout of "+d.timeout+"ms exceeded":"timeout exceeded";const re=d.transitional||Ph;d.timeoutErrorMessage&&(ue=d.timeoutErrorMessage),u(new P(ue,re.clarifyTimeoutError?P.ETIMEDOUT:P.ECONNABORTED,i,q)),q=null},h===void 0&&g.setContentType(null),"setRequestHeader"in q&&z.forEach(g.toJSON(),function(ue,re){q.setRequestHeader(re,ue)}),z.isUndefined(d.withCredentials)||(q.withCredentials=!!d.withCredentials),b&&b!=="json"&&(q.responseType=d.responseType),j&&([k,U]=Rs(j,!0),q.addEventListener("progress",k)),w&&q.upload&&([M,Z]=Rs(w),q.upload.addEventListener("progress",M),q.upload.addEventListener("loadend",Z)),(d.cancelToken||d.signal)&&(v=J=>{q&&(u(!J||J.type?new en(null,i,q):J),q.abort(),q=null)},d.cancelToken&&d.cancelToken.subscribe(v),d.signal&&(d.signal.aborted?v():d.signal.addEventListener("abort",v)));const ne=i1(d.url);if(ne&&et.protocols.indexOf(ne)===-1){u(new P("Unsupported protocol "+ne+":",P.ERR_BAD_REQUEST,i));return}q.send(h||null)})},h1=(i,r)=>{const{length:c}=i=i?i.filter(Boolean):[];if(r||c){let u=new AbortController,d;const h=function(j){if(!d){d=!0,b();const v=j instanceof Error?j:this.reason;u.abort(v instanceof P?v:new en(v instanceof Error?v.message:v))}};let g=r&&setTimeout(()=>{g=null,h(new P(`timeout ${r} of ms exceeded`,P.ETIMEDOUT))},r);const b=()=>{i&&(g&&clearTimeout(g),g=null,i.forEach(j=>{j.unsubscribe?j.unsubscribe(h):j.removeEventListener("abort",h)}),i=null)};i.forEach(j=>j.addEventListener("abort",h));const{signal:w}=u;return w.unsubscribe=()=>z.asap(b),w}},g1=function*(i,r){let c=i.byteLength;if(c<r){yield i;return}let u=0,d;for(;u<c;)d=u+r,yield i.slice(u,d),u=d},y1=async function*(i,r){for await(const c of b1(i))yield*g1(c,r)},b1=async function*(i){if(i[Symbol.asyncIterator]){yield*i;return}const r=i.getReader();try{for(;;){const{done:c,value:u}=await r.read();if(c)break;yield u}}finally{await r.cancel()}},eh=(i,r,c,u)=>{const d=y1(i,r);let h=0,g,b=w=>{g||(g=!0,u&&u(w))};return new ReadableStream({async pull(w){try{const{done:j,value:v}=await d.next();if(j){b(),w.close();return}let M=v.byteLength;if(c){let k=h+=M;c(k)}w.enqueue(new Uint8Array(v))}catch(j){throw b(j),j}},cancel(w){return b(w),d.return()}},{highWaterMark:2})},Gs=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",n0=Gs&&typeof ReadableStream=="function",p1=Gs&&(typeof TextEncoder=="function"?(i=>r=>i.encode(r))(new TextEncoder):async i=>new Uint8Array(await new Response(i).arrayBuffer())),i0=(i,...r)=>{try{return!!i(...r)}catch{return!1}},x1=n0&&i0(()=>{let i=!1;const r=new Request(et.origin,{body:new ReadableStream,method:"POST",get duplex(){return i=!0,"half"}}).headers.has("Content-Type");return i&&!r}),th=64*1024,jc=n0&&i0(()=>z.isReadableStream(new Response("").body)),Cs={stream:jc&&(i=>i.body)};Gs&&(i=>{["text","arrayBuffer","blob","formData","stream"].forEach(r=>{!Cs[r]&&(Cs[r]=z.isFunction(i[r])?c=>c[r]():(c,u)=>{throw new P(`Response type '${r}' is not supported`,P.ERR_NOT_SUPPORT,u)})})})(new Response);const v1=async i=>{if(i==null)return 0;if(z.isBlob(i))return i.size;if(z.isSpecCompliantForm(i))return(await new Request(et.origin,{method:"POST",body:i}).arrayBuffer()).byteLength;if(z.isArrayBufferView(i)||z.isArrayBuffer(i))return i.byteLength;if(z.isURLSearchParams(i)&&(i=i+""),z.isString(i))return(await p1(i)).byteLength},S1=async(i,r)=>{const c=z.toFiniteNumber(i.getContentLength());return c??v1(r)},j1=Gs&&(async i=>{let{url:r,method:c,data:u,signal:d,cancelToken:h,timeout:g,onDownloadProgress:b,onUploadProgress:w,responseType:j,headers:v,withCredentials:M="same-origin",fetchOptions:k}=l0(i);j=j?(j+"").toLowerCase():"text";let Z=h1([d,h&&h.toAbortSignal()],g),U;const _=Z&&Z.unsubscribe&&(()=>{Z.unsubscribe()});let q;try{if(w&&x1&&c!=="get"&&c!=="head"&&(q=await S1(v,u))!==0){let re=new Request(r,{method:"POST",body:u,duplex:"half"}),de;if(z.isFormData(u)&&(de=re.headers.get("content-type"))&&v.setContentType(de),re.body){const[je,Y]=$m(q,Rs(Pm(w)));u=eh(re.body,th,je,Y)}}z.isString(M)||(M=M?"include":"omit");const $="credentials"in Request.prototype;U=new Request(r,{...k,signal:Z,method:c.toUpperCase(),headers:v.normalize().toJSON(),body:u,duplex:"half",credentials:$?M:void 0});let ne=await fetch(U);const J=jc&&(j==="stream"||j==="response");if(jc&&(b||J&&_)){const re={};["status","statusText","headers"].forEach(Ee=>{re[Ee]=ne[Ee]});const de=z.toFiniteNumber(ne.headers.get("content-length")),[je,Y]=b&&$m(de,Rs(Pm(b),!0))||[];ne=new Response(eh(ne.body,th,je,()=>{Y&&Y(),_&&_()}),re)}j=j||"text";let ue=await Cs[z.findKey(Cs,j)||"text"](ne,i);return!J&&_&&_(),await new Promise((re,de)=>{t0(re,de,{data:ue,headers:ot.from(ne.headers),status:ne.status,statusText:ne.statusText,config:i,request:U})})}catch($){throw _&&_(),$&&$.name==="TypeError"&&/Load failed|fetch/i.test($.message)?Object.assign(new P("Network Error",P.ERR_NETWORK,i,U),{cause:$.cause||$}):P.from($,$&&$.code,i,U)}}),Nc={http:Bv,xhr:m1,fetch:j1};z.forEach(Nc,(i,r)=>{if(i){try{Object.defineProperty(i,"name",{value:r})}catch{}Object.defineProperty(i,"adapterName",{value:r})}});const ah=i=>`- ${i}`,N1=i=>z.isFunction(i)||i===null||i===!1,s0={getAdapter:i=>{i=z.isArray(i)?i:[i];const{length:r}=i;let c,u;const d={};for(let h=0;h<r;h++){c=i[h];let g;if(u=c,!N1(c)&&(u=Nc[(g=String(c)).toLowerCase()],u===void 0))throw new P(`Unknown adapter '${g}'`);if(u)break;d[g||"#"+h]=u}if(!u){const h=Object.entries(d).map(([b,w])=>`adapter ${b} `+(w===!1?"is not supported by the environment":"is not available in the build"));let g=r?h.length>1?`since :
`+h.map(ah).join(`
`):" "+ah(h[0]):"as no adapter specified";throw new P("There is no suitable adapter to dispatch the request "+g,"ERR_NOT_SUPPORT")}return u},adapters:Nc};function fc(i){if(i.cancelToken&&i.cancelToken.throwIfRequested(),i.signal&&i.signal.aborted)throw new en(null,i)}function lh(i){return fc(i),i.headers=ot.from(i.headers),i.data=oc.call(i,i.transformRequest),["post","put","patch"].indexOf(i.method)!==-1&&i.headers.setContentType("application/x-www-form-urlencoded",!1),s0.getAdapter(i.adapter||ri.adapter)(i).then(function(u){return fc(i),u.data=oc.call(i,i.transformResponse,u),u.headers=ot.from(u.headers),u},function(u){return e0(u)||(fc(i),u&&u.response&&(u.response.data=oc.call(i,i.transformResponse,u.response),u.response.headers=ot.from(u.response.headers))),Promise.reject(u)})}const r0="1.9.0",Ys={};["object","boolean","number","function","string","symbol"].forEach((i,r)=>{Ys[i]=function(u){return typeof u===i||"a"+(r<1?"n ":" ")+i}});const nh={};Ys.transitional=function(r,c,u){function d(h,g){return"[Axios v"+r0+"] Transitional option '"+h+"'"+g+(u?". "+u:"")}return(h,g,b)=>{if(r===!1)throw new P(d(g," has been removed"+(c?" in "+c:"")),P.ERR_DEPRECATED);return c&&!nh[g]&&(nh[g]=!0,console.warn(d(g," has been deprecated since v"+c+" and will be removed in the near future"))),r?r(h,g,b):!0}};Ys.spelling=function(r){return(c,u)=>(console.warn(`${u} is likely a misspelling of ${r}`),!0)};function w1(i,r,c){if(typeof i!="object")throw new P("options must be an object",P.ERR_BAD_OPTION_VALUE);const u=Object.keys(i);let d=u.length;for(;d-- >0;){const h=u[d],g=r[h];if(g){const b=i[h],w=b===void 0||g(b,h,i);if(w!==!0)throw new P("option "+h+" must be "+w,P.ERR_BAD_OPTION_VALUE);continue}if(c!==!0)throw new P("Unknown option "+h,P.ERR_BAD_OPTION)}}const Ms={assertOptions:w1,validators:Ys},Xt=Ms.validators;let ul=class{constructor(r){this.defaults=r||{},this.interceptors={request:new Wm,response:new Wm}}async request(r,c){try{return await this._request(r,c)}catch(u){if(u instanceof Error){let d={};Error.captureStackTrace?Error.captureStackTrace(d):d=new Error;const h=d.stack?d.stack.replace(/^.+\n/,""):"";try{u.stack?h&&!String(u.stack).endsWith(h.replace(/^.+\n.+\n/,""))&&(u.stack+=`
`+h):u.stack=h}catch{}}throw u}}_request(r,c){typeof r=="string"?(c=c||{},c.url=r):c=r||{},c=cl(this.defaults,c);const{transitional:u,paramsSerializer:d,headers:h}=c;u!==void 0&&Ms.assertOptions(u,{silentJSONParsing:Xt.transitional(Xt.boolean),forcedJSONParsing:Xt.transitional(Xt.boolean),clarifyTimeoutError:Xt.transitional(Xt.boolean)},!1),d!=null&&(z.isFunction(d)?c.paramsSerializer={serialize:d}:Ms.assertOptions(d,{encode:Xt.function,serialize:Xt.function},!0)),c.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?c.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:c.allowAbsoluteUrls=!0),Ms.assertOptions(c,{baseUrl:Xt.spelling("baseURL"),withXsrfToken:Xt.spelling("withXSRFToken")},!0),c.method=(c.method||this.defaults.method||"get").toLowerCase();let g=h&&z.merge(h.common,h[c.method]);h&&z.forEach(["delete","get","head","post","put","patch","common"],U=>{delete h[U]}),c.headers=ot.concat(g,h);const b=[];let w=!0;this.interceptors.request.forEach(function(_){typeof _.runWhen=="function"&&_.runWhen(c)===!1||(w=w&&_.synchronous,b.unshift(_.fulfilled,_.rejected))});const j=[];this.interceptors.response.forEach(function(_){j.push(_.fulfilled,_.rejected)});let v,M=0,k;if(!w){const U=[lh.bind(this),void 0];for(U.unshift.apply(U,b),U.push.apply(U,j),k=U.length,v=Promise.resolve(c);M<k;)v=v.then(U[M++],U[M++]);return v}k=b.length;let Z=c;for(M=0;M<k;){const U=b[M++],_=b[M++];try{Z=U(Z)}catch(q){_.call(this,q);break}}try{v=lh.call(this,Z)}catch(U){return Promise.reject(U)}for(M=0,k=j.length;M<k;)v=v.then(j[M++],j[M++]);return v}getUri(r){r=cl(this.defaults,r);const c=a0(r.baseURL,r.url,r.allowAbsoluteUrls);return $h(c,r.params,r.paramsSerializer)}};z.forEach(["delete","get","head","options"],function(r){ul.prototype[r]=function(c,u){return this.request(cl(u||{},{method:r,url:c,data:(u||{}).data}))}});z.forEach(["post","put","patch"],function(r){function c(u){return function(h,g,b){return this.request(cl(b||{},{method:r,headers:u?{"Content-Type":"multipart/form-data"}:{},url:h,data:g}))}}ul.prototype[r]=c(),ul.prototype[r+"Form"]=c(!0)});let A1=class u0{constructor(r){if(typeof r!="function")throw new TypeError("executor must be a function.");let c;this.promise=new Promise(function(h){c=h});const u=this;this.promise.then(d=>{if(!u._listeners)return;let h=u._listeners.length;for(;h-- >0;)u._listeners[h](d);u._listeners=null}),this.promise.then=d=>{let h;const g=new Promise(b=>{u.subscribe(b),h=b}).then(d);return g.cancel=function(){u.unsubscribe(h)},g},r(function(h,g,b){u.reason||(u.reason=new en(h,g,b),c(u.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(r){if(this.reason){r(this.reason);return}this._listeners?this._listeners.push(r):this._listeners=[r]}unsubscribe(r){if(!this._listeners)return;const c=this._listeners.indexOf(r);c!==-1&&this._listeners.splice(c,1)}toAbortSignal(){const r=new AbortController,c=u=>{r.abort(u)};return this.subscribe(c),r.signal.unsubscribe=()=>this.unsubscribe(c),r.signal}static source(){let r;return{token:new u0(function(d){r=d}),cancel:r}}};function T1(i){return function(c){return i.apply(null,c)}}function E1(i){return z.isObject(i)&&i.isAxiosError===!0}const wc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(wc).forEach(([i,r])=>{wc[r]=i});function c0(i){const r=new ul(i),c=Hh(ul.prototype.request,r);return z.extend(c,ul.prototype,r,{allOwnKeys:!0}),z.extend(c,r,null,{allOwnKeys:!0}),c.create=function(d){return c0(cl(i,d))},c}const Ue=c0(ri);Ue.Axios=ul;Ue.CanceledError=en;Ue.CancelToken=A1;Ue.isCancel=e0;Ue.VERSION=r0;Ue.toFormData=Ls;Ue.AxiosError=P;Ue.Cancel=Ue.CanceledError;Ue.all=function(r){return Promise.all(r)};Ue.spread=T1;Ue.isAxiosError=E1;Ue.mergeConfig=cl;Ue.AxiosHeaders=ot;Ue.formToJSON=i=>Ih(z.isHTMLForm(i)?new FormData(i):i);Ue.getAdapter=s0.getAdapter;Ue.HttpStatusCode=wc;Ue.default=Ue;const{Axios:s2,AxiosError:r2,CanceledError:u2,isCancel:c2,CancelToken:o2,VERSION:f2,all:d2,Cancel:m2,isAxiosError:h2,spread:g2,toFormData:y2,AxiosHeaders:b2,HttpStatusCode:p2,formToJSON:x2,getAdapter:v2,mergeConfig:S2}=Ue,z1={cpl:45.5,cac:180,cpc:2.3,conversionRate:12.5,revenuePerAgent:15420,activeLeads:234,totalConversations:1847,qualifiedConversations:456},M1=[{id:1,name:"Bot WhatsApp Vendas",channel:"WhatsApp",leadsAttended:156,activeConversations:23,responseRate:94.2,status:"online",lastActivity:"2024-01-15T10:30:00Z"},{id:2,name:"Assistente Instagram",channel:"Instagram",leadsAttended:89,activeConversations:12,responseRate:87.5,status:"online",lastActivity:"2024-01-15T09:45:00Z"},{id:3,name:"Chat Site Principal",channel:"Site",leadsAttended:203,activeConversations:31,responseRate:91.8,status:"online",lastActivity:"2024-01-15T11:15:00Z"},{id:4,name:"Bot Facebook Messenger",channel:"Facebook",leadsAttended:67,activeConversations:8,responseRate:83.1,status:"offline",lastActivity:"2024-01-15T08:20:00Z"},{id:5,name:"Suporte Telegram",channel:"Telegram",leadsAttended:34,activeConversations:5,responseRate:96.7,status:"online",lastActivity:"2024-01-15T11:00:00Z"}],O1=[{id:1,leadName:"Maria Silva",channel:"WhatsApp",agentId:1,status:"quente",intent:"orçamento",lastMessage:"Gostaria de saber o preço do plano premium",lastMessageTime:"2024-01-15T11:30:00Z",messagesCount:8,phone:"+5511999887766",email:"<EMAIL>",messages:[{id:1,sender:"lead",content:"Olá, gostaria de informações sobre seus serviços",timestamp:"2024-01-15T10:00:00Z"},{id:2,sender:"agent",content:"Olá Maria! Fico feliz em ajudar. Que tipo de serviço você está procurando?",timestamp:"2024-01-15T10:01:00Z"},{id:3,sender:"lead",content:"Preciso de uma solução para automação de vendas",timestamp:"2024-01-15T10:05:00Z"},{id:4,sender:"agent",content:"Perfeito! Temos várias opções. Você já tem alguma ferramenta atualmente?",timestamp:"2024-01-15T10:06:00Z"},{id:5,sender:"lead",content:"Não, seria minha primeira vez usando algo assim",timestamp:"2024-01-15T10:10:00Z"},{id:6,sender:"agent",content:"Entendi! Vou te mostrar nosso plano ideal para iniciantes. Posso enviar um material?",timestamp:"2024-01-15T10:11:00Z"},{id:7,sender:"lead",content:"Sim, por favor! E gostaria de saber o preço também",timestamp:"2024-01-15T10:15:00Z"},{id:8,sender:"lead",content:"Gostaria de saber o preço do plano premium",timestamp:"2024-01-15T11:30:00Z"}]},{id:2,leadName:"João Santos",channel:"Instagram",agentId:2,status:"morno",intent:"suporte",lastMessage:"Estou com dificuldades para configurar",lastMessageTime:"2024-01-15T10:45:00Z",messagesCount:5,phone:"+5511888776655",email:"<EMAIL>",messages:[{id:1,sender:"lead",content:"Oi, comprei o produto mas estou com dificuldades",timestamp:"2024-01-15T09:30:00Z"},{id:2,sender:"agent",content:"Olá João! Vou te ajudar. Qual dificuldade específica você está enfrentando?",timestamp:"2024-01-15T09:31:00Z"},{id:3,sender:"lead",content:"Não consigo fazer a integração com meu sistema",timestamp:"2024-01-15T09:35:00Z"},{id:4,sender:"agent",content:"Entendo. Que sistema você está usando? Posso te enviar um tutorial específico.",timestamp:"2024-01-15T09:36:00Z"},{id:5,sender:"lead",content:"Estou com dificuldades para configurar",timestamp:"2024-01-15T10:45:00Z"}]},{id:3,leadName:"Ana Costa",channel:"Site",agentId:3,status:"quente",intent:"agendamento",lastMessage:"Posso agendar uma demonstração?",lastMessageTime:"2024-01-15T11:20:00Z",messagesCount:6,phone:"+5511777665544",email:"<EMAIL>",messages:[{id:1,sender:"lead",content:"Olá, vi vocês no Google e gostei do que fazem",timestamp:"2024-01-15T10:30:00Z"},{id:2,sender:"agent",content:"Olá Ana! Muito obrigado pelo interesse. Como posso ajudá-la?",timestamp:"2024-01-15T10:31:00Z"},{id:3,sender:"lead",content:"Gostaria de ver uma demonstração do produto",timestamp:"2024-01-15T10:35:00Z"},{id:4,sender:"agent",content:"Claro! Seria ótimo mostrar como nossa solução pode ajudar sua empresa.",timestamp:"2024-01-15T10:36:00Z"},{id:5,sender:"lead",content:"Que horários vocês têm disponível esta semana?",timestamp:"2024-01-15T11:00:00Z"},{id:6,sender:"lead",content:"Posso agendar uma demonstração?",timestamp:"2024-01-15T11:20:00Z"}]},{id:4,leadName:"Pedro Oliveira",channel:"Facebook",agentId:4,status:"frio",intent:"informação",lastMessage:"Obrigado pelas informações",lastMessageTime:"2024-01-15T08:30:00Z",messagesCount:3,phone:"+5511666554433",email:"<EMAIL>",messages:[{id:1,sender:"lead",content:"Oi, vi o anúncio de vocês no Facebook",timestamp:"2024-01-15T08:00:00Z"},{id:2,sender:"agent",content:"Olá Pedro! Que bom que nos encontrou. Em que posso ajudá-lo?",timestamp:"2024-01-15T08:01:00Z"},{id:3,sender:"lead",content:"Obrigado pelas informações",timestamp:"2024-01-15T08:30:00Z"}]}],D1={conversionByChannel:[{name:"WhatsApp",value:35,color:"#25D366"},{name:"Instagram",value:25,color:"#E4405F"},{name:"Site",value:30,color:"#3b82f6"},{name:"Facebook",value:10,color:"#1877F2"}],revenueOverTime:[{month:"Jan",revenue:12e3},{month:"Fev",revenue:15e3},{month:"Mar",revenue:18e3},{month:"Abr",revenue:16e3},{month:"Mai",revenue:22e3},{month:"Jun",revenue:25e3}],leadsOverTime:[{month:"Jan",leads:120},{month:"Fev",leads:150},{month:"Mar",leads:180},{month:"Abr",leads:160},{month:"Mai",leads:220},{month:"Jun",leads:250}]},Xs=i=>new Promise(r=>setTimeout(r,i)),ih=async()=>(await Xs(500),z1),sh=async()=>(await Xs(300),M1),rh=async(i={})=>{await Xs(400);let r=[...O1];return i.channel&&(r=r.filter(c=>c.channel===i.channel)),i.status&&(r=r.filter(c=>c.status===i.status)),i.agentId&&(r=r.filter(c=>c.agentId===i.agentId)),r},uh=async()=>(await Xs(600),D1);var o0={};const R1=o0.REACT_APP_API_URL||"http://localhost:3001",C1=o0.REACT_APP_N8N_WEBHOOK_URL||"",Vt=Ue.create({baseURL:R1,timeout:1e4,headers:{"Content-Type":"application/json"}});Vt.interceptors.request.use(i=>{const r=localStorage.getItem("authToken");return r&&(i.headers.Authorization=`Bearer ${r}`),i},i=>Promise.reject(i));Vt.interceptors.response.use(i=>i,i=>{var r;return console.error("API Error:",i),((r=i.response)==null?void 0:r.status)===401&&(localStorage.removeItem("authToken"),window.location.href="/login"),Promise.reject(i)});const La=!C1,U1=async(i={})=>{if(La)return await ih();try{return(await Vt.get("/api/kpis",{params:i})).data}catch(r){return console.error("Erro ao buscar KPIs:",r),await ih()}},f0=async()=>{if(La)return await sh();try{return(await Vt.get("/api/agents")).data}catch(i){return console.error("Erro ao buscar agentes:",i),await sh()}},k1=async(i={})=>{if(La)return await rh(i);try{return(await Vt.get("/api/conversations",{params:i})).data}catch(r){return console.error("Erro ao buscar conversas:",r),await rh(i)}},_1=async(i="all",r={})=>{if(La)return await uh();try{return(await Vt.get("/api/charts",{params:{type:i,...r}})).data}catch(c){return console.error("Erro ao buscar dados dos gráficos:",c),await uh()}},B1=async(i,r)=>{if(La)return console.log("Mock: Enviando WhatsApp para",i,":",r),{success:!0,message:"Mensagem enviada com sucesso (mock)"};try{return(await Vt.post("/api/whatsapp/send",{phone:i,message:r})).data}catch(c){throw console.error("Erro ao enviar WhatsApp:",c),c}},q1=async(i,r,c="")=>{if(La)return console.log("Mock: Agendando call para lead",i,"em",r),{success:!0,message:"Call agendada com sucesso (mock)"};try{return(await Vt.post("/api/schedule",{leadId:i,datetime:r,notes:c})).data}catch(u){throw console.error("Erro ao agendar call:",u),u}},H1=async(i,r="medium")=>{if(La)return console.log("Mock: Marcando conversa",i,"como oportunidade"),{success:!0,message:"Marcado como oportunidade (mock)"};try{return(await Vt.patch(`/api/conversations/${i}/opportunity`,{priority:r})).data}catch(c){throw console.error("Erro ao marcar como oportunidade:",c),c}},L1=async(i,r)=>{if(La)return console.log("Mock: Atualizando status do agente",i,"para",r),{success:!0};try{return(await Vt.patch(`/api/agents/${i}/status`,{status:r})).data}catch(c){throw console.error("Erro ao atualizar status do agente:",c),c}},G1=()=>{const[i,r]=se.useState(!0),[c,u]=se.useState({}),[d,h]=se.useState([]),[g,b]=se.useState({}),w=lb();se.useEffect(()=>{j()},[]);const j=async()=>{r(!0);try{const[M,k,Z]=await Promise.all([U1(),f0(),_1()]);u(M),h(k),b(Z)}catch(M){console.error("Erro ao carregar dados do dashboard:",M)}finally{r(!1)}},v=[{title:"Custo por Lead (CPL)",value:qa(c.cpl||0),trend:"down",trendValue:5.2,icon:By,color:"blue"},{title:"Custo de Aquisição (CAC)",value:qa(c.cac||0),trend:"down",trendValue:8.1,icon:Rm,color:"green"},{title:"Custo por Clique (CPC)",value:qa(c.cpc||0),trend:"up",trendValue:2.3,icon:qy,color:"purple"},{title:"Taxa de Conversão",value:pc(c.conversionRate||0),trend:"up",trendValue:12.5,icon:Hy,color:"orange"}];return o.jsxs(fe,{children:[o.jsx(fe.Header,{title:"Dashboard CRM",subtitle:"Visão geral do desempenho dos seus agentes de IA",actions:[o.jsx(Be,{variant:"secondary",size:"sm",leftIcon:o.jsx(fh,{size:16}),children:"Últimos 30 dias"},"period"),o.jsx(Be,{variant:"ghost",size:"sm",leftIcon:o.jsx(dh,{size:16,className:i?"animate-spin":""}),onClick:j,disabled:i,children:"Atualizar"},"refresh")]}),o.jsx(fe.Section,{title:"Indicadores Principais",icon:ni,children:o.jsx(fe.Grid,{cols:4,children:v.map((M,k)=>o.jsx(np,{...M,loading:i},k))})}),o.jsx(fe.Section,{title:"Análise de Performance",icon:dc,children:o.jsx($x,{chartData:g,loading:i})}),o.jsxs(fe.Section,{title:"Agentes de IA",icon:Cm,children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsx("div",{}),o.jsx(Be,{variant:"ghost",size:"sm",rightIcon:o.jsx(Ly,{size:16}),onClick:()=>w("/agents"),children:"Ver todos"})]}),o.jsx(fe.Grid,{cols:3,children:d.slice(0,3).map(M=>o.jsxs(fe.Card,{hover:!0,children:[o.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[o.jsx("div",{className:"w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center",children:o.jsx(Cm,{size:18,className:"text-blue-600 dark:text-blue-400"})}),o.jsxs("div",{children:[o.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white text-sm",children:M.name}),o.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:M.channel})]})]}),o.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-3",children:[o.jsxs("div",{children:[o.jsx("div",{className:"text-lg font-bold text-blue-600 dark:text-blue-400",children:Ha(M.leadsAttended)}),o.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Leads"})]}),o.jsxs("div",{children:[o.jsx("div",{className:"text-lg font-bold text-green-600 dark:text-green-400",children:Ha(M.activeConversations)}),o.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Conversas"})]})]}),o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center gap-1",children:[o.jsx("div",{className:`w-2 h-2 rounded-full ${M.status==="online"?"bg-green-500":"bg-gray-400"}`}),o.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:M.status==="online"?"Online":"Offline"})]}),o.jsx("div",{className:"text-sm font-semibold text-gray-900 dark:text-white",children:pc(M.responseRate)})]})]},M.id))})]}),o.jsx(fe.Section,{children:o.jsxs(fe.Grid,{cols:3,children:[o.jsxs(fe.Card,{className:"text-center",children:[o.jsx("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mx-auto mb-3",children:o.jsx(Ac,{size:24,className:"text-blue-600 dark:text-blue-400"})}),o.jsx("div",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-1",children:i?"-":Ha(c.activeLeads||0)}),o.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Leads Ativos"})]}),o.jsxs(fe.Card,{className:"text-center",children:[o.jsx("div",{className:"w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mx-auto mb-3",children:o.jsx(dc,{size:24,className:"text-green-600 dark:text-green-400"})}),o.jsx("div",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-1",children:i?"-":Ha(c.totalConversations||0)}),o.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Total de Conversas"})]}),o.jsxs(fe.Card,{className:"text-center",children:[o.jsx("div",{className:"w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mx-auto mb-3",children:o.jsx(Rm,{size:24,className:"text-purple-600 dark:text-purple-400"})}),o.jsx("div",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-1",children:i?"-":qa(c.revenuePerAgent||0)}),o.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Receita por Agente"})]})]})})]})},ch=({agent:i,onViewDetails:r,onToggleStatus:c,onSettings:u,onEdit:d,loading:h=!1})=>{const[g,b]=se.useState(!1);if(h)return o.jsx(fe.Card,{children:o.jsxs("div",{className:"animate-pulse",children:[o.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[o.jsx("div",{className:"w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg"}),o.jsxs("div",{children:[o.jsx("div",{className:"w-24 h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"}),o.jsx("div",{className:"w-16 h-3 bg-gray-200 dark:bg-gray-700 rounded"})]})]}),o.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[o.jsx("div",{className:"w-full h-12 bg-gray-200 dark:bg-gray-700 rounded"}),o.jsx("div",{className:"w-full h-12 bg-gray-200 dark:bg-gray-700 rounded"})]}),o.jsx("div",{className:"w-full h-8 bg-gray-200 dark:bg-gray-700 rounded"})]})});const{id:w,name:j,channel:v,status:M,leadsAttended:k,activeConversations:Z,responseRate:U,lastActivity:_}=i,q=Uc(v),$=M==="online"?"bg-green-500":"bg-gray-400";return o.jsxs(fe.Card,{hover:!0,className:"relative",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center text-lg",children:q}),o.jsx("div",{className:`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800 ${$}`})]}),o.jsxs("div",{children:[o.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white text-sm",children:j}),o.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:v})]})]}),o.jsxs("div",{className:"relative",children:[o.jsx("button",{onClick:()=>b(!g),className:"p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors",children:o.jsx(Gy,{size:16,className:"text-gray-400"})}),g&&o.jsxs("div",{className:"absolute right-0 top-8 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-10 min-w-[120px]",children:[o.jsxs("button",{onClick:()=>{d==null||d(i),b(!1)},className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300",children:[o.jsx(mh,{size:14}),"Editar"]}),o.jsxs("button",{onClick:()=>{r==null||r(i),b(!1)},className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300",children:[o.jsx(Yy,{size:14}),"Ver detalhes"]}),o.jsxs("button",{onClick:()=>{u==null||u(i),b(!1)},className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300",children:[o.jsx(Us,{size:14}),"Configurar"]}),o.jsxs("button",{onClick:()=>{c==null||c(i),b(!1)},className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300",children:[o.jsx(Xy,{size:14}),M==="online"?"Desativar":"Ativar"]})]})]})]}),o.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[o.jsxs("div",{className:"text-center p-2 bg-blue-50 dark:bg-blue-900/10 rounded-lg",children:[o.jsx("div",{className:"text-lg font-bold text-blue-600 dark:text-blue-400",children:Ha(k)}),o.jsx("div",{className:"text-xs text-blue-600 dark:text-blue-400",children:"Leads"})]}),o.jsxs("div",{className:"text-center p-2 bg-green-50 dark:bg-green-900/10 rounded-lg",children:[o.jsx("div",{className:"text-lg font-bold text-green-600 dark:text-green-400",children:Ha(Z)}),o.jsx("div",{className:"text-xs text-green-600 dark:text-green-400",children:"Conversas"})]})]}),o.jsxs("div",{className:"mb-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-2",children:[o.jsx("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Taxa de Resposta"}),o.jsx("span",{className:"text-sm font-semibold text-gray-900 dark:text-white",children:pc(U)})]}),o.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:o.jsx("div",{className:"h-2 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300",style:{width:`${Math.min(U,100)}%`}})})]}),o.jsxs("div",{className:"flex items-center justify-between text-xs",children:[o.jsxs("div",{className:"flex items-center gap-1 text-gray-500 dark:text-gray-400",children:[o.jsx(Zy,{size:12}),o.jsx("span",{children:"Última atividade"})]}),o.jsx("span",{className:"text-gray-600 dark:text-gray-300",children:Bh(_)})]}),o.jsx("div",{className:"mt-4",children:o.jsx(Be,{variant:"primary",size:"sm",onClick:()=>r==null?void 0:r(i),className:"w-full",children:"Ver Conversas"})})]})},Y1=({agents:i,loading:r=!1,onViewDetails:c,onToggleStatus:u,onSettings:d,onEdit:h,className:g=""})=>r?o.jsx(fe.Grid,{cols:3,className:g,children:[...Array(6)].map((b,w)=>o.jsx(ch,{loading:!0},w))}):o.jsx(fe.Grid,{cols:3,className:g,children:i.map(b=>o.jsx(ch,{agent:b,onViewDetails:c,onToggleStatus:u,onSettings:d,onEdit:h},b.id))}),X1=({conversation:i,isOpen:r,onClose:c})=>{const[u,d]=se.useState(!1),[h,g]=se.useState(""),[b,w]=se.useState(""),[j,v]=se.useState("");if(!r||!i)return null;const{id:M,leadName:k,channel:Z,status:U,intent:_,lastMessage:q,lastMessageTime:$,messagesCount:ne,phone:J,email:ue,messages:re=[]}=i,de=Vx(U),je=Uc(Z),Y=async()=>{if(!(!h.trim()||!J)){d(!0);try{await B1(J,h),g(""),alert("Mensagem enviada com sucesso!")}catch{alert("Erro ao enviar mensagem")}finally{d(!1)}}},Ee=async()=>{if(b){d(!0);try{await q1(M,b,j),w(""),v(""),alert("Call agendada com sucesso!")}catch{alert("Erro ao agendar call")}finally{d(!1)}}},Mt=async()=>{d(!0);try{await H1(M),alert("Marcado como oportunidade!")}catch{alert("Erro ao marcar como oportunidade")}finally{d(!1)}};return o.jsx("div",{className:"modal-overlay",onClick:c,children:o.jsxs("div",{className:"modal-content",onClick:he=>he.stopPropagation(),children:[o.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-xl",children:je}),o.jsx("div",{className:"absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white",style:{backgroundColor:de}})]}),o.jsxs("div",{children:[o.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:k}),o.jsxs("p",{className:"text-sm text-gray-500",children:[Z," • ",Kx(_)]})]})]}),o.jsx("button",{onClick:c,className:"p-2 hover:bg-gray-100 rounded-lg",children:o.jsx(Tc,{size:20})})]}),o.jsxs("div",{className:"p-6 border-b border-gray-200",children:[o.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Informações do Lead"}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx(hh,{size:16,className:"text-gray-500"}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm text-gray-500",children:"Nome"}),o.jsx("p",{className:"font-medium",children:k})]})]}),J&&o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx(Um,{size:16,className:"text-gray-500"}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm text-gray-500",children:"Telefone"}),o.jsx("p",{className:"font-medium",children:Zx(J)})]})]}),ue&&o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx(Qy,{size:16,className:"text-gray-500"}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),o.jsx("p",{className:"font-medium",children:ue})]})]}),o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx(Vy,{size:16,className:"text-gray-500"}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm text-gray-500",children:"Total de Mensagens"}),o.jsx("p",{className:"font-medium",children:ne})]})]})]})]}),o.jsxs("div",{className:"p-6 border-b border-gray-200",children:[o.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Histórico de Mensagens"}),o.jsx("div",{className:"max-h-64 overflow-y-auto space-y-3",children:re.map(he=>o.jsx("div",{className:`flex ${he.sender==="agent"?"justify-end":"justify-start"}`,children:o.jsxs("div",{className:`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${he.sender==="agent"?"bg-blue-500 text-white":"bg-gray-100 text-gray-900"}`,children:[o.jsx("p",{className:"text-sm",children:he.content}),o.jsx("p",{className:`text-xs mt-1 ${he.sender==="agent"?"text-blue-100":"text-gray-500"}`,children:Bh(he.timestamp)})]})},he.id))})]}),o.jsxs("div",{className:"p-6",children:[o.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Ações Rápidas"}),J&&o.jsxs("div",{className:"mb-6",children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enviar WhatsApp"}),o.jsxs("div",{className:"flex gap-2",children:[o.jsx("input",{type:"text",value:h,onChange:he=>g(he.target.value),placeholder:"Digite sua mensagem...",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),o.jsxs("button",{onClick:Y,disabled:u||!h.trim(),className:"btn btn-primary",children:[o.jsx(Um,{size:16}),"Enviar"]})]})]}),o.jsxs("div",{className:"mb-6",children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Agendar Call"}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("input",{type:"datetime-local",value:b,onChange:he=>w(he.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),o.jsx("input",{type:"text",value:j,onChange:he=>v(he.target.value),placeholder:"Observações (opcional)",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),o.jsxs("button",{onClick:Ee,disabled:u||!b,className:"btn btn-secondary",children:[o.jsx(fh,{size:16}),"Agendar"]})]})]}),o.jsxs("div",{className:"flex gap-3",children:[o.jsxs("button",{onClick:Mt,disabled:u,className:"btn btn-primary flex-1",children:[o.jsx(Ky,{size:16}),"Marcar como Oportunidade"]}),J&&o.jsxs("a",{href:`https://wa.me/${J.replace(/\D/g,"")}`,target:"_blank",rel:"noopener noreferrer",className:"btn btn-secondary",children:[o.jsx(Jy,{size:16}),"Abrir WhatsApp"]})]})]})]})})},Os=se.forwardRef(({children:i,variant:r="default",padding:c="default",hover:u=!1,animate:d=!0,className:h,onClick:g,...b},w)=>{const j=Fe("rounded-2xl overflow-hidden","dark:bg-gray-800 dark:border-gray-700",Rc.default,Dc.card[r],{"p-6":c==="default","p-4":c==="sm","p-8":c==="lg","p-0":c==="none"},u&&"hover:scale-[1.02] hover:-translate-y-1",g&&"cursor-pointer",h),v=i;return d?o.jsx($e.div,{ref:w,className:j,onClick:g,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,ease:"easeOut"},whileHover:u?{scale:1.02,y:-4}:{},...b,children:v}):o.jsx("div",{ref:w,className:j,onClick:g,...b,children:v})});Os.displayName="Card";const Z1=({children:i,variant:r="gray",size:c="md",dot:u=!1,pulse:d=!1,animate:h=!0,className:g,...b})=>{const w=Fe("inline-flex items-center gap-1.5 font-medium rounded-full",Rc.default,Dc.badge[r],{"px-2 py-0.5 text-xs":c==="sm","px-2.5 py-1 text-xs":c==="md","px-3 py-1.5 text-sm":c==="lg"},g),j=o.jsxs(o.Fragment,{children:[u&&o.jsx("span",{className:Fe("w-1.5 h-1.5 rounded-full",{"bg-primary-500":r==="primary","bg-success-500":r==="success","bg-warning-500":r==="warning","bg-danger-500":r==="danger","bg-gray-500":r==="gray"},d&&"animate-pulse")}),i]});return h?o.jsx($e.span,{className:w,initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.2,ease:"easeOut"},...b,children:j}):o.jsx("span",{className:w,...b,children:j})},oh=({status:i,...r})=>{const c={online:{variant:"success",children:"Online",dot:!0,pulse:!0},offline:{variant:"gray",children:"Offline",dot:!0},busy:{variant:"warning",children:"Ocupado",dot:!0},away:{variant:"warning",children:"Ausente",dot:!0}},u=c[i]||c.offline;return o.jsx(Z1,{...u,...r})},Q1=({agent:i,isOpen:r,onClose:c,onSave:u,onDelete:d})=>{const[h,g]=se.useState(i||{}),[b,w]=se.useState(!1),[j,v]=se.useState(!1);if(!r||!i)return null;const M=async()=>{v(!0);try{await(u==null?void 0:u(h)),w(!1)}catch(_){console.error("Erro ao salvar agente:",_)}finally{v(!1)}},k=async()=>{if(window.confirm("Tem certeza que deseja excluir este agente?"))try{await(d==null?void 0:d(i.id)),c()}catch(_){console.error("Erro ao excluir agente:",_)}},Z=()=>{const _={...h,id:Date.now(),name:`${h.name} (Cópia)`,status:"offline"};u==null||u(_)},U=Uc(h.channel);return o.jsx(Ds,{children:o.jsx($e.div,{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:c,children:o.jsxs($e.div,{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-y-auto mx-4",initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},onClick:_=>_.stopPropagation(),children:[o.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-lg shadow-lg",children:U}),o.jsxs("div",{children:[o.jsx("h2",{className:"text-lg font-bold text-gray-900 dark:text-white",children:h.name}),o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(oh,{status:h.status,size:"sm"}),o.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:h.channel})]})]})]}),o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(Be,{variant:"ghost",size:"sm",onClick:()=>w(!b),leftIcon:o.jsx(mh,{size:14}),className:"text-xs",children:b?"Cancelar":"Editar"}),o.jsx(Be,{variant:"ghost",size:"sm",onClick:c,children:o.jsx(Tc,{size:18})})]})]}),o.jsx("div",{className:"p-4 space-y-4",children:o.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[o.jsxs(Os,{className:"p-4",children:[o.jsxs("h3",{className:"text-base font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2",children:[o.jsx(hh,{size:18}),"Informações Básicas"]}),o.jsxs("div",{className:"space-y-3",children:[o.jsxs("div",{children:[o.jsx("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Nome do Agente"}),b?o.jsx("input",{type:"text",value:h.name||"",onChange:_=>g({...h,name:_.target.value}),className:"w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"}):o.jsx("p",{className:"text-gray-900 dark:text-white font-medium text-sm",children:h.name})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Canal"}),b?o.jsxs("select",{value:h.channel||"",onChange:_=>g({...h,channel:_.target.value}),className:"w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[o.jsx("option",{value:"WhatsApp",children:"WhatsApp"}),o.jsx("option",{value:"Instagram",children:"Instagram"}),o.jsx("option",{value:"Facebook",children:"Facebook"}),o.jsx("option",{value:"Site",children:"Site"}),o.jsx("option",{value:"Telegram",children:"Telegram"}),o.jsx("option",{value:"Email",children:"Email"})]}):o.jsx("p",{className:"text-gray-900 dark:text-white font-medium text-sm",children:h.channel})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Status"}),b?o.jsxs("select",{value:h.status||"",onChange:_=>g({...h,status:_.target.value}),className:"w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[o.jsx("option",{value:"online",children:"Online"}),o.jsx("option",{value:"offline",children:"Offline"}),o.jsx("option",{value:"busy",children:"Ocupado"}),o.jsx("option",{value:"away",children:"Ausente"})]}):o.jsx(oh,{status:h.status})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Taxa de Resposta"}),b?o.jsx("input",{type:"number",min:"0",max:"100",step:"0.1",value:h.responseRate||"",onChange:_=>g({...h,responseRate:parseFloat(_.target.value)}),className:"w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"}):o.jsxs("p",{className:"text-gray-900 dark:text-white font-medium text-sm",children:[h.responseRate,"%"]})]})]})]}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs(Os,{className:"p-4",children:[o.jsxs("h3",{className:"text-base font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2",children:[o.jsx(Ec,{size:18}),"Métricas de Performance"]}),o.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-lg font-bold text-blue-600 dark:text-blue-400",children:h.leadsAttended||0}),o.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Leads"})]}),o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-lg font-bold text-green-600 dark:text-green-400",children:h.activeConversations||0}),o.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Conversas"})]}),o.jsxs("div",{className:"text-center",children:[o.jsxs("div",{className:"text-lg font-bold text-purple-600 dark:text-purple-400",children:[h.responseRate||0,"%"]}),o.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Taxa"})]})]})]}),o.jsxs(Os,{className:"p-4",children:[o.jsxs("h3",{className:"text-base font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2",children:[o.jsx(Us,{size:18}),"Configurações"]}),o.jsxs("div",{className:"space-y-3",children:[o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{children:[o.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Auto-resposta"}),o.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Responder automaticamente"})]}),o.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[o.jsx("input",{type:"checkbox",className:"sr-only peer",defaultChecked:!0}),o.jsx("div",{className:"w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{children:[o.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Notificações"}),o.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Receber notificações"})]}),o.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[o.jsx("input",{type:"checkbox",className:"sr-only peer",defaultChecked:!0}),o.jsx("div",{className:"w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]})]})]})]})}),o.jsxs("div",{className:"flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(Be,{variant:"ghost",size:"sm",onClick:Z,leftIcon:o.jsx(Wy,{size:14}),className:"text-xs",children:"Duplicar"}),o.jsx(Be,{variant:"ghost",size:"sm",onClick:k,leftIcon:o.jsx(Fy,{size:14}),className:"text-red-600 hover:text-red-700 hover:bg-red-50 text-xs",children:"Excluir"})]}),o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(Be,{variant:"secondary",size:"sm",onClick:c,className:"text-xs",children:"Cancelar"}),b&&o.jsx(Be,{variant:"primary",size:"sm",onClick:M,loading:j,leftIcon:o.jsx($y,{size:14}),className:"text-xs",children:"Salvar"})]})]})]})})})},V1=()=>{const[i,r]=se.useState([]),[c,u]=se.useState(!0),[d,h]=se.useState(""),[g,b]=se.useState("all"),[w,j]=se.useState("all"),[v,M]=se.useState(null),[k,Z]=se.useState(!1),[U,_]=se.useState(null),[q,$]=se.useState(!1),[ne,J]=se.useState(null);se.useEffect(()=>{ue()},[]);const ue=async()=>{u(!0),J(null);try{const X=await f0();r(X)}catch(X){console.error("Erro ao carregar agentes:",X),J("Erro ao carregar agentes. Tente novamente.")}finally{u(!1)}},re=async X=>{try{const A=await k1(X.id);A&&A.length>0?(M(A[0]),Z(!0)):alert("Nenhuma conversa encontrada para este agente.")}catch(A){console.error("Erro ao carregar conversas:",A),alert("Erro ao carregar conversas.")}},de=async X=>{try{const A=X.status==="online"?"offline":"online";await L1(X.id,A),r(B=>B.map(C=>C.id===X.id?{...C,status:A}:C))}catch(A){console.error("Erro ao atualizar status:",A),alert("Erro ao atualizar status do agente.")}},je=X=>{console.log("Configurações do agente:",X),alert(`Configurações do ${X.name} - Em desenvolvimento`)},Y=X=>{_(X),$(!0)},Ee=async X=>{try{r(A=>A.map(B=>B.id===X.id?X:B)),$(!1),_(null)}catch(A){console.error("Erro ao salvar agente:",A),alert("Erro ao salvar agente.")}},Mt=async X=>{try{r(A=>A.filter(B=>B.id!==X)),$(!1),_(null)}catch(A){console.error("Erro ao deletar agente:",A),alert("Erro ao deletar agente.")}},he=()=>{const X=`data:text/csv;charset=utf-8,Nome,Canal,Status,Leads Atendidos,Conversas Ativas,Taxa de Resposta
`+i.map(C=>`${C.name},${C.channel},${C.status},${C.leadsAttended},${C.activeConversations},${C.responseRate}%`).join(`
`),A=encodeURI(X),B=document.createElement("a");B.setAttribute("href",A),B.setAttribute("download","agentes_crm.csv"),document.body.appendChild(B),B.click(),document.body.removeChild(B)},qe=()=>[...new Set(i.map(X=>X.channel))],Kt=i.filter(X=>{const A=X.name.toLowerCase().includes(d.toLowerCase())||X.channel.toLowerCase().includes(d.toLowerCase()),B=g==="all"||X.status===g,C=w==="all"||X.channel===w;return A&&B&&C}),lt={total:i.length,online:i.filter(X=>X.status==="online").length,totalConversations:i.reduce((X,A)=>X+A.activeConversations,0),avgResponseRate:i.length>0?(i.reduce((X,A)=>X+A.responseRate,0)/i.length).toFixed(1):0};return o.jsxs(fe,{children:[o.jsx(fe.Header,{title:"Agentes de IA",subtitle:"Gerencie e monitore seus agentes de atendimento automatizado",actions:[o.jsx(Be,{variant:"secondary",size:"sm",onClick:ue,disabled:c,leftIcon:o.jsx(dh,{size:16,className:c?"animate-spin":""}),children:"Atualizar"},"refresh"),o.jsx(Be,{variant:"secondary",size:"sm",onClick:he,leftIcon:o.jsx(Py,{size:16}),children:"Exportar"},"export"),o.jsx(Be,{variant:"primary",size:"sm",leftIcon:o.jsx(Iy,{size:16}),children:"Novo Agente"},"new")]}),o.jsx(fe.Section,{children:o.jsxs(fe.Grid,{cols:4,children:[o.jsxs(fe.Card,{className:"text-center",children:[o.jsx("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1",children:lt.total}),o.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Total de Agentes"})]}),o.jsxs(fe.Card,{className:"text-center",children:[o.jsx("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400 mb-1",children:lt.online}),o.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Online"})]}),o.jsxs(fe.Card,{className:"text-center",children:[o.jsx("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1",children:lt.totalConversations}),o.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Conversas Ativas"})]}),o.jsxs(fe.Card,{className:"text-center",children:[o.jsxs("div",{className:"text-2xl font-bold text-orange-600 dark:text-orange-400 mb-1",children:[lt.avgResponseRate,"%"]}),o.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Taxa Média"})]})]})}),o.jsx(fe.Section,{children:o.jsx(fe.Card,{children:o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[o.jsxs("div",{className:"relative",children:[o.jsx(eb,{size:16,className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),o.jsx("input",{type:"text",placeholder:"Buscar agentes...",value:d,onChange:X=>h(X.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),o.jsxs("select",{value:g,onChange:X=>b(X.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[o.jsx("option",{value:"all",children:"Todos os Status"}),o.jsx("option",{value:"online",children:"Online"}),o.jsx("option",{value:"offline",children:"Offline"})]}),o.jsxs("select",{value:w,onChange:X=>j(X.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[o.jsx("option",{value:"all",children:"Todos os Canais"}),qe().map(X=>o.jsx("option",{value:X,children:X},X))]}),o.jsx(Be,{variant:"secondary",onClick:()=>{h(""),b("all"),j("all")},leftIcon:o.jsx(tb,{size:16}),children:"Limpar Filtros"})]})})}),o.jsx(fe.Section,{children:ne?o.jsxs("div",{className:"text-center py-8",children:[o.jsx("p",{className:"text-red-600 dark:text-red-400 mb-4",children:ne}),o.jsx(Be,{onClick:ue,variant:"primary",children:"Tentar Novamente"})]}):o.jsx(Y1,{agents:Kt,loading:c,onViewDetails:re,onToggleStatus:de,onSettings:je,onEdit:Y})}),o.jsx(X1,{conversation:v,isOpen:k,onClose:()=>{Z(!1),M(null)}}),o.jsx(Q1,{agent:U,isOpen:q,onClose:()=>{$(!1),_(null)},onSave:Ee,onDelete:Mt})]})},K1=()=>{const i=ph(),r=[{path:"/",icon:gh,label:"Dashboard",exact:!0},{path:"/agents",icon:Ac,label:"Agentes"},{path:"/conversations",icon:Ec,label:"Conversas"},{path:"/analytics",icon:ni,label:"Analytics"},{path:"/settings",icon:Us,label:"Config"}],c=(u,d=!1)=>d?i.pathname===u:i.pathname.startsWith(u);return o.jsx("nav",{className:"mobile-nav",children:o.jsx("div",{className:"flex justify-around items-center",children:r.map(({path:u,icon:d,label:h,exact:g})=>o.jsxs(xh,{to:u,className:`mobile-nav-item ${c(u,g)?"active":""}`,children:[o.jsx(d,{size:20}),o.jsx("span",{children:h})]},u))})})},d0=se.createContext(),m0=()=>{const i=se.useContext(d0);if(!i)throw new Error("useTheme must be used within a ThemeProvider");return i},J1=({children:i})=>{const[r,c]=se.useState(()=>{const b=localStorage.getItem("theme");return b||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")});se.useEffect(()=>{const b=window.document.documentElement;b.classList.remove("light","dark"),b.classList.add(r),localStorage.setItem("theme",r)},[r]);const g={theme:r,toggleTheme:()=>{c(b=>b==="light"?"dark":"light")},setLightTheme:()=>c("light"),setDarkTheme:()=>c("dark"),isDark:r==="dark",isLight:r==="light"};return o.jsx(d0.Provider,{value:g,children:i})},W1=()=>{const[i,r]=se.useState({width:typeof window<"u"?window.innerWidth:0,height:typeof window<"u"?window.innerHeight:0}),[c,u]=se.useState("lg");return se.useEffect(()=>{const M=()=>{const k=window.innerWidth,Z=window.innerHeight;r({width:k,height:Z}),k<640?u("xs"):k<768?u("sm"):k<1024?u("md"):k<1280?u("lg"):u("xl")};return M(),window.addEventListener("resize",M),()=>window.removeEventListener("resize",M)},[]),{windowSize:i,breakpoint:c,isMobile:c==="xs"||c==="sm",isTablet:c==="md",isDesktop:c==="lg"||c==="xl",isSmallScreen:c==="xs",isLargeScreen:c==="xl",isAbove:M=>{const k={xs:0,sm:640,md:768,lg:1024,xl:1280};return i.width>=k[M]},isBelow:M=>{const k={xs:640,sm:768,md:1024,lg:1280,xl:1/0};return i.width<k[M]}}},F1=({isOpen:i,onClose:r})=>{const c=ph(),{theme:u,toggleTheme:d}=m0(),h=[{path:"/",icon:gh,label:"Dashboard",exact:!0},{path:"/agents",icon:Ac,label:"Agentes de IA"},{path:"/conversations",icon:Ec,label:"Conversas"},{path:"/analytics",icon:ni,label:"Analytics"},{path:"/settings",icon:Us,label:"Configurações"}],g=(b,w=!1)=>w?c.pathname===b:c.pathname.startsWith(b);return o.jsx(Ds,{children:(i||window.innerWidth>=768)&&o.jsxs($e.div,{className:Fe("fixed left-0 top-0 bottom-0 w-72 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 z-50","flex flex-col p-6 glass-effect","md:relative md:translate-x-0",!i&&"md:block hidden"),initial:{x:-288},animate:{x:0},exit:{x:-288},transition:{type:"spring",damping:25,stiffness:200},children:[o.jsxs("div",{className:"flex items-center justify-between mb-8",children:[o.jsxs($e.div,{className:"flex items-center gap-3",whileHover:{scale:1.05},transition:{type:"spring",stiffness:300},children:[o.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center shadow-lg",children:o.jsx(ni,{size:24,className:"text-white"})}),o.jsx("h1",{className:"text-xl font-bold text-gray-900 dark:text-white gradient-text",children:"CRM AI"})]}),o.jsx(Be,{variant:"ghost",size:"sm",onClick:r,className:"md:hidden",children:o.jsx(Tc,{size:20})})]}),o.jsx("nav",{className:"flex-1 space-y-2",children:h.map(({path:b,icon:w,label:j,exact:v},M)=>o.jsx($e.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:M*.1},children:o.jsxs(xh,{to:b,onClick:r,className:Fe("flex items-center gap-3 px-4 py-3 rounded-xl font-medium transition-all duration-200","hover:bg-gray-100 dark:hover:bg-gray-800",g(b,v)?"bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 shadow-sm":"text-gray-700 dark:text-gray-300"),children:[o.jsx(w,{size:20}),o.jsx("span",{children:j}),g(b,v)&&o.jsx($e.div,{className:"ml-auto w-2 h-2 bg-primary-500 rounded-full",layoutId:"activeIndicator",transition:{type:"spring",stiffness:300,damping:30}})]})},b))}),o.jsx("div",{className:"mb-6",children:o.jsx(Be,{variant:"ghost",onClick:d,className:"w-full justify-start",leftIcon:u==="dark"?o.jsx(yh,{size:20}):o.jsx(bh,{size:20}),children:u==="dark"?"Modo Claro":"Modo Escuro"})}),o.jsx("div",{className:"pt-6 border-t border-gray-200 dark:border-gray-700",children:o.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 text-center space-y-1",children:[o.jsx("p",{className:"font-medium",children:"CRM AI v2.0"}),o.jsx("p",{children:"Powered by n8n"})]})})]})})},$1=({onMenuClick:i})=>{const{theme:r,toggleTheme:c}=m0();return o.jsxs($e.header,{className:"md:hidden bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex items-center justify-between glass-effect",initial:{y:-60},animate:{y:0},transition:{type:"spring",damping:20,stiffness:300},children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center shadow-md",children:o.jsx(ni,{size:20,className:"text-white"})}),o.jsx("h1",{className:"text-xl font-bold text-gray-900 dark:text-white gradient-text",children:"CRM AI"})]}),o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(Be,{variant:"ghost",size:"sm",onClick:c,children:r==="dark"?o.jsx(yh,{size:18}):o.jsx(bh,{size:18})}),o.jsx(Be,{variant:"ghost",size:"sm",onClick:i,children:o.jsx(ab,{size:20})})]})]})},P1=()=>{const[i,r]=ib.useState(!1),{isMobile:c}=W1(),u=()=>r(!1),d=()=>r(!0);return o.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300",children:[o.jsx(F1,{isOpen:i,onClose:u}),o.jsx(Ds,{children:c&&i&&o.jsx($e.div,{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-40",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:u})}),o.jsxs("div",{className:Fe("transition-all duration-300","md:ml-72"),children:[o.jsx($1,{onMenuClick:d}),o.jsx("main",{className:"min-h-screen",children:o.jsx(Ds,{mode:"wait",children:o.jsxs(sb,{children:[o.jsx(In,{path:"/",element:o.jsx(G1,{})}),o.jsx(In,{path:"/agents",element:o.jsx(V1,{})}),o.jsx(In,{path:"/conversations",element:o.jsxs($e.div,{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:[o.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Conversas"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Em desenvolvimento..."})]})}),o.jsx(In,{path:"/analytics",element:o.jsxs($e.div,{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:[o.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Analytics"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Em desenvolvimento..."})]})}),o.jsx(In,{path:"/settings",element:o.jsxs($e.div,{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:[o.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Configurações"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Em desenvolvimento..."})]})})]})})})]}),o.jsx(K1,{})]})};function I1(){return o.jsx(J1,{children:o.jsx(nb,{children:o.jsx(P1,{})})})}pb.createRoot(document.getElementById("root")).render(o.jsx(se.StrictMode,{children:o.jsx(I1,{})}));

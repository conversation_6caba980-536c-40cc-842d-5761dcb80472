import{j as d,m as Ne,M as By,T as Hy,a as ii,U as sl,E as ky,b as Ly,S as Ec,P as Yy,c as Us,C as Gy,D as Xy,d as Zy,e as Qy,A as Mm,f as Om,R as hc,g as sh,h as ui,X as uh,i as Vy,k as Dm,l as Ky,n as Jy,o as Wy,p as Fy,q as $y,r as Py,F as Iy,H as rh,s as ch,t as gc,u as oh,v as fh,w as eb}from"./ui-NtpTKsRi.js";import{r as me,u as dh,L as mh,B as tb,R as ab,a as lb,b as ei}from"./router-CocWSbQb.js";import{r as nb,a as ib}from"./vendor-Csw2ODfV.js";import{c as sb,R as zc,P as ub,a as rb,C as cb,T as Mc,L as hh,b as gh,X as yh,Y as yc,d as bc,e as ob}from"./charts-BEMEAT8Z.js";(function(){const u=document.createElement("link").relList;if(u&&u.supports&&u.supports("modulepreload"))return;for(const f of document.querySelectorAll('link[rel="modulepreload"]'))r(f);new MutationObserver(f=>{for(const h of f)if(h.type==="childList")for(const g of h.addedNodes)g.tagName==="LINK"&&g.rel==="modulepreload"&&r(g)}).observe(document,{childList:!0,subtree:!0});function c(f){const h={};return f.integrity&&(h.integrity=f.integrity),f.referrerPolicy&&(h.referrerPolicy=f.referrerPolicy),f.crossOrigin==="use-credentials"?h.credentials="include":f.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function r(f){if(f.ep)return;f.ep=!0;const h=c(f);fetch(f.href,h)}})();var ic={exports:{}},ti={},sc={exports:{}},uc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rm;function fb(){return Rm||(Rm=1,function(i){function u(z,H){var _=z.length;z.push(H);e:for(;0<_;){var be=_-1>>>1,pe=z[be];if(0<f(pe,H))z[be]=H,z[_]=pe,_=be;else break e}}function c(z){return z.length===0?null:z[0]}function r(z){if(z.length===0)return null;var H=z[0],_=z.pop();if(_!==H){z[0]=_;e:for(var be=0,pe=z.length,Ye=pe>>>1;be<Ye;){var we=2*(be+1)-1,$=z[we],ce=we+1,et=z[ce];if(0>f($,_))ce<pe&&0>f(et,$)?(z[be]=et,z[ce]=_,be=ce):(z[be]=$,z[we]=_,be=we);else if(ce<pe&&0>f(et,_))z[be]=et,z[ce]=_,be=ce;else break e}}return H}function f(z,H){var _=z.sortIndex-H.sortIndex;return _!==0?_:z.id-H.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;i.unstable_now=function(){return h.now()}}else{var g=Date,b=g.now();i.unstable_now=function(){return g.now()-b}}var w=[],j=[],x=1,R=null,C=3,Z=!1,U=!1,q=!1,B=!1,J=typeof setTimeout=="function"?setTimeout:null,I=typeof clearTimeout=="function"?clearTimeout:null,F=typeof setImmediate<"u"?setImmediate:null;function he(z){for(var H=c(j);H!==null;){if(H.callback===null)r(j);else if(H.startTime<=z)r(j),H.sortIndex=H.expirationTime,u(w,H);else break;H=c(j)}}function ue(z){if(q=!1,he(z),!U)if(c(w)!==null)U=!0,fe||(fe=!0,Ee());else{var H=c(j);H!==null&&Fe(ue,H.startTime-z)}}var fe=!1,je=-1,G=5,X=-1;function ie(){return B?!0:!(i.unstable_now()-X<G)}function re(){if(B=!1,fe){var z=i.unstable_now();X=z;var H=!0;try{e:{U=!1,q&&(q=!1,I(je),je=-1),Z=!0;var _=C;try{t:{for(he(z),R=c(w);R!==null&&!(R.expirationTime>z&&ie());){var be=R.callback;if(typeof be=="function"){R.callback=null,C=R.priorityLevel;var pe=be(R.expirationTime<=z);if(z=i.unstable_now(),typeof pe=="function"){R.callback=pe,he(z),H=!0;break t}R===c(w)&&r(w),he(z)}else r(w);R=c(w)}if(R!==null)H=!0;else{var Ye=c(j);Ye!==null&&Fe(ue,Ye.startTime-z),H=!1}}break e}finally{R=null,C=_,Z=!1}H=void 0}}finally{H?Ee():fe=!1}}}var Ee;if(typeof F=="function")Ee=function(){F(re)};else if(typeof MessageChannel<"u"){var We=new MessageChannel,Qt=We.port2;We.port1.onmessage=re,Ee=function(){Qt.postMessage(null)}}else Ee=function(){J(re,0)};function Fe(z,H){je=J(function(){z(i.unstable_now())},H)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(z){z.callback=null},i.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<z?Math.floor(1e3/z):5},i.unstable_getCurrentPriorityLevel=function(){return C},i.unstable_next=function(z){switch(C){case 1:case 2:case 3:var H=3;break;default:H=C}var _=C;C=H;try{return z()}finally{C=_}},i.unstable_requestPaint=function(){B=!0},i.unstable_runWithPriority=function(z,H){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var _=C;C=z;try{return H()}finally{C=_}},i.unstable_scheduleCallback=function(z,H,_){var be=i.unstable_now();switch(typeof _=="object"&&_!==null?(_=_.delay,_=typeof _=="number"&&0<_?be+_:be):_=be,z){case 1:var pe=-1;break;case 2:pe=250;break;case 5:pe=1073741823;break;case 4:pe=1e4;break;default:pe=5e3}return pe=_+pe,z={id:x++,callback:H,priorityLevel:z,startTime:_,expirationTime:pe,sortIndex:-1},_>be?(z.sortIndex=_,u(j,z),c(w)===null&&z===c(j)&&(q?(I(je),je=-1):q=!0,Fe(ue,_-be))):(z.sortIndex=pe,u(w,z),U||Z||(U=!0,fe||(fe=!0,Ee()))),z},i.unstable_shouldYield=ie,i.unstable_wrapCallback=function(z){var H=C;return function(){var _=C;C=H;try{return z.apply(this,arguments)}finally{C=_}}}}(uc)),uc}var Cm;function db(){return Cm||(Cm=1,sc.exports=fb()),sc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Um;function mb(){if(Um)return ti;Um=1;var i=db(),u=nb(),c=ib();function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function h(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function g(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function b(e){if(h(e)!==e)throw Error(r(188))}function w(e){var t=e.alternate;if(!t){if(t=h(e),t===null)throw Error(r(188));return t!==e?null:e}for(var a=e,l=t;;){var n=a.return;if(n===null)break;var s=n.alternate;if(s===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===s.child){for(s=n.child;s;){if(s===a)return b(n),e;if(s===l)return b(n),t;s=s.sibling}throw Error(r(188))}if(a.return!==l.return)a=n,l=s;else{for(var o=!1,m=n.child;m;){if(m===a){o=!0,a=n,l=s;break}if(m===l){o=!0,l=n,a=s;break}m=m.sibling}if(!o){for(m=s.child;m;){if(m===a){o=!0,a=s,l=n;break}if(m===l){o=!0,l=s,a=n;break}m=m.sibling}if(!o)throw Error(r(189))}}if(a.alternate!==l)throw Error(r(190))}if(a.tag!==3)throw Error(r(188));return a.stateNode.current===a?e:t}function j(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=j(e),t!==null)return t;e=e.sibling}return null}var x=Object.assign,R=Symbol.for("react.element"),C=Symbol.for("react.transitional.element"),Z=Symbol.for("react.portal"),U=Symbol.for("react.fragment"),q=Symbol.for("react.strict_mode"),B=Symbol.for("react.profiler"),J=Symbol.for("react.provider"),I=Symbol.for("react.consumer"),F=Symbol.for("react.context"),he=Symbol.for("react.forward_ref"),ue=Symbol.for("react.suspense"),fe=Symbol.for("react.suspense_list"),je=Symbol.for("react.memo"),G=Symbol.for("react.lazy"),X=Symbol.for("react.activity"),ie=Symbol.for("react.memo_cache_sentinel"),re=Symbol.iterator;function Ee(e){return e===null||typeof e!="object"?null:(e=re&&e[re]||e["@@iterator"],typeof e=="function"?e:null)}var We=Symbol.for("react.client.reference");function Qt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===We?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case U:return"Fragment";case B:return"Profiler";case q:return"StrictMode";case ue:return"Suspense";case fe:return"SuspenseList";case X:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case Z:return"Portal";case F:return(e.displayName||"Context")+".Provider";case I:return(e._context.displayName||"Context")+".Consumer";case he:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case je:return t=e.displayName||null,t!==null?t:Qt(e.type)||"Memo";case G:t=e._payload,e=e._init;try{return Qt(e(t))}catch{}}return null}var Fe=Array.isArray,z=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,_={pending:!1,data:null,method:null,action:null},be=[],pe=-1;function Ye(e){return{current:e}}function we(e){0>pe||(e.current=be[pe],be[pe]=null,pe--)}function $(e,t){pe++,be[pe]=e.current,e.current=t}var ce=Ye(null),et=Ye(null),zt=Ye(null),Me=Ye(null);function Ha(e,t){switch($(zt,t),$(et,e),$(ce,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?lm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=lm(t),e=nm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}we(ce),$(ce,e)}function Mt(){we(ce),we(et),we(zt)}function ca(e){e.memoizedState!==null&&$(Me,e);var t=ce.current,a=nm(t,e.type);t!==a&&($(et,e),$(ce,a))}function oa(e){et.current===e&&(we(ce),we(et)),Me.current===e&&(we(Me),Wn._currentValue=_)}var fa=Object.prototype.hasOwnProperty,Vs=i.unstable_scheduleCallback,Ks=i.unstable_cancelCallback,g0=i.unstable_shouldYield,y0=i.unstable_requestPaint,_t=i.unstable_now,b0=i.unstable_getCurrentPriorityLevel,_c=i.unstable_ImmediatePriority,qc=i.unstable_UserBlockingPriority,fi=i.unstable_NormalPriority,p0=i.unstable_LowPriority,Bc=i.unstable_IdlePriority,v0=i.log,x0=i.unstable_setDisableYieldValue,an=null,ct=null;function da(e){if(typeof v0=="function"&&x0(e),ct&&typeof ct.setStrictMode=="function")try{ct.setStrictMode(an,e)}catch{}}var ot=Math.clz32?Math.clz32:j0,S0=Math.log,N0=Math.LN2;function j0(e){return e>>>=0,e===0?32:31-(S0(e)/N0|0)|0}var di=256,mi=4194304;function ka(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function hi(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var n=0,s=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var m=l&134217727;return m!==0?(l=m&~s,l!==0?n=ka(l):(o&=m,o!==0?n=ka(o):a||(a=m&~e,a!==0&&(n=ka(a))))):(m=l&~s,m!==0?n=ka(m):o!==0?n=ka(o):a||(a=l&~e,a!==0&&(n=ka(a)))),n===0?0:t!==0&&t!==n&&(t&s)===0&&(s=n&-n,a=t&-t,s>=a||s===32&&(a&4194048)!==0)?t:n}function ln(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function w0(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Hc(){var e=di;return di<<=1,(di&4194048)===0&&(di=256),e}function kc(){var e=mi;return mi<<=1,(mi&62914560)===0&&(mi=4194304),e}function Js(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function nn(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function A0(e,t,a,l,n,s){var o=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var m=e.entanglements,y=e.expirationTimes,N=e.hiddenUpdates;for(a=o&~a;0<a;){var M=31-ot(a),D=1<<M;m[M]=0,y[M]=-1;var A=N[M];if(A!==null)for(N[M]=null,M=0;M<A.length;M++){var T=A[M];T!==null&&(T.lane&=-536870913)}a&=~D}l!==0&&Lc(e,l,0),s!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=s&~(o&~t))}function Lc(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-ot(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function Yc(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-ot(a),n=1<<l;n&t|e[l]&t&&(e[l]|=t),a&=~n}}function Ws(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Fs(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Gc(){var e=H.p;return e!==0?e:(e=window.event,e===void 0?32:jm(e.type))}function T0(e,t){var a=H.p;try{return H.p=e,t()}finally{H.p=a}}var ma=Math.random().toString(36).slice(2),$e="__reactFiber$"+ma,at="__reactProps$"+ma,rl="__reactContainer$"+ma,$s="__reactEvents$"+ma,E0="__reactListeners$"+ma,z0="__reactHandles$"+ma,Xc="__reactResources$"+ma,sn="__reactMarker$"+ma;function Ps(e){delete e[$e],delete e[at],delete e[$s],delete e[E0],delete e[z0]}function cl(e){var t=e[$e];if(t)return t;for(var a=e.parentNode;a;){if(t=a[rl]||a[$e]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=rm(e);e!==null;){if(a=e[$e])return a;e=rm(e)}return t}e=a,a=e.parentNode}return null}function ol(e){if(e=e[$e]||e[rl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function un(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(r(33))}function fl(e){var t=e[Xc];return t||(t=e[Xc]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ge(e){e[sn]=!0}var Zc=new Set,Qc={};function La(e,t){dl(e,t),dl(e+"Capture",t)}function dl(e,t){for(Qc[e]=t,e=0;e<t.length;e++)Zc.add(t[e])}var M0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Vc={},Kc={};function O0(e){return fa.call(Kc,e)?!0:fa.call(Vc,e)?!1:M0.test(e)?Kc[e]=!0:(Vc[e]=!0,!1)}function gi(e,t,a){if(O0(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function yi(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Vt(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var Is,Jc;function ml(e){if(Is===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);Is=t&&t[1]||"",Jc=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Is+e+Jc}var eu=!1;function tu(e,t){if(!e||eu)return"";eu=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var D=function(){throw Error()};if(Object.defineProperty(D.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(D,[])}catch(T){var A=T}Reflect.construct(e,[],D)}else{try{D.call()}catch(T){A=T}e.call(D.prototype)}}else{try{throw Error()}catch(T){A=T}(D=e())&&typeof D.catch=="function"&&D.catch(function(){})}}catch(T){if(T&&A&&typeof T.stack=="string")return[T.stack,A.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var s=l.DetermineComponentFrameRoot(),o=s[0],m=s[1];if(o&&m){var y=o.split(`
`),N=m.split(`
`);for(n=l=0;l<y.length&&!y[l].includes("DetermineComponentFrameRoot");)l++;for(;n<N.length&&!N[n].includes("DetermineComponentFrameRoot");)n++;if(l===y.length||n===N.length)for(l=y.length-1,n=N.length-1;1<=l&&0<=n&&y[l]!==N[n];)n--;for(;1<=l&&0<=n;l--,n--)if(y[l]!==N[n]){if(l!==1||n!==1)do if(l--,n--,0>n||y[l]!==N[n]){var M=`
`+y[l].replace(" at new "," at ");return e.displayName&&M.includes("<anonymous>")&&(M=M.replace("<anonymous>",e.displayName)),M}while(1<=l&&0<=n);break}}}finally{eu=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?ml(a):""}function D0(e){switch(e.tag){case 26:case 27:case 5:return ml(e.type);case 16:return ml("Lazy");case 13:return ml("Suspense");case 19:return ml("SuspenseList");case 0:case 15:return tu(e.type,!1);case 11:return tu(e.type.render,!1);case 1:return tu(e.type,!0);case 31:return ml("Activity");default:return""}}function Wc(e){try{var t="";do t+=D0(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function vt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Fc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function R0(e){var t=Fc(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,s=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(o){l=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(o){l=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function bi(e){e._valueTracker||(e._valueTracker=R0(e))}function $c(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=Fc(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function pi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var C0=/[\n"\\]/g;function xt(e){return e.replace(C0,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function au(e,t,a,l,n,s,o,m){e.name="",o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?e.type=o:e.removeAttribute("type"),t!=null?o==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+vt(t)):e.value!==""+vt(t)&&(e.value=""+vt(t)):o!=="submit"&&o!=="reset"||e.removeAttribute("value"),t!=null?lu(e,o,vt(t)):a!=null?lu(e,o,vt(a)):l!=null&&e.removeAttribute("value"),n==null&&s!=null&&(e.defaultChecked=!!s),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.name=""+vt(m):e.removeAttribute("name")}function Pc(e,t,a,l,n,s,o,m){if(s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"&&(e.type=s),t!=null||a!=null){if(!(s!=="submit"&&s!=="reset"||t!=null))return;a=a!=null?""+vt(a):"",t=t!=null?""+vt(t):a,m||t===e.value||(e.value=t),e.defaultValue=t}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=m?e.checked:!!l,e.defaultChecked=!!l,o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(e.name=o)}function lu(e,t,a){t==="number"&&pi(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function hl(e,t,a,l){if(e=e.options,t){t={};for(var n=0;n<a.length;n++)t["$"+a[n]]=!0;for(a=0;a<e.length;a++)n=t.hasOwnProperty("$"+e[a].value),e[a].selected!==n&&(e[a].selected=n),n&&l&&(e[a].defaultSelected=!0)}else{for(a=""+vt(a),t=null,n=0;n<e.length;n++){if(e[n].value===a){e[n].selected=!0,l&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function Ic(e,t,a){if(t!=null&&(t=""+vt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+vt(a):""}function eo(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(r(92));if(Fe(l)){if(1<l.length)throw Error(r(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=vt(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function gl(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var U0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function to(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||U0.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function ao(e,t,a){if(t!=null&&typeof t!="object")throw Error(r(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var n in t)l=t[n],t.hasOwnProperty(n)&&a[n]!==l&&to(e,n,l)}else for(var s in t)t.hasOwnProperty(s)&&to(e,s,t[s])}function nu(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var _0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),q0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function vi(e){return q0.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var iu=null;function su(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var yl=null,bl=null;function lo(e){var t=ol(e);if(t&&(e=t.stateNode)){var a=e[at]||null;e:switch(e=t.stateNode,t.type){case"input":if(au(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+xt(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var n=l[at]||null;if(!n)throw Error(r(90));au(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&$c(l)}break e;case"textarea":Ic(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&hl(e,!!a.multiple,t,!1)}}}var uu=!1;function no(e,t,a){if(uu)return e(t,a);uu=!0;try{var l=e(t);return l}finally{if(uu=!1,(yl!==null||bl!==null)&&(ns(),yl&&(t=yl,e=bl,bl=yl=null,lo(t),e)))for(t=0;t<e.length;t++)lo(e[t])}}function rn(e,t){var a=e.stateNode;if(a===null)return null;var l=a[at]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(r(231,t,typeof a));return a}var Kt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ru=!1;if(Kt)try{var cn={};Object.defineProperty(cn,"passive",{get:function(){ru=!0}}),window.addEventListener("test",cn,cn),window.removeEventListener("test",cn,cn)}catch{ru=!1}var ha=null,cu=null,xi=null;function io(){if(xi)return xi;var e,t=cu,a=t.length,l,n="value"in ha?ha.value:ha.textContent,s=n.length;for(e=0;e<a&&t[e]===n[e];e++);var o=a-e;for(l=1;l<=o&&t[a-l]===n[s-l];l++);return xi=n.slice(e,1<l?1-l:void 0)}function Si(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ni(){return!0}function so(){return!1}function lt(e){function t(a,l,n,s,o){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var m in e)e.hasOwnProperty(m)&&(a=e[m],this[m]=a?a(s):s[m]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Ni:so,this.isPropagationStopped=so,this}return x(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Ni)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Ni)},persist:function(){},isPersistent:Ni}),t}var Ya={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ji=lt(Ya),on=x({},Ya,{view:0,detail:0}),B0=lt(on),ou,fu,fn,wi=x({},on,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:mu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==fn&&(fn&&e.type==="mousemove"?(ou=e.screenX-fn.screenX,fu=e.screenY-fn.screenY):fu=ou=0,fn=e),ou)},movementY:function(e){return"movementY"in e?e.movementY:fu}}),uo=lt(wi),H0=x({},wi,{dataTransfer:0}),k0=lt(H0),L0=x({},on,{relatedTarget:0}),du=lt(L0),Y0=x({},Ya,{animationName:0,elapsedTime:0,pseudoElement:0}),G0=lt(Y0),X0=x({},Ya,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Z0=lt(X0),Q0=x({},Ya,{data:0}),ro=lt(Q0),V0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},K0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},J0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function W0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=J0[e])?!!t[e]:!1}function mu(){return W0}var F0=x({},on,{key:function(e){if(e.key){var t=V0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Si(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?K0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:mu,charCode:function(e){return e.type==="keypress"?Si(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Si(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),$0=lt(F0),P0=x({},wi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),co=lt(P0),I0=x({},on,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:mu}),eg=lt(I0),tg=x({},Ya,{propertyName:0,elapsedTime:0,pseudoElement:0}),ag=lt(tg),lg=x({},wi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ng=lt(lg),ig=x({},Ya,{newState:0,oldState:0}),sg=lt(ig),ug=[9,13,27,32],hu=Kt&&"CompositionEvent"in window,dn=null;Kt&&"documentMode"in document&&(dn=document.documentMode);var rg=Kt&&"TextEvent"in window&&!dn,oo=Kt&&(!hu||dn&&8<dn&&11>=dn),fo=" ",mo=!1;function ho(e,t){switch(e){case"keyup":return ug.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function go(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var pl=!1;function cg(e,t){switch(e){case"compositionend":return go(t);case"keypress":return t.which!==32?null:(mo=!0,fo);case"textInput":return e=t.data,e===fo&&mo?null:e;default:return null}}function og(e,t){if(pl)return e==="compositionend"||!hu&&ho(e,t)?(e=io(),xi=cu=ha=null,pl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return oo&&t.locale!=="ko"?null:t.data;default:return null}}var fg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!fg[e.type]:t==="textarea"}function bo(e,t,a,l){yl?bl?bl.push(l):bl=[l]:yl=l,t=os(t,"onChange"),0<t.length&&(a=new ji("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var mn=null,hn=null;function dg(e){Pd(e,0)}function Ai(e){var t=un(e);if($c(t))return e}function po(e,t){if(e==="change")return t}var vo=!1;if(Kt){var gu;if(Kt){var yu="oninput"in document;if(!yu){var xo=document.createElement("div");xo.setAttribute("oninput","return;"),yu=typeof xo.oninput=="function"}gu=yu}else gu=!1;vo=gu&&(!document.documentMode||9<document.documentMode)}function So(){mn&&(mn.detachEvent("onpropertychange",No),hn=mn=null)}function No(e){if(e.propertyName==="value"&&Ai(hn)){var t=[];bo(t,hn,e,su(e)),no(dg,t)}}function mg(e,t,a){e==="focusin"?(So(),mn=t,hn=a,mn.attachEvent("onpropertychange",No)):e==="focusout"&&So()}function hg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ai(hn)}function gg(e,t){if(e==="click")return Ai(t)}function yg(e,t){if(e==="input"||e==="change")return Ai(t)}function bg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ft=typeof Object.is=="function"?Object.is:bg;function gn(e,t){if(ft(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!fa.call(t,n)||!ft(e[n],t[n]))return!1}return!0}function jo(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wo(e,t){var a=jo(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=jo(a)}}function Ao(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ao(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function To(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=pi(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=pi(e.document)}return t}function bu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var pg=Kt&&"documentMode"in document&&11>=document.documentMode,vl=null,pu=null,yn=null,vu=!1;function Eo(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;vu||vl==null||vl!==pi(l)||(l=vl,"selectionStart"in l&&bu(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),yn&&gn(yn,l)||(yn=l,l=os(pu,"onSelect"),0<l.length&&(t=new ji("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=vl)))}function Ga(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var xl={animationend:Ga("Animation","AnimationEnd"),animationiteration:Ga("Animation","AnimationIteration"),animationstart:Ga("Animation","AnimationStart"),transitionrun:Ga("Transition","TransitionRun"),transitionstart:Ga("Transition","TransitionStart"),transitioncancel:Ga("Transition","TransitionCancel"),transitionend:Ga("Transition","TransitionEnd")},xu={},zo={};Kt&&(zo=document.createElement("div").style,"AnimationEvent"in window||(delete xl.animationend.animation,delete xl.animationiteration.animation,delete xl.animationstart.animation),"TransitionEvent"in window||delete xl.transitionend.transition);function Xa(e){if(xu[e])return xu[e];if(!xl[e])return e;var t=xl[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in zo)return xu[e]=t[a];return e}var Mo=Xa("animationend"),Oo=Xa("animationiteration"),Do=Xa("animationstart"),vg=Xa("transitionrun"),xg=Xa("transitionstart"),Sg=Xa("transitioncancel"),Ro=Xa("transitionend"),Co=new Map,Su="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Su.push("scrollEnd");function Ot(e,t){Co.set(e,t),La(t,[e])}var Uo=new WeakMap;function St(e,t){if(typeof e=="object"&&e!==null){var a=Uo.get(e);return a!==void 0?a:(t={value:e,source:t,stack:Wc(t)},Uo.set(e,t),t)}return{value:e,source:t,stack:Wc(t)}}var Nt=[],Sl=0,Nu=0;function Ti(){for(var e=Sl,t=Nu=Sl=0;t<e;){var a=Nt[t];Nt[t++]=null;var l=Nt[t];Nt[t++]=null;var n=Nt[t];Nt[t++]=null;var s=Nt[t];if(Nt[t++]=null,l!==null&&n!==null){var o=l.pending;o===null?n.next=n:(n.next=o.next,o.next=n),l.pending=n}s!==0&&_o(a,n,s)}}function Ei(e,t,a,l){Nt[Sl++]=e,Nt[Sl++]=t,Nt[Sl++]=a,Nt[Sl++]=l,Nu|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function ju(e,t,a,l){return Ei(e,t,a,l),zi(e)}function Nl(e,t){return Ei(e,null,null,t),zi(e)}function _o(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var n=!1,s=e.return;s!==null;)s.childLanes|=a,l=s.alternate,l!==null&&(l.childLanes|=a),s.tag===22&&(e=s.stateNode,e===null||e._visibility&1||(n=!0)),e=s,s=s.return;return e.tag===3?(s=e.stateNode,n&&t!==null&&(n=31-ot(a),e=s.hiddenUpdates,l=e[n],l===null?e[n]=[t]:l.push(t),t.lane=a|536870912),s):null}function zi(e){if(50<Yn)throw Yn=0,Mr=null,Error(r(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var jl={};function Ng(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function dt(e,t,a,l){return new Ng(e,t,a,l)}function wu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Jt(e,t){var a=e.alternate;return a===null?(a=dt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function qo(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Mi(e,t,a,l,n,s){var o=0;if(l=e,typeof e=="function")wu(e)&&(o=1);else if(typeof e=="string")o=wy(e,a,ce.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case X:return e=dt(31,a,t,n),e.elementType=X,e.lanes=s,e;case U:return Za(a.children,n,s,t);case q:o=8,n|=24;break;case B:return e=dt(12,a,t,n|2),e.elementType=B,e.lanes=s,e;case ue:return e=dt(13,a,t,n),e.elementType=ue,e.lanes=s,e;case fe:return e=dt(19,a,t,n),e.elementType=fe,e.lanes=s,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case J:case F:o=10;break e;case I:o=9;break e;case he:o=11;break e;case je:o=14;break e;case G:o=16,l=null;break e}o=29,a=Error(r(130,e===null?"null":typeof e,"")),l=null}return t=dt(o,a,t,n),t.elementType=e,t.type=l,t.lanes=s,t}function Za(e,t,a,l){return e=dt(7,e,l,t),e.lanes=a,e}function Au(e,t,a){return e=dt(6,e,null,t),e.lanes=a,e}function Tu(e,t,a){return t=dt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var wl=[],Al=0,Oi=null,Di=0,jt=[],wt=0,Qa=null,Wt=1,Ft="";function Va(e,t){wl[Al++]=Di,wl[Al++]=Oi,Oi=e,Di=t}function Bo(e,t,a){jt[wt++]=Wt,jt[wt++]=Ft,jt[wt++]=Qa,Qa=e;var l=Wt;e=Ft;var n=32-ot(l)-1;l&=~(1<<n),a+=1;var s=32-ot(t)+n;if(30<s){var o=n-n%5;s=(l&(1<<o)-1).toString(32),l>>=o,n-=o,Wt=1<<32-ot(t)+n|a<<n|l,Ft=s+e}else Wt=1<<s|a<<n|l,Ft=e}function Eu(e){e.return!==null&&(Va(e,1),Bo(e,1,0))}function zu(e){for(;e===Oi;)Oi=wl[--Al],wl[Al]=null,Di=wl[--Al],wl[Al]=null;for(;e===Qa;)Qa=jt[--wt],jt[wt]=null,Ft=jt[--wt],jt[wt]=null,Wt=jt[--wt],jt[wt]=null}var tt=null,De=null,de=!1,Ka=null,qt=!1,Mu=Error(r(519));function Ja(e){var t=Error(r(418,""));throw vn(St(t,e)),Mu}function Ho(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[$e]=e,t[at]=l,a){case"dialog":ne("cancel",t),ne("close",t);break;case"iframe":case"object":case"embed":ne("load",t);break;case"video":case"audio":for(a=0;a<Xn.length;a++)ne(Xn[a],t);break;case"source":ne("error",t);break;case"img":case"image":case"link":ne("error",t),ne("load",t);break;case"details":ne("toggle",t);break;case"input":ne("invalid",t),Pc(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),bi(t);break;case"select":ne("invalid",t);break;case"textarea":ne("invalid",t),eo(t,l.value,l.defaultValue,l.children),bi(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||am(t.textContent,a)?(l.popover!=null&&(ne("beforetoggle",t),ne("toggle",t)),l.onScroll!=null&&ne("scroll",t),l.onScrollEnd!=null&&ne("scrollend",t),l.onClick!=null&&(t.onclick=fs),t=!0):t=!1,t||Ja(e)}function ko(e){for(tt=e.return;tt;)switch(tt.tag){case 5:case 13:qt=!1;return;case 27:case 3:qt=!0;return;default:tt=tt.return}}function bn(e){if(e!==tt)return!1;if(!de)return ko(e),de=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Qr(e.type,e.memoizedProps)),a=!a),a&&De&&Ja(e),ko(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(r(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){De=Rt(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}De=null}}else t===27?(t=De,Oa(e.type)?(e=Wr,Wr=null,De=e):De=t):De=tt?Rt(e.stateNode.nextSibling):null;return!0}function pn(){De=tt=null,de=!1}function Lo(){var e=Ka;return e!==null&&(st===null?st=e:st.push.apply(st,e),Ka=null),e}function vn(e){Ka===null?Ka=[e]:Ka.push(e)}var Ou=Ye(null),Wa=null,$t=null;function ga(e,t,a){$(Ou,t._currentValue),t._currentValue=a}function Pt(e){e._currentValue=Ou.current,we(Ou)}function Du(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function Ru(e,t,a,l){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var s=n.dependencies;if(s!==null){var o=n.child;s=s.firstContext;e:for(;s!==null;){var m=s;s=n;for(var y=0;y<t.length;y++)if(m.context===t[y]){s.lanes|=a,m=s.alternate,m!==null&&(m.lanes|=a),Du(s.return,a,e),l||(o=null);break e}s=m.next}}else if(n.tag===18){if(o=n.return,o===null)throw Error(r(341));o.lanes|=a,s=o.alternate,s!==null&&(s.lanes|=a),Du(o,a,e),o=null}else o=n.child;if(o!==null)o.return=n;else for(o=n;o!==null;){if(o===e){o=null;break}if(n=o.sibling,n!==null){n.return=o.return,o=n;break}o=o.return}n=o}}function xn(e,t,a,l){e=null;for(var n=t,s=!1;n!==null;){if(!s){if((n.flags&524288)!==0)s=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var o=n.alternate;if(o===null)throw Error(r(387));if(o=o.memoizedProps,o!==null){var m=n.type;ft(n.pendingProps.value,o.value)||(e!==null?e.push(m):e=[m])}}else if(n===Me.current){if(o=n.alternate,o===null)throw Error(r(387));o.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Wn):e=[Wn])}n=n.return}e!==null&&Ru(t,e,a,l),t.flags|=262144}function Ri(e){for(e=e.firstContext;e!==null;){if(!ft(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Fa(e){Wa=e,$t=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Pe(e){return Yo(Wa,e)}function Ci(e,t){return Wa===null&&Fa(e),Yo(e,t)}function Yo(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},$t===null){if(e===null)throw Error(r(308));$t=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else $t=$t.next=t;return a}var jg=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},wg=i.unstable_scheduleCallback,Ag=i.unstable_NormalPriority,He={$$typeof:F,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Cu(){return{controller:new jg,data:new Map,refCount:0}}function Sn(e){e.refCount--,e.refCount===0&&wg(Ag,function(){e.controller.abort()})}var Nn=null,Uu=0,Tl=0,El=null;function Tg(e,t){if(Nn===null){var a=Nn=[];Uu=0,Tl=qr(),El={status:"pending",value:void 0,then:function(l){a.push(l)}}}return Uu++,t.then(Go,Go),t}function Go(){if(--Uu===0&&Nn!==null){El!==null&&(El.status="fulfilled");var e=Nn;Nn=null,Tl=0,El=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Eg(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var n=0;n<a.length;n++)(0,a[n])(t)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var Xo=z.S;z.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Tg(e,t),Xo!==null&&Xo(e,t)};var $a=Ye(null);function _u(){var e=$a.current;return e!==null?e:Te.pooledCache}function Ui(e,t){t===null?$($a,$a.current):$($a,t.pool)}function Zo(){var e=_u();return e===null?null:{parent:He._currentValue,pool:e}}var jn=Error(r(460)),Qo=Error(r(474)),_i=Error(r(542)),qu={then:function(){}};function Vo(e){return e=e.status,e==="fulfilled"||e==="rejected"}function qi(){}function Ko(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(qi,qi),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Wo(e),e;default:if(typeof t.status=="string")t.then(qi,qi);else{if(e=Te,e!==null&&100<e.shellSuspendCounter)throw Error(r(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=l}},function(l){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Wo(e),e}throw wn=t,jn}}var wn=null;function Jo(){if(wn===null)throw Error(r(459));var e=wn;return wn=null,e}function Wo(e){if(e===jn||e===_i)throw Error(r(483))}var ya=!1;function Bu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Hu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ba(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function pa(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(ge&2)!==0){var n=l.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),l.pending=t,t=zi(e),_o(e,null,a),t}return Ei(e,l,t,a),zi(e)}function An(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Yc(e,a)}}function ku(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,s=null;if(a=a.firstBaseUpdate,a!==null){do{var o={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};s===null?n=s=o:s=s.next=o,a=a.next}while(a!==null);s===null?n=s=t:s=s.next=t}else n=s=t;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:s,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Lu=!1;function Tn(){if(Lu){var e=El;if(e!==null)throw e}}function En(e,t,a,l){Lu=!1;var n=e.updateQueue;ya=!1;var s=n.firstBaseUpdate,o=n.lastBaseUpdate,m=n.shared.pending;if(m!==null){n.shared.pending=null;var y=m,N=y.next;y.next=null,o===null?s=N:o.next=N,o=y;var M=e.alternate;M!==null&&(M=M.updateQueue,m=M.lastBaseUpdate,m!==o&&(m===null?M.firstBaseUpdate=N:m.next=N,M.lastBaseUpdate=y))}if(s!==null){var D=n.baseState;o=0,M=N=y=null,m=s;do{var A=m.lane&-536870913,T=A!==m.lane;if(T?(se&A)===A:(l&A)===A){A!==0&&A===Tl&&(Lu=!0),M!==null&&(M=M.next={lane:0,tag:m.tag,payload:m.payload,callback:null,next:null});e:{var W=e,V=m;A=t;var Se=a;switch(V.tag){case 1:if(W=V.payload,typeof W=="function"){D=W.call(Se,D,A);break e}D=W;break e;case 3:W.flags=W.flags&-65537|128;case 0:if(W=V.payload,A=typeof W=="function"?W.call(Se,D,A):W,A==null)break e;D=x({},D,A);break e;case 2:ya=!0}}A=m.callback,A!==null&&(e.flags|=64,T&&(e.flags|=8192),T=n.callbacks,T===null?n.callbacks=[A]:T.push(A))}else T={lane:A,tag:m.tag,payload:m.payload,callback:m.callback,next:null},M===null?(N=M=T,y=D):M=M.next=T,o|=A;if(m=m.next,m===null){if(m=n.shared.pending,m===null)break;T=m,m=T.next,T.next=null,n.lastBaseUpdate=T,n.shared.pending=null}}while(!0);M===null&&(y=D),n.baseState=y,n.firstBaseUpdate=N,n.lastBaseUpdate=M,s===null&&(n.shared.lanes=0),Ta|=o,e.lanes=o,e.memoizedState=D}}function Fo(e,t){if(typeof e!="function")throw Error(r(191,e));e.call(t)}function $o(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Fo(a[e],t)}var zl=Ye(null),Bi=Ye(0);function Po(e,t){e=ia,$(Bi,e),$(zl,t),ia=e|t.baseLanes}function Yu(){$(Bi,ia),$(zl,zl.current)}function Gu(){ia=Bi.current,we(zl),we(Bi)}var va=0,ee=null,ve=null,qe=null,Hi=!1,Ml=!1,Pa=!1,ki=0,zn=0,Ol=null,zg=0;function Ce(){throw Error(r(321))}function Xu(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!ft(e[a],t[a]))return!1;return!0}function Zu(e,t,a,l,n,s){return va=s,ee=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,z.H=e===null||e.memoizedState===null?qf:Bf,Pa=!1,s=a(l,n),Pa=!1,Ml&&(s=ef(t,a,l,n)),Io(e),s}function Io(e){z.H=Qi;var t=ve!==null&&ve.next!==null;if(va=0,qe=ve=ee=null,Hi=!1,zn=0,Ol=null,t)throw Error(r(300));e===null||Xe||(e=e.dependencies,e!==null&&Ri(e)&&(Xe=!0))}function ef(e,t,a,l){ee=e;var n=0;do{if(Ml&&(Ol=null),zn=0,Ml=!1,25<=n)throw Error(r(301));if(n+=1,qe=ve=null,e.updateQueue!=null){var s=e.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,s.memoCache!=null&&(s.memoCache.index=0)}z.H=_g,s=t(a,l)}while(Ml);return s}function Mg(){var e=z.H,t=e.useState()[0];return t=typeof t.then=="function"?Mn(t):t,e=e.useState()[0],(ve!==null?ve.memoizedState:null)!==e&&(ee.flags|=1024),t}function Qu(){var e=ki!==0;return ki=0,e}function Vu(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Ku(e){if(Hi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Hi=!1}va=0,qe=ve=ee=null,Ml=!1,zn=ki=0,Ol=null}function nt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return qe===null?ee.memoizedState=qe=e:qe=qe.next=e,qe}function Be(){if(ve===null){var e=ee.alternate;e=e!==null?e.memoizedState:null}else e=ve.next;var t=qe===null?ee.memoizedState:qe.next;if(t!==null)qe=t,ve=e;else{if(e===null)throw ee.alternate===null?Error(r(467)):Error(r(310));ve=e,e={memoizedState:ve.memoizedState,baseState:ve.baseState,baseQueue:ve.baseQueue,queue:ve.queue,next:null},qe===null?ee.memoizedState=qe=e:qe=qe.next=e}return qe}function Ju(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Mn(e){var t=zn;return zn+=1,Ol===null&&(Ol=[]),e=Ko(Ol,e,t),t=ee,(qe===null?t.memoizedState:qe.next)===null&&(t=t.alternate,z.H=t===null||t.memoizedState===null?qf:Bf),e}function Li(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Mn(e);if(e.$$typeof===F)return Pe(e)}throw Error(r(438,String(e)))}function Wu(e){var t=null,a=ee.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=ee.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Ju(),ee.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=ie;return t.index++,a}function It(e,t){return typeof t=="function"?t(e):t}function Yi(e){var t=Be();return Fu(t,ve,e)}function Fu(e,t,a){var l=e.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=a;var n=e.baseQueue,s=l.pending;if(s!==null){if(n!==null){var o=n.next;n.next=s.next,s.next=o}t.baseQueue=n=s,l.pending=null}if(s=e.baseState,n===null)e.memoizedState=s;else{t=n.next;var m=o=null,y=null,N=t,M=!1;do{var D=N.lane&-536870913;if(D!==N.lane?(se&D)===D:(va&D)===D){var A=N.revertLane;if(A===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null}),D===Tl&&(M=!0);else if((va&A)===A){N=N.next,A===Tl&&(M=!0);continue}else D={lane:0,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},y===null?(m=y=D,o=s):y=y.next=D,ee.lanes|=A,Ta|=A;D=N.action,Pa&&a(s,D),s=N.hasEagerState?N.eagerState:a(s,D)}else A={lane:D,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},y===null?(m=y=A,o=s):y=y.next=A,ee.lanes|=D,Ta|=D;N=N.next}while(N!==null&&N!==t);if(y===null?o=s:y.next=m,!ft(s,e.memoizedState)&&(Xe=!0,M&&(a=El,a!==null)))throw a;e.memoizedState=s,e.baseState=o,e.baseQueue=y,l.lastRenderedState=s}return n===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function $u(e){var t=Be(),a=t.queue;if(a===null)throw Error(r(311));a.lastRenderedReducer=e;var l=a.dispatch,n=a.pending,s=t.memoizedState;if(n!==null){a.pending=null;var o=n=n.next;do s=e(s,o.action),o=o.next;while(o!==n);ft(s,t.memoizedState)||(Xe=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),a.lastRenderedState=s}return[s,l]}function tf(e,t,a){var l=ee,n=Be(),s=de;if(s){if(a===void 0)throw Error(r(407));a=a()}else a=t();var o=!ft((ve||n).memoizedState,a);o&&(n.memoizedState=a,Xe=!0),n=n.queue;var m=nf.bind(null,l,n,e);if(On(2048,8,m,[e]),n.getSnapshot!==t||o||qe!==null&&qe.memoizedState.tag&1){if(l.flags|=2048,Dl(9,Gi(),lf.bind(null,l,n,a,t),null),Te===null)throw Error(r(349));s||(va&124)!==0||af(l,t,a)}return a}function af(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=ee.updateQueue,t===null?(t=Ju(),ee.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function lf(e,t,a,l){t.value=a,t.getSnapshot=l,sf(t)&&uf(e)}function nf(e,t,a){return a(function(){sf(t)&&uf(e)})}function sf(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!ft(e,a)}catch{return!0}}function uf(e){var t=Nl(e,2);t!==null&&bt(t,e,2)}function Pu(e){var t=nt();if(typeof e=="function"){var a=e;if(e=a(),Pa){da(!0);try{a()}finally{da(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:It,lastRenderedState:e},t}function rf(e,t,a,l){return e.baseState=a,Fu(e,ve,typeof l=="function"?l:It)}function Og(e,t,a,l,n){if(Zi(e))throw Error(r(485));if(e=t.action,e!==null){var s={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(o){s.listeners.push(o)}};z.T!==null?a(!0):s.isTransition=!1,l(s),a=t.pending,a===null?(s.next=t.pending=s,cf(t,s)):(s.next=a.next,t.pending=a.next=s)}}function cf(e,t){var a=t.action,l=t.payload,n=e.state;if(t.isTransition){var s=z.T,o={};z.T=o;try{var m=a(n,l),y=z.S;y!==null&&y(o,m),of(e,t,m)}catch(N){Iu(e,t,N)}finally{z.T=s}}else try{s=a(n,l),of(e,t,s)}catch(N){Iu(e,t,N)}}function of(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){ff(e,t,l)},function(l){return Iu(e,t,l)}):ff(e,t,a)}function ff(e,t,a){t.status="fulfilled",t.value=a,df(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,cf(e,a)))}function Iu(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,df(t),t=t.next;while(t!==l)}e.action=null}function df(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function mf(e,t){return t}function hf(e,t){if(de){var a=Te.formState;if(a!==null){e:{var l=ee;if(de){if(De){t:{for(var n=De,s=qt;n.nodeType!==8;){if(!s){n=null;break t}if(n=Rt(n.nextSibling),n===null){n=null;break t}}s=n.data,n=s==="F!"||s==="F"?n:null}if(n){De=Rt(n.nextSibling),l=n.data==="F!";break e}}Ja(l)}l=!1}l&&(t=a[0])}}return a=nt(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:mf,lastRenderedState:t},a.queue=l,a=Cf.bind(null,ee,l),l.dispatch=a,l=Pu(!1),s=nr.bind(null,ee,!1,l.queue),l=nt(),n={state:t,dispatch:null,action:e,pending:null},l.queue=n,a=Og.bind(null,ee,n,s,a),n.dispatch=a,l.memoizedState=e,[t,a,!1]}function gf(e){var t=Be();return yf(t,ve,e)}function yf(e,t,a){if(t=Fu(e,t,mf)[0],e=Yi(It)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Mn(t)}catch(o){throw o===jn?_i:o}else l=t;t=Be();var n=t.queue,s=n.dispatch;return a!==t.memoizedState&&(ee.flags|=2048,Dl(9,Gi(),Dg.bind(null,n,a),null)),[l,s,e]}function Dg(e,t){e.action=t}function bf(e){var t=Be(),a=ve;if(a!==null)return yf(t,a,e);Be(),t=t.memoizedState,a=Be();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function Dl(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=ee.updateQueue,t===null&&(t=Ju(),ee.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function Gi(){return{destroy:void 0,resource:void 0}}function pf(){return Be().memoizedState}function Xi(e,t,a,l){var n=nt();l=l===void 0?null:l,ee.flags|=e,n.memoizedState=Dl(1|t,Gi(),a,l)}function On(e,t,a,l){var n=Be();l=l===void 0?null:l;var s=n.memoizedState.inst;ve!==null&&l!==null&&Xu(l,ve.memoizedState.deps)?n.memoizedState=Dl(t,s,a,l):(ee.flags|=e,n.memoizedState=Dl(1|t,s,a,l))}function vf(e,t){Xi(8390656,8,e,t)}function xf(e,t){On(2048,8,e,t)}function Sf(e,t){return On(4,2,e,t)}function Nf(e,t){return On(4,4,e,t)}function jf(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function wf(e,t,a){a=a!=null?a.concat([e]):null,On(4,4,jf.bind(null,t,e),a)}function er(){}function Af(e,t){var a=Be();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&Xu(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function Tf(e,t){var a=Be();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&Xu(t,l[1]))return l[0];if(l=e(),Pa){da(!0);try{e()}finally{da(!1)}}return a.memoizedState=[l,t],l}function tr(e,t,a){return a===void 0||(va&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=Md(),ee.lanes|=e,Ta|=e,a)}function Ef(e,t,a,l){return ft(a,t)?a:zl.current!==null?(e=tr(e,a,l),ft(e,t)||(Xe=!0),e):(va&42)===0?(Xe=!0,e.memoizedState=a):(e=Md(),ee.lanes|=e,Ta|=e,t)}function zf(e,t,a,l,n){var s=H.p;H.p=s!==0&&8>s?s:8;var o=z.T,m={};z.T=m,nr(e,!1,t,a);try{var y=n(),N=z.S;if(N!==null&&N(m,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var M=Eg(y,l);Dn(e,t,M,yt(e))}else Dn(e,t,l,yt(e))}catch(D){Dn(e,t,{then:function(){},status:"rejected",reason:D},yt())}finally{H.p=s,z.T=o}}function Rg(){}function ar(e,t,a,l){if(e.tag!==5)throw Error(r(476));var n=Mf(e).queue;zf(e,n,t,_,a===null?Rg:function(){return Of(e),a(l)})}function Mf(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:_,baseState:_,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:It,lastRenderedState:_},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:It,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Of(e){var t=Mf(e).next.queue;Dn(e,t,{},yt())}function lr(){return Pe(Wn)}function Df(){return Be().memoizedState}function Rf(){return Be().memoizedState}function Cg(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=yt();e=ba(a);var l=pa(t,e,a);l!==null&&(bt(l,t,a),An(l,t,a)),t={cache:Cu()},e.payload=t;return}t=t.return}}function Ug(e,t,a){var l=yt();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Zi(e)?Uf(t,a):(a=ju(e,t,a,l),a!==null&&(bt(a,e,l),_f(a,t,l)))}function Cf(e,t,a){var l=yt();Dn(e,t,a,l)}function Dn(e,t,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Zi(e))Uf(t,n);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,m=s(o,a);if(n.hasEagerState=!0,n.eagerState=m,ft(m,o))return Ei(e,t,n,0),Te===null&&Ti(),!1}catch{}finally{}if(a=ju(e,t,n,l),a!==null)return bt(a,e,l),_f(a,t,l),!0}return!1}function nr(e,t,a,l){if(l={lane:2,revertLane:qr(),action:l,hasEagerState:!1,eagerState:null,next:null},Zi(e)){if(t)throw Error(r(479))}else t=ju(e,a,l,2),t!==null&&bt(t,e,2)}function Zi(e){var t=e.alternate;return e===ee||t!==null&&t===ee}function Uf(e,t){Ml=Hi=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function _f(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Yc(e,a)}}var Qi={readContext:Pe,use:Li,useCallback:Ce,useContext:Ce,useEffect:Ce,useImperativeHandle:Ce,useLayoutEffect:Ce,useInsertionEffect:Ce,useMemo:Ce,useReducer:Ce,useRef:Ce,useState:Ce,useDebugValue:Ce,useDeferredValue:Ce,useTransition:Ce,useSyncExternalStore:Ce,useId:Ce,useHostTransitionStatus:Ce,useFormState:Ce,useActionState:Ce,useOptimistic:Ce,useMemoCache:Ce,useCacheRefresh:Ce},qf={readContext:Pe,use:Li,useCallback:function(e,t){return nt().memoizedState=[e,t===void 0?null:t],e},useContext:Pe,useEffect:vf,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Xi(4194308,4,jf.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Xi(4194308,4,e,t)},useInsertionEffect:function(e,t){Xi(4,2,e,t)},useMemo:function(e,t){var a=nt();t=t===void 0?null:t;var l=e();if(Pa){da(!0);try{e()}finally{da(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=nt();if(a!==void 0){var n=a(t);if(Pa){da(!0);try{a(t)}finally{da(!1)}}}else n=t;return l.memoizedState=l.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},l.queue=e,e=e.dispatch=Ug.bind(null,ee,e),[l.memoizedState,e]},useRef:function(e){var t=nt();return e={current:e},t.memoizedState=e},useState:function(e){e=Pu(e);var t=e.queue,a=Cf.bind(null,ee,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:er,useDeferredValue:function(e,t){var a=nt();return tr(a,e,t)},useTransition:function(){var e=Pu(!1);return e=zf.bind(null,ee,e.queue,!0,!1),nt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=ee,n=nt();if(de){if(a===void 0)throw Error(r(407));a=a()}else{if(a=t(),Te===null)throw Error(r(349));(se&124)!==0||af(l,t,a)}n.memoizedState=a;var s={value:a,getSnapshot:t};return n.queue=s,vf(nf.bind(null,l,s,e),[e]),l.flags|=2048,Dl(9,Gi(),lf.bind(null,l,s,a,t),null),a},useId:function(){var e=nt(),t=Te.identifierPrefix;if(de){var a=Ft,l=Wt;a=(l&~(1<<32-ot(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=ki++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=zg++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:lr,useFormState:hf,useActionState:hf,useOptimistic:function(e){var t=nt();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=nr.bind(null,ee,!0,a),a.dispatch=t,[e,t]},useMemoCache:Wu,useCacheRefresh:function(){return nt().memoizedState=Cg.bind(null,ee)}},Bf={readContext:Pe,use:Li,useCallback:Af,useContext:Pe,useEffect:xf,useImperativeHandle:wf,useInsertionEffect:Sf,useLayoutEffect:Nf,useMemo:Tf,useReducer:Yi,useRef:pf,useState:function(){return Yi(It)},useDebugValue:er,useDeferredValue:function(e,t){var a=Be();return Ef(a,ve.memoizedState,e,t)},useTransition:function(){var e=Yi(It)[0],t=Be().memoizedState;return[typeof e=="boolean"?e:Mn(e),t]},useSyncExternalStore:tf,useId:Df,useHostTransitionStatus:lr,useFormState:gf,useActionState:gf,useOptimistic:function(e,t){var a=Be();return rf(a,ve,e,t)},useMemoCache:Wu,useCacheRefresh:Rf},_g={readContext:Pe,use:Li,useCallback:Af,useContext:Pe,useEffect:xf,useImperativeHandle:wf,useInsertionEffect:Sf,useLayoutEffect:Nf,useMemo:Tf,useReducer:$u,useRef:pf,useState:function(){return $u(It)},useDebugValue:er,useDeferredValue:function(e,t){var a=Be();return ve===null?tr(a,e,t):Ef(a,ve.memoizedState,e,t)},useTransition:function(){var e=$u(It)[0],t=Be().memoizedState;return[typeof e=="boolean"?e:Mn(e),t]},useSyncExternalStore:tf,useId:Df,useHostTransitionStatus:lr,useFormState:bf,useActionState:bf,useOptimistic:function(e,t){var a=Be();return ve!==null?rf(a,ve,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Wu,useCacheRefresh:Rf},Rl=null,Rn=0;function Vi(e){var t=Rn;return Rn+=1,Rl===null&&(Rl=[]),Ko(Rl,e,t)}function Cn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Ki(e,t){throw t.$$typeof===R?Error(r(525)):(e=Object.prototype.toString.call(t),Error(r(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Hf(e){var t=e._init;return t(e._payload)}function kf(e){function t(v,p){if(e){var S=v.deletions;S===null?(v.deletions=[p],v.flags|=16):S.push(p)}}function a(v,p){if(!e)return null;for(;p!==null;)t(v,p),p=p.sibling;return null}function l(v){for(var p=new Map;v!==null;)v.key!==null?p.set(v.key,v):p.set(v.index,v),v=v.sibling;return p}function n(v,p){return v=Jt(v,p),v.index=0,v.sibling=null,v}function s(v,p,S){return v.index=S,e?(S=v.alternate,S!==null?(S=S.index,S<p?(v.flags|=67108866,p):S):(v.flags|=67108866,p)):(v.flags|=1048576,p)}function o(v){return e&&v.alternate===null&&(v.flags|=67108866),v}function m(v,p,S,O){return p===null||p.tag!==6?(p=Au(S,v.mode,O),p.return=v,p):(p=n(p,S),p.return=v,p)}function y(v,p,S,O){var k=S.type;return k===U?M(v,p,S.props.children,O,S.key):p!==null&&(p.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===G&&Hf(k)===p.type)?(p=n(p,S.props),Cn(p,S),p.return=v,p):(p=Mi(S.type,S.key,S.props,null,v.mode,O),Cn(p,S),p.return=v,p)}function N(v,p,S,O){return p===null||p.tag!==4||p.stateNode.containerInfo!==S.containerInfo||p.stateNode.implementation!==S.implementation?(p=Tu(S,v.mode,O),p.return=v,p):(p=n(p,S.children||[]),p.return=v,p)}function M(v,p,S,O,k){return p===null||p.tag!==7?(p=Za(S,v.mode,O,k),p.return=v,p):(p=n(p,S),p.return=v,p)}function D(v,p,S){if(typeof p=="string"&&p!==""||typeof p=="number"||typeof p=="bigint")return p=Au(""+p,v.mode,S),p.return=v,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case C:return S=Mi(p.type,p.key,p.props,null,v.mode,S),Cn(S,p),S.return=v,S;case Z:return p=Tu(p,v.mode,S),p.return=v,p;case G:var O=p._init;return p=O(p._payload),D(v,p,S)}if(Fe(p)||Ee(p))return p=Za(p,v.mode,S,null),p.return=v,p;if(typeof p.then=="function")return D(v,Vi(p),S);if(p.$$typeof===F)return D(v,Ci(v,p),S);Ki(v,p)}return null}function A(v,p,S,O){var k=p!==null?p.key:null;if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return k!==null?null:m(v,p,""+S,O);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case C:return S.key===k?y(v,p,S,O):null;case Z:return S.key===k?N(v,p,S,O):null;case G:return k=S._init,S=k(S._payload),A(v,p,S,O)}if(Fe(S)||Ee(S))return k!==null?null:M(v,p,S,O,null);if(typeof S.then=="function")return A(v,p,Vi(S),O);if(S.$$typeof===F)return A(v,p,Ci(v,S),O);Ki(v,S)}return null}function T(v,p,S,O,k){if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return v=v.get(S)||null,m(p,v,""+O,k);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case C:return v=v.get(O.key===null?S:O.key)||null,y(p,v,O,k);case Z:return v=v.get(O.key===null?S:O.key)||null,N(p,v,O,k);case G:var ae=O._init;return O=ae(O._payload),T(v,p,S,O,k)}if(Fe(O)||Ee(O))return v=v.get(S)||null,M(p,v,O,k,null);if(typeof O.then=="function")return T(v,p,S,Vi(O),k);if(O.$$typeof===F)return T(v,p,S,Ci(p,O),k);Ki(p,O)}return null}function W(v,p,S,O){for(var k=null,ae=null,Q=p,K=p=0,Qe=null;Q!==null&&K<S.length;K++){Q.index>K?(Qe=Q,Q=null):Qe=Q.sibling;var oe=A(v,Q,S[K],O);if(oe===null){Q===null&&(Q=Qe);break}e&&Q&&oe.alternate===null&&t(v,Q),p=s(oe,p,K),ae===null?k=oe:ae.sibling=oe,ae=oe,Q=Qe}if(K===S.length)return a(v,Q),de&&Va(v,K),k;if(Q===null){for(;K<S.length;K++)Q=D(v,S[K],O),Q!==null&&(p=s(Q,p,K),ae===null?k=Q:ae.sibling=Q,ae=Q);return de&&Va(v,K),k}for(Q=l(Q);K<S.length;K++)Qe=T(Q,v,K,S[K],O),Qe!==null&&(e&&Qe.alternate!==null&&Q.delete(Qe.key===null?K:Qe.key),p=s(Qe,p,K),ae===null?k=Qe:ae.sibling=Qe,ae=Qe);return e&&Q.forEach(function(_a){return t(v,_a)}),de&&Va(v,K),k}function V(v,p,S,O){if(S==null)throw Error(r(151));for(var k=null,ae=null,Q=p,K=p=0,Qe=null,oe=S.next();Q!==null&&!oe.done;K++,oe=S.next()){Q.index>K?(Qe=Q,Q=null):Qe=Q.sibling;var _a=A(v,Q,oe.value,O);if(_a===null){Q===null&&(Q=Qe);break}e&&Q&&_a.alternate===null&&t(v,Q),p=s(_a,p,K),ae===null?k=_a:ae.sibling=_a,ae=_a,Q=Qe}if(oe.done)return a(v,Q),de&&Va(v,K),k;if(Q===null){for(;!oe.done;K++,oe=S.next())oe=D(v,oe.value,O),oe!==null&&(p=s(oe,p,K),ae===null?k=oe:ae.sibling=oe,ae=oe);return de&&Va(v,K),k}for(Q=l(Q);!oe.done;K++,oe=S.next())oe=T(Q,v,K,oe.value,O),oe!==null&&(e&&oe.alternate!==null&&Q.delete(oe.key===null?K:oe.key),p=s(oe,p,K),ae===null?k=oe:ae.sibling=oe,ae=oe);return e&&Q.forEach(function(qy){return t(v,qy)}),de&&Va(v,K),k}function Se(v,p,S,O){if(typeof S=="object"&&S!==null&&S.type===U&&S.key===null&&(S=S.props.children),typeof S=="object"&&S!==null){switch(S.$$typeof){case C:e:{for(var k=S.key;p!==null;){if(p.key===k){if(k=S.type,k===U){if(p.tag===7){a(v,p.sibling),O=n(p,S.props.children),O.return=v,v=O;break e}}else if(p.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===G&&Hf(k)===p.type){a(v,p.sibling),O=n(p,S.props),Cn(O,S),O.return=v,v=O;break e}a(v,p);break}else t(v,p);p=p.sibling}S.type===U?(O=Za(S.props.children,v.mode,O,S.key),O.return=v,v=O):(O=Mi(S.type,S.key,S.props,null,v.mode,O),Cn(O,S),O.return=v,v=O)}return o(v);case Z:e:{for(k=S.key;p!==null;){if(p.key===k)if(p.tag===4&&p.stateNode.containerInfo===S.containerInfo&&p.stateNode.implementation===S.implementation){a(v,p.sibling),O=n(p,S.children||[]),O.return=v,v=O;break e}else{a(v,p);break}else t(v,p);p=p.sibling}O=Tu(S,v.mode,O),O.return=v,v=O}return o(v);case G:return k=S._init,S=k(S._payload),Se(v,p,S,O)}if(Fe(S))return W(v,p,S,O);if(Ee(S)){if(k=Ee(S),typeof k!="function")throw Error(r(150));return S=k.call(S),V(v,p,S,O)}if(typeof S.then=="function")return Se(v,p,Vi(S),O);if(S.$$typeof===F)return Se(v,p,Ci(v,S),O);Ki(v,S)}return typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint"?(S=""+S,p!==null&&p.tag===6?(a(v,p.sibling),O=n(p,S),O.return=v,v=O):(a(v,p),O=Au(S,v.mode,O),O.return=v,v=O),o(v)):a(v,p)}return function(v,p,S,O){try{Rn=0;var k=Se(v,p,S,O);return Rl=null,k}catch(Q){if(Q===jn||Q===_i)throw Q;var ae=dt(29,Q,null,v.mode);return ae.lanes=O,ae.return=v,ae}finally{}}}var Cl=kf(!0),Lf=kf(!1),At=Ye(null),Bt=null;function xa(e){var t=e.alternate;$(ke,ke.current&1),$(At,e),Bt===null&&(t===null||zl.current!==null||t.memoizedState!==null)&&(Bt=e)}function Yf(e){if(e.tag===22){if($(ke,ke.current),$(At,e),Bt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Bt=e)}}else Sa()}function Sa(){$(ke,ke.current),$(At,At.current)}function ea(e){we(At),Bt===e&&(Bt=null),we(ke)}var ke=Ye(0);function Ji(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Jr(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function ir(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:x({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var sr={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=yt(),n=ba(l);n.payload=t,a!=null&&(n.callback=a),t=pa(e,n,l),t!==null&&(bt(t,e,l),An(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=yt(),n=ba(l);n.tag=1,n.payload=t,a!=null&&(n.callback=a),t=pa(e,n,l),t!==null&&(bt(t,e,l),An(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=yt(),l=ba(a);l.tag=2,t!=null&&(l.callback=t),t=pa(e,l,a),t!==null&&(bt(t,e,a),An(t,e,a))}};function Gf(e,t,a,l,n,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,s,o):t.prototype&&t.prototype.isPureReactComponent?!gn(a,l)||!gn(n,s):!0}function Xf(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&sr.enqueueReplaceState(t,t.state,null)}function Ia(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=x({},a));for(var n in e)a[n]===void 0&&(a[n]=e[n])}return a}var Wi=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Zf(e){Wi(e)}function Qf(e){console.error(e)}function Vf(e){Wi(e)}function Fi(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Kf(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function ur(e,t,a){return a=ba(a),a.tag=3,a.payload={element:null},a.callback=function(){Fi(e,t)},a}function Jf(e){return e=ba(e),e.tag=3,e}function Wf(e,t,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var s=l.value;e.payload=function(){return n(s)},e.callback=function(){Kf(t,a,l)}}var o=a.stateNode;o!==null&&typeof o.componentDidCatch=="function"&&(e.callback=function(){Kf(t,a,l),typeof n!="function"&&(Ea===null?Ea=new Set([this]):Ea.add(this));var m=l.stack;this.componentDidCatch(l.value,{componentStack:m!==null?m:""})})}function qg(e,t,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&xn(t,a,n,!0),a=At.current,a!==null){switch(a.tag){case 13:return Bt===null?Dr():a.alternate===null&&Re===0&&(Re=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===qu?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),Cr(e,l,n)),!1;case 22:return a.flags|=65536,l===qu?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),Cr(e,l,n)),!1}throw Error(r(435,a.tag))}return Cr(e,l,n),Dr(),!1}if(de)return t=At.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,l!==Mu&&(e=Error(r(422),{cause:l}),vn(St(e,a)))):(l!==Mu&&(t=Error(r(423),{cause:l}),vn(St(t,a))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,l=St(l,a),n=ur(e.stateNode,l,n),ku(e,n),Re!==4&&(Re=2)),!1;var s=Error(r(520),{cause:l});if(s=St(s,a),Ln===null?Ln=[s]:Ln.push(s),Re!==4&&(Re=2),t===null)return!0;l=St(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=n&-n,a.lanes|=e,e=ur(a.stateNode,l,e),ku(a,e),!1;case 1:if(t=a.type,s=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||s!==null&&typeof s.componentDidCatch=="function"&&(Ea===null||!Ea.has(s))))return a.flags|=65536,n&=-n,a.lanes|=n,n=Jf(n),Wf(n,e,a,l),ku(a,n),!1}a=a.return}while(a!==null);return!1}var Ff=Error(r(461)),Xe=!1;function Ve(e,t,a,l){t.child=e===null?Lf(t,null,a,l):Cl(t,e.child,a,l)}function $f(e,t,a,l,n){a=a.render;var s=t.ref;if("ref"in l){var o={};for(var m in l)m!=="ref"&&(o[m]=l[m])}else o=l;return Fa(t),l=Zu(e,t,a,o,s,n),m=Qu(),e!==null&&!Xe?(Vu(e,t,n),ta(e,t,n)):(de&&m&&Eu(t),t.flags|=1,Ve(e,t,l,n),t.child)}function Pf(e,t,a,l,n){if(e===null){var s=a.type;return typeof s=="function"&&!wu(s)&&s.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=s,If(e,t,s,l,n)):(e=Mi(a.type,null,l,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!gr(e,n)){var o=s.memoizedProps;if(a=a.compare,a=a!==null?a:gn,a(o,l)&&e.ref===t.ref)return ta(e,t,n)}return t.flags|=1,e=Jt(s,l),e.ref=t.ref,e.return=t,t.child=e}function If(e,t,a,l,n){if(e!==null){var s=e.memoizedProps;if(gn(s,l)&&e.ref===t.ref)if(Xe=!1,t.pendingProps=l=s,gr(e,n))(e.flags&131072)!==0&&(Xe=!0);else return t.lanes=e.lanes,ta(e,t,n)}return rr(e,t,a,l,n)}function ed(e,t,a){var l=t.pendingProps,n=l.children,s=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=s!==null?s.baseLanes|a:a,e!==null){for(n=t.child=e.child,s=0;n!==null;)s=s|n.lanes|n.childLanes,n=n.sibling;t.childLanes=s&~l}else t.childLanes=0,t.child=null;return td(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Ui(t,s!==null?s.cachePool:null),s!==null?Po(t,s):Yu(),Yf(t);else return t.lanes=t.childLanes=536870912,td(e,t,s!==null?s.baseLanes|a:a,a)}else s!==null?(Ui(t,s.cachePool),Po(t,s),Sa(),t.memoizedState=null):(e!==null&&Ui(t,null),Yu(),Sa());return Ve(e,t,n,a),t.child}function td(e,t,a,l){var n=_u();return n=n===null?null:{parent:He._currentValue,pool:n},t.memoizedState={baseLanes:a,cachePool:n},e!==null&&Ui(t,null),Yu(),Yf(t),e!==null&&xn(e,t,l,!0),null}function $i(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(r(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function rr(e,t,a,l,n){return Fa(t),a=Zu(e,t,a,l,void 0,n),l=Qu(),e!==null&&!Xe?(Vu(e,t,n),ta(e,t,n)):(de&&l&&Eu(t),t.flags|=1,Ve(e,t,a,n),t.child)}function ad(e,t,a,l,n,s){return Fa(t),t.updateQueue=null,a=ef(t,l,a,n),Io(e),l=Qu(),e!==null&&!Xe?(Vu(e,t,s),ta(e,t,s)):(de&&l&&Eu(t),t.flags|=1,Ve(e,t,a,s),t.child)}function ld(e,t,a,l,n){if(Fa(t),t.stateNode===null){var s=jl,o=a.contextType;typeof o=="object"&&o!==null&&(s=Pe(o)),s=new a(l,s),t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,s.updater=sr,t.stateNode=s,s._reactInternals=t,s=t.stateNode,s.props=l,s.state=t.memoizedState,s.refs={},Bu(t),o=a.contextType,s.context=typeof o=="object"&&o!==null?Pe(o):jl,s.state=t.memoizedState,o=a.getDerivedStateFromProps,typeof o=="function"&&(ir(t,a,o,l),s.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(o=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),o!==s.state&&sr.enqueueReplaceState(s,s.state,null),En(t,l,s,n),Tn(),s.state=t.memoizedState),typeof s.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){s=t.stateNode;var m=t.memoizedProps,y=Ia(a,m);s.props=y;var N=s.context,M=a.contextType;o=jl,typeof M=="object"&&M!==null&&(o=Pe(M));var D=a.getDerivedStateFromProps;M=typeof D=="function"||typeof s.getSnapshotBeforeUpdate=="function",m=t.pendingProps!==m,M||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(m||N!==o)&&Xf(t,s,l,o),ya=!1;var A=t.memoizedState;s.state=A,En(t,l,s,n),Tn(),N=t.memoizedState,m||A!==N||ya?(typeof D=="function"&&(ir(t,a,D,l),N=t.memoizedState),(y=ya||Gf(t,a,y,l,A,N,o))?(M||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=N),s.props=l,s.state=N,s.context=o,l=y):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{s=t.stateNode,Hu(e,t),o=t.memoizedProps,M=Ia(a,o),s.props=M,D=t.pendingProps,A=s.context,N=a.contextType,y=jl,typeof N=="object"&&N!==null&&(y=Pe(N)),m=a.getDerivedStateFromProps,(N=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(o!==D||A!==y)&&Xf(t,s,l,y),ya=!1,A=t.memoizedState,s.state=A,En(t,l,s,n),Tn();var T=t.memoizedState;o!==D||A!==T||ya||e!==null&&e.dependencies!==null&&Ri(e.dependencies)?(typeof m=="function"&&(ir(t,a,m,l),T=t.memoizedState),(M=ya||Gf(t,a,M,l,A,T,y)||e!==null&&e.dependencies!==null&&Ri(e.dependencies))?(N||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(l,T,y),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(l,T,y)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||o===e.memoizedProps&&A===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&A===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=T),s.props=l,s.state=T,s.context=y,l=M):(typeof s.componentDidUpdate!="function"||o===e.memoizedProps&&A===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&A===e.memoizedState||(t.flags|=1024),l=!1)}return s=l,$i(e,t),l=(t.flags&128)!==0,s||l?(s=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:s.render(),t.flags|=1,e!==null&&l?(t.child=Cl(t,e.child,null,n),t.child=Cl(t,null,a,n)):Ve(e,t,a,n),t.memoizedState=s.state,e=t.child):e=ta(e,t,n),e}function nd(e,t,a,l){return pn(),t.flags|=256,Ve(e,t,a,l),t.child}var cr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function or(e){return{baseLanes:e,cachePool:Zo()}}function fr(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=Tt),e}function id(e,t,a){var l=t.pendingProps,n=!1,s=(t.flags&128)!==0,o;if((o=s)||(o=e!==null&&e.memoizedState===null?!1:(ke.current&2)!==0),o&&(n=!0,t.flags&=-129),o=(t.flags&32)!==0,t.flags&=-33,e===null){if(de){if(n?xa(t):Sa(),de){var m=De,y;if(y=m){e:{for(y=m,m=qt;y.nodeType!==8;){if(!m){m=null;break e}if(y=Rt(y.nextSibling),y===null){m=null;break e}}m=y}m!==null?(t.memoizedState={dehydrated:m,treeContext:Qa!==null?{id:Wt,overflow:Ft}:null,retryLane:536870912,hydrationErrors:null},y=dt(18,null,null,0),y.stateNode=m,y.return=t,t.child=y,tt=t,De=null,y=!0):y=!1}y||Ja(t)}if(m=t.memoizedState,m!==null&&(m=m.dehydrated,m!==null))return Jr(m)?t.lanes=32:t.lanes=536870912,null;ea(t)}return m=l.children,l=l.fallback,n?(Sa(),n=t.mode,m=Pi({mode:"hidden",children:m},n),l=Za(l,n,a,null),m.return=t,l.return=t,m.sibling=l,t.child=m,n=t.child,n.memoizedState=or(a),n.childLanes=fr(e,o,a),t.memoizedState=cr,l):(xa(t),dr(t,m))}if(y=e.memoizedState,y!==null&&(m=y.dehydrated,m!==null)){if(s)t.flags&256?(xa(t),t.flags&=-257,t=mr(e,t,a)):t.memoizedState!==null?(Sa(),t.child=e.child,t.flags|=128,t=null):(Sa(),n=l.fallback,m=t.mode,l=Pi({mode:"visible",children:l.children},m),n=Za(n,m,a,null),n.flags|=2,l.return=t,n.return=t,l.sibling=n,t.child=l,Cl(t,e.child,null,a),l=t.child,l.memoizedState=or(a),l.childLanes=fr(e,o,a),t.memoizedState=cr,t=n);else if(xa(t),Jr(m)){if(o=m.nextSibling&&m.nextSibling.dataset,o)var N=o.dgst;o=N,l=Error(r(419)),l.stack="",l.digest=o,vn({value:l,source:null,stack:null}),t=mr(e,t,a)}else if(Xe||xn(e,t,a,!1),o=(a&e.childLanes)!==0,Xe||o){if(o=Te,o!==null&&(l=a&-a,l=(l&42)!==0?1:Ws(l),l=(l&(o.suspendedLanes|a))!==0?0:l,l!==0&&l!==y.retryLane))throw y.retryLane=l,Nl(e,l),bt(o,e,l),Ff;m.data==="$?"||Dr(),t=mr(e,t,a)}else m.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=y.treeContext,De=Rt(m.nextSibling),tt=t,de=!0,Ka=null,qt=!1,e!==null&&(jt[wt++]=Wt,jt[wt++]=Ft,jt[wt++]=Qa,Wt=e.id,Ft=e.overflow,Qa=t),t=dr(t,l.children),t.flags|=4096);return t}return n?(Sa(),n=l.fallback,m=t.mode,y=e.child,N=y.sibling,l=Jt(y,{mode:"hidden",children:l.children}),l.subtreeFlags=y.subtreeFlags&65011712,N!==null?n=Jt(N,n):(n=Za(n,m,a,null),n.flags|=2),n.return=t,l.return=t,l.sibling=n,t.child=l,l=n,n=t.child,m=e.child.memoizedState,m===null?m=or(a):(y=m.cachePool,y!==null?(N=He._currentValue,y=y.parent!==N?{parent:N,pool:N}:y):y=Zo(),m={baseLanes:m.baseLanes|a,cachePool:y}),n.memoizedState=m,n.childLanes=fr(e,o,a),t.memoizedState=cr,l):(xa(t),a=e.child,e=a.sibling,a=Jt(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(o=t.deletions,o===null?(t.deletions=[e],t.flags|=16):o.push(e)),t.child=a,t.memoizedState=null,a)}function dr(e,t){return t=Pi({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Pi(e,t){return e=dt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function mr(e,t,a){return Cl(t,e.child,null,a),e=dr(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function sd(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Du(e.return,t,a)}function hr(e,t,a,l,n){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=l,s.tail=a,s.tailMode=n)}function ud(e,t,a){var l=t.pendingProps,n=l.revealOrder,s=l.tail;if(Ve(e,t,l.children,a),l=ke.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&sd(e,a,t);else if(e.tag===19)sd(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch($(ke,l),n){case"forwards":for(a=t.child,n=null;a!==null;)e=a.alternate,e!==null&&Ji(e)===null&&(n=a),a=a.sibling;a=n,a===null?(n=t.child,t.child=null):(n=a.sibling,a.sibling=null),hr(t,!1,n,a,s);break;case"backwards":for(a=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Ji(e)===null){t.child=n;break}e=n.sibling,n.sibling=a,a=n,n=e}hr(t,!0,a,null,s);break;case"together":hr(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ta(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Ta|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(xn(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(r(153));if(t.child!==null){for(e=t.child,a=Jt(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Jt(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function gr(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Ri(e)))}function Bg(e,t,a){switch(t.tag){case 3:Ha(t,t.stateNode.containerInfo),ga(t,He,e.memoizedState.cache),pn();break;case 27:case 5:ca(t);break;case 4:Ha(t,t.stateNode.containerInfo);break;case 10:ga(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(xa(t),t.flags|=128,null):(a&t.child.childLanes)!==0?id(e,t,a):(xa(t),e=ta(e,t,a),e!==null?e.sibling:null);xa(t);break;case 19:var n=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(xn(e,t,a,!1),l=(a&t.childLanes)!==0),n){if(l)return ud(e,t,a);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),$(ke,ke.current),l)break;return null;case 22:case 23:return t.lanes=0,ed(e,t,a);case 24:ga(t,He,e.memoizedState.cache)}return ta(e,t,a)}function rd(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)Xe=!0;else{if(!gr(e,a)&&(t.flags&128)===0)return Xe=!1,Bg(e,t,a);Xe=(e.flags&131072)!==0}else Xe=!1,de&&(t.flags&1048576)!==0&&Bo(t,Di,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,n=l._init;if(l=n(l._payload),t.type=l,typeof l=="function")wu(l)?(e=Ia(l,e),t.tag=1,t=ld(null,t,l,e,a)):(t.tag=0,t=rr(null,t,l,e,a));else{if(l!=null){if(n=l.$$typeof,n===he){t.tag=11,t=$f(null,t,l,e,a);break e}else if(n===je){t.tag=14,t=Pf(null,t,l,e,a);break e}}throw t=Qt(l)||l,Error(r(306,t,""))}}return t;case 0:return rr(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,n=Ia(l,t.pendingProps),ld(e,t,l,n,a);case 3:e:{if(Ha(t,t.stateNode.containerInfo),e===null)throw Error(r(387));l=t.pendingProps;var s=t.memoizedState;n=s.element,Hu(e,t),En(t,l,null,a);var o=t.memoizedState;if(l=o.cache,ga(t,He,l),l!==s.cache&&Ru(t,[He],a,!0),Tn(),l=o.element,s.isDehydrated)if(s={element:l,isDehydrated:!1,cache:o.cache},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){t=nd(e,t,l,a);break e}else if(l!==n){n=St(Error(r(424)),t),vn(n),t=nd(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(De=Rt(e.firstChild),tt=t,de=!0,Ka=null,qt=!0,a=Lf(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(pn(),l===n){t=ta(e,t,a);break e}Ve(e,t,l,a)}t=t.child}return t;case 26:return $i(e,t),e===null?(a=dm(t.type,null,t.pendingProps,null))?t.memoizedState=a:de||(a=t.type,e=t.pendingProps,l=ds(zt.current).createElement(a),l[$e]=t,l[at]=e,Je(l,a,e),Ge(l),t.stateNode=l):t.memoizedState=dm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return ca(t),e===null&&de&&(l=t.stateNode=cm(t.type,t.pendingProps,zt.current),tt=t,qt=!0,n=De,Oa(t.type)?(Wr=n,De=Rt(l.firstChild)):De=n),Ve(e,t,t.pendingProps.children,a),$i(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&de&&((n=l=De)&&(l=fy(l,t.type,t.pendingProps,qt),l!==null?(t.stateNode=l,tt=t,De=Rt(l.firstChild),qt=!1,n=!0):n=!1),n||Ja(t)),ca(t),n=t.type,s=t.pendingProps,o=e!==null?e.memoizedProps:null,l=s.children,Qr(n,s)?l=null:o!==null&&Qr(n,o)&&(t.flags|=32),t.memoizedState!==null&&(n=Zu(e,t,Mg,null,null,a),Wn._currentValue=n),$i(e,t),Ve(e,t,l,a),t.child;case 6:return e===null&&de&&((e=a=De)&&(a=dy(a,t.pendingProps,qt),a!==null?(t.stateNode=a,tt=t,De=null,e=!0):e=!1),e||Ja(t)),null;case 13:return id(e,t,a);case 4:return Ha(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=Cl(t,null,l,a):Ve(e,t,l,a),t.child;case 11:return $f(e,t,t.type,t.pendingProps,a);case 7:return Ve(e,t,t.pendingProps,a),t.child;case 8:return Ve(e,t,t.pendingProps.children,a),t.child;case 12:return Ve(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,ga(t,t.type,l.value),Ve(e,t,l.children,a),t.child;case 9:return n=t.type._context,l=t.pendingProps.children,Fa(t),n=Pe(n),l=l(n),t.flags|=1,Ve(e,t,l,a),t.child;case 14:return Pf(e,t,t.type,t.pendingProps,a);case 15:return If(e,t,t.type,t.pendingProps,a);case 19:return ud(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=Pi(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Jt(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return ed(e,t,a);case 24:return Fa(t),l=Pe(He),e===null?(n=_u(),n===null&&(n=Te,s=Cu(),n.pooledCache=s,s.refCount++,s!==null&&(n.pooledCacheLanes|=a),n=s),t.memoizedState={parent:l,cache:n},Bu(t),ga(t,He,n)):((e.lanes&a)!==0&&(Hu(e,t),En(t,null,null,a),Tn()),n=e.memoizedState,s=t.memoizedState,n.parent!==l?(n={parent:l,cache:l},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),ga(t,He,l)):(l=s.cache,ga(t,He,l),l!==n.cache&&Ru(t,[He],a,!0))),Ve(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function aa(e){e.flags|=4}function cd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!bm(t)){if(t=At.current,t!==null&&((se&4194048)===se?Bt!==null:(se&62914560)!==se&&(se&536870912)===0||t!==Bt))throw wn=qu,Qo;e.flags|=8192}}function Ii(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?kc():536870912,e.lanes|=t,Bl|=t)}function Un(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Oe(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function Hg(e,t,a){var l=t.pendingProps;switch(zu(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Oe(t),null;case 1:return Oe(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Pt(He),Mt(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(bn(t)?aa(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Lo())),Oe(t),null;case 26:return a=t.memoizedState,e===null?(aa(t),a!==null?(Oe(t),cd(t,a)):(Oe(t),t.flags&=-16777217)):a?a!==e.memoizedState?(aa(t),Oe(t),cd(t,a)):(Oe(t),t.flags&=-16777217):(e.memoizedProps!==l&&aa(t),Oe(t),t.flags&=-16777217),null;case 27:oa(t),a=zt.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&aa(t);else{if(!l){if(t.stateNode===null)throw Error(r(166));return Oe(t),null}e=ce.current,bn(t)?Ho(t):(e=cm(n,l,a),t.stateNode=e,aa(t))}return Oe(t),null;case 5:if(oa(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&aa(t);else{if(!l){if(t.stateNode===null)throw Error(r(166));return Oe(t),null}if(e=ce.current,bn(t))Ho(t);else{switch(n=ds(zt.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}e[$e]=t,e[at]=l;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(Je(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&aa(t)}}return Oe(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&aa(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(r(166));if(e=zt.current,bn(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,n=tt,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}e[$e]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||am(e.nodeValue,a)),e||Ja(t)}else e=ds(e).createTextNode(l),e[$e]=t,t.stateNode=e}return Oe(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=bn(t),l!==null&&l.dehydrated!==null){if(e===null){if(!n)throw Error(r(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(r(317));n[$e]=t}else pn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Oe(t),n=!1}else n=Lo(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(ea(t),t):(ea(t),null)}if(ea(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var s=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(s=l.memoizedState.cachePool.pool),s!==n&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),Ii(t,t.updateQueue),Oe(t),null;case 4:return Mt(),e===null&&Lr(t.stateNode.containerInfo),Oe(t),null;case 10:return Pt(t.type),Oe(t),null;case 19:if(we(ke),n=t.memoizedState,n===null)return Oe(t),null;if(l=(t.flags&128)!==0,s=n.rendering,s===null)if(l)Un(n,!1);else{if(Re!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(s=Ji(e),s!==null){for(t.flags|=128,Un(n,!1),e=s.updateQueue,t.updateQueue=e,Ii(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)qo(a,e),a=a.sibling;return $(ke,ke.current&1|2),t.child}e=e.sibling}n.tail!==null&&_t()>as&&(t.flags|=128,l=!0,Un(n,!1),t.lanes=4194304)}else{if(!l)if(e=Ji(s),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,Ii(t,e),Un(n,!0),n.tail===null&&n.tailMode==="hidden"&&!s.alternate&&!de)return Oe(t),null}else 2*_t()-n.renderingStartTime>as&&a!==536870912&&(t.flags|=128,l=!0,Un(n,!1),t.lanes=4194304);n.isBackwards?(s.sibling=t.child,t.child=s):(e=n.last,e!==null?e.sibling=s:t.child=s,n.last=s)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=_t(),t.sibling=null,e=ke.current,$(ke,l?e&1|2:e&1),t):(Oe(t),null);case 22:case 23:return ea(t),Gu(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(Oe(t),t.subtreeFlags&6&&(t.flags|=8192)):Oe(t),a=t.updateQueue,a!==null&&Ii(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&we($a),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Pt(He),Oe(t),null;case 25:return null;case 30:return null}throw Error(r(156,t.tag))}function kg(e,t){switch(zu(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Pt(He),Mt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return oa(t),null;case 13:if(ea(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(r(340));pn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return we(ke),null;case 4:return Mt(),null;case 10:return Pt(t.type),null;case 22:case 23:return ea(t),Gu(),e!==null&&we($a),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Pt(He),null;case 25:return null;default:return null}}function od(e,t){switch(zu(t),t.tag){case 3:Pt(He),Mt();break;case 26:case 27:case 5:oa(t);break;case 4:Mt();break;case 13:ea(t);break;case 19:we(ke);break;case 10:Pt(t.type);break;case 22:case 23:ea(t),Gu(),e!==null&&we($a);break;case 24:Pt(He)}}function _n(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&e)===e){l=void 0;var s=a.create,o=a.inst;l=s(),o.destroy=l}a=a.next}while(a!==n)}}catch(m){Ae(t,t.return,m)}}function Na(e,t,a){try{var l=t.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var s=n.next;l=s;do{if((l.tag&e)===e){var o=l.inst,m=o.destroy;if(m!==void 0){o.destroy=void 0,n=t;var y=a,N=m;try{N()}catch(M){Ae(n,y,M)}}}l=l.next}while(l!==s)}}catch(M){Ae(t,t.return,M)}}function fd(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{$o(t,a)}catch(l){Ae(e,e.return,l)}}}function dd(e,t,a){a.props=Ia(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){Ae(e,t,l)}}function qn(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(n){Ae(e,t,n)}}function Ht(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){Ae(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){Ae(e,t,n)}else a.current=null}function md(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){Ae(e,e.return,n)}}function yr(e,t,a){try{var l=e.stateNode;sy(l,e.type,a,t),l[at]=t}catch(n){Ae(e,e.return,n)}}function hd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Oa(e.type)||e.tag===4}function br(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||hd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Oa(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function pr(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=fs));else if(l!==4&&(l===27&&Oa(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(pr(e,t,a),e=e.sibling;e!==null;)pr(e,t,a),e=e.sibling}function es(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&Oa(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(es(e,t,a),e=e.sibling;e!==null;)es(e,t,a),e=e.sibling}function gd(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);Je(t,l,a),t[$e]=e,t[at]=a}catch(s){Ae(e,e.return,s)}}var la=!1,Ue=!1,vr=!1,yd=typeof WeakSet=="function"?WeakSet:Set,Ze=null;function Lg(e,t){if(e=e.containerInfo,Xr=ps,e=To(e),bu(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,s=l.focusNode;l=l.focusOffset;try{a.nodeType,s.nodeType}catch{a=null;break e}var o=0,m=-1,y=-1,N=0,M=0,D=e,A=null;t:for(;;){for(var T;D!==a||n!==0&&D.nodeType!==3||(m=o+n),D!==s||l!==0&&D.nodeType!==3||(y=o+l),D.nodeType===3&&(o+=D.nodeValue.length),(T=D.firstChild)!==null;)A=D,D=T;for(;;){if(D===e)break t;if(A===a&&++N===n&&(m=o),A===s&&++M===l&&(y=o),(T=D.nextSibling)!==null)break;D=A,A=D.parentNode}D=T}a=m===-1||y===-1?null:{start:m,end:y}}else a=null}a=a||{start:0,end:0}}else a=null;for(Zr={focusedElem:e,selectionRange:a},ps=!1,Ze=t;Ze!==null;)if(t=Ze,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ze=e;else for(;Ze!==null;){switch(t=Ze,s=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&s!==null){e=void 0,a=t,n=s.memoizedProps,s=s.memoizedState,l=a.stateNode;try{var W=Ia(a.type,n,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(W,s),l.__reactInternalSnapshotBeforeUpdate=e}catch(V){Ae(a,a.return,V)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Kr(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Kr(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(r(163))}if(e=t.sibling,e!==null){e.return=t.return,Ze=e;break}Ze=t.return}}function bd(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:ja(e,a),l&4&&_n(5,a);break;case 1:if(ja(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(o){Ae(a,a.return,o)}else{var n=Ia(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(o){Ae(a,a.return,o)}}l&64&&fd(a),l&512&&qn(a,a.return);break;case 3:if(ja(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{$o(e,t)}catch(o){Ae(a,a.return,o)}}break;case 27:t===null&&l&4&&gd(a);case 26:case 5:ja(e,a),t===null&&l&4&&md(a),l&512&&qn(a,a.return);break;case 12:ja(e,a);break;case 13:ja(e,a),l&4&&xd(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Wg.bind(null,a),my(e,a))));break;case 22:if(l=a.memoizedState!==null||la,!l){t=t!==null&&t.memoizedState!==null||Ue,n=la;var s=Ue;la=l,(Ue=t)&&!s?wa(e,a,(a.subtreeFlags&8772)!==0):ja(e,a),la=n,Ue=s}break;case 30:break;default:ja(e,a)}}function pd(e){var t=e.alternate;t!==null&&(e.alternate=null,pd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Ps(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ze=null,it=!1;function na(e,t,a){for(a=a.child;a!==null;)vd(e,t,a),a=a.sibling}function vd(e,t,a){if(ct&&typeof ct.onCommitFiberUnmount=="function")try{ct.onCommitFiberUnmount(an,a)}catch{}switch(a.tag){case 26:Ue||Ht(a,t),na(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ue||Ht(a,t);var l=ze,n=it;Oa(a.type)&&(ze=a.stateNode,it=!1),na(e,t,a),Qn(a.stateNode),ze=l,it=n;break;case 5:Ue||Ht(a,t);case 6:if(l=ze,n=it,ze=null,na(e,t,a),ze=l,it=n,ze!==null)if(it)try{(ze.nodeType===9?ze.body:ze.nodeName==="HTML"?ze.ownerDocument.body:ze).removeChild(a.stateNode)}catch(s){Ae(a,t,s)}else try{ze.removeChild(a.stateNode)}catch(s){Ae(a,t,s)}break;case 18:ze!==null&&(it?(e=ze,um(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),In(e)):um(ze,a.stateNode));break;case 4:l=ze,n=it,ze=a.stateNode.containerInfo,it=!0,na(e,t,a),ze=l,it=n;break;case 0:case 11:case 14:case 15:Ue||Na(2,a,t),Ue||Na(4,a,t),na(e,t,a);break;case 1:Ue||(Ht(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&dd(a,t,l)),na(e,t,a);break;case 21:na(e,t,a);break;case 22:Ue=(l=Ue)||a.memoizedState!==null,na(e,t,a),Ue=l;break;default:na(e,t,a)}}function xd(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{In(e)}catch(a){Ae(t,t.return,a)}}function Yg(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new yd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new yd),t;default:throw Error(r(435,e.tag))}}function xr(e,t){var a=Yg(e);t.forEach(function(l){var n=Fg.bind(null,e,l);a.has(l)||(a.add(l),l.then(n,n))})}function mt(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],s=e,o=t,m=o;e:for(;m!==null;){switch(m.tag){case 27:if(Oa(m.type)){ze=m.stateNode,it=!1;break e}break;case 5:ze=m.stateNode,it=!1;break e;case 3:case 4:ze=m.stateNode.containerInfo,it=!0;break e}m=m.return}if(ze===null)throw Error(r(160));vd(s,o,n),ze=null,it=!1,s=n.alternate,s!==null&&(s.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Sd(t,e),t=t.sibling}var Dt=null;function Sd(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:mt(t,e),ht(e),l&4&&(Na(3,e,e.return),_n(3,e),Na(5,e,e.return));break;case 1:mt(t,e),ht(e),l&512&&(Ue||a===null||Ht(a,a.return)),l&64&&la&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=Dt;if(mt(t,e),ht(e),l&512&&(Ue||a===null||Ht(a,a.return)),l&4){var s=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,n=n.ownerDocument||n;t:switch(l){case"title":s=n.getElementsByTagName("title")[0],(!s||s[sn]||s[$e]||s.namespaceURI==="http://www.w3.org/2000/svg"||s.hasAttribute("itemprop"))&&(s=n.createElement(l),n.head.insertBefore(s,n.querySelector("head > title"))),Je(s,l,a),s[$e]=e,Ge(s),l=s;break e;case"link":var o=gm("link","href",n).get(l+(a.href||""));if(o){for(var m=0;m<o.length;m++)if(s=o[m],s.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&s.getAttribute("rel")===(a.rel==null?null:a.rel)&&s.getAttribute("title")===(a.title==null?null:a.title)&&s.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){o.splice(m,1);break t}}s=n.createElement(l),Je(s,l,a),n.head.appendChild(s);break;case"meta":if(o=gm("meta","content",n).get(l+(a.content||""))){for(m=0;m<o.length;m++)if(s=o[m],s.getAttribute("content")===(a.content==null?null:""+a.content)&&s.getAttribute("name")===(a.name==null?null:a.name)&&s.getAttribute("property")===(a.property==null?null:a.property)&&s.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&s.getAttribute("charset")===(a.charSet==null?null:a.charSet)){o.splice(m,1);break t}}s=n.createElement(l),Je(s,l,a),n.head.appendChild(s);break;default:throw Error(r(468,l))}s[$e]=e,Ge(s),l=s}e.stateNode=l}else ym(n,e.type,e.stateNode);else e.stateNode=hm(n,l,e.memoizedProps);else s!==l?(s===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):s.count--,l===null?ym(n,e.type,e.stateNode):hm(n,l,e.memoizedProps)):l===null&&e.stateNode!==null&&yr(e,e.memoizedProps,a.memoizedProps)}break;case 27:mt(t,e),ht(e),l&512&&(Ue||a===null||Ht(a,a.return)),a!==null&&l&4&&yr(e,e.memoizedProps,a.memoizedProps);break;case 5:if(mt(t,e),ht(e),l&512&&(Ue||a===null||Ht(a,a.return)),e.flags&32){n=e.stateNode;try{gl(n,"")}catch(T){Ae(e,e.return,T)}}l&4&&e.stateNode!=null&&(n=e.memoizedProps,yr(e,n,a!==null?a.memoizedProps:n)),l&1024&&(vr=!0);break;case 6:if(mt(t,e),ht(e),l&4){if(e.stateNode===null)throw Error(r(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(T){Ae(e,e.return,T)}}break;case 3:if(gs=null,n=Dt,Dt=ms(t.containerInfo),mt(t,e),Dt=n,ht(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{In(t.containerInfo)}catch(T){Ae(e,e.return,T)}vr&&(vr=!1,Nd(e));break;case 4:l=Dt,Dt=ms(e.stateNode.containerInfo),mt(t,e),ht(e),Dt=l;break;case 12:mt(t,e),ht(e);break;case 13:mt(t,e),ht(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Tr=_t()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,xr(e,l)));break;case 22:n=e.memoizedState!==null;var y=a!==null&&a.memoizedState!==null,N=la,M=Ue;if(la=N||n,Ue=M||y,mt(t,e),Ue=M,la=N,ht(e),l&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(a===null||y||la||Ue||el(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){y=a=t;try{if(s=y.stateNode,n)o=s.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none";else{m=y.stateNode;var D=y.memoizedProps.style,A=D!=null&&D.hasOwnProperty("display")?D.display:null;m.style.display=A==null||typeof A=="boolean"?"":(""+A).trim()}}catch(T){Ae(y,y.return,T)}}}else if(t.tag===6){if(a===null){y=t;try{y.stateNode.nodeValue=n?"":y.memoizedProps}catch(T){Ae(y,y.return,T)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,xr(e,a))));break;case 19:mt(t,e),ht(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,xr(e,l)));break;case 30:break;case 21:break;default:mt(t,e),ht(e)}}function ht(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(hd(l)){a=l;break}l=l.return}if(a==null)throw Error(r(160));switch(a.tag){case 27:var n=a.stateNode,s=br(e);es(e,s,n);break;case 5:var o=a.stateNode;a.flags&32&&(gl(o,""),a.flags&=-33);var m=br(e);es(e,m,o);break;case 3:case 4:var y=a.stateNode.containerInfo,N=br(e);pr(e,N,y);break;default:throw Error(r(161))}}catch(M){Ae(e,e.return,M)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Nd(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Nd(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function ja(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)bd(e,t.alternate,t),t=t.sibling}function el(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Na(4,t,t.return),el(t);break;case 1:Ht(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&dd(t,t.return,a),el(t);break;case 27:Qn(t.stateNode);case 26:case 5:Ht(t,t.return),el(t);break;case 22:t.memoizedState===null&&el(t);break;case 30:el(t);break;default:el(t)}e=e.sibling}}function wa(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,n=e,s=t,o=s.flags;switch(s.tag){case 0:case 11:case 15:wa(n,s,a),_n(4,s);break;case 1:if(wa(n,s,a),l=s,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(N){Ae(l,l.return,N)}if(l=s,n=l.updateQueue,n!==null){var m=l.stateNode;try{var y=n.shared.hiddenCallbacks;if(y!==null)for(n.shared.hiddenCallbacks=null,n=0;n<y.length;n++)Fo(y[n],m)}catch(N){Ae(l,l.return,N)}}a&&o&64&&fd(s),qn(s,s.return);break;case 27:gd(s);case 26:case 5:wa(n,s,a),a&&l===null&&o&4&&md(s),qn(s,s.return);break;case 12:wa(n,s,a);break;case 13:wa(n,s,a),a&&o&4&&xd(n,s);break;case 22:s.memoizedState===null&&wa(n,s,a),qn(s,s.return);break;case 30:break;default:wa(n,s,a)}t=t.sibling}}function Sr(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Sn(a))}function Nr(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Sn(e))}function kt(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)jd(e,t,a,l),t=t.sibling}function jd(e,t,a,l){var n=t.flags;switch(t.tag){case 0:case 11:case 15:kt(e,t,a,l),n&2048&&_n(9,t);break;case 1:kt(e,t,a,l);break;case 3:kt(e,t,a,l),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Sn(e)));break;case 12:if(n&2048){kt(e,t,a,l),e=t.stateNode;try{var s=t.memoizedProps,o=s.id,m=s.onPostCommit;typeof m=="function"&&m(o,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(y){Ae(t,t.return,y)}}else kt(e,t,a,l);break;case 13:kt(e,t,a,l);break;case 23:break;case 22:s=t.stateNode,o=t.alternate,t.memoizedState!==null?s._visibility&2?kt(e,t,a,l):Bn(e,t):s._visibility&2?kt(e,t,a,l):(s._visibility|=2,Ul(e,t,a,l,(t.subtreeFlags&10256)!==0)),n&2048&&Sr(o,t);break;case 24:kt(e,t,a,l),n&2048&&Nr(t.alternate,t);break;default:kt(e,t,a,l)}}function Ul(e,t,a,l,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var s=e,o=t,m=a,y=l,N=o.flags;switch(o.tag){case 0:case 11:case 15:Ul(s,o,m,y,n),_n(8,o);break;case 23:break;case 22:var M=o.stateNode;o.memoizedState!==null?M._visibility&2?Ul(s,o,m,y,n):Bn(s,o):(M._visibility|=2,Ul(s,o,m,y,n)),n&&N&2048&&Sr(o.alternate,o);break;case 24:Ul(s,o,m,y,n),n&&N&2048&&Nr(o.alternate,o);break;default:Ul(s,o,m,y,n)}t=t.sibling}}function Bn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,n=l.flags;switch(l.tag){case 22:Bn(a,l),n&2048&&Sr(l.alternate,l);break;case 24:Bn(a,l),n&2048&&Nr(l.alternate,l);break;default:Bn(a,l)}t=t.sibling}}var Hn=8192;function _l(e){if(e.subtreeFlags&Hn)for(e=e.child;e!==null;)wd(e),e=e.sibling}function wd(e){switch(e.tag){case 26:_l(e),e.flags&Hn&&e.memoizedState!==null&&Ty(Dt,e.memoizedState,e.memoizedProps);break;case 5:_l(e);break;case 3:case 4:var t=Dt;Dt=ms(e.stateNode.containerInfo),_l(e),Dt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Hn,Hn=16777216,_l(e),Hn=t):_l(e));break;default:_l(e)}}function Ad(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function kn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Ze=l,Ed(l,e)}Ad(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Td(e),e=e.sibling}function Td(e){switch(e.tag){case 0:case 11:case 15:kn(e),e.flags&2048&&Na(9,e,e.return);break;case 3:kn(e);break;case 12:kn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,ts(e)):kn(e);break;default:kn(e)}}function ts(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Ze=l,Ed(l,e)}Ad(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Na(8,t,t.return),ts(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,ts(t));break;default:ts(t)}e=e.sibling}}function Ed(e,t){for(;Ze!==null;){var a=Ze;switch(a.tag){case 0:case 11:case 15:Na(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Sn(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Ze=l;else e:for(a=e;Ze!==null;){l=Ze;var n=l.sibling,s=l.return;if(pd(l),l===a){Ze=null;break e}if(n!==null){n.return=s,Ze=n;break e}Ze=s}}}var Gg={getCacheForType:function(e){var t=Pe(He),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},Xg=typeof WeakMap=="function"?WeakMap:Map,ge=0,Te=null,le=null,se=0,ye=0,gt=null,Aa=!1,ql=!1,jr=!1,ia=0,Re=0,Ta=0,tl=0,wr=0,Tt=0,Bl=0,Ln=null,st=null,Ar=!1,Tr=0,as=1/0,ls=null,Ea=null,Ke=0,za=null,Hl=null,kl=0,Er=0,zr=null,zd=null,Yn=0,Mr=null;function yt(){if((ge&2)!==0&&se!==0)return se&-se;if(z.T!==null){var e=Tl;return e!==0?e:qr()}return Gc()}function Md(){Tt===0&&(Tt=(se&536870912)===0||de?Hc():536870912);var e=At.current;return e!==null&&(e.flags|=32),Tt}function bt(e,t,a){(e===Te&&(ye===2||ye===9)||e.cancelPendingCommit!==null)&&(Ll(e,0),Ma(e,se,Tt,!1)),nn(e,a),((ge&2)===0||e!==Te)&&(e===Te&&((ge&2)===0&&(tl|=a),Re===4&&Ma(e,se,Tt,!1)),Lt(e))}function Od(e,t,a){if((ge&6)!==0)throw Error(r(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||ln(e,t),n=l?Vg(e,t):Rr(e,t,!0),s=l;do{if(n===0){ql&&!l&&Ma(e,t,0,!1);break}else{if(a=e.current.alternate,s&&!Zg(a)){n=Rr(e,t,!1),s=!1;continue}if(n===2){if(s=t,e.errorRecoveryDisabledLanes&s)var o=0;else o=e.pendingLanes&-536870913,o=o!==0?o:o&536870912?536870912:0;if(o!==0){t=o;e:{var m=e;n=Ln;var y=m.current.memoizedState.isDehydrated;if(y&&(Ll(m,o).flags|=256),o=Rr(m,o,!1),o!==2){if(jr&&!y){m.errorRecoveryDisabledLanes|=s,tl|=s,n=4;break e}s=st,st=n,s!==null&&(st===null?st=s:st.push.apply(st,s))}n=o}if(s=!1,n!==2)continue}}if(n===1){Ll(e,0),Ma(e,t,0,!0);break}e:{switch(l=e,s=n,s){case 0:case 1:throw Error(r(345));case 4:if((t&4194048)!==t)break;case 6:Ma(l,t,Tt,!Aa);break e;case 2:st=null;break;case 3:case 5:break;default:throw Error(r(329))}if((t&62914560)===t&&(n=Tr+300-_t(),10<n)){if(Ma(l,t,Tt,!Aa),hi(l,0,!0)!==0)break e;l.timeoutHandle=im(Dd.bind(null,l,a,st,ls,Ar,t,Tt,tl,Bl,Aa,s,2,-0,0),n);break e}Dd(l,a,st,ls,Ar,t,Tt,tl,Bl,Aa,s,0,-0,0)}}break}while(!0);Lt(e)}function Dd(e,t,a,l,n,s,o,m,y,N,M,D,A,T){if(e.timeoutHandle=-1,D=t.subtreeFlags,(D&8192||(D&16785408)===16785408)&&(Jn={stylesheets:null,count:0,unsuspend:Ay},wd(t),D=Ey(),D!==null)){e.cancelPendingCommit=D(Hd.bind(null,e,t,s,a,l,n,o,m,y,M,1,A,T)),Ma(e,s,o,!N);return}Hd(e,t,s,a,l,n,o,m,y)}function Zg(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],s=n.getSnapshot;n=n.value;try{if(!ft(s(),n))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ma(e,t,a,l){t&=~wr,t&=~tl,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var n=t;0<n;){var s=31-ot(n),o=1<<s;l[s]=-1,n&=~o}a!==0&&Lc(e,a,t)}function ns(){return(ge&6)===0?(Gn(0),!1):!0}function Or(){if(le!==null){if(ye===0)var e=le.return;else e=le,$t=Wa=null,Ku(e),Rl=null,Rn=0,e=le;for(;e!==null;)od(e.alternate,e),e=e.return;le=null}}function Ll(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,ry(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Or(),Te=e,le=a=Jt(e.current,null),se=t,ye=0,gt=null,Aa=!1,ql=ln(e,t),jr=!1,Bl=Tt=wr=tl=Ta=Re=0,st=Ln=null,Ar=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var n=31-ot(l),s=1<<n;t|=e[n],l&=~s}return ia=t,Ti(),a}function Rd(e,t){ee=null,z.H=Qi,t===jn||t===_i?(t=Jo(),ye=3):t===Qo?(t=Jo(),ye=4):ye=t===Ff?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,gt=t,le===null&&(Re=1,Fi(e,St(t,e.current)))}function Cd(){var e=z.H;return z.H=Qi,e===null?Qi:e}function Ud(){var e=z.A;return z.A=Gg,e}function Dr(){Re=4,Aa||(se&4194048)!==se&&At.current!==null||(ql=!0),(Ta&134217727)===0&&(tl&134217727)===0||Te===null||Ma(Te,se,Tt,!1)}function Rr(e,t,a){var l=ge;ge|=2;var n=Cd(),s=Ud();(Te!==e||se!==t)&&(ls=null,Ll(e,t)),t=!1;var o=Re;e:do try{if(ye!==0&&le!==null){var m=le,y=gt;switch(ye){case 8:Or(),o=6;break e;case 3:case 2:case 9:case 6:At.current===null&&(t=!0);var N=ye;if(ye=0,gt=null,Yl(e,m,y,N),a&&ql){o=0;break e}break;default:N=ye,ye=0,gt=null,Yl(e,m,y,N)}}Qg(),o=Re;break}catch(M){Rd(e,M)}while(!0);return t&&e.shellSuspendCounter++,$t=Wa=null,ge=l,z.H=n,z.A=s,le===null&&(Te=null,se=0,Ti()),o}function Qg(){for(;le!==null;)_d(le)}function Vg(e,t){var a=ge;ge|=2;var l=Cd(),n=Ud();Te!==e||se!==t?(ls=null,as=_t()+500,Ll(e,t)):ql=ln(e,t);e:do try{if(ye!==0&&le!==null){t=le;var s=gt;t:switch(ye){case 1:ye=0,gt=null,Yl(e,t,s,1);break;case 2:case 9:if(Vo(s)){ye=0,gt=null,qd(t);break}t=function(){ye!==2&&ye!==9||Te!==e||(ye=7),Lt(e)},s.then(t,t);break e;case 3:ye=7;break e;case 4:ye=5;break e;case 7:Vo(s)?(ye=0,gt=null,qd(t)):(ye=0,gt=null,Yl(e,t,s,7));break;case 5:var o=null;switch(le.tag){case 26:o=le.memoizedState;case 5:case 27:var m=le;if(!o||bm(o)){ye=0,gt=null;var y=m.sibling;if(y!==null)le=y;else{var N=m.return;N!==null?(le=N,is(N)):le=null}break t}}ye=0,gt=null,Yl(e,t,s,5);break;case 6:ye=0,gt=null,Yl(e,t,s,6);break;case 8:Or(),Re=6;break e;default:throw Error(r(462))}}Kg();break}catch(M){Rd(e,M)}while(!0);return $t=Wa=null,z.H=l,z.A=n,ge=a,le!==null?0:(Te=null,se=0,Ti(),Re)}function Kg(){for(;le!==null&&!g0();)_d(le)}function _d(e){var t=rd(e.alternate,e,ia);e.memoizedProps=e.pendingProps,t===null?is(e):le=t}function qd(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=ad(a,t,t.pendingProps,t.type,void 0,se);break;case 11:t=ad(a,t,t.pendingProps,t.type.render,t.ref,se);break;case 5:Ku(t);default:od(a,t),t=le=qo(t,ia),t=rd(a,t,ia)}e.memoizedProps=e.pendingProps,t===null?is(e):le=t}function Yl(e,t,a,l){$t=Wa=null,Ku(t),Rl=null,Rn=0;var n=t.return;try{if(qg(e,n,t,a,se)){Re=1,Fi(e,St(a,e.current)),le=null;return}}catch(s){if(n!==null)throw le=n,s;Re=1,Fi(e,St(a,e.current)),le=null;return}t.flags&32768?(de||l===1?e=!0:ql||(se&536870912)!==0?e=!1:(Aa=e=!0,(l===2||l===9||l===3||l===6)&&(l=At.current,l!==null&&l.tag===13&&(l.flags|=16384))),Bd(t,e)):is(t)}function is(e){var t=e;do{if((t.flags&32768)!==0){Bd(t,Aa);return}e=t.return;var a=Hg(t.alternate,t,ia);if(a!==null){le=a;return}if(t=t.sibling,t!==null){le=t;return}le=t=e}while(t!==null);Re===0&&(Re=5)}function Bd(e,t){do{var a=kg(e.alternate,e);if(a!==null){a.flags&=32767,le=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){le=e;return}le=e=a}while(e!==null);Re=6,le=null}function Hd(e,t,a,l,n,s,o,m,y){e.cancelPendingCommit=null;do ss();while(Ke!==0);if((ge&6)!==0)throw Error(r(327));if(t!==null){if(t===e.current)throw Error(r(177));if(s=t.lanes|t.childLanes,s|=Nu,A0(e,a,s,o,m,y),e===Te&&(le=Te=null,se=0),Hl=t,za=e,kl=a,Er=s,zr=n,zd=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,$g(fi,function(){return Xd(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=z.T,z.T=null,n=H.p,H.p=2,o=ge,ge|=4;try{Lg(e,t,a)}finally{ge=o,H.p=n,z.T=l}}Ke=1,kd(),Ld(),Yd()}}function kd(){if(Ke===1){Ke=0;var e=za,t=Hl,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=z.T,z.T=null;var l=H.p;H.p=2;var n=ge;ge|=4;try{Sd(t,e);var s=Zr,o=To(e.containerInfo),m=s.focusedElem,y=s.selectionRange;if(o!==m&&m&&m.ownerDocument&&Ao(m.ownerDocument.documentElement,m)){if(y!==null&&bu(m)){var N=y.start,M=y.end;if(M===void 0&&(M=N),"selectionStart"in m)m.selectionStart=N,m.selectionEnd=Math.min(M,m.value.length);else{var D=m.ownerDocument||document,A=D&&D.defaultView||window;if(A.getSelection){var T=A.getSelection(),W=m.textContent.length,V=Math.min(y.start,W),Se=y.end===void 0?V:Math.min(y.end,W);!T.extend&&V>Se&&(o=Se,Se=V,V=o);var v=wo(m,V),p=wo(m,Se);if(v&&p&&(T.rangeCount!==1||T.anchorNode!==v.node||T.anchorOffset!==v.offset||T.focusNode!==p.node||T.focusOffset!==p.offset)){var S=D.createRange();S.setStart(v.node,v.offset),T.removeAllRanges(),V>Se?(T.addRange(S),T.extend(p.node,p.offset)):(S.setEnd(p.node,p.offset),T.addRange(S))}}}}for(D=[],T=m;T=T.parentNode;)T.nodeType===1&&D.push({element:T,left:T.scrollLeft,top:T.scrollTop});for(typeof m.focus=="function"&&m.focus(),m=0;m<D.length;m++){var O=D[m];O.element.scrollLeft=O.left,O.element.scrollTop=O.top}}ps=!!Xr,Zr=Xr=null}finally{ge=n,H.p=l,z.T=a}}e.current=t,Ke=2}}function Ld(){if(Ke===2){Ke=0;var e=za,t=Hl,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=z.T,z.T=null;var l=H.p;H.p=2;var n=ge;ge|=4;try{bd(e,t.alternate,t)}finally{ge=n,H.p=l,z.T=a}}Ke=3}}function Yd(){if(Ke===4||Ke===3){Ke=0,y0();var e=za,t=Hl,a=kl,l=zd;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ke=5:(Ke=0,Hl=za=null,Gd(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(Ea=null),Fs(a),t=t.stateNode,ct&&typeof ct.onCommitFiberRoot=="function")try{ct.onCommitFiberRoot(an,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=z.T,n=H.p,H.p=2,z.T=null;try{for(var s=e.onRecoverableError,o=0;o<l.length;o++){var m=l[o];s(m.value,{componentStack:m.stack})}}finally{z.T=t,H.p=n}}(kl&3)!==0&&ss(),Lt(e),n=e.pendingLanes,(a&4194090)!==0&&(n&42)!==0?e===Mr?Yn++:(Yn=0,Mr=e):Yn=0,Gn(0)}}function Gd(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Sn(t)))}function ss(e){return kd(),Ld(),Yd(),Xd()}function Xd(){if(Ke!==5)return!1;var e=za,t=Er;Er=0;var a=Fs(kl),l=z.T,n=H.p;try{H.p=32>a?32:a,z.T=null,a=zr,zr=null;var s=za,o=kl;if(Ke=0,Hl=za=null,kl=0,(ge&6)!==0)throw Error(r(331));var m=ge;if(ge|=4,Td(s.current),jd(s,s.current,o,a),ge=m,Gn(0,!1),ct&&typeof ct.onPostCommitFiberRoot=="function")try{ct.onPostCommitFiberRoot(an,s)}catch{}return!0}finally{H.p=n,z.T=l,Gd(e,t)}}function Zd(e,t,a){t=St(a,t),t=ur(e.stateNode,t,2),e=pa(e,t,2),e!==null&&(nn(e,2),Lt(e))}function Ae(e,t,a){if(e.tag===3)Zd(e,e,a);else for(;t!==null;){if(t.tag===3){Zd(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Ea===null||!Ea.has(l))){e=St(a,e),a=Jf(2),l=pa(t,a,2),l!==null&&(Wf(a,l,t,e),nn(l,2),Lt(l));break}}t=t.return}}function Cr(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new Xg;var n=new Set;l.set(t,n)}else n=l.get(t),n===void 0&&(n=new Set,l.set(t,n));n.has(a)||(jr=!0,n.add(a),e=Jg.bind(null,e,t,a),t.then(e,e))}function Jg(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Te===e&&(se&a)===a&&(Re===4||Re===3&&(se&62914560)===se&&300>_t()-Tr?(ge&2)===0&&Ll(e,0):wr|=a,Bl===se&&(Bl=0)),Lt(e)}function Qd(e,t){t===0&&(t=kc()),e=Nl(e,t),e!==null&&(nn(e,t),Lt(e))}function Wg(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Qd(e,a)}function Fg(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,n=e.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(r(314))}l!==null&&l.delete(t),Qd(e,a)}function $g(e,t){return Vs(e,t)}var us=null,Gl=null,Ur=!1,rs=!1,_r=!1,al=0;function Lt(e){e!==Gl&&e.next===null&&(Gl===null?us=Gl=e:Gl=Gl.next=e),rs=!0,Ur||(Ur=!0,Ig())}function Gn(e,t){if(!_r&&rs){_r=!0;do for(var a=!1,l=us;l!==null;){if(e!==0){var n=l.pendingLanes;if(n===0)var s=0;else{var o=l.suspendedLanes,m=l.pingedLanes;s=(1<<31-ot(42|e)+1)-1,s&=n&~(o&~m),s=s&201326741?s&201326741|1:s?s|2:0}s!==0&&(a=!0,Wd(l,s))}else s=se,s=hi(l,l===Te?s:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(s&3)===0||ln(l,s)||(a=!0,Wd(l,s));l=l.next}while(a);_r=!1}}function Pg(){Vd()}function Vd(){rs=Ur=!1;var e=0;al!==0&&(uy()&&(e=al),al=0);for(var t=_t(),a=null,l=us;l!==null;){var n=l.next,s=Kd(l,t);s===0?(l.next=null,a===null?us=n:a.next=n,n===null&&(Gl=a)):(a=l,(e!==0||(s&3)!==0)&&(rs=!0)),l=n}Gn(e)}function Kd(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,n=e.expirationTimes,s=e.pendingLanes&-62914561;0<s;){var o=31-ot(s),m=1<<o,y=n[o];y===-1?((m&a)===0||(m&l)!==0)&&(n[o]=w0(m,t)):y<=t&&(e.expiredLanes|=m),s&=~m}if(t=Te,a=se,a=hi(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(ye===2||ye===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&Ks(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||ln(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&Ks(l),Fs(a)){case 2:case 8:a=qc;break;case 32:a=fi;break;case 268435456:a=Bc;break;default:a=fi}return l=Jd.bind(null,e),a=Vs(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&Ks(l),e.callbackPriority=2,e.callbackNode=null,2}function Jd(e,t){if(Ke!==0&&Ke!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(ss()&&e.callbackNode!==a)return null;var l=se;return l=hi(e,e===Te?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Od(e,l,t),Kd(e,_t()),e.callbackNode!=null&&e.callbackNode===a?Jd.bind(null,e):null)}function Wd(e,t){if(ss())return null;Od(e,t,!0)}function Ig(){cy(function(){(ge&6)!==0?Vs(_c,Pg):Vd()})}function qr(){return al===0&&(al=Hc()),al}function Fd(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:vi(""+e)}function $d(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function ey(e,t,a,l,n){if(t==="submit"&&a&&a.stateNode===n){var s=Fd((n[at]||null).action),o=l.submitter;o&&(t=(t=o[at]||null)?Fd(t.formAction):o.getAttribute("formAction"),t!==null&&(s=t,o=null));var m=new ji("action","action",null,l,n);e.push({event:m,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(al!==0){var y=o?$d(n,o):new FormData(n);ar(a,{pending:!0,data:y,method:n.method,action:s},null,y)}}else typeof s=="function"&&(m.preventDefault(),y=o?$d(n,o):new FormData(n),ar(a,{pending:!0,data:y,method:n.method,action:s},s,y))},currentTarget:n}]})}}for(var Br=0;Br<Su.length;Br++){var Hr=Su[Br],ty=Hr.toLowerCase(),ay=Hr[0].toUpperCase()+Hr.slice(1);Ot(ty,"on"+ay)}Ot(Mo,"onAnimationEnd"),Ot(Oo,"onAnimationIteration"),Ot(Do,"onAnimationStart"),Ot("dblclick","onDoubleClick"),Ot("focusin","onFocus"),Ot("focusout","onBlur"),Ot(vg,"onTransitionRun"),Ot(xg,"onTransitionStart"),Ot(Sg,"onTransitionCancel"),Ot(Ro,"onTransitionEnd"),dl("onMouseEnter",["mouseout","mouseover"]),dl("onMouseLeave",["mouseout","mouseover"]),dl("onPointerEnter",["pointerout","pointerover"]),dl("onPointerLeave",["pointerout","pointerover"]),La("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),La("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),La("onBeforeInput",["compositionend","keypress","textInput","paste"]),La("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),La("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),La("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Xn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ly=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Xn));function Pd(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],n=l.event;l=l.listeners;e:{var s=void 0;if(t)for(var o=l.length-1;0<=o;o--){var m=l[o],y=m.instance,N=m.currentTarget;if(m=m.listener,y!==s&&n.isPropagationStopped())break e;s=m,n.currentTarget=N;try{s(n)}catch(M){Wi(M)}n.currentTarget=null,s=y}else for(o=0;o<l.length;o++){if(m=l[o],y=m.instance,N=m.currentTarget,m=m.listener,y!==s&&n.isPropagationStopped())break e;s=m,n.currentTarget=N;try{s(n)}catch(M){Wi(M)}n.currentTarget=null,s=y}}}}function ne(e,t){var a=t[$s];a===void 0&&(a=t[$s]=new Set);var l=e+"__bubble";a.has(l)||(Id(t,e,2,!1),a.add(l))}function kr(e,t,a){var l=0;t&&(l|=4),Id(a,e,l,t)}var cs="_reactListening"+Math.random().toString(36).slice(2);function Lr(e){if(!e[cs]){e[cs]=!0,Zc.forEach(function(a){a!=="selectionchange"&&(ly.has(a)||kr(a,!1,e),kr(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[cs]||(t[cs]=!0,kr("selectionchange",!1,t))}}function Id(e,t,a,l){switch(jm(t)){case 2:var n=Oy;break;case 8:n=Dy;break;default:n=ec}a=n.bind(null,t,a,e),n=void 0,!ru||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),l?n!==void 0?e.addEventListener(t,a,{capture:!0,passive:n}):e.addEventListener(t,a,!0):n!==void 0?e.addEventListener(t,a,{passive:n}):e.addEventListener(t,a,!1)}function Yr(e,t,a,l,n){var s=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var o=l.tag;if(o===3||o===4){var m=l.stateNode.containerInfo;if(m===n)break;if(o===4)for(o=l.return;o!==null;){var y=o.tag;if((y===3||y===4)&&o.stateNode.containerInfo===n)return;o=o.return}for(;m!==null;){if(o=cl(m),o===null)return;if(y=o.tag,y===5||y===6||y===26||y===27){l=s=o;continue e}m=m.parentNode}}l=l.return}no(function(){var N=s,M=su(a),D=[];e:{var A=Co.get(e);if(A!==void 0){var T=ji,W=e;switch(e){case"keypress":if(Si(a)===0)break e;case"keydown":case"keyup":T=$0;break;case"focusin":W="focus",T=du;break;case"focusout":W="blur",T=du;break;case"beforeblur":case"afterblur":T=du;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":T=uo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":T=k0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":T=eg;break;case Mo:case Oo:case Do:T=G0;break;case Ro:T=ag;break;case"scroll":case"scrollend":T=B0;break;case"wheel":T=ng;break;case"copy":case"cut":case"paste":T=Z0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":T=co;break;case"toggle":case"beforetoggle":T=sg}var V=(t&4)!==0,Se=!V&&(e==="scroll"||e==="scrollend"),v=V?A!==null?A+"Capture":null:A;V=[];for(var p=N,S;p!==null;){var O=p;if(S=O.stateNode,O=O.tag,O!==5&&O!==26&&O!==27||S===null||v===null||(O=rn(p,v),O!=null&&V.push(Zn(p,O,S))),Se)break;p=p.return}0<V.length&&(A=new T(A,W,null,a,M),D.push({event:A,listeners:V}))}}if((t&7)===0){e:{if(A=e==="mouseover"||e==="pointerover",T=e==="mouseout"||e==="pointerout",A&&a!==iu&&(W=a.relatedTarget||a.fromElement)&&(cl(W)||W[rl]))break e;if((T||A)&&(A=M.window===M?M:(A=M.ownerDocument)?A.defaultView||A.parentWindow:window,T?(W=a.relatedTarget||a.toElement,T=N,W=W?cl(W):null,W!==null&&(Se=h(W),V=W.tag,W!==Se||V!==5&&V!==27&&V!==6)&&(W=null)):(T=null,W=N),T!==W)){if(V=uo,O="onMouseLeave",v="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(V=co,O="onPointerLeave",v="onPointerEnter",p="pointer"),Se=T==null?A:un(T),S=W==null?A:un(W),A=new V(O,p+"leave",T,a,M),A.target=Se,A.relatedTarget=S,O=null,cl(M)===N&&(V=new V(v,p+"enter",W,a,M),V.target=S,V.relatedTarget=Se,O=V),Se=O,T&&W)t:{for(V=T,v=W,p=0,S=V;S;S=Xl(S))p++;for(S=0,O=v;O;O=Xl(O))S++;for(;0<p-S;)V=Xl(V),p--;for(;0<S-p;)v=Xl(v),S--;for(;p--;){if(V===v||v!==null&&V===v.alternate)break t;V=Xl(V),v=Xl(v)}V=null}else V=null;T!==null&&em(D,A,T,V,!1),W!==null&&Se!==null&&em(D,Se,W,V,!0)}}e:{if(A=N?un(N):window,T=A.nodeName&&A.nodeName.toLowerCase(),T==="select"||T==="input"&&A.type==="file")var k=po;else if(yo(A))if(vo)k=yg;else{k=hg;var ae=mg}else T=A.nodeName,!T||T.toLowerCase()!=="input"||A.type!=="checkbox"&&A.type!=="radio"?N&&nu(N.elementType)&&(k=po):k=gg;if(k&&(k=k(e,N))){bo(D,k,a,M);break e}ae&&ae(e,A,N),e==="focusout"&&N&&A.type==="number"&&N.memoizedProps.value!=null&&lu(A,"number",A.value)}switch(ae=N?un(N):window,e){case"focusin":(yo(ae)||ae.contentEditable==="true")&&(vl=ae,pu=N,yn=null);break;case"focusout":yn=pu=vl=null;break;case"mousedown":vu=!0;break;case"contextmenu":case"mouseup":case"dragend":vu=!1,Eo(D,a,M);break;case"selectionchange":if(pg)break;case"keydown":case"keyup":Eo(D,a,M)}var Q;if(hu)e:{switch(e){case"compositionstart":var K="onCompositionStart";break e;case"compositionend":K="onCompositionEnd";break e;case"compositionupdate":K="onCompositionUpdate";break e}K=void 0}else pl?ho(e,a)&&(K="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(K="onCompositionStart");K&&(oo&&a.locale!=="ko"&&(pl||K!=="onCompositionStart"?K==="onCompositionEnd"&&pl&&(Q=io()):(ha=M,cu="value"in ha?ha.value:ha.textContent,pl=!0)),ae=os(N,K),0<ae.length&&(K=new ro(K,e,null,a,M),D.push({event:K,listeners:ae}),Q?K.data=Q:(Q=go(a),Q!==null&&(K.data=Q)))),(Q=rg?cg(e,a):og(e,a))&&(K=os(N,"onBeforeInput"),0<K.length&&(ae=new ro("onBeforeInput","beforeinput",null,a,M),D.push({event:ae,listeners:K}),ae.data=Q)),ey(D,e,N,a,M)}Pd(D,t)})}function Zn(e,t,a){return{instance:e,listener:t,currentTarget:a}}function os(e,t){for(var a=t+"Capture",l=[];e!==null;){var n=e,s=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||s===null||(n=rn(e,a),n!=null&&l.unshift(Zn(e,n,s)),n=rn(e,t),n!=null&&l.push(Zn(e,n,s))),e.tag===3)return l;e=e.return}return[]}function Xl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function em(e,t,a,l,n){for(var s=t._reactName,o=[];a!==null&&a!==l;){var m=a,y=m.alternate,N=m.stateNode;if(m=m.tag,y!==null&&y===l)break;m!==5&&m!==26&&m!==27||N===null||(y=N,n?(N=rn(a,s),N!=null&&o.unshift(Zn(a,N,y))):n||(N=rn(a,s),N!=null&&o.push(Zn(a,N,y)))),a=a.return}o.length!==0&&e.push({event:t,listeners:o})}var ny=/\r\n?/g,iy=/\u0000|\uFFFD/g;function tm(e){return(typeof e=="string"?e:""+e).replace(ny,`
`).replace(iy,"")}function am(e,t){return t=tm(t),tm(e)===t}function fs(){}function xe(e,t,a,l,n,s){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||gl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&gl(e,""+l);break;case"className":yi(e,"class",l);break;case"tabIndex":yi(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":yi(e,a,l);break;case"style":ao(e,l,s);break;case"data":if(t!=="object"){yi(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=vi(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof s=="function"&&(a==="formAction"?(t!=="input"&&xe(e,t,"name",n.name,n,null),xe(e,t,"formEncType",n.formEncType,n,null),xe(e,t,"formMethod",n.formMethod,n,null),xe(e,t,"formTarget",n.formTarget,n,null)):(xe(e,t,"encType",n.encType,n,null),xe(e,t,"method",n.method,n,null),xe(e,t,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=vi(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=fs);break;case"onScroll":l!=null&&ne("scroll",e);break;case"onScrollEnd":l!=null&&ne("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(r(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=vi(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":ne("beforetoggle",e),ne("toggle",e),gi(e,"popover",l);break;case"xlinkActuate":Vt(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Vt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Vt(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Vt(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Vt(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Vt(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Vt(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Vt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Vt(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":gi(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=_0.get(a)||a,gi(e,a,l))}}function Gr(e,t,a,l,n,s){switch(a){case"style":ao(e,l,s);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(r(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(r(60));e.innerHTML=a}}break;case"children":typeof l=="string"?gl(e,l):(typeof l=="number"||typeof l=="bigint")&&gl(e,""+l);break;case"onScroll":l!=null&&ne("scroll",e);break;case"onScrollEnd":l!=null&&ne("scrollend",e);break;case"onClick":l!=null&&(e.onclick=fs);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Qc.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),t=a.slice(2,n?a.length-7:void 0),s=e[at]||null,s=s!=null?s[a]:null,typeof s=="function"&&e.removeEventListener(t,s,n),typeof l=="function")){typeof s!="function"&&s!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,n);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):gi(e,a,l)}}}function Je(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ne("error",e),ne("load",e);var l=!1,n=!1,s;for(s in a)if(a.hasOwnProperty(s)){var o=a[s];if(o!=null)switch(s){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:xe(e,t,s,o,a,null)}}n&&xe(e,t,"srcSet",a.srcSet,a,null),l&&xe(e,t,"src",a.src,a,null);return;case"input":ne("invalid",e);var m=s=o=n=null,y=null,N=null;for(l in a)if(a.hasOwnProperty(l)){var M=a[l];if(M!=null)switch(l){case"name":n=M;break;case"type":o=M;break;case"checked":y=M;break;case"defaultChecked":N=M;break;case"value":s=M;break;case"defaultValue":m=M;break;case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(r(137,t));break;default:xe(e,t,l,M,a,null)}}Pc(e,s,m,y,N,o,n,!1),bi(e);return;case"select":ne("invalid",e),l=o=s=null;for(n in a)if(a.hasOwnProperty(n)&&(m=a[n],m!=null))switch(n){case"value":s=m;break;case"defaultValue":o=m;break;case"multiple":l=m;default:xe(e,t,n,m,a,null)}t=s,a=o,e.multiple=!!l,t!=null?hl(e,!!l,t,!1):a!=null&&hl(e,!!l,a,!0);return;case"textarea":ne("invalid",e),s=n=l=null;for(o in a)if(a.hasOwnProperty(o)&&(m=a[o],m!=null))switch(o){case"value":l=m;break;case"defaultValue":n=m;break;case"children":s=m;break;case"dangerouslySetInnerHTML":if(m!=null)throw Error(r(91));break;default:xe(e,t,o,m,a,null)}eo(e,l,n,s),bi(e);return;case"option":for(y in a)if(a.hasOwnProperty(y)&&(l=a[y],l!=null))switch(y){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:xe(e,t,y,l,a,null)}return;case"dialog":ne("beforetoggle",e),ne("toggle",e),ne("cancel",e),ne("close",e);break;case"iframe":case"object":ne("load",e);break;case"video":case"audio":for(l=0;l<Xn.length;l++)ne(Xn[l],e);break;case"image":ne("error",e),ne("load",e);break;case"details":ne("toggle",e);break;case"embed":case"source":case"link":ne("error",e),ne("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(N in a)if(a.hasOwnProperty(N)&&(l=a[N],l!=null))switch(N){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:xe(e,t,N,l,a,null)}return;default:if(nu(t)){for(M in a)a.hasOwnProperty(M)&&(l=a[M],l!==void 0&&Gr(e,t,M,l,a,void 0));return}}for(m in a)a.hasOwnProperty(m)&&(l=a[m],l!=null&&xe(e,t,m,l,a,null))}function sy(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,s=null,o=null,m=null,y=null,N=null,M=null;for(T in a){var D=a[T];if(a.hasOwnProperty(T)&&D!=null)switch(T){case"checked":break;case"value":break;case"defaultValue":y=D;default:l.hasOwnProperty(T)||xe(e,t,T,null,l,D)}}for(var A in l){var T=l[A];if(D=a[A],l.hasOwnProperty(A)&&(T!=null||D!=null))switch(A){case"type":s=T;break;case"name":n=T;break;case"checked":N=T;break;case"defaultChecked":M=T;break;case"value":o=T;break;case"defaultValue":m=T;break;case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(r(137,t));break;default:T!==D&&xe(e,t,A,T,l,D)}}au(e,o,m,y,N,M,s,n);return;case"select":T=o=m=A=null;for(s in a)if(y=a[s],a.hasOwnProperty(s)&&y!=null)switch(s){case"value":break;case"multiple":T=y;default:l.hasOwnProperty(s)||xe(e,t,s,null,l,y)}for(n in l)if(s=l[n],y=a[n],l.hasOwnProperty(n)&&(s!=null||y!=null))switch(n){case"value":A=s;break;case"defaultValue":m=s;break;case"multiple":o=s;default:s!==y&&xe(e,t,n,s,l,y)}t=m,a=o,l=T,A!=null?hl(e,!!a,A,!1):!!l!=!!a&&(t!=null?hl(e,!!a,t,!0):hl(e,!!a,a?[]:"",!1));return;case"textarea":T=A=null;for(m in a)if(n=a[m],a.hasOwnProperty(m)&&n!=null&&!l.hasOwnProperty(m))switch(m){case"value":break;case"children":break;default:xe(e,t,m,null,l,n)}for(o in l)if(n=l[o],s=a[o],l.hasOwnProperty(o)&&(n!=null||s!=null))switch(o){case"value":A=n;break;case"defaultValue":T=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(r(91));break;default:n!==s&&xe(e,t,o,n,l,s)}Ic(e,A,T);return;case"option":for(var W in a)if(A=a[W],a.hasOwnProperty(W)&&A!=null&&!l.hasOwnProperty(W))switch(W){case"selected":e.selected=!1;break;default:xe(e,t,W,null,l,A)}for(y in l)if(A=l[y],T=a[y],l.hasOwnProperty(y)&&A!==T&&(A!=null||T!=null))switch(y){case"selected":e.selected=A&&typeof A!="function"&&typeof A!="symbol";break;default:xe(e,t,y,A,l,T)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var V in a)A=a[V],a.hasOwnProperty(V)&&A!=null&&!l.hasOwnProperty(V)&&xe(e,t,V,null,l,A);for(N in l)if(A=l[N],T=a[N],l.hasOwnProperty(N)&&A!==T&&(A!=null||T!=null))switch(N){case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(r(137,t));break;default:xe(e,t,N,A,l,T)}return;default:if(nu(t)){for(var Se in a)A=a[Se],a.hasOwnProperty(Se)&&A!==void 0&&!l.hasOwnProperty(Se)&&Gr(e,t,Se,void 0,l,A);for(M in l)A=l[M],T=a[M],!l.hasOwnProperty(M)||A===T||A===void 0&&T===void 0||Gr(e,t,M,A,l,T);return}}for(var v in a)A=a[v],a.hasOwnProperty(v)&&A!=null&&!l.hasOwnProperty(v)&&xe(e,t,v,null,l,A);for(D in l)A=l[D],T=a[D],!l.hasOwnProperty(D)||A===T||A==null&&T==null||xe(e,t,D,A,l,T)}var Xr=null,Zr=null;function ds(e){return e.nodeType===9?e:e.ownerDocument}function lm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function nm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Qr(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Vr=null;function uy(){var e=window.event;return e&&e.type==="popstate"?e===Vr?!1:(Vr=e,!0):(Vr=null,!1)}var im=typeof setTimeout=="function"?setTimeout:void 0,ry=typeof clearTimeout=="function"?clearTimeout:void 0,sm=typeof Promise=="function"?Promise:void 0,cy=typeof queueMicrotask=="function"?queueMicrotask:typeof sm<"u"?function(e){return sm.resolve(null).then(e).catch(oy)}:im;function oy(e){setTimeout(function(){throw e})}function Oa(e){return e==="head"}function um(e,t){var a=t,l=0,n=0;do{var s=a.nextSibling;if(e.removeChild(a),s&&s.nodeType===8)if(a=s.data,a==="/$"){if(0<l&&8>l){a=l;var o=e.ownerDocument;if(a&1&&Qn(o.documentElement),a&2&&Qn(o.body),a&4)for(a=o.head,Qn(a),o=a.firstChild;o;){var m=o.nextSibling,y=o.nodeName;o[sn]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&o.rel.toLowerCase()==="stylesheet"||a.removeChild(o),o=m}}if(n===0){e.removeChild(s),In(t);return}n--}else a==="$"||a==="$?"||a==="$!"?n++:l=a.charCodeAt(0)-48;else l=0;a=s}while(a);In(t)}function Kr(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Kr(a),Ps(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function fy(e,t,a,l){for(;e.nodeType===1;){var n=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[sn])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(s=e.getAttribute("rel"),s==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(s!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(s=e.getAttribute("src"),(s!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&s&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var s=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===s)return e}else return e;if(e=Rt(e.nextSibling),e===null)break}return null}function dy(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Rt(e.nextSibling),e===null))return null;return e}function Jr(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function my(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Rt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Wr=null;function rm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function cm(e,t,a){switch(t=ds(a),e){case"html":if(e=t.documentElement,!e)throw Error(r(452));return e;case"head":if(e=t.head,!e)throw Error(r(453));return e;case"body":if(e=t.body,!e)throw Error(r(454));return e;default:throw Error(r(451))}}function Qn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ps(e)}var Et=new Map,om=new Set;function ms(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var sa=H.d;H.d={f:hy,r:gy,D:yy,C:by,L:py,m:vy,X:Sy,S:xy,M:Ny};function hy(){var e=sa.f(),t=ns();return e||t}function gy(e){var t=ol(e);t!==null&&t.tag===5&&t.type==="form"?Of(t):sa.r(e)}var Zl=typeof document>"u"?null:document;function fm(e,t,a){var l=Zl;if(l&&typeof t=="string"&&t){var n=xt(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),om.has(n)||(om.add(n),e={rel:e,crossOrigin:a,href:t},l.querySelector(n)===null&&(t=l.createElement("link"),Je(t,"link",e),Ge(t),l.head.appendChild(t)))}}function yy(e){sa.D(e),fm("dns-prefetch",e,null)}function by(e,t){sa.C(e,t),fm("preconnect",e,t)}function py(e,t,a){sa.L(e,t,a);var l=Zl;if(l&&e&&t){var n='link[rel="preload"][as="'+xt(t)+'"]';t==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+xt(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+xt(a.imageSizes)+'"]')):n+='[href="'+xt(e)+'"]';var s=n;switch(t){case"style":s=Ql(e);break;case"script":s=Vl(e)}Et.has(s)||(e=x({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Et.set(s,e),l.querySelector(n)!==null||t==="style"&&l.querySelector(Vn(s))||t==="script"&&l.querySelector(Kn(s))||(t=l.createElement("link"),Je(t,"link",e),Ge(t),l.head.appendChild(t)))}}function vy(e,t){sa.m(e,t);var a=Zl;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+xt(l)+'"][href="'+xt(e)+'"]',s=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":s=Vl(e)}if(!Et.has(s)&&(e=x({rel:"modulepreload",href:e},t),Et.set(s,e),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Kn(s)))return}l=a.createElement("link"),Je(l,"link",e),Ge(l),a.head.appendChild(l)}}}function xy(e,t,a){sa.S(e,t,a);var l=Zl;if(l&&e){var n=fl(l).hoistableStyles,s=Ql(e);t=t||"default";var o=n.get(s);if(!o){var m={loading:0,preload:null};if(o=l.querySelector(Vn(s)))m.loading=5;else{e=x({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Et.get(s))&&Fr(e,a);var y=o=l.createElement("link");Ge(y),Je(y,"link",e),y._p=new Promise(function(N,M){y.onload=N,y.onerror=M}),y.addEventListener("load",function(){m.loading|=1}),y.addEventListener("error",function(){m.loading|=2}),m.loading|=4,hs(o,t,l)}o={type:"stylesheet",instance:o,count:1,state:m},n.set(s,o)}}}function Sy(e,t){sa.X(e,t);var a=Zl;if(a&&e){var l=fl(a).hoistableScripts,n=Vl(e),s=l.get(n);s||(s=a.querySelector(Kn(n)),s||(e=x({src:e,async:!0},t),(t=Et.get(n))&&$r(e,t),s=a.createElement("script"),Ge(s),Je(s,"link",e),a.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},l.set(n,s))}}function Ny(e,t){sa.M(e,t);var a=Zl;if(a&&e){var l=fl(a).hoistableScripts,n=Vl(e),s=l.get(n);s||(s=a.querySelector(Kn(n)),s||(e=x({src:e,async:!0,type:"module"},t),(t=Et.get(n))&&$r(e,t),s=a.createElement("script"),Ge(s),Je(s,"link",e),a.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},l.set(n,s))}}function dm(e,t,a,l){var n=(n=zt.current)?ms(n):null;if(!n)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Ql(a.href),a=fl(n).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Ql(a.href);var s=fl(n).hoistableStyles,o=s.get(e);if(o||(n=n.ownerDocument||n,o={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},s.set(e,o),(s=n.querySelector(Vn(e)))&&!s._p&&(o.instance=s,o.state.loading=5),Et.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Et.set(e,a),s||jy(n,e,a,o.state))),t&&l===null)throw Error(r(528,""));return o}if(t&&l!==null)throw Error(r(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Vl(a),a=fl(n).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function Ql(e){return'href="'+xt(e)+'"'}function Vn(e){return'link[rel="stylesheet"]['+e+"]"}function mm(e){return x({},e,{"data-precedence":e.precedence,precedence:null})}function jy(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),Je(t,"link",a),Ge(t),e.head.appendChild(t))}function Vl(e){return'[src="'+xt(e)+'"]'}function Kn(e){return"script[async]"+e}function hm(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+xt(a.href)+'"]');if(l)return t.instance=l,Ge(l),l;var n=x({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Ge(l),Je(l,"style",n),hs(l,a.precedence,e),t.instance=l;case"stylesheet":n=Ql(a.href);var s=e.querySelector(Vn(n));if(s)return t.state.loading|=4,t.instance=s,Ge(s),s;l=mm(a),(n=Et.get(n))&&Fr(l,n),s=(e.ownerDocument||e).createElement("link"),Ge(s);var o=s;return o._p=new Promise(function(m,y){o.onload=m,o.onerror=y}),Je(s,"link",l),t.state.loading|=4,hs(s,a.precedence,e),t.instance=s;case"script":return s=Vl(a.src),(n=e.querySelector(Kn(s)))?(t.instance=n,Ge(n),n):(l=a,(n=Et.get(s))&&(l=x({},a),$r(l,n)),e=e.ownerDocument||e,n=e.createElement("script"),Ge(n),Je(n,"link",l),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(r(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,hs(l,a.precedence,e));return t.instance}function hs(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,s=n,o=0;o<l.length;o++){var m=l[o];if(m.dataset.precedence===t)s=m;else if(s!==n)break}s?s.parentNode.insertBefore(e,s.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Fr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function $r(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var gs=null;function gm(e,t,a){if(gs===null){var l=new Map,n=gs=new Map;n.set(a,l)}else n=gs,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),n=0;n<a.length;n++){var s=a[n];if(!(s[sn]||s[$e]||e==="link"&&s.getAttribute("rel")==="stylesheet")&&s.namespaceURI!=="http://www.w3.org/2000/svg"){var o=s.getAttribute(t)||"";o=e+o;var m=l.get(o);m?m.push(s):l.set(o,[s])}}return l}function ym(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function wy(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function bm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Jn=null;function Ay(){}function Ty(e,t,a){if(Jn===null)throw Error(r(475));var l=Jn;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=Ql(a.href),s=e.querySelector(Vn(n));if(s){e=s._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=ys.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=s,Ge(s);return}s=e.ownerDocument||e,a=mm(a),(n=Et.get(n))&&Fr(a,n),s=s.createElement("link"),Ge(s);var o=s;o._p=new Promise(function(m,y){o.onload=m,o.onerror=y}),Je(s,"link",a),t.instance=s}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=ys.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function Ey(){if(Jn===null)throw Error(r(475));var e=Jn;return e.stylesheets&&e.count===0&&Pr(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Pr(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function ys(){if(this.count--,this.count===0){if(this.stylesheets)Pr(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var bs=null;function Pr(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,bs=new Map,t.forEach(zy,e),bs=null,ys.call(e))}function zy(e,t){if(!(t.state.loading&4)){var a=bs.get(e);if(a)var l=a.get(null);else{a=new Map,bs.set(e,a);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),s=0;s<n.length;s++){var o=n[s];(o.nodeName==="LINK"||o.getAttribute("media")!=="not all")&&(a.set(o.dataset.precedence,o),l=o)}l&&a.set(null,l)}n=t.instance,o=n.getAttribute("data-precedence"),s=a.get(o)||l,s===l&&a.set(null,n),a.set(o,n),this.count++,l=ys.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),s?s.parentNode.insertBefore(n,s.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var Wn={$$typeof:F,Provider:null,Consumer:null,_currentValue:_,_currentValue2:_,_threadCount:0};function My(e,t,a,l,n,s,o,m){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Js(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Js(0),this.hiddenUpdates=Js(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=s,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=m,this.incompleteTransitions=new Map}function pm(e,t,a,l,n,s,o,m,y,N,M,D){return e=new My(e,t,a,o,m,y,N,D),t=1,s===!0&&(t|=24),s=dt(3,null,null,t),e.current=s,s.stateNode=e,t=Cu(),t.refCount++,e.pooledCache=t,t.refCount++,s.memoizedState={element:l,isDehydrated:a,cache:t},Bu(s),e}function vm(e){return e?(e=jl,e):jl}function xm(e,t,a,l,n,s){n=vm(n),l.context===null?l.context=n:l.pendingContext=n,l=ba(t),l.payload={element:a},s=s===void 0?null:s,s!==null&&(l.callback=s),a=pa(e,l,t),a!==null&&(bt(a,e,t),An(a,e,t))}function Sm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function Ir(e,t){Sm(e,t),(e=e.alternate)&&Sm(e,t)}function Nm(e){if(e.tag===13){var t=Nl(e,67108864);t!==null&&bt(t,e,67108864),Ir(e,67108864)}}var ps=!0;function Oy(e,t,a,l){var n=z.T;z.T=null;var s=H.p;try{H.p=2,ec(e,t,a,l)}finally{H.p=s,z.T=n}}function Dy(e,t,a,l){var n=z.T;z.T=null;var s=H.p;try{H.p=8,ec(e,t,a,l)}finally{H.p=s,z.T=n}}function ec(e,t,a,l){if(ps){var n=tc(l);if(n===null)Yr(e,t,l,vs,a),wm(e,l);else if(Cy(n,e,t,a,l))l.stopPropagation();else if(wm(e,l),t&4&&-1<Ry.indexOf(e)){for(;n!==null;){var s=ol(n);if(s!==null)switch(s.tag){case 3:if(s=s.stateNode,s.current.memoizedState.isDehydrated){var o=ka(s.pendingLanes);if(o!==0){var m=s;for(m.pendingLanes|=2,m.entangledLanes|=2;o;){var y=1<<31-ot(o);m.entanglements[1]|=y,o&=~y}Lt(s),(ge&6)===0&&(as=_t()+500,Gn(0))}}break;case 13:m=Nl(s,2),m!==null&&bt(m,s,2),ns(),Ir(s,2)}if(s=tc(l),s===null&&Yr(e,t,l,vs,a),s===n)break;n=s}n!==null&&l.stopPropagation()}else Yr(e,t,l,null,a)}}function tc(e){return e=su(e),ac(e)}var vs=null;function ac(e){if(vs=null,e=cl(e),e!==null){var t=h(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=g(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return vs=e,null}function jm(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(b0()){case _c:return 2;case qc:return 8;case fi:case p0:return 32;case Bc:return 268435456;default:return 32}default:return 32}}var lc=!1,Da=null,Ra=null,Ca=null,Fn=new Map,$n=new Map,Ua=[],Ry="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function wm(e,t){switch(e){case"focusin":case"focusout":Da=null;break;case"dragenter":case"dragleave":Ra=null;break;case"mouseover":case"mouseout":Ca=null;break;case"pointerover":case"pointerout":Fn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":$n.delete(t.pointerId)}}function Pn(e,t,a,l,n,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:s,targetContainers:[n]},t!==null&&(t=ol(t),t!==null&&Nm(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function Cy(e,t,a,l,n){switch(t){case"focusin":return Da=Pn(Da,e,t,a,l,n),!0;case"dragenter":return Ra=Pn(Ra,e,t,a,l,n),!0;case"mouseover":return Ca=Pn(Ca,e,t,a,l,n),!0;case"pointerover":var s=n.pointerId;return Fn.set(s,Pn(Fn.get(s)||null,e,t,a,l,n)),!0;case"gotpointercapture":return s=n.pointerId,$n.set(s,Pn($n.get(s)||null,e,t,a,l,n)),!0}return!1}function Am(e){var t=cl(e.target);if(t!==null){var a=h(t);if(a!==null){if(t=a.tag,t===13){if(t=g(a),t!==null){e.blockedOn=t,T0(e.priority,function(){if(a.tag===13){var l=yt();l=Ws(l);var n=Nl(a,l);n!==null&&bt(n,a,l),Ir(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function xs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=tc(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);iu=l,a.target.dispatchEvent(l),iu=null}else return t=ol(a),t!==null&&Nm(t),e.blockedOn=a,!1;t.shift()}return!0}function Tm(e,t,a){xs(e)&&a.delete(t)}function Uy(){lc=!1,Da!==null&&xs(Da)&&(Da=null),Ra!==null&&xs(Ra)&&(Ra=null),Ca!==null&&xs(Ca)&&(Ca=null),Fn.forEach(Tm),$n.forEach(Tm)}function Ss(e,t){e.blockedOn===t&&(e.blockedOn=null,lc||(lc=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Uy)))}var Ns=null;function Em(e){Ns!==e&&(Ns=e,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Ns===e&&(Ns=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],n=e[t+2];if(typeof l!="function"){if(ac(l||a)===null)continue;break}var s=ol(a);s!==null&&(e.splice(t,3),t-=3,ar(s,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function In(e){function t(y){return Ss(y,e)}Da!==null&&Ss(Da,e),Ra!==null&&Ss(Ra,e),Ca!==null&&Ss(Ca,e),Fn.forEach(t),$n.forEach(t);for(var a=0;a<Ua.length;a++){var l=Ua[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Ua.length&&(a=Ua[0],a.blockedOn===null);)Am(a),a.blockedOn===null&&Ua.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],s=a[l+1],o=n[at]||null;if(typeof s=="function")o||Em(a);else if(o){var m=null;if(s&&s.hasAttribute("formAction")){if(n=s,o=s[at]||null)m=o.formAction;else if(ac(n)!==null)continue}else m=o.action;typeof m=="function"?a[l+1]=m:(a.splice(l,3),l-=3),Em(a)}}}function nc(e){this._internalRoot=e}js.prototype.render=nc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(r(409));var a=t.current,l=yt();xm(a,l,e,t,null,null)},js.prototype.unmount=nc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;xm(e.current,2,null,e,null,null),ns(),t[rl]=null}};function js(e){this._internalRoot=e}js.prototype.unstable_scheduleHydration=function(e){if(e){var t=Gc();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Ua.length&&t!==0&&t<Ua[a].priority;a++);Ua.splice(a,0,e),a===0&&Am(e)}};var zm=u.version;if(zm!=="19.1.0")throw Error(r(527,zm,"19.1.0"));H.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(r(188)):(e=Object.keys(e).join(","),Error(r(268,e)));return e=w(t),e=e!==null?j(e):null,e=e===null?null:e.stateNode,e};var _y={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:z,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ws=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ws.isDisabled&&ws.supportsFiber)try{an=ws.inject(_y),ct=ws}catch{}}return ti.createRoot=function(e,t){if(!f(e))throw Error(r(299));var a=!1,l="",n=Zf,s=Qf,o=Vf,m=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(s=t.onCaughtError),t.onRecoverableError!==void 0&&(o=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(m=t.unstable_transitionCallbacks)),t=pm(e,1,!1,null,null,a,l,n,s,o,m,null),e[rl]=t.current,Lr(e),new nc(t)},ti.hydrateRoot=function(e,t,a){if(!f(e))throw Error(r(299));var l=!1,n="",s=Zf,o=Qf,m=Vf,y=null,N=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(s=a.onUncaughtError),a.onCaughtError!==void 0&&(o=a.onCaughtError),a.onRecoverableError!==void 0&&(m=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(y=a.unstable_transitionCallbacks),a.formState!==void 0&&(N=a.formState)),t=pm(e,1,!0,t,a??null,l,n,s,o,m,y,N),t.context=vm(null),a=t.current,l=yt(),l=Ws(l),n=ba(l),n.callback=null,pa(a,n,l),a=l,t.current.lanes=a,nn(t,a),Lt(t),e[rl]=t.current,Lr(e),new js(t)},ti.version="19.1.0",ti}var _m;function hb(){if(_m)return ic.exports;_m=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(u){console.error(u)}}return i(),ic.exports=mb(),ic.exports}var gb=hb();const bh=6e4,ph=36e5,As=43200,qm=1440,Bm=Symbol.for("constructDateFrom");function Bs(i,u){return typeof i=="function"?i(u):i&&typeof i=="object"&&Bm in i?i[Bm](u):i instanceof Date?new i.constructor(u):new Date(u)}function Ct(i,u){return Bs(i,i)}let yb={};function bb(){return yb}function Hm(i){const u=Ct(i),c=new Date(Date.UTC(u.getFullYear(),u.getMonth(),u.getDate(),u.getHours(),u.getMinutes(),u.getSeconds(),u.getMilliseconds()));return c.setUTCFullYear(u.getFullYear()),+i-+c}function Oc(i,...u){const c=Bs.bind(null,i||u.find(r=>typeof r=="object"));return u.map(c)}function Os(i,u){const c=+Ct(i)-+Ct(u);return c<0?-1:c>0?1:c}function pb(i){return Bs(i,Date.now())}function vb(i){return i instanceof Date||typeof i=="object"&&Object.prototype.toString.call(i)==="[object Date]"}function xb(i){return!(!vb(i)&&typeof i!="number"||isNaN(+Ct(i)))}function Sb(i,u,c){const[r,f]=Oc(c==null?void 0:c.in,i,u),h=r.getFullYear()-f.getFullYear(),g=r.getMonth()-f.getMonth();return h*12+g}function Nb(i){return u=>{const r=(i?Math[i]:Math.trunc)(u);return r===0?0:r}}function jb(i,u){return+Ct(i)-+Ct(u)}function wb(i,u){const c=Ct(i);return c.setHours(23,59,59,999),c}function Ab(i,u){const c=Ct(i),r=c.getMonth();return c.setFullYear(c.getFullYear(),r+1,0),c.setHours(23,59,59,999),c}function Tb(i,u){const c=Ct(i);return+wb(c)==+Ab(c)}function Eb(i,u,c){const[r,f,h]=Oc(c==null?void 0:c.in,i,i,u),g=Os(f,h),b=Math.abs(Sb(f,h));if(b<1)return 0;f.getMonth()===1&&f.getDate()>27&&f.setDate(30),f.setMonth(f.getMonth()-g*b);let w=Os(f,h)===-g;Tb(r)&&b===1&&Os(r,h)===1&&(w=!1);const j=g*(b-+w);return j===0?0:j}function zb(i,u,c){const r=jb(i,u)/1e3;return Nb(c==null?void 0:c.roundingMethod)(r)}const Mb={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Ob=(i,u,c)=>{let r;const f=Mb[i];return typeof f=="string"?r=f:u===1?r=f.one:r=f.other.replace("{{count}}",u.toString()),c!=null&&c.addSuffix?c.comparison&&c.comparison>0?"in "+r:r+" ago":r};function Wl(i){return(u={})=>{const c=u.width?String(u.width):i.defaultWidth;return i.formats[c]||i.formats[i.defaultWidth]}}const Db={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Rb={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Cb={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Ub={date:Wl({formats:Db,defaultWidth:"full"}),time:Wl({formats:Rb,defaultWidth:"full"}),dateTime:Wl({formats:Cb,defaultWidth:"full"})},_b={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},qb=(i,u,c,r)=>_b[i];function Gt(i){return(u,c)=>{const r=c!=null&&c.context?String(c.context):"standalone";let f;if(r==="formatting"&&i.formattingValues){const g=i.defaultFormattingWidth||i.defaultWidth,b=c!=null&&c.width?String(c.width):g;f=i.formattingValues[b]||i.formattingValues[g]}else{const g=i.defaultWidth,b=c!=null&&c.width?String(c.width):i.defaultWidth;f=i.values[b]||i.values[g]}const h=i.argumentCallback?i.argumentCallback(u):u;return f[h]}}const Bb={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Hb={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},kb={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Lb={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Yb={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Gb={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Xb=(i,u)=>{const c=Number(i),r=c%100;if(r>20||r<10)switch(r%10){case 1:return c+"st";case 2:return c+"nd";case 3:return c+"rd"}return c+"th"},Zb={ordinalNumber:Xb,era:Gt({values:Bb,defaultWidth:"wide"}),quarter:Gt({values:Hb,defaultWidth:"wide",argumentCallback:i=>i-1}),month:Gt({values:kb,defaultWidth:"wide"}),day:Gt({values:Lb,defaultWidth:"wide"}),dayPeriod:Gt({values:Yb,defaultWidth:"wide",formattingValues:Gb,defaultFormattingWidth:"wide"})};function Xt(i){return(u,c={})=>{const r=c.width,f=r&&i.matchPatterns[r]||i.matchPatterns[i.defaultMatchWidth],h=u.match(f);if(!h)return null;const g=h[0],b=r&&i.parsePatterns[r]||i.parsePatterns[i.defaultParseWidth],w=Array.isArray(b)?Vb(b,R=>R.test(g)):Qb(b,R=>R.test(g));let j;j=i.valueCallback?i.valueCallback(w):w,j=c.valueCallback?c.valueCallback(j):j;const x=u.slice(g.length);return{value:j,rest:x}}}function Qb(i,u){for(const c in i)if(Object.prototype.hasOwnProperty.call(i,c)&&u(i[c]))return c}function Vb(i,u){for(let c=0;c<i.length;c++)if(u(i[c]))return c}function vh(i){return(u,c={})=>{const r=u.match(i.matchPattern);if(!r)return null;const f=r[0],h=u.match(i.parsePattern);if(!h)return null;let g=i.valueCallback?i.valueCallback(h[0]):h[0];g=c.valueCallback?c.valueCallback(g):g;const b=u.slice(f.length);return{value:g,rest:b}}}const Kb=/^(\d+)(th|st|nd|rd)?/i,Jb=/\d+/i,Wb={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Fb={any:[/^b/i,/^(a|c)/i]},$b={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Pb={any:[/1/i,/2/i,/3/i,/4/i]},Ib={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},ep={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},tp={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},ap={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},lp={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},np={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},ip={ordinalNumber:vh({matchPattern:Kb,parsePattern:Jb,valueCallback:i=>parseInt(i,10)}),era:Xt({matchPatterns:Wb,defaultMatchWidth:"wide",parsePatterns:Fb,defaultParseWidth:"any"}),quarter:Xt({matchPatterns:$b,defaultMatchWidth:"wide",parsePatterns:Pb,defaultParseWidth:"any",valueCallback:i=>i+1}),month:Xt({matchPatterns:Ib,defaultMatchWidth:"wide",parsePatterns:ep,defaultParseWidth:"any"}),day:Xt({matchPatterns:tp,defaultMatchWidth:"wide",parsePatterns:ap,defaultParseWidth:"any"}),dayPeriod:Xt({matchPatterns:lp,defaultMatchWidth:"any",parsePatterns:np,defaultParseWidth:"any"})},sp={code:"en-US",formatDistance:Ob,formatLong:Ub,formatRelative:qb,localize:Zb,match:ip,options:{weekStartsOn:0,firstWeekContainsDate:1}};function up(i,u,c){const r=bb(),f=(c==null?void 0:c.locale)??r.locale??sp,h=2520,g=Os(i,u);if(isNaN(g))throw new RangeError("Invalid time value");const b=Object.assign({},c,{addSuffix:c==null?void 0:c.addSuffix,comparison:g}),[w,j]=Oc(c==null?void 0:c.in,...g>0?[u,i]:[i,u]),x=zb(j,w),R=(Hm(j)-Hm(w))/1e3,C=Math.round((x-R)/60);let Z;if(C<2)return c!=null&&c.includeSeconds?x<5?f.formatDistance("lessThanXSeconds",5,b):x<10?f.formatDistance("lessThanXSeconds",10,b):x<20?f.formatDistance("lessThanXSeconds",20,b):x<40?f.formatDistance("halfAMinute",0,b):x<60?f.formatDistance("lessThanXMinutes",1,b):f.formatDistance("xMinutes",1,b):C===0?f.formatDistance("lessThanXMinutes",1,b):f.formatDistance("xMinutes",C,b);if(C<45)return f.formatDistance("xMinutes",C,b);if(C<90)return f.formatDistance("aboutXHours",1,b);if(C<qm){const U=Math.round(C/60);return f.formatDistance("aboutXHours",U,b)}else{if(C<h)return f.formatDistance("xDays",1,b);if(C<As){const U=Math.round(C/qm);return f.formatDistance("xDays",U,b)}else if(C<As*2)return Z=Math.round(C/As),f.formatDistance("aboutXMonths",Z,b)}if(Z=Eb(j,w),Z<12){const U=Math.round(C/As);return f.formatDistance("xMonths",U,b)}else{const U=Z%12,q=Math.trunc(Z/12);return U<3?f.formatDistance("aboutXYears",q,b):U<9?f.formatDistance("overXYears",q,b):f.formatDistance("almostXYears",q+1,b)}}function rp(i,u){return up(i,pb(i),u)}function cp(i,u){const c=()=>Bs(u==null?void 0:u.in,NaN),f=mp(i);let h;if(f.date){const j=hp(f.date,2);h=gp(j.restDateString,j.year)}if(!h||isNaN(+h))return c();const g=+h;let b=0,w;if(f.time&&(b=yp(f.time),isNaN(b)))return c();if(f.timezone){if(w=bp(f.timezone),isNaN(w))return c()}else{const j=new Date(g+b),x=Ct(0);return x.setFullYear(j.getUTCFullYear(),j.getUTCMonth(),j.getUTCDate()),x.setHours(j.getUTCHours(),j.getUTCMinutes(),j.getUTCSeconds(),j.getUTCMilliseconds()),x}return Ct(g+b+w)}const Ts={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},op=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,fp=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,dp=/^([+-])(\d{2})(?::?(\d{2}))?$/;function mp(i){const u={},c=i.split(Ts.dateTimeDelimiter);let r;if(c.length>2)return u;if(/:/.test(c[0])?r=c[0]:(u.date=c[0],r=c[1],Ts.timeZoneDelimiter.test(u.date)&&(u.date=i.split(Ts.timeZoneDelimiter)[0],r=i.substr(u.date.length,i.length))),r){const f=Ts.timezone.exec(r);f?(u.time=r.replace(f[1],""),u.timezone=f[1]):u.time=r}return u}function hp(i,u){const c=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+u)+"})|(\\d{2}|[+-]\\d{"+(2+u)+"})$)"),r=i.match(c);if(!r)return{year:NaN,restDateString:""};const f=r[1]?parseInt(r[1]):null,h=r[2]?parseInt(r[2]):null;return{year:h===null?f:h*100,restDateString:i.slice((r[1]||r[2]).length)}}function gp(i,u){if(u===null)return new Date(NaN);const c=i.match(op);if(!c)return new Date(NaN);const r=!!c[4],f=ai(c[1]),h=ai(c[2])-1,g=ai(c[3]),b=ai(c[4]),w=ai(c[5])-1;if(r)return Np(u,b,w)?pp(u,b,w):new Date(NaN);{const j=new Date(0);return!xp(u,h,g)||!Sp(u,f)?new Date(NaN):(j.setUTCFullYear(u,h,Math.max(f,g)),j)}}function ai(i){return i?parseInt(i):1}function yp(i){const u=i.match(fp);if(!u)return NaN;const c=rc(u[1]),r=rc(u[2]),f=rc(u[3]);return jp(c,r,f)?c*ph+r*bh+f*1e3:NaN}function rc(i){return i&&parseFloat(i.replace(",","."))||0}function bp(i){if(i==="Z")return 0;const u=i.match(dp);if(!u)return 0;const c=u[1]==="+"?-1:1,r=parseInt(u[2]),f=u[3]&&parseInt(u[3])||0;return wp(r,f)?c*(r*ph+f*bh):NaN}function pp(i,u,c){const r=new Date(0);r.setUTCFullYear(i,0,4);const f=r.getUTCDay()||7,h=(u-1)*7+c+1-f;return r.setUTCDate(r.getUTCDate()+h),r}const vp=[31,null,31,30,31,30,31,31,30,31,30,31];function xh(i){return i%400===0||i%4===0&&i%100!==0}function xp(i,u,c){return u>=0&&u<=11&&c>=1&&c<=(vp[u]||(xh(i)?29:28))}function Sp(i,u){return u>=1&&u<=(xh(i)?366:365)}function Np(i,u,c){return u>=1&&u<=53&&c>=0&&c<=6}function jp(i,u,c){return i===24?u===0&&c===0:c>=0&&c<60&&u>=0&&u<60&&i>=0&&i<25}function wp(i,u){return u>=0&&u<=59}const Ap={lessThanXSeconds:{one:"menos de um segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"meio minuto",lessThanXMinutes:{one:"menos de um minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"cerca de 1 hora",other:"cerca de {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 dia",other:"{{count}} dias"},aboutXWeeks:{one:"cerca de 1 semana",other:"cerca de {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"cerca de 1 mês",other:"cerca de {{count}} meses"},xMonths:{one:"1 mês",other:"{{count}} meses"},aboutXYears:{one:"cerca de 1 ano",other:"cerca de {{count}} anos"},xYears:{one:"1 ano",other:"{{count}} anos"},overXYears:{one:"mais de 1 ano",other:"mais de {{count}} anos"},almostXYears:{one:"quase 1 ano",other:"quase {{count}} anos"}},Tp=(i,u,c)=>{let r;const f=Ap[i];return typeof f=="string"?r=f:u===1?r=f.one:r=f.other.replace("{{count}}",String(u)),c!=null&&c.addSuffix?c.comparison&&c.comparison>0?"em "+r:"há "+r:r},Ep={full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"d MMM y",short:"dd/MM/yyyy"},zp={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},Mp={full:"{{date}} 'às' {{time}}",long:"{{date}} 'às' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Op={date:Wl({formats:Ep,defaultWidth:"full"}),time:Wl({formats:zp,defaultWidth:"full"}),dateTime:Wl({formats:Mp,defaultWidth:"full"})},Dp={lastWeek:i=>{const u=i.getDay();return"'"+(u===0||u===6?"último":"última")+"' eeee 'às' p"},yesterday:"'ontem às' p",today:"'hoje às' p",tomorrow:"'amanhã às' p",nextWeek:"eeee 'às' p",other:"P"},Rp=(i,u,c,r)=>{const f=Dp[i];return typeof f=="function"?f(u):f},Cp={narrow:["AC","DC"],abbreviated:["AC","DC"],wide:["antes de cristo","depois de cristo"]},Up={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1º trimestre","2º trimestre","3º trimestre","4º trimestre"]},_p={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez"],wide:["janeiro","fevereiro","março","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro"]},qp={narrow:["D","S","T","Q","Q","S","S"],short:["dom","seg","ter","qua","qui","sex","sab"],abbreviated:["domingo","segunda","terça","quarta","quinta","sexta","sábado"],wide:["domingo","segunda-feira","terça-feira","quarta-feira","quinta-feira","sexta-feira","sábado"]},Bp={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"manhã",afternoon:"tarde",evening:"tarde",night:"noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manhã",afternoon:"tarde",evening:"tarde",night:"noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"manhã",afternoon:"tarde",evening:"tarde",night:"noite"}},Hp={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"da manhã",afternoon:"da tarde",evening:"da tarde",night:"da noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manhã",afternoon:"da tarde",evening:"da tarde",night:"da noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"da manhã",afternoon:"da tarde",evening:"da tarde",night:"da noite"}},kp=(i,u)=>{const c=Number(i);return(u==null?void 0:u.unit)==="week"?c+"ª":c+"º"},Lp={ordinalNumber:kp,era:Gt({values:Cp,defaultWidth:"wide"}),quarter:Gt({values:Up,defaultWidth:"wide",argumentCallback:i=>i-1}),month:Gt({values:_p,defaultWidth:"wide"}),day:Gt({values:qp,defaultWidth:"wide"}),dayPeriod:Gt({values:Bp,defaultWidth:"wide",formattingValues:Hp,defaultFormattingWidth:"wide"})},Yp=/^(\d+)[ºªo]?/i,Gp=/\d+/i,Xp={narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|d\.?\s?c\.?)/i,wide:/^(antes de cristo|depois de cristo)/i},Zp={any:[/^ac/i,/^dc/i],wide:[/^antes de cristo/i,/^depois de cristo/i]},Qp={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º)? trimestre/i},Vp={any:[/1/i,/2/i,/3/i,/4/i]},Kp={narrow:/^[jfmajsond]/i,abbreviated:/^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,wide:/^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i},Jp={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^fev/i,/^mar/i,/^abr/i,/^mai/i,/^jun/i,/^jul/i,/^ago/i,/^set/i,/^out/i,/^nov/i,/^dez/i]},Wp={narrow:/^(dom|[23456]ª?|s[aá]b)/i,short:/^(dom|[23456]ª?|s[aá]b)/i,abbreviated:/^(dom|seg|ter|qua|qui|sex|s[aá]b)/i,wide:/^(domingo|(segunda|ter[cç]a|quarta|quinta|sexta)([- ]feira)?|s[aá]bado)/i},Fp={short:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],narrow:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],any:[/^d/i,/^seg/i,/^t/i,/^qua/i,/^qui/i,/^sex/i,/^s[aá]b/i]},$p={narrow:/^(a|p|mn|md|(da) (manhã|tarde|noite))/i,any:/^([ap]\.?\s?m\.?|meia[-\s]noite|meio[-\s]dia|(da) (manhã|tarde|noite))/i},Pp={any:{am:/^a/i,pm:/^p/i,midnight:/^mn|^meia[-\s]noite/i,noon:/^md|^meio[-\s]dia/i,morning:/manhã/i,afternoon:/tarde/i,evening:/tarde/i,night:/noite/i}},Ip={ordinalNumber:vh({matchPattern:Yp,parsePattern:Gp,valueCallback:i=>parseInt(i,10)}),era:Xt({matchPatterns:Xp,defaultMatchWidth:"wide",parsePatterns:Zp,defaultParseWidth:"any"}),quarter:Xt({matchPatterns:Qp,defaultMatchWidth:"wide",parsePatterns:Vp,defaultParseWidth:"any",valueCallback:i=>i+1}),month:Xt({matchPatterns:Kp,defaultMatchWidth:"wide",parsePatterns:Jp,defaultParseWidth:"any"}),day:Xt({matchPatterns:Wp,defaultMatchWidth:"wide",parsePatterns:Fp,defaultParseWidth:"any"}),dayPeriod:Xt({matchPatterns:$p,defaultMatchWidth:"any",parsePatterns:Pp,defaultParseWidth:"any"})},ev={code:"pt-BR",formatDistance:Tp,formatLong:Op,formatRelative:Rp,localize:Lp,match:Ip,options:{weekStartsOn:0,firstWeekContainsDate:1}},Fl=(i,u={})=>{const{minimumFractionDigits:c=2,maximumFractionDigits:r=2,showSymbol:f=!0}=u;return i==null||isNaN(i)?f?"R$ 0,00":"0,00":new Intl.NumberFormat("pt-BR",{style:f?"currency":"decimal",currency:"BRL",minimumFractionDigits:c,maximumFractionDigits:r}).format(i)},$l=(i,u={})=>{const{minimumFractionDigits:c=0,maximumFractionDigits:r=0}=u;return i==null||isNaN(i)?"0":new Intl.NumberFormat("pt-BR",{minimumFractionDigits:c,maximumFractionDigits:r}).format(i)},si=(i,u={})=>{const{minimumFractionDigits:c=1,maximumFractionDigits:r=1,showSymbol:f=!0}=u;if(i==null||isNaN(i))return f?"0,0%":"0,0";const h=new Intl.NumberFormat("pt-BR",{style:f?"percent":"decimal",minimumFractionDigits:c,maximumFractionDigits:r}),g=f?i/100:i;return h.format(g)},Sh=i=>{if(!i)return"";let u;return typeof i=="string"?u=cp(i):u=i,xb(u)?rp(u,{addSuffix:!0,locale:ev}):""},tv=i=>{if(!i)return"";const u=i.replace(/\D/g,"");return u.length===11?u.replace(/(\d{2})(\d{5})(\d{4})/,"($1) $2-$3"):u.length===10?u.replace(/(\d{2})(\d{4})(\d{4})/,"($1) $2-$3"):u.length===13&&u.startsWith("55")?u.replace(/55(\d{2})(\d{5})(\d{4})/,"+55 ($1) $2-$3"):i},av=i=>i?i.toLowerCase().split(" ").map(u=>u.charAt(0).toUpperCase()+u.slice(1)).join(" "):"",Nh=i=>({online:"var(--success)",offline:"var(--gray-400)",quente:"var(--danger)",morno:"var(--warning)",frio:"var(--primary-blue)",ativo:"var(--success)",inativo:"var(--gray-400)",pendente:"var(--warning)",concluído:"var(--success)",cancelado:"var(--danger)"})[i==null?void 0:i.toLowerCase()]||"var(--gray-500)",jh=i=>({WhatsApp:"💬",Instagram:"📷",Facebook:"👥",Site:"🌐",Telegram:"✈️",Email:"📧"})[i]||"💬",lv=i=>({orçamento:"Solicitação de Orçamento",suporte:"Suporte Técnico",agendamento:"Agendamento",informação:"Informações Gerais",reclamação:"Reclamação",elogio:"Elogio",dúvida:"Dúvida"})[i==null?void 0:i.toLowerCase()]||av(i||""),Dc="-",nv=i=>{const u=sv(i),{conflictingClassGroups:c,conflictingClassGroupModifiers:r}=i;return{getClassGroupId:g=>{const b=g.split(Dc);return b[0]===""&&b.length!==1&&b.shift(),wh(b,u)||iv(g)},getConflictingClassGroupIds:(g,b)=>{const w=c[g]||[];return b&&r[g]?[...w,...r[g]]:w}}},wh=(i,u)=>{var g;if(i.length===0)return u.classGroupId;const c=i[0],r=u.nextPart.get(c),f=r?wh(i.slice(1),r):void 0;if(f)return f;if(u.validators.length===0)return;const h=i.join(Dc);return(g=u.validators.find(({validator:b})=>b(h)))==null?void 0:g.classGroupId},km=/^\[(.+)\]$/,iv=i=>{if(km.test(i)){const u=km.exec(i)[1],c=u==null?void 0:u.substring(0,u.indexOf(":"));if(c)return"arbitrary.."+c}},sv=i=>{const{theme:u,classGroups:c}=i,r={nextPart:new Map,validators:[]};for(const f in c)pc(c[f],r,f,u);return r},pc=(i,u,c,r)=>{i.forEach(f=>{if(typeof f=="string"){const h=f===""?u:Lm(u,f);h.classGroupId=c;return}if(typeof f=="function"){if(uv(f)){pc(f(r),u,c,r);return}u.validators.push({validator:f,classGroupId:c});return}Object.entries(f).forEach(([h,g])=>{pc(g,Lm(u,h),c,r)})})},Lm=(i,u)=>{let c=i;return u.split(Dc).forEach(r=>{c.nextPart.has(r)||c.nextPart.set(r,{nextPart:new Map,validators:[]}),c=c.nextPart.get(r)}),c},uv=i=>i.isThemeGetter,rv=i=>{if(i<1)return{get:()=>{},set:()=>{}};let u=0,c=new Map,r=new Map;const f=(h,g)=>{c.set(h,g),u++,u>i&&(u=0,r=c,c=new Map)};return{get(h){let g=c.get(h);if(g!==void 0)return g;if((g=r.get(h))!==void 0)return f(h,g),g},set(h,g){c.has(h)?c.set(h,g):f(h,g)}}},vc="!",xc=":",cv=xc.length,ov=i=>{const{prefix:u,experimentalParseClassName:c}=i;let r=f=>{const h=[];let g=0,b=0,w=0,j;for(let U=0;U<f.length;U++){let q=f[U];if(g===0&&b===0){if(q===xc){h.push(f.slice(w,U)),w=U+cv;continue}if(q==="/"){j=U;continue}}q==="["?g++:q==="]"?g--:q==="("?b++:q===")"&&b--}const x=h.length===0?f:f.substring(w),R=fv(x),C=R!==x,Z=j&&j>w?j-w:void 0;return{modifiers:h,hasImportantModifier:C,baseClassName:R,maybePostfixModifierPosition:Z}};if(u){const f=u+xc,h=r;r=g=>g.startsWith(f)?h(g.substring(f.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:g,maybePostfixModifierPosition:void 0}}if(c){const f=r;r=h=>c({className:h,parseClassName:f})}return r},fv=i=>i.endsWith(vc)?i.substring(0,i.length-1):i.startsWith(vc)?i.substring(1):i,dv=i=>{const u=Object.fromEntries(i.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const f=[];let h=[];return r.forEach(g=>{g[0]==="["||u[g]?(f.push(...h.sort(),g),h=[]):h.push(g)}),f.push(...h.sort()),f}},mv=i=>({cache:rv(i.cacheSize),parseClassName:ov(i),sortModifiers:dv(i),...nv(i)}),hv=/\s+/,gv=(i,u)=>{const{parseClassName:c,getClassGroupId:r,getConflictingClassGroupIds:f,sortModifiers:h}=u,g=[],b=i.trim().split(hv);let w="";for(let j=b.length-1;j>=0;j-=1){const x=b[j],{isExternal:R,modifiers:C,hasImportantModifier:Z,baseClassName:U,maybePostfixModifierPosition:q}=c(x);if(R){w=x+(w.length>0?" "+w:w);continue}let B=!!q,J=r(B?U.substring(0,q):U);if(!J){if(!B){w=x+(w.length>0?" "+w:w);continue}if(J=r(U),!J){w=x+(w.length>0?" "+w:w);continue}B=!1}const I=h(C).join(":"),F=Z?I+vc:I,he=F+J;if(g.includes(he))continue;g.push(he);const ue=f(J,B);for(let fe=0;fe<ue.length;++fe){const je=ue[fe];g.push(F+je)}w=x+(w.length>0?" "+w:w)}return w};function yv(){let i=0,u,c,r="";for(;i<arguments.length;)(u=arguments[i++])&&(c=Ah(u))&&(r&&(r+=" "),r+=c);return r}const Ah=i=>{if(typeof i=="string")return i;let u,c="";for(let r=0;r<i.length;r++)i[r]&&(u=Ah(i[r]))&&(c&&(c+=" "),c+=u);return c};function bv(i,...u){let c,r,f,h=g;function g(w){const j=u.reduce((x,R)=>R(x),i());return c=mv(j),r=c.cache.get,f=c.cache.set,h=b,b(w)}function b(w){const j=r(w);if(j)return j;const x=gv(w,c);return f(w,x),x}return function(){return h(yv.apply(null,arguments))}}const Le=i=>{const u=c=>c[i]||[];return u.isThemeGetter=!0,u},Th=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Eh=/^\((?:(\w[\w-]*):)?(.+)\)$/i,pv=/^\d+\/\d+$/,vv=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,xv=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Sv=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Nv=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,jv=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Kl=i=>pv.test(i),te=i=>!!i&&!Number.isNaN(Number(i)),qa=i=>!!i&&Number.isInteger(Number(i)),cc=i=>i.endsWith("%")&&te(i.slice(0,-1)),ua=i=>vv.test(i),wv=()=>!0,Av=i=>xv.test(i)&&!Sv.test(i),zh=()=>!1,Tv=i=>Nv.test(i),Ev=i=>jv.test(i),zv=i=>!L(i)&&!Y(i),Mv=i=>Pl(i,Dh,zh),L=i=>Th.test(i),ll=i=>Pl(i,Rh,Av),oc=i=>Pl(i,Uv,te),Ym=i=>Pl(i,Mh,zh),Ov=i=>Pl(i,Oh,Ev),Es=i=>Pl(i,Ch,Tv),Y=i=>Eh.test(i),li=i=>Il(i,Rh),Dv=i=>Il(i,_v),Gm=i=>Il(i,Mh),Rv=i=>Il(i,Dh),Cv=i=>Il(i,Oh),zs=i=>Il(i,Ch,!0),Pl=(i,u,c)=>{const r=Th.exec(i);return r?r[1]?u(r[1]):c(r[2]):!1},Il=(i,u,c=!1)=>{const r=Eh.exec(i);return r?r[1]?u(r[1]):c:!1},Mh=i=>i==="position"||i==="percentage",Oh=i=>i==="image"||i==="url",Dh=i=>i==="length"||i==="size"||i==="bg-size",Rh=i=>i==="length",Uv=i=>i==="number",_v=i=>i==="family-name",Ch=i=>i==="shadow",qv=()=>{const i=Le("color"),u=Le("font"),c=Le("text"),r=Le("font-weight"),f=Le("tracking"),h=Le("leading"),g=Le("breakpoint"),b=Le("container"),w=Le("spacing"),j=Le("radius"),x=Le("shadow"),R=Le("inset-shadow"),C=Le("text-shadow"),Z=Le("drop-shadow"),U=Le("blur"),q=Le("perspective"),B=Le("aspect"),J=Le("ease"),I=Le("animate"),F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],he=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],ue=()=>[...he(),Y,L],fe=()=>["auto","hidden","clip","visible","scroll"],je=()=>["auto","contain","none"],G=()=>[Y,L,w],X=()=>[Kl,"full","auto",...G()],ie=()=>[qa,"none","subgrid",Y,L],re=()=>["auto",{span:["full",qa,Y,L]},qa,Y,L],Ee=()=>[qa,"auto",Y,L],We=()=>["auto","min","max","fr",Y,L],Qt=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Fe=()=>["start","end","center","stretch","center-safe","end-safe"],z=()=>["auto",...G()],H=()=>[Kl,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...G()],_=()=>[i,Y,L],be=()=>[...he(),Gm,Ym,{position:[Y,L]}],pe=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Ye=()=>["auto","cover","contain",Rv,Mv,{size:[Y,L]}],we=()=>[cc,li,ll],$=()=>["","none","full",j,Y,L],ce=()=>["",te,li,ll],et=()=>["solid","dashed","dotted","double"],zt=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Me=()=>[te,cc,Gm,Ym],Ha=()=>["","none",U,Y,L],Mt=()=>["none",te,Y,L],ca=()=>["none",te,Y,L],oa=()=>[te,Y,L],fa=()=>[Kl,"full",...G()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[ua],breakpoint:[ua],color:[wv],container:[ua],"drop-shadow":[ua],ease:["in","out","in-out"],font:[zv],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[ua],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[ua],shadow:[ua],spacing:["px",te],text:[ua],"text-shadow":[ua],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Kl,L,Y,B]}],container:["container"],columns:[{columns:[te,L,Y,b]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:ue()}],overflow:[{overflow:fe()}],"overflow-x":[{"overflow-x":fe()}],"overflow-y":[{"overflow-y":fe()}],overscroll:[{overscroll:je()}],"overscroll-x":[{"overscroll-x":je()}],"overscroll-y":[{"overscroll-y":je()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:X()}],"inset-x":[{"inset-x":X()}],"inset-y":[{"inset-y":X()}],start:[{start:X()}],end:[{end:X()}],top:[{top:X()}],right:[{right:X()}],bottom:[{bottom:X()}],left:[{left:X()}],visibility:["visible","invisible","collapse"],z:[{z:[qa,"auto",Y,L]}],basis:[{basis:[Kl,"full","auto",b,...G()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[te,Kl,"auto","initial","none",L]}],grow:[{grow:["",te,Y,L]}],shrink:[{shrink:["",te,Y,L]}],order:[{order:[qa,"first","last","none",Y,L]}],"grid-cols":[{"grid-cols":ie()}],"col-start-end":[{col:re()}],"col-start":[{"col-start":Ee()}],"col-end":[{"col-end":Ee()}],"grid-rows":[{"grid-rows":ie()}],"row-start-end":[{row:re()}],"row-start":[{"row-start":Ee()}],"row-end":[{"row-end":Ee()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":We()}],"auto-rows":[{"auto-rows":We()}],gap:[{gap:G()}],"gap-x":[{"gap-x":G()}],"gap-y":[{"gap-y":G()}],"justify-content":[{justify:[...Qt(),"normal"]}],"justify-items":[{"justify-items":[...Fe(),"normal"]}],"justify-self":[{"justify-self":["auto",...Fe()]}],"align-content":[{content:["normal",...Qt()]}],"align-items":[{items:[...Fe(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Fe(),{baseline:["","last"]}]}],"place-content":[{"place-content":Qt()}],"place-items":[{"place-items":[...Fe(),"baseline"]}],"place-self":[{"place-self":["auto",...Fe()]}],p:[{p:G()}],px:[{px:G()}],py:[{py:G()}],ps:[{ps:G()}],pe:[{pe:G()}],pt:[{pt:G()}],pr:[{pr:G()}],pb:[{pb:G()}],pl:[{pl:G()}],m:[{m:z()}],mx:[{mx:z()}],my:[{my:z()}],ms:[{ms:z()}],me:[{me:z()}],mt:[{mt:z()}],mr:[{mr:z()}],mb:[{mb:z()}],ml:[{ml:z()}],"space-x":[{"space-x":G()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":G()}],"space-y-reverse":["space-y-reverse"],size:[{size:H()}],w:[{w:[b,"screen",...H()]}],"min-w":[{"min-w":[b,"screen","none",...H()]}],"max-w":[{"max-w":[b,"screen","none","prose",{screen:[g]},...H()]}],h:[{h:["screen","lh",...H()]}],"min-h":[{"min-h":["screen","lh","none",...H()]}],"max-h":[{"max-h":["screen","lh",...H()]}],"font-size":[{text:["base",c,li,ll]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,Y,oc]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",cc,L]}],"font-family":[{font:[Dv,L,u]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[f,Y,L]}],"line-clamp":[{"line-clamp":[te,"none",Y,oc]}],leading:[{leading:[h,...G()]}],"list-image":[{"list-image":["none",Y,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Y,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:_()}],"text-color":[{text:_()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...et(),"wavy"]}],"text-decoration-thickness":[{decoration:[te,"from-font","auto",Y,ll]}],"text-decoration-color":[{decoration:_()}],"underline-offset":[{"underline-offset":[te,"auto",Y,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:G()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Y,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Y,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:be()}],"bg-repeat":[{bg:pe()}],"bg-size":[{bg:Ye()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},qa,Y,L],radial:["",Y,L],conic:[qa,Y,L]},Cv,Ov]}],"bg-color":[{bg:_()}],"gradient-from-pos":[{from:we()}],"gradient-via-pos":[{via:we()}],"gradient-to-pos":[{to:we()}],"gradient-from":[{from:_()}],"gradient-via":[{via:_()}],"gradient-to":[{to:_()}],rounded:[{rounded:$()}],"rounded-s":[{"rounded-s":$()}],"rounded-e":[{"rounded-e":$()}],"rounded-t":[{"rounded-t":$()}],"rounded-r":[{"rounded-r":$()}],"rounded-b":[{"rounded-b":$()}],"rounded-l":[{"rounded-l":$()}],"rounded-ss":[{"rounded-ss":$()}],"rounded-se":[{"rounded-se":$()}],"rounded-ee":[{"rounded-ee":$()}],"rounded-es":[{"rounded-es":$()}],"rounded-tl":[{"rounded-tl":$()}],"rounded-tr":[{"rounded-tr":$()}],"rounded-br":[{"rounded-br":$()}],"rounded-bl":[{"rounded-bl":$()}],"border-w":[{border:ce()}],"border-w-x":[{"border-x":ce()}],"border-w-y":[{"border-y":ce()}],"border-w-s":[{"border-s":ce()}],"border-w-e":[{"border-e":ce()}],"border-w-t":[{"border-t":ce()}],"border-w-r":[{"border-r":ce()}],"border-w-b":[{"border-b":ce()}],"border-w-l":[{"border-l":ce()}],"divide-x":[{"divide-x":ce()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ce()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...et(),"hidden","none"]}],"divide-style":[{divide:[...et(),"hidden","none"]}],"border-color":[{border:_()}],"border-color-x":[{"border-x":_()}],"border-color-y":[{"border-y":_()}],"border-color-s":[{"border-s":_()}],"border-color-e":[{"border-e":_()}],"border-color-t":[{"border-t":_()}],"border-color-r":[{"border-r":_()}],"border-color-b":[{"border-b":_()}],"border-color-l":[{"border-l":_()}],"divide-color":[{divide:_()}],"outline-style":[{outline:[...et(),"none","hidden"]}],"outline-offset":[{"outline-offset":[te,Y,L]}],"outline-w":[{outline:["",te,li,ll]}],"outline-color":[{outline:_()}],shadow:[{shadow:["","none",x,zs,Es]}],"shadow-color":[{shadow:_()}],"inset-shadow":[{"inset-shadow":["none",R,zs,Es]}],"inset-shadow-color":[{"inset-shadow":_()}],"ring-w":[{ring:ce()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:_()}],"ring-offset-w":[{"ring-offset":[te,ll]}],"ring-offset-color":[{"ring-offset":_()}],"inset-ring-w":[{"inset-ring":ce()}],"inset-ring-color":[{"inset-ring":_()}],"text-shadow":[{"text-shadow":["none",C,zs,Es]}],"text-shadow-color":[{"text-shadow":_()}],opacity:[{opacity:[te,Y,L]}],"mix-blend":[{"mix-blend":[...zt(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":zt()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[te]}],"mask-image-linear-from-pos":[{"mask-linear-from":Me()}],"mask-image-linear-to-pos":[{"mask-linear-to":Me()}],"mask-image-linear-from-color":[{"mask-linear-from":_()}],"mask-image-linear-to-color":[{"mask-linear-to":_()}],"mask-image-t-from-pos":[{"mask-t-from":Me()}],"mask-image-t-to-pos":[{"mask-t-to":Me()}],"mask-image-t-from-color":[{"mask-t-from":_()}],"mask-image-t-to-color":[{"mask-t-to":_()}],"mask-image-r-from-pos":[{"mask-r-from":Me()}],"mask-image-r-to-pos":[{"mask-r-to":Me()}],"mask-image-r-from-color":[{"mask-r-from":_()}],"mask-image-r-to-color":[{"mask-r-to":_()}],"mask-image-b-from-pos":[{"mask-b-from":Me()}],"mask-image-b-to-pos":[{"mask-b-to":Me()}],"mask-image-b-from-color":[{"mask-b-from":_()}],"mask-image-b-to-color":[{"mask-b-to":_()}],"mask-image-l-from-pos":[{"mask-l-from":Me()}],"mask-image-l-to-pos":[{"mask-l-to":Me()}],"mask-image-l-from-color":[{"mask-l-from":_()}],"mask-image-l-to-color":[{"mask-l-to":_()}],"mask-image-x-from-pos":[{"mask-x-from":Me()}],"mask-image-x-to-pos":[{"mask-x-to":Me()}],"mask-image-x-from-color":[{"mask-x-from":_()}],"mask-image-x-to-color":[{"mask-x-to":_()}],"mask-image-y-from-pos":[{"mask-y-from":Me()}],"mask-image-y-to-pos":[{"mask-y-to":Me()}],"mask-image-y-from-color":[{"mask-y-from":_()}],"mask-image-y-to-color":[{"mask-y-to":_()}],"mask-image-radial":[{"mask-radial":[Y,L]}],"mask-image-radial-from-pos":[{"mask-radial-from":Me()}],"mask-image-radial-to-pos":[{"mask-radial-to":Me()}],"mask-image-radial-from-color":[{"mask-radial-from":_()}],"mask-image-radial-to-color":[{"mask-radial-to":_()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":he()}],"mask-image-conic-pos":[{"mask-conic":[te]}],"mask-image-conic-from-pos":[{"mask-conic-from":Me()}],"mask-image-conic-to-pos":[{"mask-conic-to":Me()}],"mask-image-conic-from-color":[{"mask-conic-from":_()}],"mask-image-conic-to-color":[{"mask-conic-to":_()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:be()}],"mask-repeat":[{mask:pe()}],"mask-size":[{mask:Ye()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Y,L]}],filter:[{filter:["","none",Y,L]}],blur:[{blur:Ha()}],brightness:[{brightness:[te,Y,L]}],contrast:[{contrast:[te,Y,L]}],"drop-shadow":[{"drop-shadow":["","none",Z,zs,Es]}],"drop-shadow-color":[{"drop-shadow":_()}],grayscale:[{grayscale:["",te,Y,L]}],"hue-rotate":[{"hue-rotate":[te,Y,L]}],invert:[{invert:["",te,Y,L]}],saturate:[{saturate:[te,Y,L]}],sepia:[{sepia:["",te,Y,L]}],"backdrop-filter":[{"backdrop-filter":["","none",Y,L]}],"backdrop-blur":[{"backdrop-blur":Ha()}],"backdrop-brightness":[{"backdrop-brightness":[te,Y,L]}],"backdrop-contrast":[{"backdrop-contrast":[te,Y,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",te,Y,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[te,Y,L]}],"backdrop-invert":[{"backdrop-invert":["",te,Y,L]}],"backdrop-opacity":[{"backdrop-opacity":[te,Y,L]}],"backdrop-saturate":[{"backdrop-saturate":[te,Y,L]}],"backdrop-sepia":[{"backdrop-sepia":["",te,Y,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":G()}],"border-spacing-x":[{"border-spacing-x":G()}],"border-spacing-y":[{"border-spacing-y":G()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Y,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[te,"initial",Y,L]}],ease:[{ease:["linear","initial",J,Y,L]}],delay:[{delay:[te,Y,L]}],animate:[{animate:["none",I,Y,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[q,Y,L]}],"perspective-origin":[{"perspective-origin":ue()}],rotate:[{rotate:Mt()}],"rotate-x":[{"rotate-x":Mt()}],"rotate-y":[{"rotate-y":Mt()}],"rotate-z":[{"rotate-z":Mt()}],scale:[{scale:ca()}],"scale-x":[{"scale-x":ca()}],"scale-y":[{"scale-y":ca()}],"scale-z":[{"scale-z":ca()}],"scale-3d":["scale-3d"],skew:[{skew:oa()}],"skew-x":[{"skew-x":oa()}],"skew-y":[{"skew-y":oa()}],transform:[{transform:[Y,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:ue()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:fa()}],"translate-x":[{"translate-x":fa()}],"translate-y":[{"translate-y":fa()}],"translate-z":[{"translate-z":fa()}],"translate-none":["translate-none"],accent:[{accent:_()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:_()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Y,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":G()}],"scroll-mx":[{"scroll-mx":G()}],"scroll-my":[{"scroll-my":G()}],"scroll-ms":[{"scroll-ms":G()}],"scroll-me":[{"scroll-me":G()}],"scroll-mt":[{"scroll-mt":G()}],"scroll-mr":[{"scroll-mr":G()}],"scroll-mb":[{"scroll-mb":G()}],"scroll-ml":[{"scroll-ml":G()}],"scroll-p":[{"scroll-p":G()}],"scroll-px":[{"scroll-px":G()}],"scroll-py":[{"scroll-py":G()}],"scroll-ps":[{"scroll-ps":G()}],"scroll-pe":[{"scroll-pe":G()}],"scroll-pt":[{"scroll-pt":G()}],"scroll-pr":[{"scroll-pr":G()}],"scroll-pb":[{"scroll-pb":G()}],"scroll-pl":[{"scroll-pl":G()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Y,L]}],fill:[{fill:["none",..._()]}],"stroke-w":[{stroke:[te,li,ll,oc]}],stroke:[{stroke:["none",..._()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Bv=bv(qv);function pt(...i){return Bv(sb(i))}const Uh={button:{primary:"bg-primary-600 hover:bg-primary-700 text-white shadow-soft hover:shadow-medium",secondary:"bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 shadow-soft hover:shadow-medium",success:"bg-success-600 hover:bg-success-700 text-white shadow-soft hover:shadow-medium",warning:"bg-warning-600 hover:bg-warning-700 text-white shadow-soft hover:shadow-medium",danger:"bg-danger-600 hover:bg-danger-700 text-white shadow-soft hover:shadow-medium",ghost:"hover:bg-gray-100 text-gray-700",outline:"border-2 border-primary-600 text-primary-600 hover:bg-primary-50"},card:{default:"bg-white border border-gray-200 shadow-soft hover:shadow-medium",elevated:"bg-white border border-gray-200 shadow-medium hover:shadow-large",interactive:"bg-white border border-gray-200 shadow-soft hover:shadow-medium hover:border-primary-300 cursor-pointer",gradient:"bg-gradient-to-br from-white to-gray-50 border border-gray-200 shadow-soft"}},Hv={button:{xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base",xl:"px-8 py-4 text-lg"}},_h={default:"transition-all duration-200 ease-in-out"},Jl=me.forwardRef(({children:i,variant:u="default",padding:c="default",hover:r=!1,animate:f=!0,className:h,onClick:g,...b},w)=>{const j=pt("rounded-2xl overflow-hidden","dark:bg-gray-800 dark:border-gray-700",_h.default,Uh.card[u],{"p-6":c==="default","p-4":c==="sm","p-8":c==="lg","p-0":c==="none"},r&&"hover:scale-[1.02] hover:-translate-y-1",g&&"cursor-pointer",h),x=i;return f?d.jsx(Ne.div,{ref:w,className:j,onClick:g,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,ease:"easeOut"},whileHover:r?{scale:1.02,y:-4}:{},...b,children:x}):d.jsx("div",{ref:w,className:j,onClick:g,...b,children:x})});Jl.displayName="Card";const Ms=({className:i,variant:u="default",animation:c="pulse",...r})=>{const f=pt("bg-gray-200 dark:bg-gray-700 rounded",{"animate-pulse":c==="pulse","animate-shimmer bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 bg-[length:200%_100%]":c==="shimmer"},i);return d.jsx("div",{className:f,...r})},qh=({className:i,...u})=>d.jsxs("div",{className:pt("p-6 bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700",i),...u,children:[d.jsxs("div",{className:"flex items-center justify-between mb-4",children:[d.jsx(Ms,{className:"w-12 h-12 rounded-xl"}),d.jsx(Ms,{className:"h-4 w-16"})]}),d.jsx(Ms,{className:"h-8 w-24 mb-2"}),d.jsx(Ms,{className:"h-4 w-32"})]}),kv=({title:i,value:u,type:c="number",trend:r=null,trendValue:f=null,icon:h,color:g="blue",loading:b=!1,subtitle:w=null,onClick:j=null,className:x,...R})=>{const C=(J,I)=>{if(J==null)return"-";switch(I){case"currency":return Fl(J);case"percentage":return si(J);case"number":return $l(J);default:return J.toString()}},Z=J=>{switch(J){case"up":return ii;case"down":return Hy;case"neutral":return By;default:return null}},q=(J=>{const I={blue:{icon:"text-primary-600 dark:text-primary-400",bg:"bg-primary-50 dark:bg-primary-900/20",trend:{up:"text-success-600 bg-success-50 dark:bg-success-900/20",down:"text-danger-600 bg-danger-50 dark:bg-danger-900/20",neutral:"text-gray-600 bg-gray-50 dark:bg-gray-900/20"}},green:{icon:"text-success-600 dark:text-success-400",bg:"bg-success-50 dark:bg-success-900/20",trend:{up:"text-success-600 bg-success-50 dark:bg-success-900/20",down:"text-danger-600 bg-danger-50 dark:bg-danger-900/20",neutral:"text-gray-600 bg-gray-50 dark:bg-gray-900/20"}},yellow:{icon:"text-warning-600 dark:text-warning-400",bg:"bg-warning-50 dark:bg-warning-900/20",trend:{up:"text-success-600 bg-success-50 dark:bg-success-900/20",down:"text-danger-600 bg-danger-50 dark:bg-danger-900/20",neutral:"text-gray-600 bg-gray-50 dark:bg-gray-900/20"}},red:{icon:"text-danger-600 dark:text-danger-400",bg:"bg-danger-50 dark:bg-danger-900/20",trend:{up:"text-success-600 bg-success-50 dark:bg-success-900/20",down:"text-danger-600 bg-danger-50 dark:bg-danger-900/20",neutral:"text-gray-600 bg-gray-50 dark:bg-gray-900/20"}},gray:{icon:"text-gray-600 dark:text-gray-400",bg:"bg-gray-50 dark:bg-gray-900/20",trend:{up:"text-success-600 bg-success-50 dark:bg-success-900/20",down:"text-danger-600 bg-danger-50 dark:bg-danger-900/20",neutral:"text-gray-600 bg-gray-50 dark:bg-gray-900/20"}}};return I[J]||I.blue})(g),B=Z(r);return b?d.jsx(qh,{className:x}):d.jsxs(Jl,{variant:j?"interactive":"default",hover:!!j,onClick:j,className:pt("group",x),...R,children:[d.jsxs("div",{className:"flex items-center justify-between mb-6",children:[h&&d.jsx(Ne.div,{className:pt("w-14 h-14 rounded-2xl flex items-center justify-center",q.bg,q.icon,"group-hover:scale-110 transition-transform duration-200"),whileHover:{rotate:5},transition:{type:"spring",stiffness:300},children:d.jsx(h,{size:28})}),r&&f&&d.jsxs(Ne.div,{className:pt("flex items-center gap-1.5 px-2.5 py-1.5 rounded-full text-sm font-medium",q.trend[r]),initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:.2},children:[B&&d.jsx(B,{size:14}),d.jsx("span",{children:si(Math.abs(f))})]})]}),d.jsx(Ne.div,{className:"mb-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:d.jsx("div",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-1 gradient-text",children:C(u,c)})}),d.jsxs(Ne.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[d.jsx("h3",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-1",children:i}),w&&d.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:w})]}),r&&f&&d.jsx(Ne.div,{className:"mt-4 pt-4 border-t border-gray-100 dark:border-gray-700",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:d.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1",children:[d.jsx("span",{className:pt("w-2 h-2 rounded-full",r==="up"&&"bg-success-500",r==="down"&&"bg-danger-500",r==="neutral"&&"bg-gray-400")}),r==="up"&&"Aumento de ",r==="down"&&"Redução de ",r==="neutral"&&"Estável ",f&&si(Math.abs(f))," vs. período anterior"]})})]})},Lv=({count:i=4,className:u})=>d.jsx(d.Fragment,{children:Array.from({length:i}).map((c,r)=>d.jsx(qh,{className:u},r))}),Yv=({kpis:i,loading:u=!1,className:c=""})=>u?d.jsx(Ne.div,{className:pt("grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",c),initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:d.jsx(Lv,{count:4})}):d.jsx(Ne.div,{className:pt("grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",c),initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:i.map((r,f)=>d.jsx(Ne.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:f*.1,duration:.5},children:d.jsx(kv,{...r})},f))}),Bh=({active:i,payload:u,label:c,type:r="default"})=>i&&u&&u.length?d.jsxs("div",{className:"bg-white p-3 border border-gray-200 rounded-lg shadow-lg",children:[c&&d.jsx("p",{className:"text-sm font-medium text-gray-900 mb-1",children:c}),u.map((f,h)=>d.jsxs("p",{className:"text-sm",style:{color:f.color},children:[`${f.name}: `,r==="currency"&&Fl(f.value),r==="number"&&$l(f.value),r==="percentage"&&`${f.value}%`,r==="default"&&f.value]},h))]}):null,Gv=({data:i,loading:u=!1})=>{if(u)return d.jsxs("div",{className:"card",children:[d.jsx("div",{className:"card-header",children:d.jsx("h3",{className:"text-lg font-semibold",children:"Conversões por Canal"})}),d.jsx("div",{className:"card-body",children:d.jsx("div",{className:"h-64 bg-gray-100 rounded-lg animate-pulse"})})]});const c=["#3b82f6","#10b981","#f59e0b","#ef4444","#8b5cf6"];return d.jsxs("div",{className:"card",children:[d.jsx("div",{className:"card-header",children:d.jsx("h3",{className:"text-lg font-semibold",children:"Conversões por Canal"})}),d.jsxs("div",{className:"card-body",children:[d.jsx(zc,{width:"100%",height:300,children:d.jsxs(ub,{children:[d.jsx(rb,{data:i,cx:"50%",cy:"50%",labelLine:!1,label:({name:r,percent:f})=>`${r} ${(f*100).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:i.map((r,f)=>d.jsx(cb,{fill:r.color||c[f%c.length]},`cell-${f}`))}),d.jsx(Mc,{content:d.jsx(Bh,{type:"percentage"})})]})}),d.jsx("div",{className:"mt-4 grid grid-cols-2 gap-2",children:i.map((r,f)=>d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:r.color||c[f%c.length]}}),d.jsx("span",{className:"text-sm text-gray-600",children:r.name})]},r.name))})]})]})},Xv=({data:i,loading:u=!1})=>u?d.jsxs("div",{className:"card",children:[d.jsx("div",{className:"card-header",children:d.jsx("h3",{className:"text-lg font-semibold",children:"Receita ao Longo do Tempo"})}),d.jsx("div",{className:"card-body",children:d.jsx("div",{className:"h-64 bg-gray-100 rounded-lg animate-pulse"})})]}):d.jsxs("div",{className:"card",children:[d.jsx("div",{className:"card-header",children:d.jsx("h3",{className:"text-lg font-semibold",children:"Receita ao Longo do Tempo"})}),d.jsx("div",{className:"card-body",children:d.jsx(zc,{width:"100%",height:300,children:d.jsxs(hh,{data:i,children:[d.jsx(gh,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),d.jsx(yh,{dataKey:"month",stroke:"#64748b",fontSize:12}),d.jsx(yc,{stroke:"#64748b",fontSize:12,tickFormatter:c=>Fl(c,{showSymbol:!1})}),d.jsx(Mc,{content:d.jsx(Bh,{type:"currency"})}),d.jsx(bc,{type:"monotone",dataKey:"revenue",stroke:"#3b82f6",strokeWidth:3,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2}})]})})})]}),Zv=({revenueData:i,leadsData:u,loading:c=!1})=>{if(c)return d.jsxs("div",{className:"card",children:[d.jsx("div",{className:"card-header",children:d.jsx("h3",{className:"text-lg font-semibold",children:"Performance Geral"})}),d.jsx("div",{className:"card-body",children:d.jsx("div",{className:"h-64 bg-gray-100 rounded-lg animate-pulse"})})]});const r=i.map((f,h)=>{var g;return{month:f.month,revenue:f.revenue,leads:((g=u[h])==null?void 0:g.leads)||0}});return d.jsxs("div",{className:"card",children:[d.jsx("div",{className:"card-header",children:d.jsx("h3",{className:"text-lg font-semibold",children:"Performance Geral"})}),d.jsx("div",{className:"card-body",children:d.jsx(zc,{width:"100%",height:300,children:d.jsxs(hh,{data:r,children:[d.jsx(gh,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),d.jsx(yh,{dataKey:"month",stroke:"#64748b",fontSize:12}),d.jsx(yc,{yAxisId:"left",stroke:"#64748b",fontSize:12,tickFormatter:f=>Fl(f,{showSymbol:!1})}),d.jsx(yc,{yAxisId:"right",orientation:"right",stroke:"#64748b",fontSize:12}),d.jsx(Mc,{content:({active:f,payload:h,label:g})=>{var b,w;return f&&h&&h.length?d.jsxs("div",{className:"bg-white p-3 border border-gray-200 rounded-lg shadow-lg",children:[d.jsx("p",{className:"text-sm font-medium text-gray-900 mb-1",children:g}),d.jsxs("p",{className:"text-sm",style:{color:"#3b82f6"},children:["Receita: ",Fl((b=h[0])==null?void 0:b.value)]}),d.jsxs("p",{className:"text-sm",style:{color:"#10b981"},children:["Leads: ",$l((w=h[1])==null?void 0:w.value)]})]}):null}}),d.jsx(ob,{}),d.jsx(bc,{yAxisId:"left",type:"monotone",dataKey:"revenue",stroke:"#3b82f6",strokeWidth:3,name:"Receita",dot:{fill:"#3b82f6",strokeWidth:2,r:4}}),d.jsx(bc,{yAxisId:"right",type:"monotone",dataKey:"leads",stroke:"#10b981",strokeWidth:3,name:"Leads",dot:{fill:"#10b981",strokeWidth:2,r:4}})]})})})]})},Qv=({chartData:i,loading:u=!1})=>d.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[d.jsx(Gv,{data:(i==null?void 0:i.conversionByChannel)||[],loading:u}),d.jsx(Xv,{data:(i==null?void 0:i.revenueOverTime)||[],loading:u}),d.jsx("div",{className:"lg:col-span-2",children:d.jsx(Zv,{revenueData:(i==null?void 0:i.revenueOverTime)||[],leadsData:(i==null?void 0:i.leadsOverTime)||[],loading:u})})]}),Hh=({agent:i,onViewDetails:u,onToggleStatus:c,onSettings:r,loading:f=!1})=>{if(f)return d.jsx("div",{className:"card",children:d.jsxs("div",{className:"card-body",children:[d.jsxs("div",{className:"flex items-center justify-between mb-4",children:[d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded-lg animate-pulse"}),d.jsxs("div",{children:[d.jsx("div",{className:"w-24 h-4 bg-gray-200 rounded animate-pulse mb-2"}),d.jsx("div",{className:"w-16 h-3 bg-gray-200 rounded animate-pulse"})]})]}),d.jsx("div",{className:"w-6 h-6 bg-gray-200 rounded animate-pulse"})]}),d.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[d.jsx("div",{className:"w-full h-16 bg-gray-200 rounded animate-pulse"}),d.jsx("div",{className:"w-full h-16 bg-gray-200 rounded animate-pulse"})]}),d.jsx("div",{className:"w-full h-8 bg-gray-200 rounded animate-pulse"})]})});const{id:h,name:g,channel:b,leadsAttended:w,activeConversations:j,responseRate:x,status:R,lastActivity:C}=i,Z=Nh(R),U=jh(b);return d.jsx("div",{className:"card hover:shadow-md transition-shadow",children:d.jsxs("div",{className:"card-body",children:[d.jsxs("div",{className:"flex items-center justify-between mb-4",children:[d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsxs("div",{className:"relative",children:[d.jsx("div",{className:"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-xl",children:U}),d.jsx("div",{className:"absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white",style:{backgroundColor:Z},title:R==="online"?"Online":"Offline"})]}),d.jsxs("div",{children:[d.jsx("h3",{className:"font-semibold text-gray-900",children:g}),d.jsx("p",{className:"text-sm text-gray-500",children:b})]})]}),d.jsxs("div",{className:"relative group",children:[d.jsx("button",{className:"p-1 hover:bg-gray-100 rounded",children:d.jsx(ky,{size:16,className:"text-gray-400"})}),d.jsxs("div",{className:"absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 opacity-0 group-hover:opacity-100 transition-opacity z-10 min-w-[120px]",children:[d.jsxs("button",{onClick:()=>u==null?void 0:u(i),className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2",children:[d.jsx(Ly,{size:14}),"Ver detalhes"]}),d.jsxs("button",{onClick:()=>r==null?void 0:r(i),className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2",children:[d.jsx(Ec,{size:14}),"Configurar"]}),d.jsxs("button",{onClick:()=>c==null?void 0:c(i),className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2",children:[d.jsx(Yy,{size:14}),R==="online"?"Desativar":"Ativar"]})]})]})]}),d.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[d.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[d.jsx(sl,{size:16,className:"text-gray-500"}),d.jsx("span",{className:"text-xs text-gray-500",children:"Leads Atendidos"})]}),d.jsx("div",{className:"text-lg font-semibold text-gray-900",children:$l(w)})]}),d.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[d.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[d.jsx(Us,{size:16,className:"text-gray-500"}),d.jsx("span",{className:"text-xs text-gray-500",children:"Conversas Ativas"})]}),d.jsx("div",{className:"text-lg font-semibold text-gray-900",children:$l(j)})]})]}),d.jsxs("div",{className:"mb-4",children:[d.jsxs("div",{className:"flex items-center justify-between mb-2",children:[d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx(ii,{size:16,className:"text-gray-500"}),d.jsx("span",{className:"text-sm text-gray-600",children:"Taxa de Resposta"})]}),d.jsx("span",{className:"text-sm font-semibold text-gray-900",children:si(x)})]}),d.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:d.jsx("div",{className:"h-2 rounded-full transition-all duration-300",style:{width:`${Math.min(x,100)}%`,backgroundColor:x>=90?"var(--success)":x>=70?"var(--warning)":"var(--danger)"}})})]}),d.jsxs("div",{className:"flex items-center justify-between text-sm",children:[d.jsxs("div",{className:"flex items-center gap-2 text-gray-500",children:[d.jsx(Gy,{size:14}),d.jsx("span",{children:"Última atividade"})]}),d.jsx("span",{className:"text-gray-600",children:Sh(C)})]}),d.jsx("div",{className:"mt-4 pt-4 border-t border-gray-100",children:d.jsx("button",{onClick:()=>u==null?void 0:u(i),className:"btn btn-primary w-full",children:"Ver Conversas"})})]})})},Vv=({count:i=3})=>d.jsx(d.Fragment,{children:Array.from({length:i}).map((u,c)=>d.jsx(Hh,{loading:!0},c))}),kh=({agents:i,loading:u=!1,onViewDetails:c,onToggleStatus:r,onSettings:f,className:h=""})=>u?d.jsx("div",{className:`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${h}`,children:d.jsx(Vv,{count:6})}):!i||i.length===0?d.jsxs("div",{className:"text-center py-12",children:[d.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:d.jsx(sl,{size:32,className:"text-gray-400"})}),d.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Nenhum agente encontrado"}),d.jsx("p",{className:"text-gray-500",children:"Configure seus primeiros agentes de IA para começar."})]}):d.jsx("div",{className:`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${h}`,children:i.map(g=>d.jsx(Hh,{agent:g,onViewDetails:c,onToggleStatus:r,onSettings:f},g.id))}),ra=me.forwardRef(({children:i,variant:u="primary",size:c="md",disabled:r=!1,loading:f=!1,leftIcon:h,rightIcon:g,fullWidth:b=!1,animate:w=!0,className:j,onClick:x,...R},C)=>{const Z=pt("inline-flex items-center justify-center gap-2 font-medium rounded-xl","focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500","disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none",_h.default,Uh.button[u],Hv.button[c],b&&"w-full",j),U=d.jsxs(d.Fragment,{children:[f&&d.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[d.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),d.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!f&&h&&d.jsx("span",{className:"flex-shrink-0",children:h}),d.jsx("span",{children:i}),!f&&g&&d.jsx("span",{className:"flex-shrink-0",children:g})]}),q=B=>{r||f||x==null||x(B)};return w?d.jsx(Ne.button,{ref:C,className:Z,onClick:q,disabled:r||f,whileHover:{scale:r||f?1:1.02},whileTap:{scale:r||f?1:.98},transition:{type:"spring",stiffness:400,damping:17},...R,children:U}):d.jsx("button",{ref:C,className:Z,onClick:q,disabled:r||f,...R,children:U})});ra.displayName="Button";function Lh(i,u){return function(){return i.apply(u,arguments)}}const{toString:Kv}=Object.prototype,{getPrototypeOf:Rc}=Object,{iterator:Hs,toStringTag:Yh}=Symbol,ks=(i=>u=>{const c=Kv.call(u);return i[c]||(i[c]=c.slice(8,-1).toLowerCase())})(Object.create(null)),Ut=i=>(i=i.toLowerCase(),u=>ks(u)===i),Ls=i=>u=>typeof u===i,{isArray:en}=Array,ri=Ls("undefined");function Jv(i){return i!==null&&!ri(i)&&i.constructor!==null&&!ri(i.constructor)&&ut(i.constructor.isBuffer)&&i.constructor.isBuffer(i)}const Gh=Ut("ArrayBuffer");function Wv(i){let u;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?u=ArrayBuffer.isView(i):u=i&&i.buffer&&Gh(i.buffer),u}const Fv=Ls("string"),ut=Ls("function"),Xh=Ls("number"),Ys=i=>i!==null&&typeof i=="object",$v=i=>i===!0||i===!1,Ds=i=>{if(ks(i)!=="object")return!1;const u=Rc(i);return(u===null||u===Object.prototype||Object.getPrototypeOf(u)===null)&&!(Yh in i)&&!(Hs in i)},Pv=Ut("Date"),Iv=Ut("File"),e1=Ut("Blob"),t1=Ut("FileList"),a1=i=>Ys(i)&&ut(i.pipe),l1=i=>{let u;return i&&(typeof FormData=="function"&&i instanceof FormData||ut(i.append)&&((u=ks(i))==="formdata"||u==="object"&&ut(i.toString)&&i.toString()==="[object FormData]"))},n1=Ut("URLSearchParams"),[i1,s1,u1,r1]=["ReadableStream","Request","Response","Headers"].map(Ut),c1=i=>i.trim?i.trim():i.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ci(i,u,{allOwnKeys:c=!1}={}){if(i===null||typeof i>"u")return;let r,f;if(typeof i!="object"&&(i=[i]),en(i))for(r=0,f=i.length;r<f;r++)u.call(null,i[r],r,i);else{const h=c?Object.getOwnPropertyNames(i):Object.keys(i),g=h.length;let b;for(r=0;r<g;r++)b=h[r],u.call(null,i[b],b,i)}}function Zh(i,u){u=u.toLowerCase();const c=Object.keys(i);let r=c.length,f;for(;r-- >0;)if(f=c[r],u===f.toLowerCase())return f;return null}const nl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Qh=i=>!ri(i)&&i!==nl;function Sc(){const{caseless:i}=Qh(this)&&this||{},u={},c=(r,f)=>{const h=i&&Zh(u,f)||f;Ds(u[h])&&Ds(r)?u[h]=Sc(u[h],r):Ds(r)?u[h]=Sc({},r):en(r)?u[h]=r.slice():u[h]=r};for(let r=0,f=arguments.length;r<f;r++)arguments[r]&&ci(arguments[r],c);return u}const o1=(i,u,c,{allOwnKeys:r}={})=>(ci(u,(f,h)=>{c&&ut(f)?i[h]=Lh(f,c):i[h]=f},{allOwnKeys:r}),i),f1=i=>(i.charCodeAt(0)===65279&&(i=i.slice(1)),i),d1=(i,u,c,r)=>{i.prototype=Object.create(u.prototype,r),i.prototype.constructor=i,Object.defineProperty(i,"super",{value:u.prototype}),c&&Object.assign(i.prototype,c)},m1=(i,u,c,r)=>{let f,h,g;const b={};if(u=u||{},i==null)return u;do{for(f=Object.getOwnPropertyNames(i),h=f.length;h-- >0;)g=f[h],(!r||r(g,i,u))&&!b[g]&&(u[g]=i[g],b[g]=!0);i=c!==!1&&Rc(i)}while(i&&(!c||c(i,u))&&i!==Object.prototype);return u},h1=(i,u,c)=>{i=String(i),(c===void 0||c>i.length)&&(c=i.length),c-=u.length;const r=i.indexOf(u,c);return r!==-1&&r===c},g1=i=>{if(!i)return null;if(en(i))return i;let u=i.length;if(!Xh(u))return null;const c=new Array(u);for(;u-- >0;)c[u]=i[u];return c},y1=(i=>u=>i&&u instanceof i)(typeof Uint8Array<"u"&&Rc(Uint8Array)),b1=(i,u)=>{const r=(i&&i[Hs]).call(i);let f;for(;(f=r.next())&&!f.done;){const h=f.value;u.call(i,h[0],h[1])}},p1=(i,u)=>{let c;const r=[];for(;(c=i.exec(u))!==null;)r.push(c);return r},v1=Ut("HTMLFormElement"),x1=i=>i.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(c,r,f){return r.toUpperCase()+f}),Xm=(({hasOwnProperty:i})=>(u,c)=>i.call(u,c))(Object.prototype),S1=Ut("RegExp"),Vh=(i,u)=>{const c=Object.getOwnPropertyDescriptors(i),r={};ci(c,(f,h)=>{let g;(g=u(f,h,i))!==!1&&(r[h]=g||f)}),Object.defineProperties(i,r)},N1=i=>{Vh(i,(u,c)=>{if(ut(i)&&["arguments","caller","callee"].indexOf(c)!==-1)return!1;const r=i[c];if(ut(r)){if(u.enumerable=!1,"writable"in u){u.writable=!1;return}u.set||(u.set=()=>{throw Error("Can not rewrite read-only method '"+c+"'")})}})},j1=(i,u)=>{const c={},r=f=>{f.forEach(h=>{c[h]=!0})};return en(i)?r(i):r(String(i).split(u)),c},w1=()=>{},A1=(i,u)=>i!=null&&Number.isFinite(i=+i)?i:u;function T1(i){return!!(i&&ut(i.append)&&i[Yh]==="FormData"&&i[Hs])}const E1=i=>{const u=new Array(10),c=(r,f)=>{if(Ys(r)){if(u.indexOf(r)>=0)return;if(!("toJSON"in r)){u[f]=r;const h=en(r)?[]:{};return ci(r,(g,b)=>{const w=c(g,f+1);!ri(w)&&(h[b]=w)}),u[f]=void 0,h}}return r};return c(i,0)},z1=Ut("AsyncFunction"),M1=i=>i&&(Ys(i)||ut(i))&&ut(i.then)&&ut(i.catch),Kh=((i,u)=>i?setImmediate:u?((c,r)=>(nl.addEventListener("message",({source:f,data:h})=>{f===nl&&h===c&&r.length&&r.shift()()},!1),f=>{r.push(f),nl.postMessage(c,"*")}))(`axios@${Math.random()}`,[]):c=>setTimeout(c))(typeof setImmediate=="function",ut(nl.postMessage)),O1=typeof queueMicrotask<"u"?queueMicrotask.bind(nl):typeof process<"u"&&process.nextTick||Kh,D1=i=>i!=null&&ut(i[Hs]),E={isArray:en,isArrayBuffer:Gh,isBuffer:Jv,isFormData:l1,isArrayBufferView:Wv,isString:Fv,isNumber:Xh,isBoolean:$v,isObject:Ys,isPlainObject:Ds,isReadableStream:i1,isRequest:s1,isResponse:u1,isHeaders:r1,isUndefined:ri,isDate:Pv,isFile:Iv,isBlob:e1,isRegExp:S1,isFunction:ut,isStream:a1,isURLSearchParams:n1,isTypedArray:y1,isFileList:t1,forEach:ci,merge:Sc,extend:o1,trim:c1,stripBOM:f1,inherits:d1,toFlatObject:m1,kindOf:ks,kindOfTest:Ut,endsWith:h1,toArray:g1,forEachEntry:b1,matchAll:p1,isHTMLForm:v1,hasOwnProperty:Xm,hasOwnProp:Xm,reduceDescriptors:Vh,freezeMethods:N1,toObjectSet:j1,toCamelCase:x1,noop:w1,toFiniteNumber:A1,findKey:Zh,global:nl,isContextDefined:Qh,isSpecCompliantForm:T1,toJSONObject:E1,isAsyncFn:z1,isThenable:M1,setImmediate:Kh,asap:O1,isIterable:D1};function P(i,u,c,r,f){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=i,this.name="AxiosError",u&&(this.code=u),c&&(this.config=c),r&&(this.request=r),f&&(this.response=f,this.status=f.status?f.status:null)}E.inherits(P,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:E.toJSONObject(this.config),code:this.code,status:this.status}}});const Jh=P.prototype,Wh={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(i=>{Wh[i]={value:i}});Object.defineProperties(P,Wh);Object.defineProperty(Jh,"isAxiosError",{value:!0});P.from=(i,u,c,r,f,h)=>{const g=Object.create(Jh);return E.toFlatObject(i,g,function(w){return w!==Error.prototype},b=>b!=="isAxiosError"),P.call(g,i.message,u,c,r,f),g.cause=i,g.name=i.name,h&&Object.assign(g,h),g};const R1=null;function Nc(i){return E.isPlainObject(i)||E.isArray(i)}function Fh(i){return E.endsWith(i,"[]")?i.slice(0,-2):i}function Zm(i,u,c){return i?i.concat(u).map(function(f,h){return f=Fh(f),!c&&h?"["+f+"]":f}).join(c?".":""):u}function C1(i){return E.isArray(i)&&!i.some(Nc)}const U1=E.toFlatObject(E,{},null,function(u){return/^is[A-Z]/.test(u)});function Gs(i,u,c){if(!E.isObject(i))throw new TypeError("target must be an object");u=u||new FormData,c=E.toFlatObject(c,{metaTokens:!0,dots:!1,indexes:!1},!1,function(q,B){return!E.isUndefined(B[q])});const r=c.metaTokens,f=c.visitor||x,h=c.dots,g=c.indexes,w=(c.Blob||typeof Blob<"u"&&Blob)&&E.isSpecCompliantForm(u);if(!E.isFunction(f))throw new TypeError("visitor must be a function");function j(U){if(U===null)return"";if(E.isDate(U))return U.toISOString();if(!w&&E.isBlob(U))throw new P("Blob is not supported. Use a Buffer instead.");return E.isArrayBuffer(U)||E.isTypedArray(U)?w&&typeof Blob=="function"?new Blob([U]):Buffer.from(U):U}function x(U,q,B){let J=U;if(U&&!B&&typeof U=="object"){if(E.endsWith(q,"{}"))q=r?q:q.slice(0,-2),U=JSON.stringify(U);else if(E.isArray(U)&&C1(U)||(E.isFileList(U)||E.endsWith(q,"[]"))&&(J=E.toArray(U)))return q=Fh(q),J.forEach(function(F,he){!(E.isUndefined(F)||F===null)&&u.append(g===!0?Zm([q],he,h):g===null?q:q+"[]",j(F))}),!1}return Nc(U)?!0:(u.append(Zm(B,q,h),j(U)),!1)}const R=[],C=Object.assign(U1,{defaultVisitor:x,convertValue:j,isVisitable:Nc});function Z(U,q){if(!E.isUndefined(U)){if(R.indexOf(U)!==-1)throw Error("Circular reference detected in "+q.join("."));R.push(U),E.forEach(U,function(J,I){(!(E.isUndefined(J)||J===null)&&f.call(u,J,E.isString(I)?I.trim():I,q,C))===!0&&Z(J,q?q.concat(I):[I])}),R.pop()}}if(!E.isObject(i))throw new TypeError("data must be an object");return Z(i),u}function Qm(i){const u={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(i).replace(/[!'()~]|%20|%00/g,function(r){return u[r]})}function Cc(i,u){this._pairs=[],i&&Gs(i,this,u)}const $h=Cc.prototype;$h.append=function(u,c){this._pairs.push([u,c])};$h.toString=function(u){const c=u?function(r){return u.call(this,r,Qm)}:Qm;return this._pairs.map(function(f){return c(f[0])+"="+c(f[1])},"").join("&")};function _1(i){return encodeURIComponent(i).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ph(i,u,c){if(!u)return i;const r=c&&c.encode||_1;E.isFunction(c)&&(c={serialize:c});const f=c&&c.serialize;let h;if(f?h=f(u,c):h=E.isURLSearchParams(u)?u.toString():new Cc(u,c).toString(r),h){const g=i.indexOf("#");g!==-1&&(i=i.slice(0,g)),i+=(i.indexOf("?")===-1?"?":"&")+h}return i}class Vm{constructor(){this.handlers=[]}use(u,c,r){return this.handlers.push({fulfilled:u,rejected:c,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(u){this.handlers[u]&&(this.handlers[u]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(u){E.forEach(this.handlers,function(r){r!==null&&u(r)})}}const Ih={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},q1=typeof URLSearchParams<"u"?URLSearchParams:Cc,B1=typeof FormData<"u"?FormData:null,H1=typeof Blob<"u"?Blob:null,k1={isBrowser:!0,classes:{URLSearchParams:q1,FormData:B1,Blob:H1},protocols:["http","https","file","blob","url","data"]},Uc=typeof window<"u"&&typeof document<"u",jc=typeof navigator=="object"&&navigator||void 0,L1=Uc&&(!jc||["ReactNative","NativeScript","NS"].indexOf(jc.product)<0),Y1=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",G1=Uc&&window.location.href||"http://localhost",X1=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Uc,hasStandardBrowserEnv:L1,hasStandardBrowserWebWorkerEnv:Y1,navigator:jc,origin:G1},Symbol.toStringTag,{value:"Module"})),Ie={...X1,...k1};function Z1(i,u){return Gs(i,new Ie.classes.URLSearchParams,Object.assign({visitor:function(c,r,f,h){return Ie.isNode&&E.isBuffer(c)?(this.append(r,c.toString("base64")),!1):h.defaultVisitor.apply(this,arguments)}},u))}function Q1(i){return E.matchAll(/\w+|\[(\w*)]/g,i).map(u=>u[0]==="[]"?"":u[1]||u[0])}function V1(i){const u={},c=Object.keys(i);let r;const f=c.length;let h;for(r=0;r<f;r++)h=c[r],u[h]=i[h];return u}function e0(i){function u(c,r,f,h){let g=c[h++];if(g==="__proto__")return!0;const b=Number.isFinite(+g),w=h>=c.length;return g=!g&&E.isArray(f)?f.length:g,w?(E.hasOwnProp(f,g)?f[g]=[f[g],r]:f[g]=r,!b):((!f[g]||!E.isObject(f[g]))&&(f[g]=[]),u(c,r,f[g],h)&&E.isArray(f[g])&&(f[g]=V1(f[g])),!b)}if(E.isFormData(i)&&E.isFunction(i.entries)){const c={};return E.forEachEntry(i,(r,f)=>{u(Q1(r),f,c,0)}),c}return null}function K1(i,u,c){if(E.isString(i))try{return(u||JSON.parse)(i),E.trim(i)}catch(r){if(r.name!=="SyntaxError")throw r}return(c||JSON.stringify)(i)}const oi={transitional:Ih,adapter:["xhr","http","fetch"],transformRequest:[function(u,c){const r=c.getContentType()||"",f=r.indexOf("application/json")>-1,h=E.isObject(u);if(h&&E.isHTMLForm(u)&&(u=new FormData(u)),E.isFormData(u))return f?JSON.stringify(e0(u)):u;if(E.isArrayBuffer(u)||E.isBuffer(u)||E.isStream(u)||E.isFile(u)||E.isBlob(u)||E.isReadableStream(u))return u;if(E.isArrayBufferView(u))return u.buffer;if(E.isURLSearchParams(u))return c.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),u.toString();let b;if(h){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Z1(u,this.formSerializer).toString();if((b=E.isFileList(u))||r.indexOf("multipart/form-data")>-1){const w=this.env&&this.env.FormData;return Gs(b?{"files[]":u}:u,w&&new w,this.formSerializer)}}return h||f?(c.setContentType("application/json",!1),K1(u)):u}],transformResponse:[function(u){const c=this.transitional||oi.transitional,r=c&&c.forcedJSONParsing,f=this.responseType==="json";if(E.isResponse(u)||E.isReadableStream(u))return u;if(u&&E.isString(u)&&(r&&!this.responseType||f)){const g=!(c&&c.silentJSONParsing)&&f;try{return JSON.parse(u)}catch(b){if(g)throw b.name==="SyntaxError"?P.from(b,P.ERR_BAD_RESPONSE,this,null,this.response):b}}return u}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ie.classes.FormData,Blob:Ie.classes.Blob},validateStatus:function(u){return u>=200&&u<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};E.forEach(["delete","get","head","post","put","patch"],i=>{oi.headers[i]={}});const J1=E.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),W1=i=>{const u={};let c,r,f;return i&&i.split(`
`).forEach(function(g){f=g.indexOf(":"),c=g.substring(0,f).trim().toLowerCase(),r=g.substring(f+1).trim(),!(!c||u[c]&&J1[c])&&(c==="set-cookie"?u[c]?u[c].push(r):u[c]=[r]:u[c]=u[c]?u[c]+", "+r:r)}),u},Km=Symbol("internals");function ni(i){return i&&String(i).trim().toLowerCase()}function Rs(i){return i===!1||i==null?i:E.isArray(i)?i.map(Rs):String(i)}function F1(i){const u=Object.create(null),c=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=c.exec(i);)u[r[1]]=r[2];return u}const $1=i=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(i.trim());function fc(i,u,c,r,f){if(E.isFunction(r))return r.call(this,u,c);if(f&&(u=c),!!E.isString(u)){if(E.isString(r))return u.indexOf(r)!==-1;if(E.isRegExp(r))return r.test(u)}}function P1(i){return i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(u,c,r)=>c.toUpperCase()+r)}function I1(i,u){const c=E.toCamelCase(" "+u);["get","set","has"].forEach(r=>{Object.defineProperty(i,r+c,{value:function(f,h,g){return this[r].call(this,u,f,h,g)},configurable:!0})})}let rt=class{constructor(u){u&&this.set(u)}set(u,c,r){const f=this;function h(b,w,j){const x=ni(w);if(!x)throw new Error("header name must be a non-empty string");const R=E.findKey(f,x);(!R||f[R]===void 0||j===!0||j===void 0&&f[R]!==!1)&&(f[R||w]=Rs(b))}const g=(b,w)=>E.forEach(b,(j,x)=>h(j,x,w));if(E.isPlainObject(u)||u instanceof this.constructor)g(u,c);else if(E.isString(u)&&(u=u.trim())&&!$1(u))g(W1(u),c);else if(E.isObject(u)&&E.isIterable(u)){let b={},w,j;for(const x of u){if(!E.isArray(x))throw TypeError("Object iterator must return a key-value pair");b[j=x[0]]=(w=b[j])?E.isArray(w)?[...w,x[1]]:[w,x[1]]:x[1]}g(b,c)}else u!=null&&h(c,u,r);return this}get(u,c){if(u=ni(u),u){const r=E.findKey(this,u);if(r){const f=this[r];if(!c)return f;if(c===!0)return F1(f);if(E.isFunction(c))return c.call(this,f,r);if(E.isRegExp(c))return c.exec(f);throw new TypeError("parser must be boolean|regexp|function")}}}has(u,c){if(u=ni(u),u){const r=E.findKey(this,u);return!!(r&&this[r]!==void 0&&(!c||fc(this,this[r],r,c)))}return!1}delete(u,c){const r=this;let f=!1;function h(g){if(g=ni(g),g){const b=E.findKey(r,g);b&&(!c||fc(r,r[b],b,c))&&(delete r[b],f=!0)}}return E.isArray(u)?u.forEach(h):h(u),f}clear(u){const c=Object.keys(this);let r=c.length,f=!1;for(;r--;){const h=c[r];(!u||fc(this,this[h],h,u,!0))&&(delete this[h],f=!0)}return f}normalize(u){const c=this,r={};return E.forEach(this,(f,h)=>{const g=E.findKey(r,h);if(g){c[g]=Rs(f),delete c[h];return}const b=u?P1(h):String(h).trim();b!==h&&delete c[h],c[b]=Rs(f),r[b]=!0}),this}concat(...u){return this.constructor.concat(this,...u)}toJSON(u){const c=Object.create(null);return E.forEach(this,(r,f)=>{r!=null&&r!==!1&&(c[f]=u&&E.isArray(r)?r.join(", "):r)}),c}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([u,c])=>u+": "+c).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(u){return u instanceof this?u:new this(u)}static concat(u,...c){const r=new this(u);return c.forEach(f=>r.set(f)),r}static accessor(u){const r=(this[Km]=this[Km]={accessors:{}}).accessors,f=this.prototype;function h(g){const b=ni(g);r[b]||(I1(f,g),r[b]=!0)}return E.isArray(u)?u.forEach(h):h(u),this}};rt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);E.reduceDescriptors(rt.prototype,({value:i},u)=>{let c=u[0].toUpperCase()+u.slice(1);return{get:()=>i,set(r){this[c]=r}}});E.freezeMethods(rt);function dc(i,u){const c=this||oi,r=u||c,f=rt.from(r.headers);let h=r.data;return E.forEach(i,function(b){h=b.call(c,h,f.normalize(),u?u.status:void 0)}),f.normalize(),h}function t0(i){return!!(i&&i.__CANCEL__)}function tn(i,u,c){P.call(this,i??"canceled",P.ERR_CANCELED,u,c),this.name="CanceledError"}E.inherits(tn,P,{__CANCEL__:!0});function a0(i,u,c){const r=c.config.validateStatus;!c.status||!r||r(c.status)?i(c):u(new P("Request failed with status code "+c.status,[P.ERR_BAD_REQUEST,P.ERR_BAD_RESPONSE][Math.floor(c.status/100)-4],c.config,c.request,c))}function ex(i){const u=/^([-+\w]{1,25})(:?\/\/|:)/.exec(i);return u&&u[1]||""}function tx(i,u){i=i||10;const c=new Array(i),r=new Array(i);let f=0,h=0,g;return u=u!==void 0?u:1e3,function(w){const j=Date.now(),x=r[h];g||(g=j),c[f]=w,r[f]=j;let R=h,C=0;for(;R!==f;)C+=c[R++],R=R%i;if(f=(f+1)%i,f===h&&(h=(h+1)%i),j-g<u)return;const Z=x&&j-x;return Z?Math.round(C*1e3/Z):void 0}}function ax(i,u){let c=0,r=1e3/u,f,h;const g=(j,x=Date.now())=>{c=x,f=null,h&&(clearTimeout(h),h=null),i.apply(null,j)};return[(...j)=>{const x=Date.now(),R=x-c;R>=r?g(j,x):(f=j,h||(h=setTimeout(()=>{h=null,g(f)},r-R)))},()=>f&&g(f)]}const _s=(i,u,c=3)=>{let r=0;const f=tx(50,250);return ax(h=>{const g=h.loaded,b=h.lengthComputable?h.total:void 0,w=g-r,j=f(w),x=g<=b;r=g;const R={loaded:g,total:b,progress:b?g/b:void 0,bytes:w,rate:j||void 0,estimated:j&&b&&x?(b-g)/j:void 0,event:h,lengthComputable:b!=null,[u?"download":"upload"]:!0};i(R)},c)},Jm=(i,u)=>{const c=i!=null;return[r=>u[0]({lengthComputable:c,total:i,loaded:r}),u[1]]},Wm=i=>(...u)=>E.asap(()=>i(...u)),lx=Ie.hasStandardBrowserEnv?((i,u)=>c=>(c=new URL(c,Ie.origin),i.protocol===c.protocol&&i.host===c.host&&(u||i.port===c.port)))(new URL(Ie.origin),Ie.navigator&&/(msie|trident)/i.test(Ie.navigator.userAgent)):()=>!0,nx=Ie.hasStandardBrowserEnv?{write(i,u,c,r,f,h){const g=[i+"="+encodeURIComponent(u)];E.isNumber(c)&&g.push("expires="+new Date(c).toGMTString()),E.isString(r)&&g.push("path="+r),E.isString(f)&&g.push("domain="+f),h===!0&&g.push("secure"),document.cookie=g.join("; ")},read(i){const u=document.cookie.match(new RegExp("(^|;\\s*)("+i+")=([^;]*)"));return u?decodeURIComponent(u[3]):null},remove(i){this.write(i,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ix(i){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)}function sx(i,u){return u?i.replace(/\/?\/$/,"")+"/"+u.replace(/^\/+/,""):i}function l0(i,u,c){let r=!ix(u);return i&&(r||c==!1)?sx(i,u):u}const Fm=i=>i instanceof rt?{...i}:i;function ul(i,u){u=u||{};const c={};function r(j,x,R,C){return E.isPlainObject(j)&&E.isPlainObject(x)?E.merge.call({caseless:C},j,x):E.isPlainObject(x)?E.merge({},x):E.isArray(x)?x.slice():x}function f(j,x,R,C){if(E.isUndefined(x)){if(!E.isUndefined(j))return r(void 0,j,R,C)}else return r(j,x,R,C)}function h(j,x){if(!E.isUndefined(x))return r(void 0,x)}function g(j,x){if(E.isUndefined(x)){if(!E.isUndefined(j))return r(void 0,j)}else return r(void 0,x)}function b(j,x,R){if(R in u)return r(j,x);if(R in i)return r(void 0,j)}const w={url:h,method:h,data:h,baseURL:g,transformRequest:g,transformResponse:g,paramsSerializer:g,timeout:g,timeoutMessage:g,withCredentials:g,withXSRFToken:g,adapter:g,responseType:g,xsrfCookieName:g,xsrfHeaderName:g,onUploadProgress:g,onDownloadProgress:g,decompress:g,maxContentLength:g,maxBodyLength:g,beforeRedirect:g,transport:g,httpAgent:g,httpsAgent:g,cancelToken:g,socketPath:g,responseEncoding:g,validateStatus:b,headers:(j,x,R)=>f(Fm(j),Fm(x),R,!0)};return E.forEach(Object.keys(Object.assign({},i,u)),function(x){const R=w[x]||f,C=R(i[x],u[x],x);E.isUndefined(C)&&R!==b||(c[x]=C)}),c}const n0=i=>{const u=ul({},i);let{data:c,withXSRFToken:r,xsrfHeaderName:f,xsrfCookieName:h,headers:g,auth:b}=u;u.headers=g=rt.from(g),u.url=Ph(l0(u.baseURL,u.url,u.allowAbsoluteUrls),i.params,i.paramsSerializer),b&&g.set("Authorization","Basic "+btoa((b.username||"")+":"+(b.password?unescape(encodeURIComponent(b.password)):"")));let w;if(E.isFormData(c)){if(Ie.hasStandardBrowserEnv||Ie.hasStandardBrowserWebWorkerEnv)g.setContentType(void 0);else if((w=g.getContentType())!==!1){const[j,...x]=w?w.split(";").map(R=>R.trim()).filter(Boolean):[];g.setContentType([j||"multipart/form-data",...x].join("; "))}}if(Ie.hasStandardBrowserEnv&&(r&&E.isFunction(r)&&(r=r(u)),r||r!==!1&&lx(u.url))){const j=f&&h&&nx.read(h);j&&g.set(f,j)}return u},ux=typeof XMLHttpRequest<"u",rx=ux&&function(i){return new Promise(function(c,r){const f=n0(i);let h=f.data;const g=rt.from(f.headers).normalize();let{responseType:b,onUploadProgress:w,onDownloadProgress:j}=f,x,R,C,Z,U;function q(){Z&&Z(),U&&U(),f.cancelToken&&f.cancelToken.unsubscribe(x),f.signal&&f.signal.removeEventListener("abort",x)}let B=new XMLHttpRequest;B.open(f.method.toUpperCase(),f.url,!0),B.timeout=f.timeout;function J(){if(!B)return;const F=rt.from("getAllResponseHeaders"in B&&B.getAllResponseHeaders()),ue={data:!b||b==="text"||b==="json"?B.responseText:B.response,status:B.status,statusText:B.statusText,headers:F,config:i,request:B};a0(function(je){c(je),q()},function(je){r(je),q()},ue),B=null}"onloadend"in B?B.onloadend=J:B.onreadystatechange=function(){!B||B.readyState!==4||B.status===0&&!(B.responseURL&&B.responseURL.indexOf("file:")===0)||setTimeout(J)},B.onabort=function(){B&&(r(new P("Request aborted",P.ECONNABORTED,i,B)),B=null)},B.onerror=function(){r(new P("Network Error",P.ERR_NETWORK,i,B)),B=null},B.ontimeout=function(){let he=f.timeout?"timeout of "+f.timeout+"ms exceeded":"timeout exceeded";const ue=f.transitional||Ih;f.timeoutErrorMessage&&(he=f.timeoutErrorMessage),r(new P(he,ue.clarifyTimeoutError?P.ETIMEDOUT:P.ECONNABORTED,i,B)),B=null},h===void 0&&g.setContentType(null),"setRequestHeader"in B&&E.forEach(g.toJSON(),function(he,ue){B.setRequestHeader(ue,he)}),E.isUndefined(f.withCredentials)||(B.withCredentials=!!f.withCredentials),b&&b!=="json"&&(B.responseType=f.responseType),j&&([C,U]=_s(j,!0),B.addEventListener("progress",C)),w&&B.upload&&([R,Z]=_s(w),B.upload.addEventListener("progress",R),B.upload.addEventListener("loadend",Z)),(f.cancelToken||f.signal)&&(x=F=>{B&&(r(!F||F.type?new tn(null,i,B):F),B.abort(),B=null)},f.cancelToken&&f.cancelToken.subscribe(x),f.signal&&(f.signal.aborted?x():f.signal.addEventListener("abort",x)));const I=ex(f.url);if(I&&Ie.protocols.indexOf(I)===-1){r(new P("Unsupported protocol "+I+":",P.ERR_BAD_REQUEST,i));return}B.send(h||null)})},cx=(i,u)=>{const{length:c}=i=i?i.filter(Boolean):[];if(u||c){let r=new AbortController,f;const h=function(j){if(!f){f=!0,b();const x=j instanceof Error?j:this.reason;r.abort(x instanceof P?x:new tn(x instanceof Error?x.message:x))}};let g=u&&setTimeout(()=>{g=null,h(new P(`timeout ${u} of ms exceeded`,P.ETIMEDOUT))},u);const b=()=>{i&&(g&&clearTimeout(g),g=null,i.forEach(j=>{j.unsubscribe?j.unsubscribe(h):j.removeEventListener("abort",h)}),i=null)};i.forEach(j=>j.addEventListener("abort",h));const{signal:w}=r;return w.unsubscribe=()=>E.asap(b),w}},ox=function*(i,u){let c=i.byteLength;if(c<u){yield i;return}let r=0,f;for(;r<c;)f=r+u,yield i.slice(r,f),r=f},fx=async function*(i,u){for await(const c of dx(i))yield*ox(c,u)},dx=async function*(i){if(i[Symbol.asyncIterator]){yield*i;return}const u=i.getReader();try{for(;;){const{done:c,value:r}=await u.read();if(c)break;yield r}}finally{await u.cancel()}},$m=(i,u,c,r)=>{const f=fx(i,u);let h=0,g,b=w=>{g||(g=!0,r&&r(w))};return new ReadableStream({async pull(w){try{const{done:j,value:x}=await f.next();if(j){b(),w.close();return}let R=x.byteLength;if(c){let C=h+=R;c(C)}w.enqueue(new Uint8Array(x))}catch(j){throw b(j),j}},cancel(w){return b(w),f.return()}},{highWaterMark:2})},Xs=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",i0=Xs&&typeof ReadableStream=="function",mx=Xs&&(typeof TextEncoder=="function"?(i=>u=>i.encode(u))(new TextEncoder):async i=>new Uint8Array(await new Response(i).arrayBuffer())),s0=(i,...u)=>{try{return!!i(...u)}catch{return!1}},hx=i0&&s0(()=>{let i=!1;const u=new Request(Ie.origin,{body:new ReadableStream,method:"POST",get duplex(){return i=!0,"half"}}).headers.has("Content-Type");return i&&!u}),Pm=64*1024,wc=i0&&s0(()=>E.isReadableStream(new Response("").body)),qs={stream:wc&&(i=>i.body)};Xs&&(i=>{["text","arrayBuffer","blob","formData","stream"].forEach(u=>{!qs[u]&&(qs[u]=E.isFunction(i[u])?c=>c[u]():(c,r)=>{throw new P(`Response type '${u}' is not supported`,P.ERR_NOT_SUPPORT,r)})})})(new Response);const gx=async i=>{if(i==null)return 0;if(E.isBlob(i))return i.size;if(E.isSpecCompliantForm(i))return(await new Request(Ie.origin,{method:"POST",body:i}).arrayBuffer()).byteLength;if(E.isArrayBufferView(i)||E.isArrayBuffer(i))return i.byteLength;if(E.isURLSearchParams(i)&&(i=i+""),E.isString(i))return(await mx(i)).byteLength},yx=async(i,u)=>{const c=E.toFiniteNumber(i.getContentLength());return c??gx(u)},bx=Xs&&(async i=>{let{url:u,method:c,data:r,signal:f,cancelToken:h,timeout:g,onDownloadProgress:b,onUploadProgress:w,responseType:j,headers:x,withCredentials:R="same-origin",fetchOptions:C}=n0(i);j=j?(j+"").toLowerCase():"text";let Z=cx([f,h&&h.toAbortSignal()],g),U;const q=Z&&Z.unsubscribe&&(()=>{Z.unsubscribe()});let B;try{if(w&&hx&&c!=="get"&&c!=="head"&&(B=await yx(x,r))!==0){let ue=new Request(u,{method:"POST",body:r,duplex:"half"}),fe;if(E.isFormData(r)&&(fe=ue.headers.get("content-type"))&&x.setContentType(fe),ue.body){const[je,G]=Jm(B,_s(Wm(w)));r=$m(ue.body,Pm,je,G)}}E.isString(R)||(R=R?"include":"omit");const J="credentials"in Request.prototype;U=new Request(u,{...C,signal:Z,method:c.toUpperCase(),headers:x.normalize().toJSON(),body:r,duplex:"half",credentials:J?R:void 0});let I=await fetch(U);const F=wc&&(j==="stream"||j==="response");if(wc&&(b||F&&q)){const ue={};["status","statusText","headers"].forEach(X=>{ue[X]=I[X]});const fe=E.toFiniteNumber(I.headers.get("content-length")),[je,G]=b&&Jm(fe,_s(Wm(b),!0))||[];I=new Response($m(I.body,Pm,je,()=>{G&&G(),q&&q()}),ue)}j=j||"text";let he=await qs[E.findKey(qs,j)||"text"](I,i);return!F&&q&&q(),await new Promise((ue,fe)=>{a0(ue,fe,{data:he,headers:rt.from(I.headers),status:I.status,statusText:I.statusText,config:i,request:U})})}catch(J){throw q&&q(),J&&J.name==="TypeError"&&/Load failed|fetch/i.test(J.message)?Object.assign(new P("Network Error",P.ERR_NETWORK,i,U),{cause:J.cause||J}):P.from(J,J&&J.code,i,U)}}),Ac={http:R1,xhr:rx,fetch:bx};E.forEach(Ac,(i,u)=>{if(i){try{Object.defineProperty(i,"name",{value:u})}catch{}Object.defineProperty(i,"adapterName",{value:u})}});const Im=i=>`- ${i}`,px=i=>E.isFunction(i)||i===null||i===!1,u0={getAdapter:i=>{i=E.isArray(i)?i:[i];const{length:u}=i;let c,r;const f={};for(let h=0;h<u;h++){c=i[h];let g;if(r=c,!px(c)&&(r=Ac[(g=String(c)).toLowerCase()],r===void 0))throw new P(`Unknown adapter '${g}'`);if(r)break;f[g||"#"+h]=r}if(!r){const h=Object.entries(f).map(([b,w])=>`adapter ${b} `+(w===!1?"is not supported by the environment":"is not available in the build"));let g=u?h.length>1?`since :
`+h.map(Im).join(`
`):" "+Im(h[0]):"as no adapter specified";throw new P("There is no suitable adapter to dispatch the request "+g,"ERR_NOT_SUPPORT")}return r},adapters:Ac};function mc(i){if(i.cancelToken&&i.cancelToken.throwIfRequested(),i.signal&&i.signal.aborted)throw new tn(null,i)}function eh(i){return mc(i),i.headers=rt.from(i.headers),i.data=dc.call(i,i.transformRequest),["post","put","patch"].indexOf(i.method)!==-1&&i.headers.setContentType("application/x-www-form-urlencoded",!1),u0.getAdapter(i.adapter||oi.adapter)(i).then(function(r){return mc(i),r.data=dc.call(i,i.transformResponse,r),r.headers=rt.from(r.headers),r},function(r){return t0(r)||(mc(i),r&&r.response&&(r.response.data=dc.call(i,i.transformResponse,r.response),r.response.headers=rt.from(r.response.headers))),Promise.reject(r)})}const r0="1.9.0",Zs={};["object","boolean","number","function","string","symbol"].forEach((i,u)=>{Zs[i]=function(r){return typeof r===i||"a"+(u<1?"n ":" ")+i}});const th={};Zs.transitional=function(u,c,r){function f(h,g){return"[Axios v"+r0+"] Transitional option '"+h+"'"+g+(r?". "+r:"")}return(h,g,b)=>{if(u===!1)throw new P(f(g," has been removed"+(c?" in "+c:"")),P.ERR_DEPRECATED);return c&&!th[g]&&(th[g]=!0,console.warn(f(g," has been deprecated since v"+c+" and will be removed in the near future"))),u?u(h,g,b):!0}};Zs.spelling=function(u){return(c,r)=>(console.warn(`${r} is likely a misspelling of ${u}`),!0)};function vx(i,u,c){if(typeof i!="object")throw new P("options must be an object",P.ERR_BAD_OPTION_VALUE);const r=Object.keys(i);let f=r.length;for(;f-- >0;){const h=r[f],g=u[h];if(g){const b=i[h],w=b===void 0||g(b,h,i);if(w!==!0)throw new P("option "+h+" must be "+w,P.ERR_BAD_OPTION_VALUE);continue}if(c!==!0)throw new P("Unknown option "+h,P.ERR_BAD_OPTION)}}const Cs={assertOptions:vx,validators:Zs},Yt=Cs.validators;let il=class{constructor(u){this.defaults=u||{},this.interceptors={request:new Vm,response:new Vm}}async request(u,c){try{return await this._request(u,c)}catch(r){if(r instanceof Error){let f={};Error.captureStackTrace?Error.captureStackTrace(f):f=new Error;const h=f.stack?f.stack.replace(/^.+\n/,""):"";try{r.stack?h&&!String(r.stack).endsWith(h.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+h):r.stack=h}catch{}}throw r}}_request(u,c){typeof u=="string"?(c=c||{},c.url=u):c=u||{},c=ul(this.defaults,c);const{transitional:r,paramsSerializer:f,headers:h}=c;r!==void 0&&Cs.assertOptions(r,{silentJSONParsing:Yt.transitional(Yt.boolean),forcedJSONParsing:Yt.transitional(Yt.boolean),clarifyTimeoutError:Yt.transitional(Yt.boolean)},!1),f!=null&&(E.isFunction(f)?c.paramsSerializer={serialize:f}:Cs.assertOptions(f,{encode:Yt.function,serialize:Yt.function},!0)),c.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?c.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:c.allowAbsoluteUrls=!0),Cs.assertOptions(c,{baseUrl:Yt.spelling("baseURL"),withXsrfToken:Yt.spelling("withXSRFToken")},!0),c.method=(c.method||this.defaults.method||"get").toLowerCase();let g=h&&E.merge(h.common,h[c.method]);h&&E.forEach(["delete","get","head","post","put","patch","common"],U=>{delete h[U]}),c.headers=rt.concat(g,h);const b=[];let w=!0;this.interceptors.request.forEach(function(q){typeof q.runWhen=="function"&&q.runWhen(c)===!1||(w=w&&q.synchronous,b.unshift(q.fulfilled,q.rejected))});const j=[];this.interceptors.response.forEach(function(q){j.push(q.fulfilled,q.rejected)});let x,R=0,C;if(!w){const U=[eh.bind(this),void 0];for(U.unshift.apply(U,b),U.push.apply(U,j),C=U.length,x=Promise.resolve(c);R<C;)x=x.then(U[R++],U[R++]);return x}C=b.length;let Z=c;for(R=0;R<C;){const U=b[R++],q=b[R++];try{Z=U(Z)}catch(B){q.call(this,B);break}}try{x=eh.call(this,Z)}catch(U){return Promise.reject(U)}for(R=0,C=j.length;R<C;)x=x.then(j[R++],j[R++]);return x}getUri(u){u=ul(this.defaults,u);const c=l0(u.baseURL,u.url,u.allowAbsoluteUrls);return Ph(c,u.params,u.paramsSerializer)}};E.forEach(["delete","get","head","options"],function(u){il.prototype[u]=function(c,r){return this.request(ul(r||{},{method:u,url:c,data:(r||{}).data}))}});E.forEach(["post","put","patch"],function(u){function c(r){return function(h,g,b){return this.request(ul(b||{},{method:u,headers:r?{"Content-Type":"multipart/form-data"}:{},url:h,data:g}))}}il.prototype[u]=c(),il.prototype[u+"Form"]=c(!0)});let xx=class c0{constructor(u){if(typeof u!="function")throw new TypeError("executor must be a function.");let c;this.promise=new Promise(function(h){c=h});const r=this;this.promise.then(f=>{if(!r._listeners)return;let h=r._listeners.length;for(;h-- >0;)r._listeners[h](f);r._listeners=null}),this.promise.then=f=>{let h;const g=new Promise(b=>{r.subscribe(b),h=b}).then(f);return g.cancel=function(){r.unsubscribe(h)},g},u(function(h,g,b){r.reason||(r.reason=new tn(h,g,b),c(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(u){if(this.reason){u(this.reason);return}this._listeners?this._listeners.push(u):this._listeners=[u]}unsubscribe(u){if(!this._listeners)return;const c=this._listeners.indexOf(u);c!==-1&&this._listeners.splice(c,1)}toAbortSignal(){const u=new AbortController,c=r=>{u.abort(r)};return this.subscribe(c),u.signal.unsubscribe=()=>this.unsubscribe(c),u.signal}static source(){let u;return{token:new c0(function(f){u=f}),cancel:u}}};function Sx(i){return function(c){return i.apply(null,c)}}function Nx(i){return E.isObject(i)&&i.isAxiosError===!0}const Tc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Tc).forEach(([i,u])=>{Tc[u]=i});function o0(i){const u=new il(i),c=Lh(il.prototype.request,u);return E.extend(c,il.prototype,u,{allOwnKeys:!0}),E.extend(c,u,null,{allOwnKeys:!0}),c.create=function(f){return o0(ul(i,f))},c}const _e=o0(oi);_e.Axios=il;_e.CanceledError=tn;_e.CancelToken=xx;_e.isCancel=t0;_e.VERSION=r0;_e.toFormData=Gs;_e.AxiosError=P;_e.Cancel=_e.CanceledError;_e.all=function(u){return Promise.all(u)};_e.spread=Sx;_e.isAxiosError=Nx;_e.mergeConfig=ul;_e.AxiosHeaders=rt;_e.formToJSON=i=>e0(E.isHTMLForm(i)?new FormData(i):i);_e.getAdapter=u0.getAdapter;_e.HttpStatusCode=Tc;_e.default=_e;const{Axios:Px,AxiosError:Ix,CanceledError:e2,isCancel:t2,CancelToken:a2,VERSION:l2,all:n2,Cancel:i2,isAxiosError:s2,spread:u2,toFormData:r2,AxiosHeaders:c2,HttpStatusCode:o2,formToJSON:f2,getAdapter:d2,mergeConfig:m2}=_e,jx={cpl:45.5,cac:180,cpc:2.3,conversionRate:12.5,revenuePerAgent:15420,activeLeads:234,totalConversations:1847,qualifiedConversations:456},wx=[{id:1,name:"Bot WhatsApp Vendas",channel:"WhatsApp",leadsAttended:156,activeConversations:23,responseRate:94.2,status:"online",lastActivity:"2024-01-15T10:30:00Z"},{id:2,name:"Assistente Instagram",channel:"Instagram",leadsAttended:89,activeConversations:12,responseRate:87.5,status:"online",lastActivity:"2024-01-15T09:45:00Z"},{id:3,name:"Chat Site Principal",channel:"Site",leadsAttended:203,activeConversations:31,responseRate:91.8,status:"online",lastActivity:"2024-01-15T11:15:00Z"},{id:4,name:"Bot Facebook Messenger",channel:"Facebook",leadsAttended:67,activeConversations:8,responseRate:83.1,status:"offline",lastActivity:"2024-01-15T08:20:00Z"},{id:5,name:"Suporte Telegram",channel:"Telegram",leadsAttended:34,activeConversations:5,responseRate:96.7,status:"online",lastActivity:"2024-01-15T11:00:00Z"}],Ax=[{id:1,leadName:"Maria Silva",channel:"WhatsApp",agentId:1,status:"quente",intent:"orçamento",lastMessage:"Gostaria de saber o preço do plano premium",lastMessageTime:"2024-01-15T11:30:00Z",messagesCount:8,phone:"+5511999887766",email:"<EMAIL>",messages:[{id:1,sender:"lead",content:"Olá, gostaria de informações sobre seus serviços",timestamp:"2024-01-15T10:00:00Z"},{id:2,sender:"agent",content:"Olá Maria! Fico feliz em ajudar. Que tipo de serviço você está procurando?",timestamp:"2024-01-15T10:01:00Z"},{id:3,sender:"lead",content:"Preciso de uma solução para automação de vendas",timestamp:"2024-01-15T10:05:00Z"},{id:4,sender:"agent",content:"Perfeito! Temos várias opções. Você já tem alguma ferramenta atualmente?",timestamp:"2024-01-15T10:06:00Z"},{id:5,sender:"lead",content:"Não, seria minha primeira vez usando algo assim",timestamp:"2024-01-15T10:10:00Z"},{id:6,sender:"agent",content:"Entendi! Vou te mostrar nosso plano ideal para iniciantes. Posso enviar um material?",timestamp:"2024-01-15T10:11:00Z"},{id:7,sender:"lead",content:"Sim, por favor! E gostaria de saber o preço também",timestamp:"2024-01-15T10:15:00Z"},{id:8,sender:"lead",content:"Gostaria de saber o preço do plano premium",timestamp:"2024-01-15T11:30:00Z"}]},{id:2,leadName:"João Santos",channel:"Instagram",agentId:2,status:"morno",intent:"suporte",lastMessage:"Estou com dificuldades para configurar",lastMessageTime:"2024-01-15T10:45:00Z",messagesCount:5,phone:"+5511888776655",email:"<EMAIL>",messages:[{id:1,sender:"lead",content:"Oi, comprei o produto mas estou com dificuldades",timestamp:"2024-01-15T09:30:00Z"},{id:2,sender:"agent",content:"Olá João! Vou te ajudar. Qual dificuldade específica você está enfrentando?",timestamp:"2024-01-15T09:31:00Z"},{id:3,sender:"lead",content:"Não consigo fazer a integração com meu sistema",timestamp:"2024-01-15T09:35:00Z"},{id:4,sender:"agent",content:"Entendo. Que sistema você está usando? Posso te enviar um tutorial específico.",timestamp:"2024-01-15T09:36:00Z"},{id:5,sender:"lead",content:"Estou com dificuldades para configurar",timestamp:"2024-01-15T10:45:00Z"}]},{id:3,leadName:"Ana Costa",channel:"Site",agentId:3,status:"quente",intent:"agendamento",lastMessage:"Posso agendar uma demonstração?",lastMessageTime:"2024-01-15T11:20:00Z",messagesCount:6,phone:"+5511777665544",email:"<EMAIL>",messages:[{id:1,sender:"lead",content:"Olá, vi vocês no Google e gostei do que fazem",timestamp:"2024-01-15T10:30:00Z"},{id:2,sender:"agent",content:"Olá Ana! Muito obrigado pelo interesse. Como posso ajudá-la?",timestamp:"2024-01-15T10:31:00Z"},{id:3,sender:"lead",content:"Gostaria de ver uma demonstração do produto",timestamp:"2024-01-15T10:35:00Z"},{id:4,sender:"agent",content:"Claro! Seria ótimo mostrar como nossa solução pode ajudar sua empresa.",timestamp:"2024-01-15T10:36:00Z"},{id:5,sender:"lead",content:"Que horários vocês têm disponível esta semana?",timestamp:"2024-01-15T11:00:00Z"},{id:6,sender:"lead",content:"Posso agendar uma demonstração?",timestamp:"2024-01-15T11:20:00Z"}]},{id:4,leadName:"Pedro Oliveira",channel:"Facebook",agentId:4,status:"frio",intent:"informação",lastMessage:"Obrigado pelas informações",lastMessageTime:"2024-01-15T08:30:00Z",messagesCount:3,phone:"+5511666554433",email:"<EMAIL>",messages:[{id:1,sender:"lead",content:"Oi, vi o anúncio de vocês no Facebook",timestamp:"2024-01-15T08:00:00Z"},{id:2,sender:"agent",content:"Olá Pedro! Que bom que nos encontrou. Em que posso ajudá-lo?",timestamp:"2024-01-15T08:01:00Z"},{id:3,sender:"lead",content:"Obrigado pelas informações",timestamp:"2024-01-15T08:30:00Z"}]}],Tx={conversionByChannel:[{name:"WhatsApp",value:35,color:"#25D366"},{name:"Instagram",value:25,color:"#E4405F"},{name:"Site",value:30,color:"#3b82f6"},{name:"Facebook",value:10,color:"#1877F2"}],revenueOverTime:[{month:"Jan",revenue:12e3},{month:"Fev",revenue:15e3},{month:"Mar",revenue:18e3},{month:"Abr",revenue:16e3},{month:"Mai",revenue:22e3},{month:"Jun",revenue:25e3}],leadsOverTime:[{month:"Jan",leads:120},{month:"Fev",leads:150},{month:"Mar",leads:180},{month:"Abr",leads:160},{month:"Mai",leads:220},{month:"Jun",leads:250}]},Qs=i=>new Promise(u=>setTimeout(u,i)),ah=async()=>(await Qs(500),jx),lh=async()=>(await Qs(300),wx),nh=async(i={})=>{await Qs(400);let u=[...Ax];return i.channel&&(u=u.filter(c=>c.channel===i.channel)),i.status&&(u=u.filter(c=>c.status===i.status)),i.agentId&&(u=u.filter(c=>c.agentId===i.agentId)),u},ih=async()=>(await Qs(600),Tx);var f0={};const Ex=f0.REACT_APP_API_URL||"http://localhost:3001",zx=f0.REACT_APP_N8N_WEBHOOK_URL||"",Zt=_e.create({baseURL:Ex,timeout:1e4,headers:{"Content-Type":"application/json"}});Zt.interceptors.request.use(i=>{const u=localStorage.getItem("authToken");return u&&(i.headers.Authorization=`Bearer ${u}`),i},i=>Promise.reject(i));Zt.interceptors.response.use(i=>i,i=>{var u;return console.error("API Error:",i),((u=i.response)==null?void 0:u.status)===401&&(localStorage.removeItem("authToken"),window.location.href="/login"),Promise.reject(i)});const Ba=!zx,Mx=async(i={})=>{if(Ba)return await ah();try{return(await Zt.get("/api/kpis",{params:i})).data}catch(u){return console.error("Erro ao buscar KPIs:",u),await ah()}},d0=async()=>{if(Ba)return await lh();try{return(await Zt.get("/api/agents")).data}catch(i){return console.error("Erro ao buscar agentes:",i),await lh()}},Ox=async(i={})=>{if(Ba)return await nh(i);try{return(await Zt.get("/api/conversations",{params:i})).data}catch(u){return console.error("Erro ao buscar conversas:",u),await nh(i)}},Dx=async(i="all",u={})=>{if(Ba)return await ih();try{return(await Zt.get("/api/charts",{params:{type:i,...u}})).data}catch(c){return console.error("Erro ao buscar dados dos gráficos:",c),await ih()}},Rx=async(i,u)=>{if(Ba)return console.log("Mock: Enviando WhatsApp para",i,":",u),{success:!0,message:"Mensagem enviada com sucesso (mock)"};try{return(await Zt.post("/api/whatsapp/send",{phone:i,message:u})).data}catch(c){throw console.error("Erro ao enviar WhatsApp:",c),c}},Cx=async(i,u,c="")=>{if(Ba)return console.log("Mock: Agendando call para lead",i,"em",u),{success:!0,message:"Call agendada com sucesso (mock)"};try{return(await Zt.post("/api/schedule",{leadId:i,datetime:u,notes:c})).data}catch(r){throw console.error("Erro ao agendar call:",r),r}},Ux=async(i,u="medium")=>{if(Ba)return console.log("Mock: Marcando conversa",i,"como oportunidade"),{success:!0,message:"Marcado como oportunidade (mock)"};try{return(await Zt.patch(`/api/conversations/${i}/opportunity`,{priority:u})).data}catch(c){throw console.error("Erro ao marcar como oportunidade:",c),c}},_x=async(i,u)=>{if(Ba)return console.log("Mock: Atualizando status do agente",i,"para",u),{success:!0};try{return(await Zt.patch(`/api/agents/${i}/status`,{status:u})).data}catch(c){throw console.error("Erro ao atualizar status do agente:",c),c}},qx=()=>{const[i,u]=me.useState(null),[c,r]=me.useState([]),[f,h]=me.useState(null),[g,b]=me.useState(!0),[w,j]=me.useState(null);me.useEffect(()=>{x()},[]);const x=async()=>{try{b(!0),j(null);const[C,Z,U]=await Promise.all([Mx(),d0(),Dx()]);u(C),r(Z),h(U)}catch(C){console.error("Erro ao carregar dados do dashboard:",C),j("Erro ao carregar dados. Tente novamente.")}finally{b(!1)}},R=i?[{title:"Custo por Lead (CPL)",value:i.cpl,type:"currency",icon:Xy,color:"blue",trend:"down",trendValue:5.2,subtitle:"Redução de 5.2% vs. mês anterior"},{title:"Custo de Aquisição (CAC)",value:i.cac,type:"currency",icon:Zy,color:"green",trend:"down",trendValue:8.1,subtitle:"Otimização de 8.1% no período"},{title:"Custo por Clique (CPC)",value:i.cpc,type:"currency",icon:Qy,color:"yellow",trend:"up",trendValue:2.3,subtitle:"Aumento de 2.3% vs. período anterior"},{title:"Taxa de Conversão",value:i.conversionRate,type:"percentage",icon:ii,color:"green",trend:"up",trendValue:12.5,subtitle:"Melhoria significativa de 12.5%"},{title:"Receita por Agente",value:i.revenuePerAgent,type:"currency",icon:Mm,color:"blue",trend:"up",trendValue:15.8,subtitle:"Crescimento de 15.8% na produtividade"},{title:"Leads Ativos",value:i.activeLeads,type:"number",icon:sl,color:"green",trend:"up",trendValue:7.2,subtitle:"Aumento de 7.2% em leads qualificados"},{title:"Total de Conversas",value:i.totalConversations,type:"number",icon:Us,color:"blue",trend:"up",trendValue:18.9,subtitle:"Crescimento de 18.9% no engajamento"},{title:"Conversas Qualificadas",value:i.qualifiedConversations,type:"number",icon:Om,color:"green",trend:"up",trendValue:22.1,subtitle:"Melhoria de 22.1% na qualificação"}]:[];return w?d.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4",children:d.jsxs(Jl,{className:"max-w-md w-full text-center",children:[d.jsx(Ne.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",duration:.5},className:"w-16 h-16 bg-danger-100 dark:bg-danger-900/20 rounded-full flex items-center justify-center mx-auto mb-4",children:d.jsx(Om,{size:32,className:"text-danger-500"})}),d.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Erro ao carregar dashboard"}),d.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:w}),d.jsx(ra,{onClick:x,leftIcon:d.jsx(hc,{size:16}),className:"w-full",children:"Tentar novamente"})]})}):d.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:d.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[d.jsx(Ne.div,{className:"mb-8",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:d.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[d.jsxs("div",{children:[d.jsx("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-2 gradient-text",children:"Dashboard CRM"}),d.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-lg",children:"Visão geral do desempenho dos seus agentes de IA"})]}),d.jsxs("div",{className:"flex items-center gap-3 mt-4 sm:mt-0",children:[d.jsx(ra,{variant:"secondary",size:"sm",leftIcon:d.jsx(sh,{size:16}),children:"Últimos 30 dias"}),d.jsx(ra,{variant:"ghost",size:"sm",leftIcon:d.jsx(hc,{size:16,className:g?"animate-spin":""}),onClick:x,disabled:g,children:"Atualizar"})]})]})}),d.jsxs(Ne.div,{className:"mb-12",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2,duration:.5},children:[d.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[d.jsx("div",{className:"w-8 h-8 bg-primary-100 dark:bg-primary-900/20 rounded-lg flex items-center justify-center",children:d.jsx(ui,{size:20,className:"text-primary-600 dark:text-primary-400"})}),d.jsx("h2",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Indicadores Principais"})]}),d.jsx(Yv,{kpis:R,loading:g})]}),d.jsxs(Ne.div,{className:"mb-12",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4,duration:.5},children:[d.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[d.jsx("div",{className:"w-8 h-8 bg-success-100 dark:bg-success-900/20 rounded-lg flex items-center justify-center",children:d.jsx(ii,{size:20,className:"text-success-600 dark:text-success-400"})}),d.jsx("h2",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Análise de Performance"})]}),d.jsx(Qv,{chartData:f,loading:g})]}),d.jsxs(Ne.div,{className:"mb-12",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6,duration:.5},children:[d.jsxs("div",{className:"flex items-center justify-between mb-6",children:[d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx("div",{className:"w-8 h-8 bg-warning-100 dark:bg-warning-900/20 rounded-lg flex items-center justify-center",children:d.jsx(sl,{size:20,className:"text-warning-600 dark:text-warning-400"})}),d.jsx("h2",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Agentes de IA"})]}),d.jsx(ra,{variant:"outline",size:"sm",children:"Ver Todos"})]}),d.jsx(kh,{agents:c.slice(0,3),loading:g,onViewDetails:C=>{console.log("Ver detalhes do agente:",C)},onToggleStatus:C=>{console.log("Toggle status do agente:",C)},onSettings:C=>{console.log("Configurações do agente:",C)}})]}),d.jsxs(Ne.div,{className:"grid grid-cols-1 md:grid-cols-3 gap-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8,duration:.5},children:[d.jsxs(Jl,{className:"text-center group hover:shadow-glow transition-all duration-300",children:[d.jsx(Ne.div,{className:"w-14 h-14 bg-primary-100 dark:bg-primary-900/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200",whileHover:{rotate:5},children:d.jsx(Us,{size:28,className:"text-primary-600 dark:text-primary-400"})}),d.jsx("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-1",children:g?"-":$l((i==null?void 0:i.totalConversations)||0)}),d.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Conversas Hoje"})]}),d.jsxs(Jl,{className:"text-center group hover:shadow-glow-success transition-all duration-300",children:[d.jsx(Ne.div,{className:"w-14 h-14 bg-success-100 dark:bg-success-900/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200",whileHover:{rotate:5},children:d.jsx(ii,{size:28,className:"text-success-600 dark:text-success-400"})}),d.jsx("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-1",children:g?"-":si((i==null?void 0:i.conversionRate)||0)}),d.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Taxa de Conversão"})]}),d.jsxs(Jl,{className:"text-center group hover:shadow-glow-warning transition-all duration-300",children:[d.jsx(Ne.div,{className:"w-14 h-14 bg-warning-100 dark:bg-warning-900/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200",whileHover:{rotate:5},children:d.jsx(Mm,{size:28,className:"text-warning-600 dark:text-warning-400"})}),d.jsx("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-1",children:g?"-":Fl((i==null?void 0:i.revenuePerAgent)||0)}),d.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Receita por Agente"})]})]})]})})},Bx=({conversation:i,isOpen:u,onClose:c})=>{const[r,f]=me.useState(!1),[h,g]=me.useState(""),[b,w]=me.useState(""),[j,x]=me.useState("");if(!u||!i)return null;const{id:R,leadName:C,channel:Z,status:U,intent:q,lastMessage:B,lastMessageTime:J,messagesCount:I,phone:F,email:he,messages:ue=[]}=i,fe=Nh(U),je=jh(Z),G=async()=>{if(!(!h.trim()||!F)){f(!0);try{await Rx(F,h),g(""),alert("Mensagem enviada com sucesso!")}catch{alert("Erro ao enviar mensagem")}finally{f(!1)}}},X=async()=>{if(b){f(!0);try{await Cx(R,b,j),w(""),x(""),alert("Call agendada com sucesso!")}catch{alert("Erro ao agendar call")}finally{f(!1)}}},ie=async()=>{f(!0);try{await Ux(R),alert("Marcado como oportunidade!")}catch{alert("Erro ao marcar como oportunidade")}finally{f(!1)}};return d.jsx("div",{className:"modal-overlay",onClick:c,children:d.jsxs("div",{className:"modal-content",onClick:re=>re.stopPropagation(),children:[d.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsxs("div",{className:"relative",children:[d.jsx("div",{className:"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-xl",children:je}),d.jsx("div",{className:"absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white",style:{backgroundColor:fe}})]}),d.jsxs("div",{children:[d.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:C}),d.jsxs("p",{className:"text-sm text-gray-500",children:[Z," • ",lv(q)]})]})]}),d.jsx("button",{onClick:c,className:"p-2 hover:bg-gray-100 rounded-lg",children:d.jsx(uh,{size:20})})]}),d.jsxs("div",{className:"p-6 border-b border-gray-200",children:[d.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Informações do Lead"}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx(Vy,{size:16,className:"text-gray-500"}),d.jsxs("div",{children:[d.jsx("p",{className:"text-sm text-gray-500",children:"Nome"}),d.jsx("p",{className:"font-medium",children:C})]})]}),F&&d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx(Dm,{size:16,className:"text-gray-500"}),d.jsxs("div",{children:[d.jsx("p",{className:"text-sm text-gray-500",children:"Telefone"}),d.jsx("p",{className:"font-medium",children:tv(F)})]})]}),he&&d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx(Ky,{size:16,className:"text-gray-500"}),d.jsxs("div",{children:[d.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),d.jsx("p",{className:"font-medium",children:he})]})]}),d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx(Us,{size:16,className:"text-gray-500"}),d.jsxs("div",{children:[d.jsx("p",{className:"text-sm text-gray-500",children:"Total de Mensagens"}),d.jsx("p",{className:"font-medium",children:I})]})]})]})]}),d.jsxs("div",{className:"p-6 border-b border-gray-200",children:[d.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Histórico de Mensagens"}),d.jsx("div",{className:"max-h-64 overflow-y-auto space-y-3",children:ue.map(re=>d.jsx("div",{className:`flex ${re.sender==="agent"?"justify-end":"justify-start"}`,children:d.jsxs("div",{className:`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${re.sender==="agent"?"bg-blue-500 text-white":"bg-gray-100 text-gray-900"}`,children:[d.jsx("p",{className:"text-sm",children:re.content}),d.jsx("p",{className:`text-xs mt-1 ${re.sender==="agent"?"text-blue-100":"text-gray-500"}`,children:Sh(re.timestamp)})]})},re.id))})]}),d.jsxs("div",{className:"p-6",children:[d.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Ações Rápidas"}),F&&d.jsxs("div",{className:"mb-6",children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enviar WhatsApp"}),d.jsxs("div",{className:"flex gap-2",children:[d.jsx("input",{type:"text",value:h,onChange:re=>g(re.target.value),placeholder:"Digite sua mensagem...",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),d.jsxs("button",{onClick:G,disabled:r||!h.trim(),className:"btn btn-primary",children:[d.jsx(Dm,{size:16}),"Enviar"]})]})]}),d.jsxs("div",{className:"mb-6",children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Agendar Call"}),d.jsxs("div",{className:"space-y-2",children:[d.jsx("input",{type:"datetime-local",value:b,onChange:re=>w(re.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),d.jsx("input",{type:"text",value:j,onChange:re=>x(re.target.value),placeholder:"Observações (opcional)",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),d.jsxs("button",{onClick:X,disabled:r||!b,className:"btn btn-secondary",children:[d.jsx(sh,{size:16}),"Agendar"]})]})]}),d.jsxs("div",{className:"flex gap-3",children:[d.jsxs("button",{onClick:ie,disabled:r,className:"btn btn-primary flex-1",children:[d.jsx(Jy,{size:16}),"Marcar como Oportunidade"]}),F&&d.jsxs("a",{href:`https://wa.me/${F.replace(/\D/g,"")}`,target:"_blank",rel:"noopener noreferrer",className:"btn btn-secondary",children:[d.jsx(Wy,{size:16}),"Abrir WhatsApp"]})]})]})]})})},Hx=()=>{const[i,u]=me.useState([]),[c,r]=me.useState([]),[f,h]=me.useState(!0),[g,b]=me.useState(""),[w,j]=me.useState("all"),[x,R]=me.useState("all"),[C,Z]=me.useState(null),[U,q]=me.useState(!1),[B,J]=me.useState(null);me.useEffect(()=>{I()},[]),me.useEffect(()=>{F()},[i,g,w,x]);const I=async()=>{try{h(!0),J(null);const X=await d0();u(X)}catch(X){console.error("Erro ao carregar agentes:",X),J("Erro ao carregar agentes. Tente novamente.")}finally{h(!1)}},F=()=>{let X=[...i];g&&(X=X.filter(ie=>ie.name.toLowerCase().includes(g.toLowerCase())||ie.channel.toLowerCase().includes(g.toLowerCase()))),w!=="all"&&(X=X.filter(ie=>ie.status===w)),x!=="all"&&(X=X.filter(ie=>ie.channel===x)),r(X)},he=async X=>{try{const ie=await Ox({agentId:X.id});ie.length>0?(Z(ie[0]),q(!0)):alert("Nenhuma conversa encontrada para este agente.")}catch(ie){console.error("Erro ao buscar conversas:",ie),alert("Erro ao carregar conversas do agente.")}},ue=async X=>{try{const ie=X.status==="online"?"offline":"online";await _x(X.id,ie),u(re=>re.map(Ee=>Ee.id===X.id?{...Ee,status:ie}:Ee))}catch(ie){console.error("Erro ao atualizar status:",ie),alert("Erro ao atualizar status do agente.")}},fe=X=>{console.log("Configurações do agente:",X),alert(`Configurações do ${X.name} - Em desenvolvimento`)},je=()=>[...new Set(i.map(ie=>ie.channel))],G=()=>{const X=[["Nome","Canal","Status","Leads Atendidos","Conversas Ativas","Taxa de Resposta"],...c.map(We=>[We.name,We.channel,We.status,We.leadsAttended,We.activeConversations,`${We.responseRate}%`])].map(We=>We.join(",")).join(`
`),ie=new Blob([X],{type:"text/csv"}),re=window.URL.createObjectURL(ie),Ee=document.createElement("a");Ee.href=re,Ee.download="agentes-crm.csv",Ee.click(),window.URL.revokeObjectURL(re)};return B?d.jsx("div",{className:"container py-8",children:d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:d.jsx(sl,{size:32,className:"text-red-500"})}),d.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Erro ao carregar agentes"}),d.jsx("p",{className:"text-gray-600 mb-4",children:B}),d.jsx("button",{onClick:I,className:"btn btn-primary",children:"Tentar novamente"})]})}):d.jsxs("div",{className:"container py-8",children:[d.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-8",children:[d.jsxs("div",{children:[d.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Agentes de IA"}),d.jsx("p",{className:"text-gray-600",children:"Gerencie e monitore seus agentes de atendimento automatizado"})]}),d.jsxs("div",{className:"flex gap-3 mt-4 md:mt-0",children:[d.jsxs("button",{onClick:I,disabled:f,className:"btn btn-secondary",children:[d.jsx(hc,{size:16,className:f?"animate-spin":""}),"Atualizar"]}),d.jsxs("button",{onClick:G,className:"btn btn-secondary",children:[d.jsx(Fy,{size:16}),"Exportar"]}),d.jsxs("button",{className:"btn btn-primary",children:[d.jsx($y,{size:16}),"Novo Agente"]})]})]}),d.jsx("div",{className:"card mb-8",children:d.jsx("div",{className:"card-body",children:d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[d.jsxs("div",{className:"relative",children:[d.jsx(Py,{size:16,className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),d.jsx("input",{type:"text",placeholder:"Buscar agentes...",value:g,onChange:X=>b(X.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),d.jsxs("select",{value:w,onChange:X=>j(X.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[d.jsx("option",{value:"all",children:"Todos os Status"}),d.jsx("option",{value:"online",children:"Online"}),d.jsx("option",{value:"offline",children:"Offline"})]}),d.jsxs("select",{value:x,onChange:X=>R(X.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[d.jsx("option",{value:"all",children:"Todos os Canais"}),je().map(X=>d.jsx("option",{value:X,children:X},X))]}),d.jsxs("button",{onClick:()=>{b(""),j("all"),R("all")},className:"btn btn-secondary",children:[d.jsx(Iy,{size:16}),"Limpar Filtros"]})]})})}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8",children:[d.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[d.jsx("p",{className:"text-sm text-blue-600 mb-1",children:"Total de Agentes"}),d.jsx("p",{className:"text-2xl font-bold text-blue-900",children:i.length})]}),d.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[d.jsx("p",{className:"text-sm text-green-600 mb-1",children:"Online"}),d.jsx("p",{className:"text-2xl font-bold text-green-900",children:i.filter(X=>X.status==="online").length})]}),d.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[d.jsx("p",{className:"text-sm text-yellow-600 mb-1",children:"Conversas Ativas"}),d.jsx("p",{className:"text-2xl font-bold text-yellow-900",children:i.reduce((X,ie)=>X+ie.activeConversations,0)})]}),d.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[d.jsx("p",{className:"text-sm text-purple-600 mb-1",children:"Taxa Média"}),d.jsx("p",{className:"text-2xl font-bold text-purple-900",children:i.length>0?`${(i.reduce((X,ie)=>X+ie.responseRate,0)/i.length).toFixed(1)}%`:"0%"})]})]}),d.jsx(kh,{agents:c,loading:f,onViewDetails:he,onToggleStatus:ue,onSettings:fe}),d.jsx(Bx,{conversation:C,isOpen:U,onClose:()=>{q(!1),Z(null)}})]})},kx=()=>{const i=dh(),u=[{path:"/",icon:rh,label:"Dashboard",exact:!0},{path:"/agents",icon:sl,label:"Agentes"},{path:"/conversations",icon:ch,label:"Conversas"},{path:"/analytics",icon:ui,label:"Analytics"},{path:"/settings",icon:Ec,label:"Config"}],c=(r,f=!1)=>f?i.pathname===r:i.pathname.startsWith(r);return d.jsx("nav",{className:"mobile-nav",children:d.jsx("div",{className:"flex justify-around items-center",children:u.map(({path:r,icon:f,label:h,exact:g})=>d.jsxs(mh,{to:r,className:`mobile-nav-item ${c(r,g)?"active":""}`,children:[d.jsx(f,{size:20}),d.jsx("span",{children:h})]},r))})})},m0=me.createContext(),h0=()=>{const i=me.useContext(m0);if(!i)throw new Error("useTheme must be used within a ThemeProvider");return i},Lx=({children:i})=>{const[u,c]=me.useState(()=>{const b=localStorage.getItem("theme");return b||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")});me.useEffect(()=>{const b=window.document.documentElement;b.classList.remove("light","dark"),b.classList.add(u),localStorage.setItem("theme",u)},[u]);const g={theme:u,toggleTheme:()=>{c(b=>b==="light"?"dark":"light")},setLightTheme:()=>c("light"),setDarkTheme:()=>c("dark"),isDark:u==="dark",isLight:u==="light"};return d.jsx(m0.Provider,{value:g,children:i})},Yx=()=>{const[i,u]=me.useState({width:typeof window<"u"?window.innerWidth:0,height:typeof window<"u"?window.innerHeight:0}),[c,r]=me.useState("lg");return me.useEffect(()=>{const R=()=>{const C=window.innerWidth,Z=window.innerHeight;u({width:C,height:Z}),C<640?r("xs"):C<768?r("sm"):C<1024?r("md"):C<1280?r("lg"):r("xl")};return R(),window.addEventListener("resize",R),()=>window.removeEventListener("resize",R)},[]),{windowSize:i,breakpoint:c,isMobile:c==="xs"||c==="sm",isTablet:c==="md",isDesktop:c==="lg"||c==="xl",isSmallScreen:c==="xs",isLargeScreen:c==="xl",isAbove:R=>{const C={xs:0,sm:640,md:768,lg:1024,xl:1280};return i.width>=C[R]},isBelow:R=>{const C={xs:640,sm:768,md:1024,lg:1280,xl:1/0};return i.width<C[R]}}},Gx=({isOpen:i,onClose:u})=>{const c=dh(),{theme:r,toggleTheme:f}=h0(),h=[{path:"/",icon:rh,label:"Dashboard",exact:!0},{path:"/agents",icon:sl,label:"Agentes de IA"},{path:"/conversations",icon:ch,label:"Conversas"},{path:"/analytics",icon:ui,label:"Analytics"},{path:"/settings",icon:Ec,label:"Configurações"}],g=(b,w=!1)=>w?c.pathname===b:c.pathname.startsWith(b);return d.jsx(gc,{children:(i||window.innerWidth>=768)&&d.jsxs(Ne.div,{className:pt("fixed left-0 top-0 bottom-0 w-72 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 z-50","flex flex-col p-6 glass-effect","md:relative md:translate-x-0",!i&&"md:block hidden"),initial:{x:-288},animate:{x:0},exit:{x:-288},transition:{type:"spring",damping:25,stiffness:200},children:[d.jsxs("div",{className:"flex items-center justify-between mb-8",children:[d.jsxs(Ne.div,{className:"flex items-center gap-3",whileHover:{scale:1.05},transition:{type:"spring",stiffness:300},children:[d.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center shadow-lg",children:d.jsx(ui,{size:24,className:"text-white"})}),d.jsx("h1",{className:"text-xl font-bold text-gray-900 dark:text-white gradient-text",children:"CRM AI"})]}),d.jsx(ra,{variant:"ghost",size:"sm",onClick:u,className:"md:hidden",children:d.jsx(uh,{size:20})})]}),d.jsx("nav",{className:"flex-1 space-y-2",children:h.map(({path:b,icon:w,label:j,exact:x},R)=>d.jsx(Ne.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:R*.1},children:d.jsxs(mh,{to:b,onClick:u,className:pt("flex items-center gap-3 px-4 py-3 rounded-xl font-medium transition-all duration-200","hover:bg-gray-100 dark:hover:bg-gray-800",g(b,x)?"bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 shadow-sm":"text-gray-700 dark:text-gray-300"),children:[d.jsx(w,{size:20}),d.jsx("span",{children:j}),g(b,x)&&d.jsx(Ne.div,{className:"ml-auto w-2 h-2 bg-primary-500 rounded-full",layoutId:"activeIndicator",transition:{type:"spring",stiffness:300,damping:30}})]})},b))}),d.jsx("div",{className:"mb-6",children:d.jsx(ra,{variant:"ghost",onClick:f,className:"w-full justify-start",leftIcon:r==="dark"?d.jsx(oh,{size:20}):d.jsx(fh,{size:20}),children:r==="dark"?"Modo Claro":"Modo Escuro"})}),d.jsx("div",{className:"pt-6 border-t border-gray-200 dark:border-gray-700",children:d.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 text-center space-y-1",children:[d.jsx("p",{className:"font-medium",children:"CRM AI v2.0"}),d.jsx("p",{children:"Powered by n8n"})]})})]})})},Xx=({onMenuClick:i})=>{const{theme:u,toggleTheme:c}=h0();return d.jsxs(Ne.header,{className:"md:hidden bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex items-center justify-between glass-effect",initial:{y:-60},animate:{y:0},transition:{type:"spring",damping:20,stiffness:300},children:[d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center shadow-md",children:d.jsx(ui,{size:20,className:"text-white"})}),d.jsx("h1",{className:"text-xl font-bold text-gray-900 dark:text-white gradient-text",children:"CRM AI"})]}),d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx(ra,{variant:"ghost",size:"sm",onClick:c,children:u==="dark"?d.jsx(oh,{size:18}):d.jsx(fh,{size:18})}),d.jsx(ra,{variant:"ghost",size:"sm",onClick:i,children:d.jsx(eb,{size:20})})]})]})},Zx=()=>{const[i,u]=ab.useState(!1),{isMobile:c}=Yx(),r=()=>u(!1),f=()=>u(!0);return d.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300",children:[d.jsx(Gx,{isOpen:i,onClose:r}),d.jsx(gc,{children:c&&i&&d.jsx(Ne.div,{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-40",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:r})}),d.jsxs("div",{className:pt("transition-all duration-300","md:ml-72"),children:[d.jsx(Xx,{onMenuClick:f}),d.jsx("main",{className:"min-h-screen",children:d.jsx(gc,{mode:"wait",children:d.jsxs(lb,{children:[d.jsx(ei,{path:"/",element:d.jsx(qx,{})}),d.jsx(ei,{path:"/agents",element:d.jsx(Hx,{})}),d.jsx(ei,{path:"/conversations",element:d.jsxs(Ne.div,{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:[d.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Conversas"}),d.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Em desenvolvimento..."})]})}),d.jsx(ei,{path:"/analytics",element:d.jsxs(Ne.div,{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:[d.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Analytics"}),d.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Em desenvolvimento..."})]})}),d.jsx(ei,{path:"/settings",element:d.jsxs(Ne.div,{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:[d.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Configurações"}),d.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Em desenvolvimento..."})]})})]})})})]}),d.jsx(kx,{})]})};function Qx(){return d.jsx(Lx,{children:d.jsx(tb,{children:d.jsx(Zx,{})})})}gb.createRoot(document.getElementById("root")).render(d.jsx(me.StrictMode,{children:d.jsx(Qx,{})}));

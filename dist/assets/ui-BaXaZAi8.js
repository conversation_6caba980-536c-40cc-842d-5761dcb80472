import{r as v}from"./router-Dh5s4hGl.js";var ne={exports:{}},vt={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xn;function dr(){if(xn)return vt;xn=1;var t=Symbol.for("react.transitional.element"),e=Symbol.for("react.fragment");function n(s,i,o){var r=null;if(o!==void 0&&(r=""+o),i.key!==void 0&&(r=""+i.key),"key"in i){o={};for(var a in i)a!=="key"&&(o[a]=i[a])}else o=i;return i=o.ref,{$$typeof:t,type:s,key:r,ref:i!==void 0?i:null,props:o}}return vt.Fragment=e,vt.jsx=n,vt.jsxs=n,vt}var Tn;function pr(){return Tn||(Tn=1,ne.exports=dr()),ne.exports}var q=pr();const Be=v.createContext({});function je(t){const e=v.useRef(null);return e.current===null&&(e.current=t()),e.current}const Ie=typeof window<"u",Bs=Ie?v.useLayoutEffect:v.useEffect,Yt=v.createContext(null);function Oe(t,e){t.indexOf(e)===-1&&t.push(e)}function Ne(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const X=(t,e,n)=>n>e?e:n<t?t:n;let _e=()=>{};const Y={},js=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function Is(t){return typeof t=="object"&&t!==null}const Os=t=>/^0[^.\s]+$/u.test(t);function $e(t){let e;return()=>(e===void 0&&(e=t()),e)}const U=t=>t,mr=(t,e)=>n=>e(t(n)),Lt=(...t)=>t.reduce(mr),Mt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s};class Ue{constructor(){this.subscriptions=[]}add(e){return Oe(this.subscriptions,e),()=>Ne(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let o=0;o<i;o++){const r=this.subscriptions[o];r&&r(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const K=t=>t*1e3,z=t=>t/1e3;function Ns(t,e){return e?t*(1e3/e):0}const _s=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,yr=1e-7,gr=12;function vr(t,e,n,s,i){let o,r,a=0;do r=e+(n-e)/2,o=_s(r,s,i)-t,o>0?n=r:e=r;while(Math.abs(o)>yr&&++a<gr);return r}function Ft(t,e,n,s){if(t===e&&n===s)return U;const i=o=>vr(o,0,1,t,n);return o=>o===0||o===1?o:_s(i(o),e,s)}const $s=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Us=t=>e=>1-t(1-e),Ws=Ft(.33,1.53,.69,.99),We=Us(Ws),Ks=$s(We),zs=t=>(t*=2)<1?.5*We(t):.5*(2-Math.pow(2,-10*(t-1))),Ke=t=>1-Math.sin(Math.acos(t)),Hs=Us(Ke),Gs=$s(Ke),xr=Ft(.42,0,1,1),Tr=Ft(0,0,.58,1),qs=Ft(.42,0,.58,1),Sr=t=>Array.isArray(t)&&typeof t[0]!="number",Xs=t=>Array.isArray(t)&&typeof t[0]=="number",Pr={linear:U,easeIn:xr,easeInOut:qs,easeOut:Tr,circIn:Ke,circInOut:Gs,circOut:Hs,backIn:We,backInOut:Ks,backOut:Ws,anticipate:zs},wr=t=>typeof t=="string",Sn=t=>{if(Xs(t)){_e(t.length===4);const[e,n,s,i]=t;return Ft(e,n,s,i)}else if(wr(t))return Pr[t];return t},It=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Pn={value:null};function br(t,e){let n=new Set,s=new Set,i=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},c=0;function u(h){r.has(h)&&(l.schedule(h),t()),c++,h(a)}const l={schedule:(h,f=!1,d=!1)=>{const y=d&&i?n:s;return f&&r.add(h),y.has(h)||y.add(h),h},cancel:h=>{s.delete(h),r.delete(h)},process:h=>{if(a=h,i){o=!0;return}i=!0,[n,s]=[s,n],n.forEach(u),e&&Pn.value&&Pn.value.frameloop[e].push(c),c=0,n.clear(),i=!1,o&&(o=!1,l.process(h))}};return l}const Ar=40;function Ys(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=It.reduce((g,M)=>(g[M]=br(o,e?M:void 0),g),{}),{setup:a,read:c,resolveKeyframes:u,preUpdate:l,update:h,preRender:f,render:d,postRender:p}=r,y=()=>{const g=Y.useManualTiming?i.timestamp:performance.now();n=!1,Y.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(g-i.timestamp,Ar),1)),i.timestamp=g,i.isProcessing=!0,a.process(i),c.process(i),u.process(i),l.process(i),h.process(i),f.process(i),d.process(i),p.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(y))},x=()=>{n=!0,s=!0,i.isProcessing||t(y)};return{schedule:It.reduce((g,M)=>{const T=r[M];return g[M]=(w,V=!1,b=!1)=>(n||x(),T.schedule(w,V,b)),g},{}),cancel:g=>{for(let M=0;M<It.length;M++)r[It[M]].cancel(g)},state:i,steps:r}}const{schedule:k,cancel:J,state:L,steps:se}=Ys(typeof requestAnimationFrame<"u"?requestAnimationFrame:U,!0);let $t;function Mr(){$t=void 0}const N={now:()=>($t===void 0&&N.set(L.isProcessing||Y.useManualTiming?L.timestamp:performance.now()),$t),set:t=>{$t=t,queueMicrotask(Mr)}},Zs=t=>e=>typeof e=="string"&&e.startsWith(t),ze=Zs("--"),Vr=Zs("var(--"),He=t=>Vr(t)?Cr.test(t.split("/*")[0].trim()):!1,Cr=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,mt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Vt={...mt,transform:t=>X(0,1,t)},Ot={...mt,default:1},St=t=>Math.round(t*1e5)/1e5,Ge=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function kr(t){return t==null}const Dr=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,qe=(t,e)=>n=>!!(typeof n=="string"&&Dr.test(n)&&n.startsWith(t)||e&&!kr(n)&&Object.prototype.hasOwnProperty.call(n,e)),Js=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,o,r,a]=s.match(Ge);return{[t]:parseFloat(i),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},Rr=t=>X(0,255,t),ie={...mt,transform:t=>Math.round(Rr(t))},st={test:qe("rgb","red"),parse:Js("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+ie.transform(t)+", "+ie.transform(e)+", "+ie.transform(n)+", "+St(Vt.transform(s))+")"};function Er(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const me={test:qe("#"),parse:Er,transform:st.transform},Bt=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Z=Bt("deg"),H=Bt("%"),S=Bt("px"),Lr=Bt("vh"),Fr=Bt("vw"),wn={...H,parse:t=>H.parse(t)/100,transform:t=>H.transform(t*100)},ct={test:qe("hsl","hue"),parse:Js("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+H.transform(St(e))+", "+H.transform(St(n))+", "+St(Vt.transform(s))+")"},F={test:t=>st.test(t)||me.test(t)||ct.test(t),parse:t=>st.test(t)?st.parse(t):ct.test(t)?ct.parse(t):me.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?st.transform(t):ct.transform(t)},Br=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function jr(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(Ge))==null?void 0:e.length)||0)+(((n=t.match(Br))==null?void 0:n.length)||0)>0}const Qs="number",ti="color",Ir="var",Or="var(",bn="${}",Nr=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ct(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let o=0;const a=e.replace(Nr,c=>(F.test(c)?(s.color.push(o),i.push(ti),n.push(F.parse(c))):c.startsWith(Or)?(s.var.push(o),i.push(Ir),n.push(c)):(s.number.push(o),i.push(Qs),n.push(parseFloat(c))),++o,bn)).split(bn);return{values:n,split:a,indexes:s,types:i}}function ei(t){return Ct(t).values}function ni(t){const{split:e,types:n}=Ct(t),s=e.length;return i=>{let o="";for(let r=0;r<s;r++)if(o+=e[r],i[r]!==void 0){const a=n[r];a===Qs?o+=St(i[r]):a===ti?o+=F.transform(i[r]):o+=i[r]}return o}}const _r=t=>typeof t=="number"?0:t;function $r(t){const e=ei(t);return ni(t)(e.map(_r))}const Q={test:jr,parse:ei,createTransformer:ni,getAnimatableNone:$r};function re(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Ur({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,o=0,r=0;if(!e)i=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,c=2*n-a;i=re(c,a,t+1/3),o=re(c,a,t),r=re(c,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:s}}function zt(t,e){return n=>n>0?e:t}const C=(t,e,n)=>t+(e-t)*n,oe=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},Wr=[me,st,ct],Kr=t=>Wr.find(e=>e.test(t));function An(t){const e=Kr(t);if(!e)return!1;let n=e.parse(t);return e===ct&&(n=Ur(n)),n}const Mn=(t,e)=>{const n=An(t),s=An(e);if(!n||!s)return zt(t,e);const i={...n};return o=>(i.red=oe(n.red,s.red,o),i.green=oe(n.green,s.green,o),i.blue=oe(n.blue,s.blue,o),i.alpha=C(n.alpha,s.alpha,o),st.transform(i))},ye=new Set(["none","hidden"]);function zr(t,e){return ye.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function Hr(t,e){return n=>C(t,e,n)}function Xe(t){return typeof t=="number"?Hr:typeof t=="string"?He(t)?zt:F.test(t)?Mn:Xr:Array.isArray(t)?si:typeof t=="object"?F.test(t)?Mn:Gr:zt}function si(t,e){const n=[...t],s=n.length,i=t.map((o,r)=>Xe(o)(o,e[r]));return o=>{for(let r=0;r<s;r++)n[r]=i[r](o);return n}}function Gr(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Xe(t[i])(t[i],e[i]));return i=>{for(const o in s)n[o]=s[o](i);return n}}function qr(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const o=e.types[i],r=t.indexes[o][s[o]],a=t.values[r]??0;n[i]=a,s[o]++}return n}const Xr=(t,e)=>{const n=Q.createTransformer(e),s=Ct(t),i=Ct(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?ye.has(t)&&!i.values.length||ye.has(e)&&!s.values.length?zr(t,e):Lt(si(qr(s,i),i.values),n):zt(t,e)};function ii(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?C(t,e,n):Xe(t)(t,e)}const Yr=t=>{const e=({timestamp:n})=>t(n);return{start:(n=!0)=>k.update(e,n),stop:()=>J(e),now:()=>L.isProcessing?L.timestamp:N.now()}},ri=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let o=0;o<i;o++)s+=t(o/(i-1))+", ";return`linear(${s.substring(0,s.length-2)})`},Ht=2e4;function Ye(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<Ht;)e+=n,s=t.next(e);return e>=Ht?1/0:e}function Zr(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(Ye(s),Ht);return{type:"keyframes",ease:o=>s.next(i*o).value/e,duration:z(i)}}const Jr=5;function oi(t,e,n){const s=Math.max(e-Jr,0);return Ns(n-t(s),e-s)}const D={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Vn=.001;function Qr({duration:t=D.duration,bounce:e=D.bounce,velocity:n=D.velocity,mass:s=D.mass}){let i,o,r=1-e;r=X(D.minDamping,D.maxDamping,r),t=X(D.minDuration,D.maxDuration,z(t)),r<1?(i=u=>{const l=u*r,h=l*t,f=l-n,d=ge(u,r),p=Math.exp(-h);return Vn-f/d*p},o=u=>{const h=u*r*t,f=h*n+n,d=Math.pow(r,2)*Math.pow(u,2)*t,p=Math.exp(-h),y=ge(Math.pow(u,2),r);return(-i(u)+Vn>0?-1:1)*((f-d)*p)/y}):(i=u=>{const l=Math.exp(-u*t),h=(u-n)*t+1;return-.001+l*h},o=u=>{const l=Math.exp(-u*t),h=(n-u)*(t*t);return l*h});const a=5/t,c=eo(i,o,a);if(t=K(t),isNaN(c))return{stiffness:D.stiffness,damping:D.damping,duration:t};{const u=Math.pow(c,2)*s;return{stiffness:u,damping:r*2*Math.sqrt(s*u),duration:t}}}const to=12;function eo(t,e,n){let s=n;for(let i=1;i<to;i++)s=s-t(s)/e(s);return s}function ge(t,e){return t*Math.sqrt(1-e*e)}const no=["duration","bounce"],so=["stiffness","damping","mass"];function Cn(t,e){return e.some(n=>t[n]!==void 0)}function io(t){let e={velocity:D.velocity,stiffness:D.stiffness,damping:D.damping,mass:D.mass,isResolvedFromDuration:!1,...t};if(!Cn(t,so)&&Cn(t,no))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,o=2*X(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:D.mass,stiffness:i,damping:o}}else{const n=Qr(t);e={...e,...n,mass:D.mass},e.isResolvedFromDuration=!0}return e}function Gt(t=D.visualDuration,e=D.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:c,damping:u,mass:l,duration:h,velocity:f,isResolvedFromDuration:d}=io({...n,velocity:-z(n.velocity||0)}),p=f||0,y=u/(2*Math.sqrt(c*l)),x=r-o,m=z(Math.sqrt(c/l)),P=Math.abs(x)<5;s||(s=P?D.restSpeed.granular:D.restSpeed.default),i||(i=P?D.restDelta.granular:D.restDelta.default);let g;if(y<1){const T=ge(m,y);g=w=>{const V=Math.exp(-y*m*w);return r-V*((p+y*m*x)/T*Math.sin(T*w)+x*Math.cos(T*w))}}else if(y===1)g=T=>r-Math.exp(-m*T)*(x+(p+m*x)*T);else{const T=m*Math.sqrt(y*y-1);g=w=>{const V=Math.exp(-y*m*w),b=Math.min(T*w,300);return r-V*((p+y*m*x)*Math.sinh(b)+T*x*Math.cosh(b))/T}}const M={calculatedDuration:d&&h||null,next:T=>{const w=g(T);if(d)a.done=T>=h;else{let V=T===0?p:0;y<1&&(V=T===0?K(p):oi(g,T,w));const b=Math.abs(V)<=s,E=Math.abs(r-w)<=i;a.done=b&&E}return a.value=a.done?r:w,a},toString:()=>{const T=Math.min(Ye(M),Ht),w=ri(V=>M.next(T*V).value,T,30);return T+"ms "+w},toTransition:()=>{}};return M}Gt.applyToOptions=t=>{const e=Zr(t,100,Gt);return t.ease=e.ease,t.duration=K(e.duration),t.type="keyframes",t};function ve({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:c,restDelta:u=.5,restSpeed:l}){const h=t[0],f={done:!1,value:h},d=b=>a!==void 0&&b<a||c!==void 0&&b>c,p=b=>a===void 0?c:c===void 0||Math.abs(a-b)<Math.abs(c-b)?a:c;let y=n*e;const x=h+y,m=r===void 0?x:r(x);m!==x&&(y=m-h);const P=b=>-y*Math.exp(-b/s),g=b=>m+P(b),M=b=>{const E=P(b),O=g(b);f.done=Math.abs(E)<=u,f.value=f.done?m:O};let T,w;const V=b=>{d(f.value)&&(T=b,w=Gt({keyframes:[f.value,p(f.value)],velocity:oi(g,b,f.value),damping:i,stiffness:o,restDelta:u,restSpeed:l}))};return V(0),{calculatedDuration:null,next:b=>{let E=!1;return!w&&T===void 0&&(E=!0,M(b),V(b)),T!==void 0&&b>=T?w.next(b-T):(!E&&M(b),f)}}}function ro(t,e,n){const s=[],i=n||Y.mix||ii,o=t.length-1;for(let r=0;r<o;r++){let a=i(t[r],t[r+1]);if(e){const c=Array.isArray(e)?e[r]||U:e;a=Lt(c,a)}s.push(a)}return s}function oo(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const o=t.length;if(_e(o===e.length),o===1)return()=>e[0];if(o===2&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=ro(e,s,i),c=a.length,u=l=>{if(r&&l<t[0])return e[0];let h=0;if(c>1)for(;h<t.length-2&&!(l<t[h+1]);h++);const f=Mt(t[h],t[h+1],l);return a[h](f)};return n?l=>u(X(t[0],t[o-1],l)):u}function ao(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=Mt(0,e,s);t.push(C(n,1,i))}}function co(t){const e=[0];return ao(e,t.length-1),e}function lo(t,e){return t.map(n=>n*e)}function uo(t,e){return t.map(()=>e||qs).splice(0,t.length-1)}function Pt({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=Sr(s)?s.map(Sn):Sn(s),o={done:!1,value:e[0]},r=lo(n&&n.length===e.length?n:co(e),t),a=oo(r,e,{ease:Array.isArray(i)?i:uo(e,i)});return{calculatedDuration:t,next:c=>(o.value=a(c),o.done=c>=t,o)}}const ho=t=>t!==null;function Ze(t,{repeat:e,repeatType:n="loop"},s,i=1){const o=t.filter(ho),a=i<0||e&&n!=="loop"&&e%2===1?0:o.length-1;return!a||s===void 0?o[a]:s}const fo={decay:ve,inertia:ve,tween:Pt,keyframes:Pt,spring:Gt};function ai(t){typeof t.type=="string"&&(t.type=fo[t.type])}class Je{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const po=t=>t/100;class Qe extends Je{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var s,i;const{motionValue:n}=this.options;n&&n.updatedAt!==N.now()&&this.tick(N.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(i=(s=this.options).onStop)==null||i.call(s))},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;ai(e);const{type:n=Pt,repeat:s=0,repeatDelay:i=0,repeatType:o,velocity:r=0}=e;let{keyframes:a}=e;const c=n||Pt;c!==Pt&&typeof a[0]!="number"&&(this.mixKeyframes=Lt(po,ii(a[0],a[1])),a=[0,100]);const u=c({...e,keyframes:a});o==="mirror"&&(this.mirroredGenerator=c({...e,keyframes:[...a].reverse(),velocity:-r})),u.calculatedDuration===null&&(u.calculatedDuration=Ye(u));const{calculatedDuration:l}=u;this.calculatedDuration=l,this.resolvedDuration=l+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=u}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:s,totalDuration:i,mixKeyframes:o,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:c}=this;if(this.startTime===null)return s.next(0);const{delay:u=0,keyframes:l,repeat:h,repeatType:f,repeatDelay:d,type:p,onUpdate:y,finalKeyframe:x}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const m=this.currentTime-u*(this.playbackSpeed>=0?1:-1),P=this.playbackSpeed>=0?m<0:m>i;this.currentTime=Math.max(m,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=i);let g=this.currentTime,M=s;if(h){const b=Math.min(this.currentTime,i)/a;let E=Math.floor(b),O=b%1;!O&&b>=1&&(O=1),O===1&&E--,E=Math.min(E,h+1),!!(E%2)&&(f==="reverse"?(O=1-O,d&&(O-=d/a)):f==="mirror"&&(M=r)),g=X(0,1,O)*a}const T=P?{done:!1,value:l[0]}:M.next(g);o&&(T.value=o(T.value));let{done:w}=T;!P&&c!==null&&(w=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const V=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&w);return V&&p!==ve&&(T.value=Ze(l,this.options,x,this.speed)),y&&y(T.value),V&&this.finish(),T}then(e,n){return this.finished.then(e,n)}get duration(){return z(this.calculatedDuration)}get time(){return z(this.currentTime)}set time(e){var n;e=K(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),(n=this.driver)==null||n.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(N.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=z(this.currentTime))}play(){var i,o;if(this.isStopped)return;const{driver:e=Yr,startTime:n}=this.options;this.driver||(this.driver=e(r=>this.tick(r))),(o=(i=this.options).onPlay)==null||o.call(i);const s=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=s):this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(N.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,n;this.notifyFinished(),this.teardown(),this.state="finished",(n=(e=this.options).onComplete)==null||n.call(e)}cancel(){var e,n;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(n=(e=this.options).onCancel)==null||n.call(e)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var n;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(n=this.driver)==null||n.stop(),e.observe(this)}}function mo(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const it=t=>t*180/Math.PI,xe=t=>{const e=it(Math.atan2(t[1],t[0]));return Te(e)},yo={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:xe,rotateZ:xe,skewX:t=>it(Math.atan(t[1])),skewY:t=>it(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Te=t=>(t=t%360,t<0&&(t+=360),t),kn=xe,Dn=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Rn=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),go={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Dn,scaleY:Rn,scale:t=>(Dn(t)+Rn(t))/2,rotateX:t=>Te(it(Math.atan2(t[6],t[5]))),rotateY:t=>Te(it(Math.atan2(-t[2],t[0]))),rotateZ:kn,rotate:kn,skewX:t=>it(Math.atan(t[4])),skewY:t=>it(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Se(t){return t.includes("scale")?1:0}function Pe(t,e){if(!t||t==="none")return Se(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=go,i=n;else{const a=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=yo,i=a}if(!i)return Se(e);const o=s[e],r=i[1].split(",").map(xo);return typeof o=="function"?o(r):r[o]}const vo=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Pe(n,e)};function xo(t){return parseFloat(t.trim())}const yt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],gt=new Set(yt),En=t=>t===mt||t===S,To=new Set(["x","y","z"]),So=yt.filter(t=>!To.has(t));function Po(t){const e=[];return So.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const rt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Pe(e,"x"),y:(t,{transform:e})=>Pe(e,"y")};rt.translateX=rt.x;rt.translateY=rt.y;const ot=new Set;let we=!1,be=!1,Ae=!1;function ci(){if(be){const t=Array.from(ot).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=Po(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([o,r])=>{var a;(a=s.getValue(o))==null||a.set(r)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}be=!1,we=!1,ot.forEach(t=>t.complete(Ae)),ot.clear()}function li(){ot.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(be=!0)})}function wo(){Ae=!0,li(),ci(),Ae=!1}class tn{constructor(e,n,s,i,o,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=o,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(ot.add(this),we||(we=!0,k.read(li),k.resolveKeyframes(ci))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;if(e[0]===null){const o=i==null?void 0:i.get(),r=e[e.length-1];if(o!==void 0)e[0]=o;else if(s&&n){const a=s.readValue(n,r);a!=null&&(e[0]=a)}e[0]===void 0&&(e[0]=r),i&&o===void 0&&i.set(e[0])}mo(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),ot.delete(this)}cancel(){this.state==="scheduled"&&(ot.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const bo=t=>t.startsWith("--");function Ao(t,e,n){bo(e)?t.style.setProperty(e,n):t.style[e]=n}const Mo=$e(()=>window.ScrollTimeline!==void 0),Vo={};function Co(t,e){const n=$e(t);return()=>Vo[e]??n()}const ui=Co(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Tt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Ln={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Tt([0,.65,.55,1]),circOut:Tt([.55,0,1,.45]),backIn:Tt([.31,.01,.66,-.59]),backOut:Tt([.33,1.53,.69,.99])};function hi(t,e){if(t)return typeof t=="function"?ui()?ri(t,e):"ease-out":Xs(t)?Tt(t):Array.isArray(t)?t.map(n=>hi(n,e)||Ln.easeOut):Ln[t]}function ko(t,e,n,{delay:s=0,duration:i=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:c}={},u=void 0){const l={[e]:n};c&&(l.offset=c);const h=hi(a,i);Array.isArray(h)&&(l.easing=h);const f={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"};return u&&(f.pseudoElement=u),t.animate(l,f)}function fi(t){return typeof t=="function"&&"applyToOptions"in t}function Do({type:t,...e}){return fi(t)&&ui()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class Ro extends Je{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:s,keyframes:i,pseudoElement:o,allowFlatten:r=!1,finalKeyframe:a,onComplete:c}=e;this.isPseudoElement=!!o,this.allowFlatten=r,this.options=e,_e(typeof e.type!="string");const u=Do(e);this.animation=ko(n,s,i,u,o),u.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!o){const l=Ze(i,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(l):Ao(n,s,l),this.animation.cancel()}c==null||c(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,n;(n=(e=this.animation).finish)==null||n.call(e)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var e,n;this.isPseudoElement||(n=(e=this.animation).commitStyles)==null||n.call(e)}get duration(){var n,s;const e=((s=(n=this.animation.effect)==null?void 0:n.getComputedTiming)==null?void 0:s.call(n).duration)||0;return z(Number(e))}get time(){return z(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=K(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){var s;return this.allowFlatten&&((s=this.animation.effect)==null||s.updateTiming({easing:"linear"})),this.animation.onfinish=null,e&&Mo()?(this.animation.timeline=e,U):n(this)}}const di={anticipate:zs,backInOut:Ks,circInOut:Gs};function Eo(t){return t in di}function Lo(t){typeof t.ease=="string"&&Eo(t.ease)&&(t.ease=di[t.ease])}const Fn=10;class Fo extends Ro{constructor(e){Lo(e),ai(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:s,onComplete:i,element:o,...r}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const a=new Qe({...r,autoplay:!1}),c=K(this.finishedTime??this.time);n.setWithVelocity(a.sample(c-Fn).value,a.sample(c).value,Fn),a.stop()}}const Bn=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Q.test(t)||t==="0")&&!t.startsWith("url("));function Bo(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function jo(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],r=Bn(i,e),a=Bn(o,e);return!r||!a?!1:Bo(t)||(n==="spring"||fi(n))&&s}function en(t){return Is(t)&&"offsetHeight"in t}const Io=new Set(["opacity","clipPath","filter","transform"]),Oo=$e(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function No(t){var u;const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:o,type:r}=t;if(!en((u=e==null?void 0:e.owner)==null?void 0:u.current))return!1;const{onUpdate:a,transformTemplate:c}=e.owner.getProps();return Oo()&&n&&Io.has(n)&&(n!=="transform"||!c)&&!a&&!s&&i!=="mirror"&&o!==0&&r!=="inertia"}const _o=40;class $o extends Je{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:r="loop",keyframes:a,name:c,motionValue:u,element:l,...h}){var p;super(),this.stop=()=>{var y,x;this._animation&&(this._animation.stop(),(y=this.stopTimeline)==null||y.call(this)),(x=this.keyframeResolver)==null||x.cancel()},this.createdAt=N.now();const f={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:o,repeatType:r,name:c,motionValue:u,element:l,...h},d=(l==null?void 0:l.KeyframeResolver)||tn;this.keyframeResolver=new d(a,(y,x,m)=>this.onKeyframesResolved(y,x,f,!m),c,u,l),(p=this.keyframeResolver)==null||p.scheduleResolve()}onKeyframesResolved(e,n,s,i){this.keyframeResolver=void 0;const{name:o,type:r,velocity:a,delay:c,isHandoff:u,onUpdate:l}=s;this.resolvedAt=N.now(),jo(e,o,r,a)||((Y.instantAnimations||!c)&&(l==null||l(Ze(e,s,n))),e[0]=e[e.length-1],s.duration=0,s.repeat=0);const f={startTime:i?this.resolvedAt?this.resolvedAt-this.createdAt>_o?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:e},d=!u&&No(f)?new Fo({...f,element:f.motionValue.owner.current}):new Qe(f);d.finished.then(()=>this.notifyFinished()).catch(U),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){var e;return this._animation||((e=this.keyframeResolver)==null||e.resume(),wo()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),(e=this.keyframeResolver)==null||e.cancel()}}const Uo=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Wo(t){const e=Uo.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function pi(t,e,n=1){const[s,i]=Wo(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const r=o.trim();return js(r)?parseFloat(r):r}return He(i)?pi(i,e,n+1):i}function nn(t,e){return(t==null?void 0:t[e])??(t==null?void 0:t.default)??t}const mi=new Set(["width","height","top","left","right","bottom",...yt]),Ko={test:t=>t==="auto",parse:t=>t},yi=t=>e=>e.test(t),gi=[mt,S,H,Z,Fr,Lr,Ko],jn=t=>gi.find(yi(t));function zo(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Os(t):!0}const Ho=new Set(["brightness","contrast","saturate","opacity"]);function Go(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(Ge)||[];if(!s)return t;const i=n.replace(s,"");let o=Ho.has(e)?1:0;return s!==n&&(o*=100),e+"("+o+i+")"}const qo=/\b([a-z-]*)\(.*?\)/gu,Me={...Q,getAnimatableNone:t=>{const e=t.match(qo);return e?e.map(Go).join(" "):t}},In={...mt,transform:Math.round},Xo={rotate:Z,rotateX:Z,rotateY:Z,rotateZ:Z,scale:Ot,scaleX:Ot,scaleY:Ot,scaleZ:Ot,skew:Z,skewX:Z,skewY:Z,distance:S,translateX:S,translateY:S,translateZ:S,x:S,y:S,z:S,perspective:S,transformPerspective:S,opacity:Vt,originX:wn,originY:wn,originZ:S},sn={borderWidth:S,borderTopWidth:S,borderRightWidth:S,borderBottomWidth:S,borderLeftWidth:S,borderRadius:S,radius:S,borderTopLeftRadius:S,borderTopRightRadius:S,borderBottomRightRadius:S,borderBottomLeftRadius:S,width:S,maxWidth:S,height:S,maxHeight:S,top:S,right:S,bottom:S,left:S,padding:S,paddingTop:S,paddingRight:S,paddingBottom:S,paddingLeft:S,margin:S,marginTop:S,marginRight:S,marginBottom:S,marginLeft:S,backgroundPositionX:S,backgroundPositionY:S,...Xo,zIndex:In,fillOpacity:Vt,strokeOpacity:Vt,numOctaves:In},Yo={...sn,color:F,backgroundColor:F,outlineColor:F,fill:F,stroke:F,borderColor:F,borderTopColor:F,borderRightColor:F,borderBottomColor:F,borderLeftColor:F,filter:Me,WebkitFilter:Me},vi=t=>Yo[t];function xi(t,e){let n=vi(t);return n!==Me&&(n=Q),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Zo=new Set(["auto","none","0"]);function Jo(t,e,n){let s=0,i;for(;s<t.length&&!i;){const o=t[s];typeof o=="string"&&!Zo.has(o)&&Ct(o).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=xi(n,i)}class Qo extends tn{constructor(e,n,s,i,o){super(e,n,s,i,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let c=0;c<e.length;c++){let u=e[c];if(typeof u=="string"&&(u=u.trim(),He(u))){const l=pi(u,n.current);l!==void 0&&(e[c]=l),c===e.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!mi.has(s)||e.length!==2)return;const[i,o]=e,r=jn(i),a=jn(o);if(r!==a)if(En(r)&&En(a))for(let c=0;c<e.length;c++){const u=e[c];typeof u=="string"&&(e[c]=parseFloat(u))}else rt[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)(e[i]===null||zo(e[i]))&&s.push(i);s.length&&Jo(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=rt[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var a;const{element:e,name:n,unresolvedKeyframes:s}=this;if(!e||!e.current)return;const i=e.getValue(n);i&&i.jump(this.measuredOrigin,!1);const o=s.length-1,r=s[o];s[o]=rt[n](e.measureViewportBox(),window.getComputedStyle(e.current)),r!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=r),(a=this.removedTransforms)!=null&&a.length&&this.removedTransforms.forEach(([c,u])=>{e.getValue(c).set(u)}),this.resolveNoneKeyframes()}}function ta(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){let s=document;const i=(n==null?void 0:n[t])??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}const Ti=(t,e)=>e&&typeof t=="number"?e.transform(t):t,On=30,ea=t=>!isNaN(parseFloat(t));class na{constructor(e,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,i=!0)=>{var r,a;const o=N.now();if(this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&((r=this.events.change)==null||r.notify(this.current),this.dependents))for(const c of this.dependents)c.dirty();i&&((a=this.events.renderRequest)==null||a.notify(this.current))},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=N.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=ea(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new Ue);const s=this.events[e].add(n);return e==="change"?()=>{s(),k.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;(e=this.events.change)==null||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=N.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>On)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,On);return Ns(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,n;(e=this.dependents)==null||e.clear(),(n=this.events.destroy)==null||n.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function dt(t,e){return new na(t,e)}const{schedule:rn}=Ys(queueMicrotask,!1),W={x:!1,y:!1};function Si(){return W.x||W.y}function sa(t){return t==="x"||t==="y"?W[t]?null:(W[t]=!0,()=>{W[t]=!1}):W.x||W.y?null:(W.x=W.y=!0,()=>{W.x=W.y=!1})}function Pi(t,e){const n=ta(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function Nn(t){return!(t.pointerType==="touch"||Si())}function ia(t,e,n={}){const[s,i,o]=Pi(t,n),r=a=>{if(!Nn(a))return;const{target:c}=a,u=e(c,a);if(typeof u!="function"||!c)return;const l=h=>{Nn(h)&&(u(h),c.removeEventListener("pointerleave",l))};c.addEventListener("pointerleave",l,i)};return s.forEach(a=>{a.addEventListener("pointerenter",r,i)}),o}const wi=(t,e)=>e?t===e?!0:wi(t,e.parentElement):!1,on=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,ra=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function oa(t){return ra.has(t.tagName)||t.tabIndex!==-1}const Ut=new WeakSet;function _n(t){return e=>{e.key==="Enter"&&t(e)}}function ae(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const aa=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=_n(()=>{if(Ut.has(n))return;ae(n,"down");const i=_n(()=>{ae(n,"up")}),o=()=>ae(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",o,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function $n(t){return on(t)&&!Si()}function ca(t,e,n={}){const[s,i,o]=Pi(t,n),r=a=>{const c=a.currentTarget;if(!$n(a))return;Ut.add(c);const u=e(c,a),l=(d,p)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),Ut.has(c)&&Ut.delete(c),$n(d)&&typeof u=="function"&&u(d,{success:p})},h=d=>{l(d,c===window||c===document||n.useGlobalTarget||wi(c,d.target))},f=d=>{l(d,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",f,i)};return s.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",r,i),en(a)&&(a.addEventListener("focus",u=>aa(u,i)),!oa(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),o}function bi(t){return Is(t)&&"ownerSVGElement"in t}function la(t){return bi(t)&&t.tagName==="svg"}const B=t=>!!(t&&t.getVelocity),ua=[...gi,F,Q],ha=t=>ua.find(yi(t)),an=v.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class fa extends v.Component{getSnapshotBeforeUpdate(e){const n=this.props.childRef.current;if(n&&e.isPresent&&!this.props.isPresent){const s=n.offsetParent,i=en(s)&&s.offsetWidth||0,o=this.props.sizeRef.current;o.height=n.offsetHeight||0,o.width=n.offsetWidth||0,o.top=n.offsetTop,o.left=n.offsetLeft,o.right=i-o.width-o.left}return null}componentDidUpdate(){}render(){return this.props.children}}function da({children:t,isPresent:e,anchorX:n}){const s=v.useId(),i=v.useRef(null),o=v.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:r}=v.useContext(an);return v.useInsertionEffect(()=>{const{width:a,height:c,top:u,left:l,right:h}=o.current;if(e||!i.current||!a||!c)return;const f=n==="left"?`left: ${l}`:`right: ${h}`;i.current.dataset.motionPopId=s;const d=document.createElement("style");return r&&(d.nonce=r),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${c}px !important;
            ${f}px !important;
            top: ${u}px !important;
          }
        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[e]),q.jsx(fa,{isPresent:e,childRef:i,sizeRef:o,children:v.cloneElement(t,{ref:i})})}const pa=({children:t,initial:e,isPresent:n,onExitComplete:s,custom:i,presenceAffectsLayout:o,mode:r,anchorX:a})=>{const c=je(ma),u=v.useId();let l=!0,h=v.useMemo(()=>(l=!1,{id:u,initial:e,isPresent:n,custom:i,onExitComplete:f=>{c.set(f,!0);for(const d of c.values())if(!d)return;s&&s()},register:f=>(c.set(f,!1),()=>c.delete(f))}),[n,c,s]);return o&&l&&(h={...h}),v.useMemo(()=>{c.forEach((f,d)=>c.set(d,!1))},[n]),v.useEffect(()=>{!n&&!c.size&&s&&s()},[n]),r==="popLayout"&&(t=q.jsx(da,{isPresent:n,anchorX:a,children:t})),q.jsx(Yt.Provider,{value:h,children:t})};function ma(){return new Map}function Ai(t=!0){const e=v.useContext(Yt);if(e===null)return[!0,null];const{isPresent:n,onExitComplete:s,register:i}=e,o=v.useId();v.useEffect(()=>{if(t)return i(o)},[t]);const r=v.useCallback(()=>t&&s&&s(o),[o,s,t]);return!n&&s?[!1,r]:[!0]}const Nt=t=>t.key||"";function Un(t){const e=[];return v.Children.forEach(t,n=>{v.isValidElement(n)&&e.push(n)}),e}const Ku=({children:t,custom:e,initial:n=!0,onExitComplete:s,presenceAffectsLayout:i=!0,mode:o="sync",propagate:r=!1,anchorX:a="left"})=>{const[c,u]=Ai(r),l=v.useMemo(()=>Un(t),[t]),h=r&&!c?[]:l.map(Nt),f=v.useRef(!0),d=v.useRef(l),p=je(()=>new Map),[y,x]=v.useState(l),[m,P]=v.useState(l);Bs(()=>{f.current=!1,d.current=l;for(let T=0;T<m.length;T++){const w=Nt(m[T]);h.includes(w)?p.delete(w):p.get(w)!==!0&&p.set(w,!1)}},[m,h.length,h.join("-")]);const g=[];if(l!==y){let T=[...l];for(let w=0;w<m.length;w++){const V=m[w],b=Nt(V);h.includes(b)||(T.splice(w,0,V),g.push(V))}return o==="wait"&&g.length&&(T=g),P(Un(T)),x(l),null}const{forceRender:M}=v.useContext(Be);return q.jsx(q.Fragment,{children:m.map(T=>{const w=Nt(T),V=r&&!c?!1:l===m||h.includes(w),b=()=>{if(p.has(w))p.set(w,!0);else return;let E=!0;p.forEach(O=>{O||(E=!1)}),E&&(M==null||M(),P(d.current),r&&(u==null||u()),s&&s())};return q.jsx(pa,{isPresent:V,initial:!f.current||n?void 0:!1,custom:e,presenceAffectsLayout:i,mode:o,onExitComplete:V?void 0:b,anchorX:a,children:T},w)})})},Mi=v.createContext({strict:!1}),Wn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},pt={};for(const t in Wn)pt[t]={isEnabled:e=>Wn[t].some(n=>!!e[n])};function ya(t){for(const e in t)pt[e]={...pt[e],...t[e]}}const ga=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function qt(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ga.has(t)}let Vi=t=>!qt(t);function va(t){t&&(Vi=e=>e.startsWith("on")?!qt(e):t(e))}try{va(require("@emotion/is-prop-valid").default)}catch{}function xa(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(Vi(i)||n===!0&&qt(i)||!e&&!qt(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function Ta(t){if(typeof Proxy>"u")return t;const e=new Map,n=(...s)=>t(...s);return new Proxy(n,{get:(s,i)=>i==="create"?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const Zt=v.createContext({});function Jt(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function kt(t){return typeof t=="string"||Array.isArray(t)}const cn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ln=["initial",...cn];function Qt(t){return Jt(t.animate)||ln.some(e=>kt(t[e]))}function Ci(t){return!!(Qt(t)||t.variants)}function Sa(t,e){if(Qt(t)){const{initial:n,animate:s}=t;return{initial:n===!1||kt(n)?n:void 0,animate:kt(s)?s:void 0}}return t.inherit!==!1?e:{}}function Pa(t){const{initial:e,animate:n}=Sa(t,v.useContext(Zt));return v.useMemo(()=>({initial:e,animate:n}),[Kn(e),Kn(n)])}function Kn(t){return Array.isArray(t)?t.join(" "):t}const wa=Symbol.for("motionComponentSymbol");function lt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function ba(t,e,n){return v.useCallback(s=>{s&&t.onMount&&t.onMount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):lt(n)&&(n.current=s))},[e])}const un=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Aa="framerAppearId",ki="data-"+un(Aa),Di=v.createContext({});function Ma(t,e,n,s,i){var y,x;const{visualElement:o}=v.useContext(Zt),r=v.useContext(Mi),a=v.useContext(Yt),c=v.useContext(an).reducedMotion,u=v.useRef(null);s=s||r.renderer,!u.current&&s&&(u.current=s(t,{visualState:e,parent:o,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:c}));const l=u.current,h=v.useContext(Di);l&&!l.projection&&i&&(l.type==="html"||l.type==="svg")&&Va(u.current,n,i,h);const f=v.useRef(!1);v.useInsertionEffect(()=>{l&&f.current&&l.update(n,a)});const d=n[ki],p=v.useRef(!!d&&!((y=window.MotionHandoffIsComplete)!=null&&y.call(window,d))&&((x=window.MotionHasOptimisedAnimation)==null?void 0:x.call(window,d)));return Bs(()=>{l&&(f.current=!0,window.MotionIsMounted=!0,l.updateFeatures(),rn.render(l.render),p.current&&l.animationState&&l.animationState.animateChanges())}),v.useEffect(()=>{l&&(!p.current&&l.animationState&&l.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{var m;(m=window.MotionHandoffMarkAsComplete)==null||m.call(window,d)}),p.current=!1))}),l}function Va(t,e,n,s){const{layoutId:i,layout:o,drag:r,dragConstraints:a,layoutScroll:c,layoutRoot:u,layoutCrossfade:l}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Ri(t.parent)),t.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!r||a&&lt(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:s,crossfade:l,layoutScroll:c,layoutRoot:u})}function Ri(t){if(t)return t.options.allowProjection!==!1?t.projection:Ri(t.parent)}function Ca({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){t&&ya(t);function o(a,c){let u;const l={...v.useContext(an),...a,layoutId:ka(a)},{isStatic:h}=l,f=Pa(a),d=s(a,h);if(!h&&Ie){Da();const p=Ra(l);u=p.MeasureLayout,f.visualElement=Ma(i,d,l,e,p.ProjectionNode)}return q.jsxs(Zt.Provider,{value:f,children:[u&&f.visualElement?q.jsx(u,{visualElement:f.visualElement,...l}):null,n(i,a,ba(d,f.visualElement,c),d,h,f.visualElement)]})}o.displayName=`motion.${typeof i=="string"?i:`create(${i.displayName??i.name??""})`}`;const r=v.forwardRef(o);return r[wa]=i,r}function ka({layoutId:t}){const e=v.useContext(Be).id;return e&&t!==void 0?e+"-"+t:t}function Da(t,e){v.useContext(Mi).strict}function Ra(t){const{drag:e,layout:n}=pt;if(!e&&!n)return{};const s={...e,...n};return{MeasureLayout:e!=null&&e.isEnabled(t)||n!=null&&n.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}const Dt={};function Ea(t){for(const e in t)Dt[e]=t[e],ze(e)&&(Dt[e].isCSSVariable=!0)}function Ei(t,{layout:e,layoutId:n}){return gt.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Dt[t]||t==="opacity")}const La={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Fa=yt.length;function Ba(t,e,n){let s="",i=!0;for(let o=0;o<Fa;o++){const r=yt[o],a=t[r];if(a===void 0)continue;let c=!0;if(typeof a=="number"?c=a===(r.startsWith("scale")?1:0):c=parseFloat(a)===0,!c||n){const u=Ti(a,sn[r]);if(!c){i=!1;const l=La[r]||r;s+=`${l}(${u}) `}n&&(e[r]=u)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}function hn(t,e,n){const{style:s,vars:i,transformOrigin:o}=t;let r=!1,a=!1;for(const c in e){const u=e[c];if(gt.has(c)){r=!0;continue}else if(ze(c)){i[c]=u;continue}else{const l=Ti(u,sn[c]);c.startsWith("origin")?(a=!0,o[c]=l):s[c]=l}}if(e.transform||(r||n?s.transform=Ba(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:c="50%",originY:u="50%",originZ:l=0}=o;s.transformOrigin=`${c} ${u} ${l}`}}const fn=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Li(t,e,n){for(const s in e)!B(e[s])&&!Ei(s,n)&&(t[s]=e[s])}function ja({transformTemplate:t},e){return v.useMemo(()=>{const n=fn();return hn(n,e,t),Object.assign({},n.vars,n.style)},[e])}function Ia(t,e){const n=t.style||{},s={};return Li(s,n,t),Object.assign(s,ja(t,e)),s}function Oa(t,e){const n={},s=Ia(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n}const Na={offset:"stroke-dashoffset",array:"stroke-dasharray"},_a={offset:"strokeDashoffset",array:"strokeDasharray"};function $a(t,e,n=1,s=0,i=!0){t.pathLength=1;const o=i?Na:_a;t[o.offset]=S.transform(-s);const r=S.transform(e),a=S.transform(n);t[o.array]=`${r} ${a}`}function Fi(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:o=1,pathOffset:r=0,...a},c,u,l){if(hn(t,a,u),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:h,style:f}=t;h.transform&&(f.transform=h.transform,delete h.transform),(f.transform||h.transformOrigin)&&(f.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),f.transform&&(f.transformBox=(l==null?void 0:l.transformBox)??"fill-box",delete h.transformBox),e!==void 0&&(h.x=e),n!==void 0&&(h.y=n),s!==void 0&&(h.scale=s),i!==void 0&&$a(h,i,o,r,!1)}const Bi=()=>({...fn(),attrs:{}}),ji=t=>typeof t=="string"&&t.toLowerCase()==="svg";function Ua(t,e,n,s){const i=v.useMemo(()=>{const o=Bi();return Fi(o,e,ji(s),t.transformTemplate,t.style),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};Li(o,t.style,t),i.style={...o,...i.style}}return i}const Wa=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function dn(t){return typeof t!="string"||t.includes("-")?!1:!!(Wa.indexOf(t)>-1||/[A-Z]/u.test(t))}function Ka(t=!1){return(n,s,i,{latestValues:o},r)=>{const c=(dn(n)?Ua:Oa)(s,o,r,n),u=xa(s,typeof n=="string",t),l=n!==v.Fragment?{...u,...c,ref:i}:{},{children:h}=s,f=v.useMemo(()=>B(h)?h.get():h,[h]);return v.createElement(n,{...l,children:f})}}function zn(t){const e=[{},{}];return t==null||t.values.forEach((n,s)=>{e[0][s]=n.get(),e[1][s]=n.getVelocity()}),e}function pn(t,e,n,s){if(typeof e=="function"){const[i,o]=zn(s);e=e(n!==void 0?n:t.custom,i,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[i,o]=zn(s);e=e(n!==void 0?n:t.custom,i,o)}return e}function Wt(t){return B(t)?t.get():t}function za({scrapeMotionValuesFromProps:t,createRenderState:e},n,s,i){return{latestValues:Ha(n,s,i,t),renderState:e()}}const Ii=t=>(e,n)=>{const s=v.useContext(Zt),i=v.useContext(Yt),o=()=>za(t,e,s,i);return n?o():je(o)};function Ha(t,e,n,s){const i={},o=s(t,{});for(const f in o)i[f]=Wt(o[f]);let{initial:r,animate:a}=t;const c=Qt(t),u=Ci(t);e&&u&&!c&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let l=n?n.initial===!1:!1;l=l||r===!1;const h=l?a:r;if(h&&typeof h!="boolean"&&!Jt(h)){const f=Array.isArray(h)?h:[h];for(let d=0;d<f.length;d++){const p=pn(t,f[d]);if(p){const{transitionEnd:y,transition:x,...m}=p;for(const P in m){let g=m[P];if(Array.isArray(g)){const M=l?g.length-1:0;g=g[M]}g!==null&&(i[P]=g)}for(const P in y)i[P]=y[P]}}}return i}function mn(t,e,n){var o;const{style:s}=t,i={};for(const r in s)(B(s[r])||e.style&&B(e.style[r])||Ei(r,t)||((o=n==null?void 0:n.getValue(r))==null?void 0:o.liveStyle)!==void 0)&&(i[r]=s[r]);return i}const Ga={useVisualState:Ii({scrapeMotionValuesFromProps:mn,createRenderState:fn})};function Oi(t,e,n){const s=mn(t,e,n);for(const i in t)if(B(t[i])||B(e[i])){const o=yt.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[o]=t[i]}return s}const qa={useVisualState:Ii({scrapeMotionValuesFromProps:Oi,createRenderState:Bi})};function Xa(t,e){return function(s,{forwardMotionProps:i}={forwardMotionProps:!1}){const r={...dn(s)?qa:Ga,preloadedFeatures:t,useRender:Ka(i),createVisualElement:e,Component:s};return Ca(r)}}function Rt(t,e,n){const s=t.getProps();return pn(s,e,n!==void 0?n:s.custom,t)}const Ve=t=>Array.isArray(t);function Ya(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,dt(n))}function Za(t){return Ve(t)?t[t.length-1]||0:t}function Ja(t,e){const n=Rt(t,e);let{transitionEnd:s={},transition:i={},...o}=n||{};o={...o,...s};for(const r in o){const a=Za(o[r]);Ya(t,r,a)}}function Qa(t){return!!(B(t)&&t.add)}function Ce(t,e){const n=t.getValue("willChange");if(Qa(n))return n.add(e);if(!n&&Y.WillChange){const s=new Y.WillChange("auto");t.addValue("willChange",s),s.add(e)}}function Ni(t){return t.props[ki]}const tc=t=>t!==null;function ec(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(tc),o=e&&n!=="loop"&&e%2===1?0:i.length-1;return i[o]}const nc={type:"spring",stiffness:500,damping:25,restSpeed:10},sc=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),ic={type:"keyframes",duration:.8},rc={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},oc=(t,{keyframes:e})=>e.length>2?ic:gt.has(t)?t.startsWith("scale")?sc(e[1]):nc:rc;function ac({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:o,repeatType:r,repeatDelay:a,from:c,elapsed:u,...l}){return!!Object.keys(l).length}const yn=(t,e,n,s={},i,o)=>r=>{const a=nn(s,t)||{},c=a.delay||s.delay||0;let{elapsed:u=0}=s;u=u-K(c);const l={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:f=>{e.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:i};ac(a)||Object.assign(l,oc(t,l)),l.duration&&(l.duration=K(l.duration)),l.repeatDelay&&(l.repeatDelay=K(l.repeatDelay)),l.from!==void 0&&(l.keyframes[0]=l.from);let h=!1;if((l.type===!1||l.duration===0&&!l.repeatDelay)&&(l.duration=0,l.delay===0&&(h=!0)),(Y.instantAnimations||Y.skipAnimations)&&(h=!0,l.duration=0,l.delay=0),l.allowFlatten=!a.type&&!a.ease,h&&!o&&e.get()!==void 0){const f=ec(l.keyframes,a);if(f!==void 0){k.update(()=>{l.onUpdate(f),l.onComplete()});return}}return a.isSync?new Qe(l):new $o(l)};function cc({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function _i(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;s&&(o=s);const c=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const l in a){const h=t.getValue(l,t.latestValues[l]??null),f=a[l];if(f===void 0||u&&cc(u,l))continue;const d={delay:n,...nn(o||{},l)},p=h.get();if(p!==void 0&&!h.isAnimating&&!Array.isArray(f)&&f===p&&!d.velocity)continue;let y=!1;if(window.MotionHandoffAnimation){const m=Ni(t);if(m){const P=window.MotionHandoffAnimation(m,l,k);P!==null&&(d.startTime=P,y=!0)}}Ce(t,l),h.start(yn(l,h,f,t.shouldReduceMotion&&mi.has(l)?{type:!1}:d,t,y));const x=h.animation;x&&c.push(x)}return r&&Promise.all(c).then(()=>{k.update(()=>{r&&Ja(t,r)})}),c}function ke(t,e,n={}){var c;const s=Rt(t,e,n.type==="exit"?(c=t.presenceContext)==null?void 0:c.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const o=s?()=>Promise.all(_i(t,s,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(u=0)=>{const{delayChildren:l=0,staggerChildren:h,staggerDirection:f}=i;return lc(t,e,l+u,h,f,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[u,l]=a==="beforeChildren"?[o,r]:[r,o];return u().then(()=>l())}else return Promise.all([o(),r(n.delay)])}function lc(t,e,n=0,s=0,i=1,o){const r=[],a=(t.variantChildren.size-1)*s,c=i===1?(u=0)=>u*s:(u=0)=>a-u*s;return Array.from(t.variantChildren).sort(uc).forEach((u,l)=>{u.notify("AnimationStart",e),r.push(ke(u,e,{...o,delay:n+c(l)}).then(()=>u.notify("AnimationComplete",e)))}),Promise.all(r)}function uc(t,e){return t.sortNodePosition(e)}function hc(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(o=>ke(t,o,n));s=Promise.all(i)}else if(typeof e=="string")s=ke(t,e,n);else{const i=typeof e=="function"?Rt(t,e,n.custom):e;s=Promise.all(_i(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}function $i(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const fc=ln.length;function Ui(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?Ui(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<fc;n++){const s=ln[n],i=t.props[s];(kt(i)||i===!1)&&(e[s]=i)}return e}const dc=[...cn].reverse(),pc=cn.length;function mc(t){return e=>Promise.all(e.map(({animation:n,options:s})=>hc(t,n,s)))}function yc(t){let e=mc(t),n=Hn(),s=!0;const i=c=>(u,l)=>{var f;const h=Rt(t,l,c==="exit"?(f=t.presenceContext)==null?void 0:f.custom:void 0);if(h){const{transition:d,transitionEnd:p,...y}=h;u={...u,...y,...p}}return u};function o(c){e=c(t)}function r(c){const{props:u}=t,l=Ui(t.parent)||{},h=[],f=new Set;let d={},p=1/0;for(let x=0;x<pc;x++){const m=dc[x],P=n[m],g=u[m]!==void 0?u[m]:l[m],M=kt(g),T=m===c?P.isActive:null;T===!1&&(p=x);let w=g===l[m]&&g!==u[m]&&M;if(w&&s&&t.manuallyAnimateOnMount&&(w=!1),P.protectedKeys={...d},!P.isActive&&T===null||!g&&!P.prevProp||Jt(g)||typeof g=="boolean")continue;const V=gc(P.prevProp,g);let b=V||m===c&&P.isActive&&!w&&M||x>p&&M,E=!1;const O=Array.isArray(g)?g:[g];let at=O.reduce(i(m),{});T===!1&&(at={});const{prevResolvedValues:gn={}}=P,fr={...gn,...at},vn=j=>{b=!0,f.has(j)&&(E=!0,f.delete(j)),P.needsAnimating[j]=!0;const G=t.getValue(j);G&&(G.liveStyle=!1)};for(const j in fr){const G=at[j],te=gn[j];if(d.hasOwnProperty(j))continue;let ee=!1;Ve(G)&&Ve(te)?ee=!$i(G,te):ee=G!==te,ee?G!=null?vn(j):f.add(j):G!==void 0&&f.has(j)?vn(j):P.protectedKeys[j]=!0}P.prevProp=g,P.prevResolvedValues=at,P.isActive&&(d={...d,...at}),s&&t.blockInitialAnimation&&(b=!1),b&&(!(w&&V)||E)&&h.push(...O.map(j=>({animation:j,options:{type:m}})))}if(f.size){const x={};if(typeof u.initial!="boolean"){const m=Rt(t,Array.isArray(u.initial)?u.initial[0]:u.initial);m&&m.transition&&(x.transition=m.transition)}f.forEach(m=>{const P=t.getBaseTarget(m),g=t.getValue(m);g&&(g.liveStyle=!0),x[m]=P??null}),h.push({animation:x})}let y=!!h.length;return s&&(u.initial===!1||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(y=!1),s=!1,y?e(h):Promise.resolve()}function a(c,u){var h;if(n[c].isActive===u)return Promise.resolve();(h=t.variantChildren)==null||h.forEach(f=>{var d;return(d=f.animationState)==null?void 0:d.setActive(c,u)}),n[c].isActive=u;const l=r(c);for(const f in n)n[f].protectedKeys={};return l}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=Hn(),s=!0}}}function gc(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!$i(e,t):!1}function et(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Hn(){return{animate:et(!0),whileInView:et(),whileHover:et(),whileTap:et(),whileDrag:et(),whileFocus:et(),exit:et()}}class tt{constructor(e){this.isMounted=!1,this.node=e}update(){}}class vc extends tt{constructor(e){super(e),e.animationState||(e.animationState=yc(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Jt(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)==null||e.call(this)}}let xc=0;class Tc extends tt{constructor(){super(...arguments),this.id=xc++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const Sc={animation:{Feature:vc},exit:{Feature:Tc}};function Et(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function jt(t){return{point:{x:t.pageX,y:t.pageY}}}const Pc=t=>e=>on(e)&&t(e,jt(e));function wt(t,e,n,s){return Et(t,e,Pc(n),s)}function Wi({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function wc({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function bc(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}const Ki=1e-4,Ac=1-Ki,Mc=1+Ki,zi=.01,Vc=0-zi,Cc=0+zi;function I(t){return t.max-t.min}function kc(t,e,n){return Math.abs(t-e)<=n}function Gn(t,e,n,s=.5){t.origin=s,t.originPoint=C(e.min,e.max,t.origin),t.scale=I(n)/I(e),t.translate=C(n.min,n.max,t.origin)-t.originPoint,(t.scale>=Ac&&t.scale<=Mc||isNaN(t.scale))&&(t.scale=1),(t.translate>=Vc&&t.translate<=Cc||isNaN(t.translate))&&(t.translate=0)}function bt(t,e,n,s){Gn(t.x,e.x,n.x,s?s.originX:void 0),Gn(t.y,e.y,n.y,s?s.originY:void 0)}function qn(t,e,n){t.min=n.min+e.min,t.max=t.min+I(e)}function Dc(t,e,n){qn(t.x,e.x,n.x),qn(t.y,e.y,n.y)}function Xn(t,e,n){t.min=e.min-n.min,t.max=t.min+I(e)}function At(t,e,n){Xn(t.x,e.x,n.x),Xn(t.y,e.y,n.y)}const Yn=()=>({translate:0,scale:1,origin:0,originPoint:0}),ut=()=>({x:Yn(),y:Yn()}),Zn=()=>({min:0,max:0}),R=()=>({x:Zn(),y:Zn()});function $(t){return[t("x"),t("y")]}function ce(t){return t===void 0||t===1}function De({scale:t,scaleX:e,scaleY:n}){return!ce(t)||!ce(e)||!ce(n)}function nt(t){return De(t)||Hi(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Hi(t){return Jn(t.x)||Jn(t.y)}function Jn(t){return t&&t!=="0%"}function Xt(t,e,n){const s=t-n,i=e*s;return n+i}function Qn(t,e,n,s,i){return i!==void 0&&(t=Xt(t,i,s)),Xt(t,n,s)+e}function Re(t,e=0,n=1,s,i){t.min=Qn(t.min,e,n,s,i),t.max=Qn(t.max,e,n,s,i)}function Gi(t,{x:e,y:n}){Re(t.x,e.translate,e.scale,e.originPoint),Re(t.y,n.translate,n.scale,n.originPoint)}const ts=.999999999999,es=1.0000000000001;function Rc(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let o,r;for(let a=0;a<i;a++){o=n[a],r=o.projectionDelta;const{visualElement:c}=o.options;c&&c.props.style&&c.props.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ft(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,Gi(t,r)),s&&nt(o.latestValues)&&ft(t,o.latestValues))}e.x<es&&e.x>ts&&(e.x=1),e.y<es&&e.y>ts&&(e.y=1)}function ht(t,e){t.min=t.min+e,t.max=t.max+e}function ns(t,e,n,s,i=.5){const o=C(t.min,t.max,i);Re(t,e,n,o,s)}function ft(t,e){ns(t.x,e.x,e.scaleX,e.scale,e.originX),ns(t.y,e.y,e.scaleY,e.scale,e.originY)}function qi(t,e){return Wi(bc(t.getBoundingClientRect(),e))}function Ec(t,e,n){const s=qi(t,n),{scroll:i}=e;return i&&(ht(s.x,i.offset.x),ht(s.y,i.offset.y)),s}const Xi=({current:t})=>t?t.ownerDocument.defaultView:null,ss=(t,e)=>Math.abs(t-e);function Lc(t,e){const n=ss(t.x,e.x),s=ss(t.y,e.y);return Math.sqrt(n**2+s**2)}class Yi{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=ue(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=Lc(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:p}=h,{timestamp:y}=L;this.history.push({...p,timestamp:y});const{onStart:x,onMove:m}=this.handlers;f||(x&&x(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),m&&m(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=le(f,this.transformPagePoint),k.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:p,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=ue(h.type==="pointercancel"?this.lastMoveEventInfo:le(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,x),p&&p(h,x)},!on(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const r=jt(e),a=le(r,this.transformPagePoint),{point:c}=a,{timestamp:u}=L;this.history=[{...c,timestamp:u}];const{onSessionStart:l}=n;l&&l(e,ue(a,this.history)),this.removeListeners=Lt(wt(this.contextWindow,"pointermove",this.handlePointerMove),wt(this.contextWindow,"pointerup",this.handlePointerUp),wt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),J(this.updatePoint)}}function le(t,e){return e?{point:e(t.point)}:t}function is(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ue({point:t},e){return{point:t,delta:is(t,Zi(e)),offset:is(t,Fc(e)),velocity:Bc(e,.1)}}function Fc(t){return t[0]}function Zi(t){return t[t.length-1]}function Bc(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=Zi(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>K(e)));)n--;if(!s)return{x:0,y:0};const o=z(i.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const r={x:(i.x-s.x)/o,y:(i.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function jc(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?C(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?C(n,t,s.max):Math.min(t,n)),t}function rs(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Ic(t,{top:e,left:n,bottom:s,right:i}){return{x:rs(t.x,n,i),y:rs(t.y,e,s)}}function os(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function Oc(t,e){return{x:os(t.x,e.x),y:os(t.y,e.y)}}function Nc(t,e){let n=.5;const s=I(t),i=I(e);return i>s?n=Mt(e.min,e.max-s,t.min):s>i&&(n=Mt(t.min,t.max-i,e.min)),X(0,1,n)}function _c(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const Ee=.35;function $c(t=Ee){return t===!1?t=0:t===!0&&(t=Ee),{x:as(t,"left","right"),y:as(t,"top","bottom")}}function as(t,e,n){return{min:cs(t,e),max:cs(t,n)}}function cs(t,e){return typeof t=="number"?t:t[e]||0}const Uc=new WeakMap;class Wc{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=R(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const i=l=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(jt(l).point)},o=(l,h)=>{const{drag:f,dragPropagation:d,onDragStart:p}=this.getProps();if(f&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=sa(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),$(x=>{let m=this.getAxisMotionValue(x).get()||0;if(H.test(m)){const{projection:P}=this.visualElement;if(P&&P.layout){const g=P.layout.layoutBox[x];g&&(m=I(g)*(parseFloat(m)/100))}}this.originPoint[x]=m}),p&&k.postRender(()=>p(l,h)),Ce(this.visualElement,"transform");const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},r=(l,h)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:p,onDrag:y}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:x}=h;if(d&&this.currentDirection===null){this.currentDirection=Kc(x),this.currentDirection!==null&&p&&p(this.currentDirection);return}this.updateAxis("x",h.point,x),this.updateAxis("y",h.point,x),this.visualElement.render(),y&&y(l,h)},a=(l,h)=>this.stop(l,h),c=()=>$(l=>{var h;return this.getAnimationState(l)==="paused"&&((h=this.getAxisMotionValue(l).animation)==null?void 0:h.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Yi(e,{onSessionStart:i,onStart:o,onMove:r,onSessionEnd:a,resumeAnimation:c},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Xi(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&k.postRender(()=>o(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!_t(e,i,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(r=jc(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var o;const{dragConstraints:e,dragElastic:n}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(o=this.visualElement.projection)==null?void 0:o.layout,i=this.constraints;e&&lt(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=Ic(s.layoutBox,e):this.constraints=!1,this.elastic=$c(n),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&$(r=>{this.constraints!==!1&&this.getAxisMotionValue(r)&&(this.constraints[r]=_c(s.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!lt(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=Ec(s,i.root,this.visualElement.getTransformPagePoint());let r=Oc(i.layout.layoutBox,o);if(n){const a=n(wc(r));this.hasMutatedConstraints=!!a,a&&(r=Wi(a))}return r}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),c=this.constraints||{},u=$(l=>{if(!_t(l,n,this.currentDirection))return;let h=c&&c[l]||{};r&&(h={min:0,max:0});const f=i?200:1e6,d=i?40:1e7,p={type:"inertia",velocity:s?e[l]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(l,p)});return Promise.all(u).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return Ce(this.visualElement,e),s.start(yn(e,s,0,n,this.visualElement,!1))}stopAnimation(){$(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){$(e=>{var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){$(n=>{const{drag:s}=this.getProps();if(!_t(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:r,max:a}=i.layout.layoutBox[n];o.set(e[n]-C(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!lt(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};$(r=>{const a=this.getAxisMotionValue(r);if(a&&this.constraints!==!1){const c=a.get();i[r]=Nc({min:c,max:c},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),$(r=>{if(!_t(r,e,null))return;const a=this.getAxisMotionValue(r),{min:c,max:u}=this.constraints[r];a.set(C(c,u,i[r]))})}addListeners(){if(!this.visualElement.current)return;Uc.set(this.visualElement,this);const e=this.visualElement.current,n=wt(e,"pointerdown",c=>{const{drag:u,dragListener:l=!0}=this.getProps();u&&l&&this.start(c)}),s=()=>{const{dragConstraints:c}=this.getProps();lt(c)&&c.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),k.read(s);const r=Et(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:c,hasLayoutChanged:u})=>{this.isDragging&&u&&($(l=>{const h=this.getAxisMotionValue(l);h&&(this.originPoint[l]+=c[l].translate,h.set(h.get()+c[l].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:r=Ee,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function _t(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Kc(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class zc extends tt{constructor(e){super(e),this.removeGroupControls=U,this.removeListeners=U,this.controls=new Wc(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||U}unmount(){this.removeGroupControls(),this.removeListeners()}}const ls=t=>(e,n)=>{t&&k.postRender(()=>t(e,n))};class Hc extends tt{constructor(){super(...arguments),this.removePointerDownListener=U}onPointerDown(e){this.session=new Yi(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Xi(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:ls(e),onStart:ls(n),onMove:s,onEnd:(o,r)=>{delete this.session,i&&k.postRender(()=>i(o,r))}}}mount(){this.removePointerDownListener=wt(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Kt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function us(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const xt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(S.test(t))t=parseFloat(t);else return t;const n=us(t,e.target.x),s=us(t,e.target.y);return`${n}% ${s}%`}},Gc={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=Q.parse(t);if(i.length>5)return s;const o=Q.createTransformer(t),r=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,c=n.y.scale*e.y;i[0+r]/=a,i[1+r]/=c;const u=C(a,c,.5);return typeof i[2+r]=="number"&&(i[2+r]/=u),typeof i[3+r]=="number"&&(i[3+r]/=u),o(i)}};class qc extends v.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:o}=e;Ea(Xc),o&&(n.group&&n.group.add(o),s&&s.register&&i&&s.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Kt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:o}=this.props,{projection:r}=s;return r&&(r.isPresent=o,i||e.layoutDependency!==n||n===void 0||e.isPresent!==o?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||k.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),rn.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Ji(t){const[e,n]=Ai(),s=v.useContext(Be);return q.jsx(qc,{...t,layoutGroup:s,switchLayoutGroup:v.useContext(Di),isPresent:e,safeToRemove:n})}const Xc={borderRadius:{...xt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:xt,borderTopRightRadius:xt,borderBottomLeftRadius:xt,borderBottomRightRadius:xt,boxShadow:Gc};function Yc(t,e,n){const s=B(t)?t:dt(t);return s.start(yn("",s,e,n)),s.animation}const Zc=(t,e)=>t.depth-e.depth;class Jc{constructor(){this.children=[],this.isDirty=!1}add(e){Oe(this.children,e),this.isDirty=!0}remove(e){Ne(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Zc),this.isDirty=!1,this.children.forEach(e)}}function Qc(t,e){const n=N.now(),s=({timestamp:i})=>{const o=i-n;o>=e&&(J(s),t(o-e))};return k.setup(s,!0),()=>J(s)}const Qi=["TopLeft","TopRight","BottomLeft","BottomRight"],tl=Qi.length,hs=t=>typeof t=="string"?parseFloat(t):t,fs=t=>typeof t=="number"||S.test(t);function el(t,e,n,s,i,o){i?(t.opacity=C(0,n.opacity??1,nl(s)),t.opacityExit=C(e.opacity??1,0,sl(s))):o&&(t.opacity=C(e.opacity??1,n.opacity??1,s));for(let r=0;r<tl;r++){const a=`border${Qi[r]}Radius`;let c=ds(e,a),u=ds(n,a);if(c===void 0&&u===void 0)continue;c||(c=0),u||(u=0),c===0||u===0||fs(c)===fs(u)?(t[a]=Math.max(C(hs(c),hs(u),s),0),(H.test(u)||H.test(c))&&(t[a]+="%")):t[a]=u}(e.rotate||n.rotate)&&(t.rotate=C(e.rotate||0,n.rotate||0,s))}function ds(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const nl=tr(0,.5,Hs),sl=tr(.5,.95,U);function tr(t,e,n){return s=>s<t?0:s>e?1:n(Mt(t,e,s))}function ps(t,e){t.min=e.min,t.max=e.max}function _(t,e){ps(t.x,e.x),ps(t.y,e.y)}function ms(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ys(t,e,n,s,i){return t-=e,t=Xt(t,1/n,s),i!==void 0&&(t=Xt(t,1/i,s)),t}function il(t,e=0,n=1,s=.5,i,o=t,r=t){if(H.test(e)&&(e=parseFloat(e),e=C(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=C(o.min,o.max,s);t===o&&(a-=e),t.min=ys(t.min,e,n,a,i),t.max=ys(t.max,e,n,a,i)}function gs(t,e,[n,s,i],o,r){il(t,e[n],e[s],e[i],e.scale,o,r)}const rl=["x","scaleX","originX"],ol=["y","scaleY","originY"];function vs(t,e,n,s){gs(t.x,e,rl,n?n.x:void 0,s?s.x:void 0),gs(t.y,e,ol,n?n.y:void 0,s?s.y:void 0)}function xs(t){return t.translate===0&&t.scale===1}function er(t){return xs(t.x)&&xs(t.y)}function Ts(t,e){return t.min===e.min&&t.max===e.max}function al(t,e){return Ts(t.x,e.x)&&Ts(t.y,e.y)}function Ss(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nr(t,e){return Ss(t.x,e.x)&&Ss(t.y,e.y)}function Ps(t){return I(t.x)/I(t.y)}function ws(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class cl{constructor(){this.members=[]}add(e){Oe(this.members,e),e.scheduleRender()}remove(e){if(Ne(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function ll(t,e,n){let s="";const i=t.x.translate/e.x,o=t.y.translate/e.y,r=(n==null?void 0:n.z)||0;if((i||o||r)&&(s=`translate3d(${i}px, ${o}px, ${r}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:u,rotate:l,rotateX:h,rotateY:f,skewX:d,skewY:p}=n;u&&(s=`perspective(${u}px) ${s}`),l&&(s+=`rotate(${l}deg) `),h&&(s+=`rotateX(${h}deg) `),f&&(s+=`rotateY(${f}deg) `),d&&(s+=`skewX(${d}deg) `),p&&(s+=`skewY(${p}deg) `)}const a=t.x.scale*e.x,c=t.y.scale*e.y;return(a!==1||c!==1)&&(s+=`scale(${a}, ${c})`),s||"none"}const he=["","X","Y","Z"],ul={visibility:"hidden"},hl=1e3;let fl=0;function fe(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function sr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=Ni(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",k,!(i||o))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&sr(s)}function ir({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(r={},a=e==null?void 0:e()){this.id=fl++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(ml),this.nodes.forEach(Tl),this.nodes.forEach(Sl),this.nodes.forEach(yl)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let c=0;c<this.path.length;c++)this.path[c].shouldResetTransform=!0;this.root===this&&(this.nodes=new Jc)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new Ue),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const c=this.eventHandlers.get(r);c&&c.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r){if(this.instance)return;this.isSVG=bi(r)&&!la(r),this.instance=r;const{layoutId:a,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(c||a)&&(this.isLayoutDirty=!0),t){let l;const h=()=>this.root.updateBlockedByResize=!1;t(r,()=>{this.root.updateBlockedByResize=!0,l&&l(),l=Qc(h,250),Kt.hasAnimatedSinceResize&&(Kt.hasAnimatedSinceResize=!1,this.nodes.forEach(As))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&u&&(a||c)&&this.addEventListener("didUpdate",({delta:l,hasLayoutChanged:h,hasRelativeLayoutChanged:f,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const p=this.options.transition||u.getDefaultTransition()||Ml,{onLayoutAnimationStart:y,onLayoutAnimationComplete:x}=u.getProps(),m=!this.targetLayout||!nr(this.targetLayout,d),P=!h&&f;if(this.options.layoutRoot||this.resumeFrom||P||h&&(m||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const g={...nn(p,"layout"),onPlay:y,onComplete:x};(u.shouldReduceMotion||this.options.layoutRoot)&&(g.delay=0,g.type=!1),this.startAnimation(g),this.setAnimationOrigin(l,P)}else h||As(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),J(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Pl),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&sr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let l=0;l<this.path.length;l++){const h=this.path[l];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:c}=this.options;if(a===void 0&&!c)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(bs);return}this.isUpdating||this.nodes.forEach(vl),this.isUpdating=!1,this.nodes.forEach(xl),this.nodes.forEach(dl),this.nodes.forEach(pl),this.clearAllSnapshots();const a=N.now();L.delta=X(0,1e3/60,a-L.timestamp),L.timestamp=a,L.isProcessing=!0,se.update.process(L),se.preRender.process(L),se.render.process(L),L.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(gl),this.sharedNodes.forEach(wl)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,k.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){k.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!I(this.snapshot.measuredBox.x)&&!I(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let c=0;c<this.path.length;c++)this.path[c].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=R(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a&&this.instance){const c=s(this.instance);this.scroll={animationId:this.root.animationId,phase:r,isRoot:c,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:c}}}resetTransform(){if(!i)return;const r=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!er(this.projectionDelta),c=this.getTransformTemplate(),u=c?c(this.latestValues,""):void 0,l=u!==this.prevTransformTemplateValue;r&&this.instance&&(a||nt(this.latestValues)||l)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let c=this.removeElementScroll(a);return r&&(c=this.removeTransform(c)),Vl(c),{animationId:this.root.animationId,measuredBox:a,layoutBox:c,latestValues:{},source:this.id}}measurePageBox(){var u;const{visualElement:r}=this.options;if(!r)return R();const a=r.measureViewportBox();if(!(((u=this.scroll)==null?void 0:u.wasRoot)||this.path.some(Cl))){const{scroll:l}=this.root;l&&(ht(a.x,l.offset.x),ht(a.y,l.offset.y))}return a}removeElementScroll(r){var c;const a=R();if(_(a,r),(c=this.scroll)!=null&&c.wasRoot)return a;for(let u=0;u<this.path.length;u++){const l=this.path[u],{scroll:h,options:f}=l;l!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&_(a,r),ht(a.x,h.offset.x),ht(a.y,h.offset.y))}return a}applyTransform(r,a=!1){const c=R();_(c,r);for(let u=0;u<this.path.length;u++){const l=this.path[u];!a&&l.options.layoutScroll&&l.scroll&&l!==l.root&&ft(c,{x:-l.scroll.offset.x,y:-l.scroll.offset.y}),nt(l.latestValues)&&ft(c,l.latestValues)}return nt(this.latestValues)&&ft(c,this.latestValues),c}removeTransform(r){const a=R();_(a,r);for(let c=0;c<this.path.length;c++){const u=this.path[c];if(!u.instance||!nt(u.latestValues))continue;De(u.latestValues)&&u.updateSnapshot();const l=R(),h=u.measurePageBox();_(l,h),vs(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,l)}return nt(this.latestValues)&&vs(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==L.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var f;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==a;if(!(r||c&&this.isSharedProjectionDirty||this.isProjectionDirty||(f=this.parent)!=null&&f.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:l,layoutId:h}=this.options;if(!(!this.layout||!(l||h))){if(this.resolvedRelativeTargetAt=L.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=R(),this.relativeTargetOrigin=R(),At(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),_(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=R(),this.targetWithTransforms=R()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Dc(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):_(this.target,this.layout.layoutBox),Gi(this.target,this.targetDelta)):_(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=R(),this.relativeTargetOrigin=R(),At(this.relativeTargetOrigin,this.target,d.target),_(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||De(this.parent.latestValues)||Hi(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var p;const r=this.getLead(),a=!!this.resumingFrom||this!==r;let c=!0;if((this.isProjectionDirty||(p=this.parent)!=null&&p.isProjectionDirty)&&(c=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===L.timestamp&&(c=!1),c)return;const{layout:u,layoutId:l}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||l))return;_(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,f=this.treeScale.y;Rc(this.layoutCorrected,this.treeScale,this.path,a),r.layout&&!r.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(r.target=r.layout.layoutBox,r.targetWithTransforms=R());const{target:d}=r;if(!d){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(ms(this.prevProjectionDelta.x,this.projectionDelta.x),ms(this.prevProjectionDelta.y,this.projectionDelta.y)),bt(this.projectionDelta,this.layoutCorrected,d,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==f||!ws(this.projectionDelta.x,this.prevProjectionDelta.x)||!ws(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",d))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){var a;if((a=this.options.visualElement)==null||a.scheduleRender(),r){const c=this.getStack();c&&c.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ut(),this.projectionDelta=ut(),this.projectionDeltaWithTransform=ut()}setAnimationOrigin(r,a=!1){const c=this.snapshot,u=c?c.latestValues:{},l={...this.latestValues},h=ut();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=R(),d=c?c.source:void 0,p=this.layout?this.layout.source:void 0,y=d!==p,x=this.getStack(),m=!x||x.members.length<=1,P=!!(y&&!m&&this.options.crossfade===!0&&!this.path.some(Al));this.animationProgress=0;let g;this.mixTargetDelta=M=>{const T=M/1e3;Ms(h.x,r.x,T),Ms(h.y,r.y,T),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(At(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),bl(this.relativeTarget,this.relativeTargetOrigin,f,T),g&&al(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=R()),_(g,this.relativeTarget)),y&&(this.animationValues=l,el(l,u,this.latestValues,T,P,m)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=T},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){var a,c,u;this.notifyListeners("animationStart"),(a=this.currentAnimation)==null||a.stop(),(u=(c=this.resumingFrom)==null?void 0:c.currentAnimation)==null||u.stop(),this.pendingAnimation&&(J(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=k.update(()=>{Kt.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=dt(0)),this.currentAnimation=Yc(this.motionValue,[0,1e3],{...r,isSync:!0,onUpdate:l=>{this.mixTargetDelta(l),r.onUpdate&&r.onUpdate(l)},onStop:()=>{},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(hl),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:c,layout:u,latestValues:l}=r;if(!(!a||!c||!u)){if(this!==r&&this.layout&&u&&rr(this.options.animationType,this.layout.layoutBox,u.layoutBox)){c=this.target||R();const h=I(this.layout.layoutBox.x);c.x.min=r.target.x.min,c.x.max=c.x.min+h;const f=I(this.layout.layoutBox.y);c.y.min=r.target.y.min,c.y.max=c.y.min+f}_(a,c),ft(a,l),bt(this.projectionDeltaWithTransform,this.layoutCorrected,a,l)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new cl),this.sharedNodes.get(r).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var a;const{layoutId:r}=this.options;return r?((a=this.getStack())==null?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:r}=this.options;return r?(a=this.getStack())==null?void 0:a.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:c}={}){const u=this.getStack();u&&u.promote(this,c),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetSkewAndRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:c}=r;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(a=!0),!a)return;const u={};c.z&&fe("z",r,u,this.animationValues);for(let l=0;l<he.length;l++)fe(`rotate${he[l]}`,r,u,this.animationValues),fe(`skew${he[l]}`,r,u,this.animationValues);r.render();for(const l in u)r.setStaticValue(l,u[l]),this.animationValues&&(this.animationValues[l]=u[l]);r.scheduleRender()}getProjectionStyles(r){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ul;const a={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,a.opacity="",a.pointerEvents=Wt(r==null?void 0:r.pointerEvents)||"",a.transform=c?c(this.latestValues,""):"none",a;const u=this.getLead();if(!this.projectionDelta||!this.layout||!u.target){const d={};return this.options.layoutId&&(d.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,d.pointerEvents=Wt(r==null?void 0:r.pointerEvents)||""),this.hasProjected&&!nt(this.latestValues)&&(d.transform=c?c({},""):"none",this.hasProjected=!1),d}const l=u.animationValues||u.latestValues;this.applyTransformsToTarget(),a.transform=ll(this.projectionDeltaWithTransform,this.treeScale,l),c&&(a.transform=c(l,a.transform));const{x:h,y:f}=this.projectionDelta;a.transformOrigin=`${h.origin*100}% ${f.origin*100}% 0`,u.animationValues?a.opacity=u===this?l.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:l.opacityExit:a.opacity=u===this?l.opacity!==void 0?l.opacity:"":l.opacityExit!==void 0?l.opacityExit:0;for(const d in Dt){if(l[d]===void 0)continue;const{correct:p,applyTo:y,isCSSVariable:x}=Dt[d],m=a.transform==="none"?l[d]:p(l[d],u);if(y){const P=y.length;for(let g=0;g<P;g++)a[y[g]]=m}else x?this.options.visualElement.renderState.vars[d]=m:a[d]=m}return this.options.layoutId&&(a.pointerEvents=u===this?Wt(r==null?void 0:r.pointerEvents)||"":"none"),a}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)==null?void 0:a.stop()}),this.root.nodes.forEach(bs),this.root.sharedNodes.clear()}}}function dl(t){t.updateLayout()}function pl(t){var n;const e=((n=t.resumeFrom)==null?void 0:n.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:o}=t.options,r=e.source!==t.layout.source;o==="size"?$(h=>{const f=r?e.measuredBox[h]:e.layoutBox[h],d=I(f);f.min=s[h].min,f.max=f.min+d}):rr(o,e.layoutBox,s)&&$(h=>{const f=r?e.measuredBox[h]:e.layoutBox[h],d=I(s[h]);f.max=f.min+d,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+d)});const a=ut();bt(a,s,e.layoutBox);const c=ut();r?bt(c,t.applyTransform(i,!0),e.measuredBox):bt(c,s,e.layoutBox);const u=!er(a);let l=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const p=R();At(p,e.layoutBox,f.layoutBox);const y=R();At(y,s,d.layoutBox),nr(p,y)||(l=!0),h.options.layoutRoot&&(t.relativeTarget=y,t.relativeTargetOrigin=p,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:e,delta:c,layoutDelta:a,hasLayoutChanged:u,hasRelativeLayoutChanged:l})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function ml(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function yl(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function gl(t){t.clearSnapshot()}function bs(t){t.clearMeasurements()}function vl(t){t.isLayoutDirty=!1}function xl(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function As(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Tl(t){t.resolveTargetDelta()}function Sl(t){t.calcProjection()}function Pl(t){t.resetSkewAndRotation()}function wl(t){t.removeLeadSnapshot()}function Ms(t,e,n){t.translate=C(e.translate,0,n),t.scale=C(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Vs(t,e,n,s){t.min=C(e.min,n.min,s),t.max=C(e.max,n.max,s)}function bl(t,e,n,s){Vs(t.x,e.x,n.x,s),Vs(t.y,e.y,n.y,s)}function Al(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const Ml={duration:.45,ease:[.4,0,.1,1]},Cs=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),ks=Cs("applewebkit/")&&!Cs("chrome/")?Math.round:U;function Ds(t){t.min=ks(t.min),t.max=ks(t.max)}function Vl(t){Ds(t.x),Ds(t.y)}function rr(t,e,n){return t==="position"||t==="preserve-aspect"&&!kc(Ps(e),Ps(n),.2)}function Cl(t){var e;return t!==t.root&&((e=t.scroll)==null?void 0:e.wasRoot)}const kl=ir({attachResizeListener:(t,e)=>Et(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),de={current:void 0},or=ir({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!de.current){const t=new kl({});t.mount(window),t.setOptions({layoutScroll:!0}),de.current=t}return de.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Dl={pan:{Feature:Hc},drag:{Feature:zc,ProjectionNode:or,MeasureLayout:Ji}};function Rs(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,o=s[i];o&&k.postRender(()=>o(e,jt(e)))}class Rl extends tt{mount(){const{current:e}=this.node;e&&(this.unmount=ia(e,(n,s)=>(Rs(this.node,s,"Start"),i=>Rs(this.node,i,"End"))))}unmount(){}}class El extends tt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Lt(Et(this.node.current,"focus",()=>this.onFocus()),Et(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Es(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),o=s[i];o&&k.postRender(()=>o(e,jt(e)))}class Ll extends tt{mount(){const{current:e}=this.node;e&&(this.unmount=ca(e,(n,s)=>(Es(this.node,s,"Start"),(i,{success:o})=>Es(this.node,i,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Le=new WeakMap,pe=new WeakMap,Fl=t=>{const e=Le.get(t.target);e&&e(t)},Bl=t=>{t.forEach(Fl)};function jl({root:t,...e}){const n=t||document;pe.has(n)||pe.set(n,{});const s=pe.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Bl,{root:t,...e})),s[i]}function Il(t,e,n){const s=jl(e);return Le.set(t,n),s.observe(t),()=>{Le.delete(t),s.unobserve(t)}}const Ol={some:0,all:1};class Nl extends tt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:Ol[i]},a=c=>{const{isIntersecting:u}=c;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:l,onViewportLeave:h}=this.node.getProps(),f=u?l:h;f&&f(c)};return Il(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(_l(e,n))&&this.startObserver()}unmount(){}}function _l({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const $l={inView:{Feature:Nl},tap:{Feature:Ll},focus:{Feature:El},hover:{Feature:Rl}},Ul={layout:{ProjectionNode:or,MeasureLayout:Ji}},Fe={current:null},ar={current:!1};function Wl(){if(ar.current=!0,!!Ie)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Fe.current=t.matches;t.addListener(e),e()}else Fe.current=!1}const Kl=new WeakMap;function zl(t,e,n){for(const s in e){const i=e[s],o=n[s];if(B(i))t.addValue(s,i);else if(B(o))t.addValue(s,dt(i,{owner:t}));else if(o!==i)if(t.hasValue(s)){const r=t.getValue(s);r.liveStyle===!0?r.jump(i):r.hasAnimated||r.set(i)}else{const r=t.getStaticValue(s);t.addValue(s,dt(r!==void 0?r:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const Ls=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Hl{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:o,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=N.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,k.render(this.render,!1,!0))};const{latestValues:c,renderState:u}=r;this.latestValues=c,this.baseTarget={...c},this.initialValues=n.initial?{...c}:{},this.renderState=u,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Qt(n),this.isVariantNode=Ci(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:l,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in h){const d=h[f];c[f]!==void 0&&B(d)&&d.set(c[f],!1)}}mount(e){this.current=e,Kl.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),ar.current||Wl(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Fe.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),J(this.notifyUpdate),J(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=gt.has(e);s&&this.onBindTransform&&this.onBindTransform();const i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&k.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),o(),r&&r(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in pt){const n=pt[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):R()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<Ls.length;s++){const i=Ls[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o="on"+i,r=e[o];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=zl(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=dt(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){let s=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return s!=null&&(typeof s=="string"&&(js(s)||Os(s))?s=parseFloat(s):!ha(s)&&Q.test(n)&&(s=xi(e,n)),this.setBaseTarget(e,B(s)?s.get():s)),B(s)?s.get():s}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var o;const{initial:n}=this.props;let s;if(typeof n=="string"||typeof n=="object"){const r=pn(this.props,n,(o=this.presenceContext)==null?void 0:o.custom);r&&(s=r[e])}if(n&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,e);return i!==void 0&&!B(i)?i:this.initialValues[e]!==void 0&&s===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new Ue),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class cr extends Hl{constructor(){super(...arguments),this.KeyframeResolver=Qo}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;B(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function lr(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const o in n)t.style.setProperty(o,n[o])}function Gl(t){return window.getComputedStyle(t)}class ql extends cr{constructor(){super(...arguments),this.type="html",this.renderInstance=lr}readValueFromInstance(e,n){var s;if(gt.has(n))return(s=this.projection)!=null&&s.isProjecting?Se(n):vo(e,n);{const i=Gl(e),o=(ze(n)?i.getPropertyValue(n):i[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(e,{transformPagePoint:n}){return qi(e,n)}build(e,n,s){hn(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return mn(e,n,s)}}const ur=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Xl(t,e,n,s){lr(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(ur.has(i)?i:un(i),e.attrs[i])}class Yl extends cr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=R}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(gt.has(n)){const s=vi(n);return s&&s.default||0}return n=ur.has(n)?n:un(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return Oi(e,n,s)}build(e,n,s){Fi(e,n,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(e,n,s,i){Xl(e,n,s,i)}mount(e){this.isSVGTag=ji(e.tagName),super.mount(e)}}const Zl=(t,e)=>dn(t)?new Yl(e):new ql(e,{allowProjection:t!==v.Fragment}),Jl=Xa({...Sc,...$l,...Dl,...Ul},Zl),Hu=Ta(Jl);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ql=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),tu=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,n,s)=>s?s.toUpperCase():n.toLowerCase()),Fs=t=>{const e=tu(t);return e.charAt(0).toUpperCase()+e.slice(1)},hr=(...t)=>t.filter((e,n,s)=>!!e&&e.trim()!==""&&s.indexOf(e)===n).join(" ").trim(),eu=t=>{for(const e in t)if(e.startsWith("aria-")||e==="role"||e==="title")return!0};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var nu={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const su=v.forwardRef(({color:t="currentColor",size:e=24,strokeWidth:n=2,absoluteStrokeWidth:s,className:i="",children:o,iconNode:r,...a},c)=>v.createElement("svg",{ref:c,...nu,width:e,height:e,stroke:t,strokeWidth:s?Number(n)*24/Number(e):n,className:hr("lucide",i),...!o&&!eu(a)&&{"aria-hidden":"true"},...a},[...r.map(([u,l])=>v.createElement(u,l)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A=(t,e)=>{const n=v.forwardRef(({className:s,...i},o)=>v.createElement(su,{ref:o,iconNode:e,className:hr(`lucide-${Ql(Fs(t))}`,`lucide-${t}`,s),...i}));return n.displayName=Fs(t),n};/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iu=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],Gu=A("arrow-right",iu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ru=[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]],qu=A("bot",ru);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ou=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],Xu=A("calendar",ou);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const au=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],Yu=A("chart-column",au);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cu=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],Zu=A("clock",cu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lu=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],Ju=A("copy",lu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uu=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],Qu=A("dollar-sign",uu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hu=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],th=A("download",hu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fu=[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]],eh=A("ellipsis-vertical",fu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const du=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]],nh=A("external-link",du);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pu=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],sh=A("eye",pu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mu=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],ih=A("funnel",mu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yu=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],rh=A("house",yu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gu=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],oh=A("mail",gu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vu=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],ah=A("menu",vu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xu=[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]],ch=A("message-circle",xu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tu=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],lh=A("message-square",Tu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Su=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],uh=A("moon",Su);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pu=[["path",{d:"M12.586 12.586 19 19",key:"ea5xo7"}],["path",{d:"M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z",key:"277e5u"}]],hh=A("mouse-pointer",Pu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wu=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],fh=A("pen-line",wu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bu=[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]],dh=A("percent",bu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Au=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],ph=A("phone",Au);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mu=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],mh=A("plus",Mu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vu=[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]],yh=A("power",Vu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cu=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],gh=A("refresh-cw",Cu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ku=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],vh=A("save",ku);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Du=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],xh=A("search",Du);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ru=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Th=A("settings",Ru);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eu=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Sh=A("star",Eu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lu=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],Ph=A("sun",Lu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fu=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],wh=A("target",Fu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bu=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],bh=A("trash-2",Bu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ju=[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]],Ah=A("trending-down",ju);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iu=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],Mh=A("trending-up",Iu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ou=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],Vh=A("user",Ou);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nu=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],Ch=A("users",Nu);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _u=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],kh=A("x",_u);export{Gu as A,qu as B,Xu as C,Qu as D,eh as E,ih as F,rh as H,hh as M,dh as P,gh as R,Th as S,Mh as T,Ch as U,kh as X,Ah as a,wh as b,Yu as c,fh as d,sh as e,yh as f,Zu as g,Vh as h,ph as i,q as j,oh as k,ch as l,Hu as m,Sh as n,nh as o,Ku as p,lh as q,Ju as r,bh as s,vh as t,th as u,mh as v,xh as w,Ph as x,uh as y,ah as z};

import{r as Le,g as $e}from"./vendor-Csw2ODfV.js";var s=Le();const tr=$e(s);var M={},oe;function Ie(){if(oe)return M;oe=1,Object.defineProperty(M,"__esModule",{value:!0}),M.parse=u,M.serialize=i;const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/,n=Object.prototype.toString,o=(()=>{const f=function(){};return f.prototype=Object.create(null),f})();function u(f,v){const d=new o,y=f.length;if(y<2)return d;const w=(v==null?void 0:v.decode)||m;let h=0;do{const g=f.indexOf("=",h);if(g===-1)break;const x=f.indexOf(";",h),R=x===-1?y:x;if(g>R){h=f.lastIndexOf(";",g-1)+1;continue}const C=c(f,h,g),N=l(f,g,C),I=f.slice(C,N);if(d[I]===void 0){let T=c(f,g+1,R),b=l(f,R,T);const F=w(f.slice(T,b));d[I]=F}h=R+1}while(h<y);return d}function c(f,v,d){do{const y=f.charCodeAt(v);if(y!==32&&y!==9)return v}while(++v<d);return d}function l(f,v,d){for(;v>d;){const y=f.charCodeAt(--v);if(y!==32&&y!==9)return v+1}return d}function i(f,v,d){const y=(d==null?void 0:d.encode)||encodeURIComponent;if(!e.test(f))throw new TypeError(`argument name is invalid: ${f}`);const w=y(v);if(!t.test(w))throw new TypeError(`argument val is invalid: ${v}`);let h=f+"="+w;if(!d)return h;if(d.maxAge!==void 0){if(!Number.isInteger(d.maxAge))throw new TypeError(`option maxAge is invalid: ${d.maxAge}`);h+="; Max-Age="+d.maxAge}if(d.domain){if(!r.test(d.domain))throw new TypeError(`option domain is invalid: ${d.domain}`);h+="; Domain="+d.domain}if(d.path){if(!a.test(d.path))throw new TypeError(`option path is invalid: ${d.path}`);h+="; Path="+d.path}if(d.expires){if(!p(d.expires)||!Number.isFinite(d.expires.valueOf()))throw new TypeError(`option expires is invalid: ${d.expires}`);h+="; Expires="+d.expires.toUTCString()}if(d.httpOnly&&(h+="; HttpOnly"),d.secure&&(h+="; Secure"),d.partitioned&&(h+="; Partitioned"),d.priority)switch(typeof d.priority=="string"?d.priority.toLowerCase():void 0){case"low":h+="; Priority=Low";break;case"medium":h+="; Priority=Medium";break;case"high":h+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${d.priority}`)}if(d.sameSite)switch(typeof d.sameSite=="string"?d.sameSite.toLowerCase():d.sameSite){case!0:case"strict":h+="; SameSite=Strict";break;case"lax":h+="; SameSite=Lax";break;case"none":h+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${d.sameSite}`)}return h}function m(f){if(f.indexOf("%")===-1)return f;try{return decodeURIComponent(f)}catch{return f}}function p(f){return n.call(f)==="[object Date]"}return M}Ie();var ie="popstate";function Fe(e={}){function t(a,n){let{pathname:o,search:u,hash:c}=a.location;return X("",{pathname:o,search:u,hash:c},n.state&&n.state.usr||null,n.state&&n.state.key||"default")}function r(a,n){return typeof n=="string"?n:U(n)}return De(t,r,null,e)}function E(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function S(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Te(){return Math.random().toString(36).substring(2,10)}function le(e,t){return{usr:e.state,key:e.key,idx:t}}function X(e,t,r=null,a){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?O(t):t,state:r,key:t&&t.key||a||Te()}}function U({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function O(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let a=e.indexOf("?");a>=0&&(t.search=e.substring(a),e=e.substring(0,a)),e&&(t.pathname=e)}return t}function De(e,t,r,a={}){let{window:n=document.defaultView,v5Compat:o=!1}=a,u=n.history,c="POP",l=null,i=m();i==null&&(i=0,u.replaceState({...u.state,idx:i},""));function m(){return(u.state||{idx:null}).idx}function p(){c="POP";let w=m(),h=w==null?null:w-i;i=w,l&&l({action:c,location:y.location,delta:h})}function f(w,h){c="PUSH";let g=X(y.location,w,h);i=m()+1;let x=le(g,i),R=y.createHref(g);try{u.pushState(x,"",R)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;n.location.assign(R)}o&&l&&l({action:c,location:y.location,delta:1})}function v(w,h){c="REPLACE";let g=X(y.location,w,h);i=m();let x=le(g,i),R=y.createHref(g);u.replaceState(x,"",R),o&&l&&l({action:c,location:y.location,delta:0})}function d(w){return Ne(w)}let y={get action(){return c},get location(){return e(n,u)},listen(w){if(l)throw new Error("A history only accepts one active listener");return n.addEventListener(ie,p),l=w,()=>{n.removeEventListener(ie,p),l=null}},createHref(w){return t(n,w)},createURL:d,encodeLocation(w){let h=d(w);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:f,replace:v,go(w){return u.go(w)}};return y}function Ne(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),E(r,"No window.location.(origin|href) available to create URL");let a=typeof e=="string"?e:U(e);return a=a.replace(/ $/,"%20"),!t&&a.startsWith("//")&&(a=r+a),new URL(a,r)}function de(e,t,r="/"){return Oe(e,t,r,!1)}function Oe(e,t,r,a){let n=typeof t=="string"?O(t):t,o=L(n.pathname||"/",r);if(o==null)return null;let u=fe(e);Ae(u);let c=null;for(let l=0;c==null&&l<u.length;++l){let i=Je(o);c=Ve(u[l],i,a)}return c}function fe(e,t=[],r=[],a=""){let n=(o,u,c)=>{let l={relativePath:c===void 0?o.path||"":c,caseSensitive:o.caseSensitive===!0,childrenIndex:u,route:o};l.relativePath.startsWith("/")&&(E(l.relativePath.startsWith(a),`Absolute route path "${l.relativePath}" nested under path "${a}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),l.relativePath=l.relativePath.slice(a.length));let i=k([a,l.relativePath]),m=r.concat(l);o.children&&o.children.length>0&&(E(o.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${i}".`),fe(o.children,t,m,i)),!(o.path==null&&!o.index)&&t.push({path:i,score:ze(i,o.index),routesMeta:m})};return e.forEach((o,u)=>{var c;if(o.path===""||!((c=o.path)!=null&&c.includes("?")))n(o,u);else for(let l of he(o.path))n(o,u,l)}),t}function he(e){let t=e.split("/");if(t.length===0)return[];let[r,...a]=t,n=r.endsWith("?"),o=r.replace(/\?$/,"");if(a.length===0)return n?[o,""]:[o];let u=he(a.join("/")),c=[];return c.push(...u.map(l=>l===""?o:[o,l].join("/"))),n&&c.push(...u),c.map(l=>e.startsWith("/")&&l===""?"/":l)}function Ae(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:je(t.routesMeta.map(a=>a.childrenIndex),r.routesMeta.map(a=>a.childrenIndex)))}var Me=/^:[\w-]+$/,Be=3,Ue=2,He=1,We=10,_e=-2,ue=e=>e==="*";function ze(e,t){let r=e.split("/"),a=r.length;return r.some(ue)&&(a+=_e),t&&(a+=Ue),r.filter(n=>!ue(n)).reduce((n,o)=>n+(Me.test(o)?Be:o===""?He:We),a)}function je(e,t){return e.length===t.length&&e.slice(0,-1).every((a,n)=>a===t[n])?e[e.length-1]-t[t.length-1]:0}function Ve(e,t,r=!1){let{routesMeta:a}=e,n={},o="/",u=[];for(let c=0;c<a.length;++c){let l=a[c],i=c===a.length-1,m=o==="/"?t:t.slice(o.length)||"/",p=K({path:l.relativePath,caseSensitive:l.caseSensitive,end:i},m),f=l.route;if(!p&&i&&r&&!a[a.length-1].route.index&&(p=K({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},m)),!p)return null;Object.assign(n,p.params),u.push({params:n,pathname:k([o,p.pathname]),pathnameBase:Xe(k([o,p.pathnameBase])),route:f}),p.pathnameBase!=="/"&&(o=k([o,p.pathnameBase]))}return u}function K(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,a]=Ke(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let o=n[0],u=o.replace(/(.)\/+$/,"$1"),c=n.slice(1);return{params:a.reduce((i,{paramName:m,isOptional:p},f)=>{if(m==="*"){let d=c[f]||"";u=o.slice(0,o.length-d.length).replace(/(.)\/+$/,"$1")}const v=c[f];return p&&!v?i[m]=void 0:i[m]=(v||"").replace(/%2F/g,"/"),i},{}),pathname:o,pathnameBase:u,pattern:e}}function Ke(e,t=!1,r=!0){S(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let a=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(u,c,l)=>(a.push({paramName:c,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(a.push({paramName:"*"}),n+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":e!==""&&e!=="/"&&(n+="(?:(?=\\/|$))"),[new RegExp(n,t?void 0:"i"),a]}function Je(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return S(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function L(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,a=e.charAt(r);return a&&a!=="/"?null:e.slice(r)||"/"}function Ye(e,t="/"){let{pathname:r,search:a="",hash:n=""}=typeof e=="string"?O(e):e;return{pathname:r?r.startsWith("/")?r:qe(r,t):t,search:Qe(a),hash:Ze(n)}}function qe(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(n=>{n===".."?r.length>1&&r.pop():n!=="."&&r.push(n)}),r.length>1?r.join("/"):"/"}function q(e,t,r,a){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(a)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Ge(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function me(e){let t=Ge(e);return t.map((r,a)=>a===t.length-1?r.pathname:r.pathnameBase)}function pe(e,t,r,a=!1){let n;typeof e=="string"?n=O(e):(n={...e},E(!n.pathname||!n.pathname.includes("?"),q("?","pathname","search",n)),E(!n.pathname||!n.pathname.includes("#"),q("#","pathname","hash",n)),E(!n.search||!n.search.includes("#"),q("#","search","hash",n)));let o=e===""||n.pathname==="",u=o?"/":n.pathname,c;if(u==null)c=r;else{let p=t.length-1;if(!a&&u.startsWith("..")){let f=u.split("/");for(;f[0]==="..";)f.shift(),p-=1;n.pathname=f.join("/")}c=p>=0?t[p]:"/"}let l=Ye(n,c),i=u&&u!=="/"&&u.endsWith("/"),m=(o||u===".")&&r.endsWith("/");return!l.pathname.endsWith("/")&&(i||m)&&(l.pathname+="/"),l}var k=e=>e.join("/").replace(/\/\/+/g,"/"),Xe=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Qe=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ze=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function et(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var ye=["POST","PUT","PATCH","DELETE"];new Set(ye);var tt=["GET",...ye];new Set(tt);var A=s.createContext(null);A.displayName="DataRouter";var J=s.createContext(null);J.displayName="DataRouterState";var ge=s.createContext({isTransitioning:!1});ge.displayName="ViewTransition";var rt=s.createContext(new Map);rt.displayName="Fetchers";var nt=s.createContext(null);nt.displayName="Await";var P=s.createContext(null);P.displayName="Navigation";var H=s.createContext(null);H.displayName="Location";var $=s.createContext({outlet:null,matches:[],isDataRoute:!1});$.displayName="Route";var Z=s.createContext(null);Z.displayName="RouteError";function at(e,{relative:t}={}){E(W(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:a}=s.useContext(P),{hash:n,pathname:o,search:u}=_(e,{relative:t}),c=o;return r!=="/"&&(c=o==="/"?r:k([r,o])),a.createHref({pathname:c,search:u,hash:n})}function W(){return s.useContext(H)!=null}function D(){return E(W(),"useLocation() may be used only in the context of a <Router> component."),s.useContext(H).location}var ve="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function we(e){s.useContext(P).static||s.useLayoutEffect(e)}function ot(){let{isDataRoute:e}=s.useContext($);return e?vt():it()}function it(){E(W(),"useNavigate() may be used only in the context of a <Router> component.");let e=s.useContext(A),{basename:t,navigator:r}=s.useContext(P),{matches:a}=s.useContext($),{pathname:n}=D(),o=JSON.stringify(me(a)),u=s.useRef(!1);return we(()=>{u.current=!0}),s.useCallback((l,i={})=>{if(S(u.current,ve),!u.current)return;if(typeof l=="number"){r.go(l);return}let m=pe(l,JSON.parse(o),n,i.relative==="path");e==null&&t!=="/"&&(m.pathname=m.pathname==="/"?t:k([t,m.pathname])),(i.replace?r.replace:r.push)(m,i.state,i)},[t,r,o,n,e])}s.createContext(null);function _(e,{relative:t}={}){let{matches:r}=s.useContext($),{pathname:a}=D(),n=JSON.stringify(me(r));return s.useMemo(()=>pe(e,JSON.parse(n),a,t==="path"),[e,n,a,t])}function lt(e,t){return xe(e,t)}function xe(e,t,r,a){var h;E(W(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:n}=s.useContext(P),{matches:o}=s.useContext($),u=o[o.length-1],c=u?u.params:{},l=u?u.pathname:"/",i=u?u.pathnameBase:"/",m=u&&u.route;{let g=m&&m.path||"";Ee(l,!m||g.endsWith("*")||g.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${l}" (under <Route path="${g}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${g}"> to <Route path="${g==="/"?"*":`${g}/*`}">.`)}let p=D(),f;if(t){let g=typeof t=="string"?O(t):t;E(i==="/"||((h=g.pathname)==null?void 0:h.startsWith(i)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${i}" but pathname "${g.pathname}" was given in the \`location\` prop.`),f=g}else f=p;let v=f.pathname||"/",d=v;if(i!=="/"){let g=i.replace(/^\//,"").split("/");d="/"+v.replace(/^\//,"").split("/").slice(g.length).join("/")}let y=de(e,{pathname:d});S(m||y!=null,`No routes matched location "${f.pathname}${f.search}${f.hash}" `),S(y==null||y[y.length-1].route.element!==void 0||y[y.length-1].route.Component!==void 0||y[y.length-1].route.lazy!==void 0,`Matched leaf route at location "${f.pathname}${f.search}${f.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let w=ft(y&&y.map(g=>Object.assign({},g,{params:Object.assign({},c,g.params),pathname:k([i,n.encodeLocation?n.encodeLocation(g.pathname).pathname:g.pathname]),pathnameBase:g.pathnameBase==="/"?i:k([i,n.encodeLocation?n.encodeLocation(g.pathnameBase).pathname:g.pathnameBase])})),o,r,a);return t&&w?s.createElement(H.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...f},navigationType:"POP"}},w):w}function ut(){let e=gt(),t=et(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",n={padding:"0.5rem",backgroundColor:a},o={padding:"2px 4px",backgroundColor:a},u=null;return console.error("Error handled by React Router default ErrorBoundary:",e),u=s.createElement(s.Fragment,null,s.createElement("p",null,"💿 Hey developer 👋"),s.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",s.createElement("code",{style:o},"ErrorBoundary")," or"," ",s.createElement("code",{style:o},"errorElement")," prop on your route.")),s.createElement(s.Fragment,null,s.createElement("h2",null,"Unexpected Application Error!"),s.createElement("h3",{style:{fontStyle:"italic"}},t),r?s.createElement("pre",{style:n},r):null,u)}var st=s.createElement(ut,null),ct=class extends s.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?s.createElement($.Provider,{value:this.props.routeContext},s.createElement(Z.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function dt({routeContext:e,match:t,children:r}){let a=s.useContext(A);return a&&a.static&&a.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=t.route.id),s.createElement($.Provider,{value:e},r)}function ft(e,t=[],r=null,a=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let n=e,o=r==null?void 0:r.errors;if(o!=null){let l=n.findIndex(i=>i.route.id&&(o==null?void 0:o[i.route.id])!==void 0);E(l>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),n=n.slice(0,Math.min(n.length,l+1))}let u=!1,c=-1;if(r)for(let l=0;l<n.length;l++){let i=n[l];if((i.route.HydrateFallback||i.route.hydrateFallbackElement)&&(c=l),i.route.id){let{loaderData:m,errors:p}=r,f=i.route.loader&&!m.hasOwnProperty(i.route.id)&&(!p||p[i.route.id]===void 0);if(i.route.lazy||f){u=!0,c>=0?n=n.slice(0,c+1):n=[n[0]];break}}}return n.reduceRight((l,i,m)=>{let p,f=!1,v=null,d=null;r&&(p=o&&i.route.id?o[i.route.id]:void 0,v=i.route.errorElement||st,u&&(c<0&&m===0?(Ee("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),f=!0,d=null):c===m&&(f=!0,d=i.route.hydrateFallbackElement||null)));let y=t.concat(n.slice(0,m+1)),w=()=>{let h;return p?h=v:f?h=d:i.route.Component?h=s.createElement(i.route.Component,null):i.route.element?h=i.route.element:h=l,s.createElement(dt,{match:i,routeContext:{outlet:l,matches:y,isDataRoute:r!=null},children:h})};return r&&(i.route.ErrorBoundary||i.route.errorElement||m===0)?s.createElement(ct,{location:r.location,revalidation:r.revalidation,component:v,error:p,children:w(),routeContext:{outlet:null,matches:y,isDataRoute:!0}}):w()},null)}function ee(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ht(e){let t=s.useContext(A);return E(t,ee(e)),t}function mt(e){let t=s.useContext(J);return E(t,ee(e)),t}function pt(e){let t=s.useContext($);return E(t,ee(e)),t}function te(e){let t=pt(e),r=t.matches[t.matches.length-1];return E(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function yt(){return te("useRouteId")}function gt(){var a;let e=s.useContext(Z),t=mt("useRouteError"),r=te("useRouteError");return e!==void 0?e:(a=t.errors)==null?void 0:a[r]}function vt(){let{router:e}=ht("useNavigate"),t=te("useNavigate"),r=s.useRef(!1);return we(()=>{r.current=!0}),s.useCallback(async(n,o={})=>{S(r.current,ve),r.current&&(typeof n=="number"?e.navigate(n):await e.navigate(n,{fromRouteId:t,...o}))},[e,t])}var se={};function Ee(e,t,r){!t&&!se[e]&&(se[e]=!0,S(!1,r))}s.memo(wt);function wt({routes:e,future:t,state:r}){return xe(e,void 0,r,t)}function xt(e){E(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Et({basename:e="/",children:t=null,location:r,navigationType:a="POP",navigator:n,static:o=!1}){E(!W(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let u=e.replace(/^\/*/,"/"),c=s.useMemo(()=>({basename:u,navigator:n,static:o,future:{}}),[u,n,o]);typeof r=="string"&&(r=O(r));let{pathname:l="/",search:i="",hash:m="",state:p=null,key:f="default"}=r,v=s.useMemo(()=>{let d=L(l,u);return d==null?null:{location:{pathname:d,search:i,hash:m,state:p,key:f},navigationType:a}},[u,l,i,m,p,f,a]);return S(v!=null,`<Router basename="${u}"> is not able to match the URL "${l}${i}${m}" because it does not start with the basename, so the <Router> won't render anything.`),v==null?null:s.createElement(P.Provider,{value:c},s.createElement(H.Provider,{children:t,value:v}))}function rr({children:e,location:t}){return lt(Q(e),t)}function Q(e,t=[]){let r=[];return s.Children.forEach(e,(a,n)=>{if(!s.isValidElement(a))return;let o=[...t,n];if(a.type===s.Fragment){r.push.apply(r,Q(a.props.children,o));return}E(a.type===xt,`[${typeof a.type=="string"?a.type:a.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),E(!a.props.index||!a.props.children,"An index route cannot have child routes.");let u={id:a.props.id||o.join("-"),caseSensitive:a.props.caseSensitive,element:a.props.element,Component:a.props.Component,index:a.props.index,path:a.props.path,loader:a.props.loader,action:a.props.action,hydrateFallbackElement:a.props.hydrateFallbackElement,HydrateFallback:a.props.HydrateFallback,errorElement:a.props.errorElement,ErrorBoundary:a.props.ErrorBoundary,hasErrorBoundary:a.props.hasErrorBoundary===!0||a.props.ErrorBoundary!=null||a.props.errorElement!=null,shouldRevalidate:a.props.shouldRevalidate,handle:a.props.handle,lazy:a.props.lazy};a.props.children&&(u.children=Q(a.props.children,o)),r.push(u)}),r}var j="get",V="application/x-www-form-urlencoded";function Y(e){return e!=null&&typeof e.tagName=="string"}function Rt(e){return Y(e)&&e.tagName.toLowerCase()==="button"}function Ct(e){return Y(e)&&e.tagName.toLowerCase()==="form"}function bt(e){return Y(e)&&e.tagName.toLowerCase()==="input"}function St(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Pt(e,t){return e.button===0&&(!t||t==="_self")&&!St(e)}var z=null;function kt(){if(z===null)try{new FormData(document.createElement("form"),0),z=!1}catch{z=!0}return z}var Lt=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function G(e){return e!=null&&!Lt.has(e)?(S(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${V}"`),null):e}function $t(e,t){let r,a,n,o,u;if(Ct(e)){let c=e.getAttribute("action");a=c?L(c,t):null,r=e.getAttribute("method")||j,n=G(e.getAttribute("enctype"))||V,o=new FormData(e)}else if(Rt(e)||bt(e)&&(e.type==="submit"||e.type==="image")){let c=e.form;if(c==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||c.getAttribute("action");if(a=l?L(l,t):null,r=e.getAttribute("formmethod")||c.getAttribute("method")||j,n=G(e.getAttribute("formenctype"))||G(c.getAttribute("enctype"))||V,o=new FormData(c,e),!kt()){let{name:i,type:m,value:p}=e;if(m==="image"){let f=i?`${i}.`:"";o.append(`${f}x`,"0"),o.append(`${f}y`,"0")}else i&&o.append(i,p)}}else{if(Y(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=j,a=null,n=V,u=e}return o&&n==="text/plain"&&(u=o,o=void 0),{action:a,method:r.toLowerCase(),encType:n,formData:o,body:u}}function re(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function It(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Ft(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Tt(e,t,r){let a=await Promise.all(e.map(async n=>{let o=t.routes[n.route.id];if(o){let u=await It(o,r);return u.links?u.links():[]}return[]}));return At(a.flat(1).filter(Ft).filter(n=>n.rel==="stylesheet"||n.rel==="preload").map(n=>n.rel==="stylesheet"?{...n,rel:"prefetch",as:"style"}:{...n,rel:"prefetch"}))}function ce(e,t,r,a,n,o){let u=(l,i)=>r[i]?l.route.id!==r[i].route.id:!0,c=(l,i)=>{var m;return r[i].pathname!==l.pathname||((m=r[i].route.path)==null?void 0:m.endsWith("*"))&&r[i].params["*"]!==l.params["*"]};return o==="assets"?t.filter((l,i)=>u(l,i)||c(l,i)):o==="data"?t.filter((l,i)=>{var p;let m=a.routes[l.route.id];if(!m||!m.hasLoader)return!1;if(u(l,i)||c(l,i))return!0;if(l.route.shouldRevalidate){let f=l.route.shouldRevalidate({currentUrl:new URL(n.pathname+n.search+n.hash,window.origin),currentParams:((p=r[0])==null?void 0:p.params)||{},nextUrl:new URL(e,window.origin),nextParams:l.params,defaultShouldRevalidate:!0});if(typeof f=="boolean")return f}return!0}):[]}function Dt(e,t,{includeHydrateFallback:r}={}){return Nt(e.map(a=>{let n=t.routes[a.route.id];if(!n)return[];let o=[n.module];return n.clientActionModule&&(o=o.concat(n.clientActionModule)),n.clientLoaderModule&&(o=o.concat(n.clientLoaderModule)),r&&n.hydrateFallbackModule&&(o=o.concat(n.hydrateFallbackModule)),n.imports&&(o=o.concat(n.imports)),o}).flat(1))}function Nt(e){return[...new Set(e)]}function Ot(e){let t={},r=Object.keys(e).sort();for(let a of r)t[a]=e[a];return t}function At(e,t){let r=new Set;return new Set(t),e.reduce((a,n)=>{let o=JSON.stringify(Ot(n));return r.has(o)||(r.add(o),a.push({key:o,link:n})),a},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Mt=new Set([100,101,204,205]);function Bt(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&L(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function Re(){let e=s.useContext(A);return re(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Ut(){let e=s.useContext(J);return re(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var ne=s.createContext(void 0);ne.displayName="FrameworkContext";function Ce(){let e=s.useContext(ne);return re(e,"You must render this element inside a <HydratedRouter> element"),e}function Ht(e,t){let r=s.useContext(ne),[a,n]=s.useState(!1),[o,u]=s.useState(!1),{onFocus:c,onBlur:l,onMouseEnter:i,onMouseLeave:m,onTouchStart:p}=t,f=s.useRef(null);s.useEffect(()=>{if(e==="render"&&u(!0),e==="viewport"){let y=h=>{h.forEach(g=>{u(g.isIntersecting)})},w=new IntersectionObserver(y,{threshold:.5});return f.current&&w.observe(f.current),()=>{w.disconnect()}}},[e]),s.useEffect(()=>{if(a){let y=setTimeout(()=>{u(!0)},100);return()=>{clearTimeout(y)}}},[a]);let v=()=>{n(!0)},d=()=>{n(!1),u(!1)};return r?e!=="intent"?[o,f,{}]:[o,f,{onFocus:B(c,v),onBlur:B(l,d),onMouseEnter:B(i,v),onMouseLeave:B(m,d),onTouchStart:B(p,v)}]:[!1,f,{}]}function B(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function Wt({page:e,...t}){let{router:r}=Re(),a=s.useMemo(()=>de(r.routes,e,r.basename),[r.routes,e,r.basename]);return a?s.createElement(zt,{page:e,matches:a,...t}):null}function _t(e){let{manifest:t,routeModules:r}=Ce(),[a,n]=s.useState([]);return s.useEffect(()=>{let o=!1;return Tt(e,t,r).then(u=>{o||n(u)}),()=>{o=!0}},[e,t,r]),a}function zt({page:e,matches:t,...r}){let a=D(),{manifest:n,routeModules:o}=Ce(),{basename:u}=Re(),{loaderData:c,matches:l}=Ut(),i=s.useMemo(()=>ce(e,t,l,n,a,"data"),[e,t,l,n,a]),m=s.useMemo(()=>ce(e,t,l,n,a,"assets"),[e,t,l,n,a]),p=s.useMemo(()=>{if(e===a.pathname+a.search+a.hash)return[];let d=new Set,y=!1;if(t.forEach(h=>{var x;let g=n.routes[h.route.id];!g||!g.hasLoader||(!i.some(R=>R.route.id===h.route.id)&&h.route.id in c&&((x=o[h.route.id])!=null&&x.shouldRevalidate)||g.hasClientLoader?y=!0:d.add(h.route.id))}),d.size===0)return[];let w=Bt(e,u);return y&&d.size>0&&w.searchParams.set("_routes",t.filter(h=>d.has(h.route.id)).map(h=>h.route.id).join(",")),[w.pathname+w.search]},[u,c,a,n,i,t,e,o]),f=s.useMemo(()=>Dt(m,n),[m,n]),v=_t(m);return s.createElement(s.Fragment,null,p.map(d=>s.createElement("link",{key:d,rel:"prefetch",as:"fetch",href:d,...r})),f.map(d=>s.createElement("link",{key:d,rel:"modulepreload",href:d,...r})),v.map(({key:d,link:y})=>s.createElement("link",{key:d,...y})))}function jt(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var be=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{be&&(window.__reactRouterVersion="7.6.1")}catch{}function nr({basename:e,children:t,window:r}){let a=s.useRef();a.current==null&&(a.current=Fe({window:r,v5Compat:!0}));let n=a.current,[o,u]=s.useState({action:n.action,location:n.location}),c=s.useCallback(l=>{s.startTransition(()=>u(l))},[u]);return s.useLayoutEffect(()=>n.listen(c),[n,c]),s.createElement(Et,{basename:e,children:t,location:o.location,navigationType:o.action,navigator:n})}var Se=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Pe=s.forwardRef(function({onClick:t,discover:r="render",prefetch:a="none",relative:n,reloadDocument:o,replace:u,state:c,target:l,to:i,preventScrollReset:m,viewTransition:p,...f},v){let{basename:d}=s.useContext(P),y=typeof i=="string"&&Se.test(i),w,h=!1;if(typeof i=="string"&&y&&(w=i,be))try{let b=new URL(window.location.href),F=i.startsWith("//")?new URL(b.protocol+i):new URL(i),ae=L(F.pathname,d);F.origin===b.origin&&ae!=null?i=ae+F.search+F.hash:h=!0}catch{S(!1,`<Link to="${i}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let g=at(i,{relative:n}),[x,R,C]=Ht(a,f),N=Yt(i,{replace:u,state:c,target:l,preventScrollReset:m,relative:n,viewTransition:p});function I(b){t&&t(b),b.defaultPrevented||N(b)}let T=s.createElement("a",{...f,...C,href:w||g,onClick:h||o?t:I,ref:jt(v,R),target:l,"data-discover":!y&&r==="render"?"true":void 0});return x&&!y?s.createElement(s.Fragment,null,T,s.createElement(Wt,{page:g})):T});Pe.displayName="Link";var Vt=s.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:a="",end:n=!1,style:o,to:u,viewTransition:c,children:l,...i},m){let p=_(u,{relative:i.relative}),f=D(),v=s.useContext(J),{navigator:d,basename:y}=s.useContext(P),w=v!=null&&Zt(p)&&c===!0,h=d.encodeLocation?d.encodeLocation(p).pathname:p.pathname,g=f.pathname,x=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;r||(g=g.toLowerCase(),x=x?x.toLowerCase():null,h=h.toLowerCase()),x&&y&&(x=L(x,y)||x);const R=h!=="/"&&h.endsWith("/")?h.length-1:h.length;let C=g===h||!n&&g.startsWith(h)&&g.charAt(R)==="/",N=x!=null&&(x===h||!n&&x.startsWith(h)&&x.charAt(h.length)==="/"),I={isActive:C,isPending:N,isTransitioning:w},T=C?t:void 0,b;typeof a=="function"?b=a(I):b=[a,C?"active":null,N?"pending":null,w?"transitioning":null].filter(Boolean).join(" ");let F=typeof o=="function"?o(I):o;return s.createElement(Pe,{...i,"aria-current":T,className:b,ref:m,style:F,to:u,viewTransition:c},typeof l=="function"?l(I):l)});Vt.displayName="NavLink";var Kt=s.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:a,replace:n,state:o,method:u=j,action:c,onSubmit:l,relative:i,preventScrollReset:m,viewTransition:p,...f},v)=>{let d=Xt(),y=Qt(c,{relative:i}),w=u.toLowerCase()==="get"?"get":"post",h=typeof c=="string"&&Se.test(c),g=x=>{if(l&&l(x),x.defaultPrevented)return;x.preventDefault();let R=x.nativeEvent.submitter,C=(R==null?void 0:R.getAttribute("formmethod"))||u;d(R||x.currentTarget,{fetcherKey:t,method:C,navigate:r,replace:n,state:o,relative:i,preventScrollReset:m,viewTransition:p})};return s.createElement("form",{ref:v,method:w,action:y,onSubmit:a?l:g,...f,"data-discover":!h&&e==="render"?"true":void 0})});Kt.displayName="Form";function Jt(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ke(e){let t=s.useContext(A);return E(t,Jt(e)),t}function Yt(e,{target:t,replace:r,state:a,preventScrollReset:n,relative:o,viewTransition:u}={}){let c=ot(),l=D(),i=_(e,{relative:o});return s.useCallback(m=>{if(Pt(m,t)){m.preventDefault();let p=r!==void 0?r:U(l)===U(i);c(e,{replace:p,state:a,preventScrollReset:n,relative:o,viewTransition:u})}},[l,c,i,r,a,t,e,n,o,u])}var qt=0,Gt=()=>`__${String(++qt)}__`;function Xt(){let{router:e}=ke("useSubmit"),{basename:t}=s.useContext(P),r=yt();return s.useCallback(async(a,n={})=>{let{action:o,method:u,encType:c,formData:l,body:i}=$t(a,t);if(n.navigate===!1){let m=n.fetcherKey||Gt();await e.fetch(m,r,n.action||o,{preventScrollReset:n.preventScrollReset,formData:l,body:i,formMethod:n.method||u,formEncType:n.encType||c,flushSync:n.flushSync})}else await e.navigate(n.action||o,{preventScrollReset:n.preventScrollReset,formData:l,body:i,formMethod:n.method||u,formEncType:n.encType||c,replace:n.replace,state:n.state,fromRouteId:r,flushSync:n.flushSync,viewTransition:n.viewTransition})},[e,t,r])}function Qt(e,{relative:t}={}){let{basename:r}=s.useContext(P),a=s.useContext($);E(a,"useFormAction must be used inside a RouteContext");let[n]=a.matches.slice(-1),o={..._(e||".",{relative:t})},u=D();if(e==null){o.search=u.search;let c=new URLSearchParams(o.search),l=c.getAll("index");if(l.some(m=>m==="")){c.delete("index"),l.filter(p=>p).forEach(p=>c.append("index",p));let m=c.toString();o.search=m?`?${m}`:""}}return(!e||e===".")&&n.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:k([r,o.pathname])),U(o)}function Zt(e,t={}){let r=s.useContext(ge);E(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=ke("useViewTransitionState"),n=_(e,{relative:t.relative});if(!r.isTransitioning)return!1;let o=L(r.currentLocation.pathname,a)||r.currentLocation.pathname,u=L(r.nextLocation.pathname,a)||r.nextLocation.pathname;return K(n.pathname,u)!=null||K(n.pathname,o)!=null}[...Mt];export{nr as B,Pe as L,tr as R,D as a,rr as b,xt as c,s as r,ot as u};

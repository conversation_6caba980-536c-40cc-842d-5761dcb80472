import{j as o,m as he,M as Ly,T as Yy,a as ss,U as rl,E as Gy,P as d0,b as Xy,S as qi,c as Zy,d as ki,C as Qy,D as Vy,e as Ky,f as Jy,A as km,g as Um,R as yc,h as m0,i as rs,X as zc,k as h0,l as _m,n as Wy,o as Fy,p as $y,q as Ui,r as Mc,s as Py,t as Iy,u as eb,v as tb,w as ab,x as lb,F as nb,H as g0,y as y0,z as b0,B as sb}from"./ui-DA8BJpkY.js";import{r as ie,u as p0,L as x0,B as ib,R as rb,a as ub,b as es}from"./router-CocWSbQb.js";import{r as cb,a as ob}from"./vendor-Csw2ODfV.js";import{c as fb,R as Oc,P as db,a as mb,C as hb,T as Dc,L as v0,b as S0,X as j0,Y as bc,d as pc,e as gb}from"./charts-BEMEAT8Z.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))u(d);new MutationObserver(d=>{for(const h of d)if(h.type==="childList")for(const g of h.addedNodes)g.tagName==="LINK"&&g.rel==="modulepreload"&&u(g)}).observe(document,{childList:!0,subtree:!0});function c(d){const h={};return d.integrity&&(h.integrity=d.integrity),d.referrerPolicy&&(h.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?h.credentials="include":d.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function u(d){if(d.ep)return;d.ep=!0;const h=c(d);fetch(d.href,h)}})();var rc={exports:{}},ts={},uc={exports:{}},cc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bm;function yb(){return Bm||(Bm=1,function(s){function r(v,C){var U=v.length;v.push(C);e:for(;0<U;){var se=U-1>>>1,ae=v[se];if(0<d(ae,C))v[se]=C,v[U]=ae,U=se;else break e}}function c(v){return v.length===0?null:v[0]}function u(v){if(v.length===0)return null;var C=v[0],U=v.pop();if(U!==C){v[0]=U;e:for(var se=0,ae=v.length,Ye=ae>>>1;se<Ye;){var je=2*(se+1)-1,F=v[je],oe=je+1,Pe=v[oe];if(0>d(F,U))oe<ae&&0>d(Pe,F)?(v[se]=Pe,v[oe]=U,se=oe):(v[se]=F,v[je]=U,se=je);else if(oe<ae&&0>d(Pe,U))v[se]=Pe,v[oe]=U,se=oe;else break e}}return C}function d(v,C){var U=v.sortIndex-C.sortIndex;return U!==0?U:v.id-C.id}if(s.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;s.unstable_now=function(){return h.now()}}else{var g=Date,b=g.now();s.unstable_now=function(){return g.now()-b}}var N=[],j=[],S=1,D=null,k=3,X=!1,_=!1,B=!1,q=!1,Q=typeof setTimeout=="function"?setTimeout:null,P=typeof clearTimeout=="function"?clearTimeout:null,K=typeof setImmediate<"u"?setImmediate:null;function de(v){for(var C=c(j);C!==null;){if(C.callback===null)u(j);else if(C.startTime<=v)u(j),C.sortIndex=C.expirationTime,r(N,C);else break;C=c(j)}}function re(v){if(B=!1,de(v),!_)if(c(N)!==null)_=!0,ce||(ce=!0,_e());else{var C=c(j);C!==null&&Le(re,C.startTime-v)}}var ce=!1,Se=-1,G=5,Te=-1;function Et(){return q?!0:!(s.unstable_now()-Te<G)}function ge(){if(q=!1,ce){var v=s.unstable_now();Te=v;var C=!0;try{e:{_=!1,B&&(B=!1,P(Se),Se=-1),X=!0;var U=k;try{t:{for(de(v),D=c(N);D!==null&&!(D.expirationTime>v&&Et());){var se=D.callback;if(typeof se=="function"){D.callback=null,k=D.priorityLevel;var ae=se(D.expirationTime<=v);if(v=s.unstable_now(),typeof ae=="function"){D.callback=ae,de(v),C=!0;break t}D===c(N)&&u(N),de(v)}else u(N);D=c(N)}if(D!==null)C=!0;else{var Ye=c(j);Ye!==null&&Le(re,Ye.startTime-v),C=!1}}break e}finally{D=null,k=U,X=!1}C=void 0}}finally{C?_e():ce=!1}}}var _e;if(typeof K=="function")_e=function(){K(ge)};else if(typeof MessageChannel<"u"){var Vt=new MessageChannel,zt=Vt.port2;Vt.port1.onmessage=ge,_e=function(){zt.postMessage(null)}}else _e=function(){Q(ge,0)};function Le(v,C){Se=Q(function(){v(s.unstable_now())},C)}s.unstable_IdlePriority=5,s.unstable_ImmediatePriority=1,s.unstable_LowPriority=4,s.unstable_NormalPriority=3,s.unstable_Profiling=null,s.unstable_UserBlockingPriority=2,s.unstable_cancelCallback=function(v){v.callback=null},s.unstable_forceFrameRate=function(v){0>v||125<v?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<v?Math.floor(1e3/v):5},s.unstable_getCurrentPriorityLevel=function(){return k},s.unstable_next=function(v){switch(k){case 1:case 2:case 3:var C=3;break;default:C=k}var U=k;k=C;try{return v()}finally{k=U}},s.unstable_requestPaint=function(){q=!0},s.unstable_runWithPriority=function(v,C){switch(v){case 1:case 2:case 3:case 4:case 5:break;default:v=3}var U=k;k=v;try{return C()}finally{k=U}},s.unstable_scheduleCallback=function(v,C,U){var se=s.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?se+U:se):U=se,v){case 1:var ae=-1;break;case 2:ae=250;break;case 5:ae=1073741823;break;case 4:ae=1e4;break;default:ae=5e3}return ae=U+ae,v={id:S++,callback:C,priorityLevel:v,startTime:U,expirationTime:ae,sortIndex:-1},U>se?(v.sortIndex=U,r(j,v),c(N)===null&&v===c(j)&&(B?(P(Se),Se=-1):B=!0,Le(re,U-se))):(v.sortIndex=ae,r(N,v),_||X||(_=!0,ce||(ce=!0,_e()))),v},s.unstable_shouldYield=Et,s.unstable_wrapCallback=function(v){var C=k;return function(){var U=k;k=C;try{return v.apply(this,arguments)}finally{k=U}}}}(cc)),cc}var qm;function bb(){return qm||(qm=1,uc.exports=yb()),uc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hm;function pb(){if(Hm)return ts;Hm=1;var s=bb(),r=cb(),c=ob();function u(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function h(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function g(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function b(e){if(h(e)!==e)throw Error(u(188))}function N(e){var t=e.alternate;if(!t){if(t=h(e),t===null)throw Error(u(188));return t!==e?null:e}for(var a=e,l=t;;){var n=a.return;if(n===null)break;var i=n.alternate;if(i===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===a)return b(n),e;if(i===l)return b(n),t;i=i.sibling}throw Error(u(188))}if(a.return!==l.return)a=n,l=i;else{for(var f=!1,m=n.child;m;){if(m===a){f=!0,a=n,l=i;break}if(m===l){f=!0,l=n,a=i;break}m=m.sibling}if(!f){for(m=i.child;m;){if(m===a){f=!0,a=i,l=n;break}if(m===l){f=!0,l=i,a=n;break}m=m.sibling}if(!f)throw Error(u(189))}}if(a.alternate!==l)throw Error(u(190))}if(a.tag!==3)throw Error(u(188));return a.stateNode.current===a?e:t}function j(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=j(e),t!==null)return t;e=e.sibling}return null}var S=Object.assign,D=Symbol.for("react.element"),k=Symbol.for("react.transitional.element"),X=Symbol.for("react.portal"),_=Symbol.for("react.fragment"),B=Symbol.for("react.strict_mode"),q=Symbol.for("react.profiler"),Q=Symbol.for("react.provider"),P=Symbol.for("react.consumer"),K=Symbol.for("react.context"),de=Symbol.for("react.forward_ref"),re=Symbol.for("react.suspense"),ce=Symbol.for("react.suspense_list"),Se=Symbol.for("react.memo"),G=Symbol.for("react.lazy"),Te=Symbol.for("react.activity"),Et=Symbol.for("react.memo_cache_sentinel"),ge=Symbol.iterator;function _e(e){return e===null||typeof e!="object"?null:(e=ge&&e[ge]||e["@@iterator"],typeof e=="function"?e:null)}var Vt=Symbol.for("react.client.reference");function zt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Vt?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case _:return"Fragment";case q:return"Profiler";case B:return"StrictMode";case re:return"Suspense";case ce:return"SuspenseList";case Te:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case X:return"Portal";case K:return(e.displayName||"Context")+".Provider";case P:return(e._context.displayName||"Context")+".Consumer";case de:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Se:return t=e.displayName||null,t!==null?t:zt(e.type)||"Memo";case G:t=e._payload,e=e._init;try{return zt(e(t))}catch{}}return null}var Le=Array.isArray,v=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,C=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,U={pending:!1,data:null,method:null,action:null},se=[],ae=-1;function Ye(e){return{current:e}}function je(e){0>ae||(e.current=se[ae],se[ae]=null,ae--)}function F(e,t){ae++,se[ae]=e.current,e.current=t}var oe=Ye(null),Pe=Ye(null),Mt=Ye(null),Ee=Ye(null);function Ha(e,t){switch(F(Mt,t),F(Pe,e),F(oe,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?um(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=um(t),e=cm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}je(oe),F(oe,e)}function Ot(){je(oe),je(Pe),je(Mt)}function oa(e){e.memoizedState!==null&&F(Ee,e);var t=oe.current,a=cm(t,e.type);t!==a&&(F(Pe,e),F(oe,a))}function fa(e){Pe.current===e&&(je(oe),je(Pe)),Ee.current===e&&(je(Ee),Wn._currentValue=U)}var da=Object.prototype.hasOwnProperty,Ji=s.unstable_scheduleCallback,Wi=s.unstable_cancelCallback,ph=s.unstable_shouldYield,xh=s.unstable_requestPaint,_t=s.unstable_now,vh=s.unstable_getCurrentPriorityLevel,Lc=s.unstable_ImmediatePriority,Yc=s.unstable_UserBlockingPriority,fs=s.unstable_NormalPriority,Sh=s.unstable_LowPriority,Gc=s.unstable_IdlePriority,jh=s.log,wh=s.unstable_setDisableYieldValue,an=null,ct=null;function ma(e){if(typeof jh=="function"&&wh(e),ct&&typeof ct.setStrictMode=="function")try{ct.setStrictMode(an,e)}catch{}}var ot=Math.clz32?Math.clz32:Th,Nh=Math.log,Ah=Math.LN2;function Th(e){return e>>>=0,e===0?32:31-(Nh(e)/Ah|0)|0}var ds=256,ms=4194304;function La(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function hs(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var n=0,i=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var m=l&134217727;return m!==0?(l=m&~i,l!==0?n=La(l):(f&=m,f!==0?n=La(f):a||(a=m&~e,a!==0&&(n=La(a))))):(m=l&~i,m!==0?n=La(m):f!==0?n=La(f):a||(a=l&~e,a!==0&&(n=La(a)))),n===0?0:t!==0&&t!==n&&(t&i)===0&&(i=n&-n,a=t&-t,i>=a||i===32&&(a&4194048)!==0)?t:n}function ln(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Eh(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Xc(){var e=ds;return ds<<=1,(ds&4194048)===0&&(ds=256),e}function Zc(){var e=ms;return ms<<=1,(ms&62914560)===0&&(ms=4194304),e}function Fi(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function nn(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function zh(e,t,a,l,n,i){var f=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var m=e.entanglements,y=e.expirationTimes,A=e.hiddenUpdates;for(a=f&~a;0<a;){var M=31-ot(a),R=1<<M;m[M]=0,y[M]=-1;var T=A[M];if(T!==null)for(A[M]=null,M=0;M<T.length;M++){var E=T[M];E!==null&&(E.lane&=-536870913)}a&=~R}l!==0&&Qc(e,l,0),i!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=i&~(f&~t))}function Qc(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-ot(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function Vc(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-ot(a),n=1<<l;n&t|e[l]&t&&(e[l]|=t),a&=~n}}function $i(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Pi(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Kc(){var e=C.p;return e!==0?e:(e=window.event,e===void 0?32:zm(e.type))}function Mh(e,t){var a=C.p;try{return C.p=e,t()}finally{C.p=a}}var ha=Math.random().toString(36).slice(2),We="__reactFiber$"+ha,at="__reactProps$"+ha,cl="__reactContainer$"+ha,Ii="__reactEvents$"+ha,Oh="__reactListeners$"+ha,Dh="__reactHandles$"+ha,Jc="__reactResources$"+ha,sn="__reactMarker$"+ha;function er(e){delete e[We],delete e[at],delete e[Ii],delete e[Oh],delete e[Dh]}function ol(e){var t=e[We];if(t)return t;for(var a=e.parentNode;a;){if(t=a[cl]||a[We]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=mm(e);e!==null;){if(a=e[We])return a;e=mm(e)}return t}e=a,a=e.parentNode}return null}function fl(e){if(e=e[We]||e[cl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function rn(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(u(33))}function dl(e){var t=e[Jc];return t||(t=e[Jc]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ge(e){e[sn]=!0}var Wc=new Set,Fc={};function Ya(e,t){ml(e,t),ml(e+"Capture",t)}function ml(e,t){for(Fc[e]=t,e=0;e<t.length;e++)Wc.add(t[e])}var Rh=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),$c={},Pc={};function Ch(e){return da.call(Pc,e)?!0:da.call($c,e)?!1:Rh.test(e)?Pc[e]=!0:($c[e]=!0,!1)}function gs(e,t,a){if(Ch(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function ys(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Kt(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var tr,Ic;function hl(e){if(tr===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);tr=t&&t[1]||"",Ic=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+tr+e+Ic}var ar=!1;function lr(e,t){if(!e||ar)return"";ar=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var R=function(){throw Error()};if(Object.defineProperty(R.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(R,[])}catch(E){var T=E}Reflect.construct(e,[],R)}else{try{R.call()}catch(E){T=E}e.call(R.prototype)}}else{try{throw Error()}catch(E){T=E}(R=e())&&typeof R.catch=="function"&&R.catch(function(){})}}catch(E){if(E&&T&&typeof E.stack=="string")return[E.stack,T.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=l.DetermineComponentFrameRoot(),f=i[0],m=i[1];if(f&&m){var y=f.split(`
`),A=m.split(`
`);for(n=l=0;l<y.length&&!y[l].includes("DetermineComponentFrameRoot");)l++;for(;n<A.length&&!A[n].includes("DetermineComponentFrameRoot");)n++;if(l===y.length||n===A.length)for(l=y.length-1,n=A.length-1;1<=l&&0<=n&&y[l]!==A[n];)n--;for(;1<=l&&0<=n;l--,n--)if(y[l]!==A[n]){if(l!==1||n!==1)do if(l--,n--,0>n||y[l]!==A[n]){var M=`
`+y[l].replace(" at new "," at ");return e.displayName&&M.includes("<anonymous>")&&(M=M.replace("<anonymous>",e.displayName)),M}while(1<=l&&0<=n);break}}}finally{ar=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?hl(a):""}function kh(e){switch(e.tag){case 26:case 27:case 5:return hl(e.type);case 16:return hl("Lazy");case 13:return hl("Suspense");case 19:return hl("SuspenseList");case 0:case 15:return lr(e.type,!1);case 11:return lr(e.type.render,!1);case 1:return lr(e.type,!0);case 31:return hl("Activity");default:return""}}function eo(e){try{var t="";do t+=kh(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function pt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function to(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Uh(e){var t=to(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,i=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(f){l=""+f,i.call(this,f)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function bs(e){e._valueTracker||(e._valueTracker=Uh(e))}function ao(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=to(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function ps(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var _h=/[\n"\\]/g;function xt(e){return e.replace(_h,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function nr(e,t,a,l,n,i,f,m){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+pt(t)):e.value!==""+pt(t)&&(e.value=""+pt(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?sr(e,f,pt(t)):a!=null?sr(e,f,pt(a)):l!=null&&e.removeAttribute("value"),n==null&&i!=null&&(e.defaultChecked=!!i),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.name=""+pt(m):e.removeAttribute("name")}function lo(e,t,a,l,n,i,f,m){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(e.type=i),t!=null||a!=null){if(!(i!=="submit"&&i!=="reset"||t!=null))return;a=a!=null?""+pt(a):"",t=t!=null?""+pt(t):a,m||t===e.value||(e.value=t),e.defaultValue=t}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=m?e.checked:!!l,e.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function sr(e,t,a){t==="number"&&ps(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function gl(e,t,a,l){if(e=e.options,t){t={};for(var n=0;n<a.length;n++)t["$"+a[n]]=!0;for(a=0;a<e.length;a++)n=t.hasOwnProperty("$"+e[a].value),e[a].selected!==n&&(e[a].selected=n),n&&l&&(e[a].defaultSelected=!0)}else{for(a=""+pt(a),t=null,n=0;n<e.length;n++){if(e[n].value===a){e[n].selected=!0,l&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function no(e,t,a){if(t!=null&&(t=""+pt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+pt(a):""}function so(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(u(92));if(Le(l)){if(1<l.length)throw Error(u(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=pt(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function yl(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var Bh=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function io(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||Bh.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function ro(e,t,a){if(t!=null&&typeof t!="object")throw Error(u(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var n in t)l=t[n],t.hasOwnProperty(n)&&a[n]!==l&&io(e,n,l)}else for(var i in t)t.hasOwnProperty(i)&&io(e,i,t[i])}function ir(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var qh=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Hh=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function xs(e){return Hh.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var rr=null;function ur(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var bl=null,pl=null;function uo(e){var t=fl(e);if(t&&(e=t.stateNode)){var a=e[at]||null;e:switch(e=t.stateNode,t.type){case"input":if(nr(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+xt(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var n=l[at]||null;if(!n)throw Error(u(90));nr(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&ao(l)}break e;case"textarea":no(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&gl(e,!!a.multiple,t,!1)}}}var cr=!1;function co(e,t,a){if(cr)return e(t,a);cr=!0;try{var l=e(t);return l}finally{if(cr=!1,(bl!==null||pl!==null)&&(ni(),bl&&(t=bl,e=pl,pl=bl=null,uo(t),e)))for(t=0;t<e.length;t++)uo(e[t])}}function un(e,t){var a=e.stateNode;if(a===null)return null;var l=a[at]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(u(231,t,typeof a));return a}var Jt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),or=!1;if(Jt)try{var cn={};Object.defineProperty(cn,"passive",{get:function(){or=!0}}),window.addEventListener("test",cn,cn),window.removeEventListener("test",cn,cn)}catch{or=!1}var ga=null,fr=null,vs=null;function oo(){if(vs)return vs;var e,t=fr,a=t.length,l,n="value"in ga?ga.value:ga.textContent,i=n.length;for(e=0;e<a&&t[e]===n[e];e++);var f=a-e;for(l=1;l<=f&&t[a-l]===n[i-l];l++);return vs=n.slice(e,1<l?1-l:void 0)}function Ss(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function js(){return!0}function fo(){return!1}function lt(e){function t(a,l,n,i,f){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=i,this.target=f,this.currentTarget=null;for(var m in e)e.hasOwnProperty(m)&&(a=e[m],this[m]=a?a(i):i[m]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?js:fo,this.isPropagationStopped=fo,this}return S(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=js)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=js)},persist:function(){},isPersistent:js}),t}var Ga={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ws=lt(Ga),on=S({},Ga,{view:0,detail:0}),Lh=lt(on),dr,mr,fn,Ns=S({},on,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gr,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==fn&&(fn&&e.type==="mousemove"?(dr=e.screenX-fn.screenX,mr=e.screenY-fn.screenY):mr=dr=0,fn=e),dr)},movementY:function(e){return"movementY"in e?e.movementY:mr}}),mo=lt(Ns),Yh=S({},Ns,{dataTransfer:0}),Gh=lt(Yh),Xh=S({},on,{relatedTarget:0}),hr=lt(Xh),Zh=S({},Ga,{animationName:0,elapsedTime:0,pseudoElement:0}),Qh=lt(Zh),Vh=S({},Ga,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Kh=lt(Vh),Jh=S({},Ga,{data:0}),ho=lt(Jh),Wh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Fh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},$h={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ph(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=$h[e])?!!t[e]:!1}function gr(){return Ph}var Ih=S({},on,{key:function(e){if(e.key){var t=Wh[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ss(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Fh[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gr,charCode:function(e){return e.type==="keypress"?Ss(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ss(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),eg=lt(Ih),tg=S({},Ns,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),go=lt(tg),ag=S({},on,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gr}),lg=lt(ag),ng=S({},Ga,{propertyName:0,elapsedTime:0,pseudoElement:0}),sg=lt(ng),ig=S({},Ns,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),rg=lt(ig),ug=S({},Ga,{newState:0,oldState:0}),cg=lt(ug),og=[9,13,27,32],yr=Jt&&"CompositionEvent"in window,dn=null;Jt&&"documentMode"in document&&(dn=document.documentMode);var fg=Jt&&"TextEvent"in window&&!dn,yo=Jt&&(!yr||dn&&8<dn&&11>=dn),bo=" ",po=!1;function xo(e,t){switch(e){case"keyup":return og.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function vo(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var xl=!1;function dg(e,t){switch(e){case"compositionend":return vo(t);case"keypress":return t.which!==32?null:(po=!0,bo);case"textInput":return e=t.data,e===bo&&po?null:e;default:return null}}function mg(e,t){if(xl)return e==="compositionend"||!yr&&xo(e,t)?(e=oo(),vs=fr=ga=null,xl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return yo&&t.locale!=="ko"?null:t.data;default:return null}}var hg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function So(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!hg[e.type]:t==="textarea"}function jo(e,t,a,l){bl?pl?pl.push(l):pl=[l]:bl=l,t=oi(t,"onChange"),0<t.length&&(a=new ws("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var mn=null,hn=null;function gg(e){lm(e,0)}function As(e){var t=rn(e);if(ao(t))return e}function wo(e,t){if(e==="change")return t}var No=!1;if(Jt){var br;if(Jt){var pr="oninput"in document;if(!pr){var Ao=document.createElement("div");Ao.setAttribute("oninput","return;"),pr=typeof Ao.oninput=="function"}br=pr}else br=!1;No=br&&(!document.documentMode||9<document.documentMode)}function To(){mn&&(mn.detachEvent("onpropertychange",Eo),hn=mn=null)}function Eo(e){if(e.propertyName==="value"&&As(hn)){var t=[];jo(t,hn,e,ur(e)),co(gg,t)}}function yg(e,t,a){e==="focusin"?(To(),mn=t,hn=a,mn.attachEvent("onpropertychange",Eo)):e==="focusout"&&To()}function bg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return As(hn)}function pg(e,t){if(e==="click")return As(t)}function xg(e,t){if(e==="input"||e==="change")return As(t)}function vg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ft=typeof Object.is=="function"?Object.is:vg;function gn(e,t){if(ft(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!da.call(t,n)||!ft(e[n],t[n]))return!1}return!0}function zo(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Mo(e,t){var a=zo(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=zo(a)}}function Oo(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Oo(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Do(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=ps(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=ps(e.document)}return t}function xr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Sg=Jt&&"documentMode"in document&&11>=document.documentMode,vl=null,vr=null,yn=null,Sr=!1;function Ro(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Sr||vl==null||vl!==ps(l)||(l=vl,"selectionStart"in l&&xr(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),yn&&gn(yn,l)||(yn=l,l=oi(vr,"onSelect"),0<l.length&&(t=new ws("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=vl)))}function Xa(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var Sl={animationend:Xa("Animation","AnimationEnd"),animationiteration:Xa("Animation","AnimationIteration"),animationstart:Xa("Animation","AnimationStart"),transitionrun:Xa("Transition","TransitionRun"),transitionstart:Xa("Transition","TransitionStart"),transitioncancel:Xa("Transition","TransitionCancel"),transitionend:Xa("Transition","TransitionEnd")},jr={},Co={};Jt&&(Co=document.createElement("div").style,"AnimationEvent"in window||(delete Sl.animationend.animation,delete Sl.animationiteration.animation,delete Sl.animationstart.animation),"TransitionEvent"in window||delete Sl.transitionend.transition);function Za(e){if(jr[e])return jr[e];if(!Sl[e])return e;var t=Sl[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in Co)return jr[e]=t[a];return e}var ko=Za("animationend"),Uo=Za("animationiteration"),_o=Za("animationstart"),jg=Za("transitionrun"),wg=Za("transitionstart"),Ng=Za("transitioncancel"),Bo=Za("transitionend"),qo=new Map,wr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");wr.push("scrollEnd");function Dt(e,t){qo.set(e,t),Ya(t,[e])}var Ho=new WeakMap;function vt(e,t){if(typeof e=="object"&&e!==null){var a=Ho.get(e);return a!==void 0?a:(t={value:e,source:t,stack:eo(t)},Ho.set(e,t),t)}return{value:e,source:t,stack:eo(t)}}var St=[],jl=0,Nr=0;function Ts(){for(var e=jl,t=Nr=jl=0;t<e;){var a=St[t];St[t++]=null;var l=St[t];St[t++]=null;var n=St[t];St[t++]=null;var i=St[t];if(St[t++]=null,l!==null&&n!==null){var f=l.pending;f===null?n.next=n:(n.next=f.next,f.next=n),l.pending=n}i!==0&&Lo(a,n,i)}}function Es(e,t,a,l){St[jl++]=e,St[jl++]=t,St[jl++]=a,St[jl++]=l,Nr|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Ar(e,t,a,l){return Es(e,t,a,l),zs(e)}function wl(e,t){return Es(e,null,null,t),zs(e)}function Lo(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var n=!1,i=e.return;i!==null;)i.childLanes|=a,l=i.alternate,l!==null&&(l.childLanes|=a),i.tag===22&&(e=i.stateNode,e===null||e._visibility&1||(n=!0)),e=i,i=i.return;return e.tag===3?(i=e.stateNode,n&&t!==null&&(n=31-ot(a),e=i.hiddenUpdates,l=e[n],l===null?e[n]=[t]:l.push(t),t.lane=a|536870912),i):null}function zs(e){if(50<Yn)throw Yn=0,Du=null,Error(u(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Nl={};function Ag(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function dt(e,t,a,l){return new Ag(e,t,a,l)}function Tr(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Wt(e,t){var a=e.alternate;return a===null?(a=dt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Yo(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ms(e,t,a,l,n,i){var f=0;if(l=e,typeof e=="function")Tr(e)&&(f=1);else if(typeof e=="string")f=Ey(e,a,oe.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case Te:return e=dt(31,a,t,n),e.elementType=Te,e.lanes=i,e;case _:return Qa(a.children,n,i,t);case B:f=8,n|=24;break;case q:return e=dt(12,a,t,n|2),e.elementType=q,e.lanes=i,e;case re:return e=dt(13,a,t,n),e.elementType=re,e.lanes=i,e;case ce:return e=dt(19,a,t,n),e.elementType=ce,e.lanes=i,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Q:case K:f=10;break e;case P:f=9;break e;case de:f=11;break e;case Se:f=14;break e;case G:f=16,l=null;break e}f=29,a=Error(u(130,e===null?"null":typeof e,"")),l=null}return t=dt(f,a,t,n),t.elementType=e,t.type=l,t.lanes=i,t}function Qa(e,t,a,l){return e=dt(7,e,l,t),e.lanes=a,e}function Er(e,t,a){return e=dt(6,e,null,t),e.lanes=a,e}function zr(e,t,a){return t=dt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Al=[],Tl=0,Os=null,Ds=0,jt=[],wt=0,Va=null,Ft=1,$t="";function Ka(e,t){Al[Tl++]=Ds,Al[Tl++]=Os,Os=e,Ds=t}function Go(e,t,a){jt[wt++]=Ft,jt[wt++]=$t,jt[wt++]=Va,Va=e;var l=Ft;e=$t;var n=32-ot(l)-1;l&=~(1<<n),a+=1;var i=32-ot(t)+n;if(30<i){var f=n-n%5;i=(l&(1<<f)-1).toString(32),l>>=f,n-=f,Ft=1<<32-ot(t)+n|a<<n|l,$t=i+e}else Ft=1<<i|a<<n|l,$t=e}function Mr(e){e.return!==null&&(Ka(e,1),Go(e,1,0))}function Or(e){for(;e===Os;)Os=Al[--Tl],Al[Tl]=null,Ds=Al[--Tl],Al[Tl]=null;for(;e===Va;)Va=jt[--wt],jt[wt]=null,$t=jt[--wt],jt[wt]=null,Ft=jt[--wt],jt[wt]=null}var Ie=null,Me=null,me=!1,Ja=null,Bt=!1,Dr=Error(u(519));function Wa(e){var t=Error(u(418,""));throw xn(vt(t,e)),Dr}function Xo(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[We]=e,t[at]=l,a){case"dialog":ne("cancel",t),ne("close",t);break;case"iframe":case"object":case"embed":ne("load",t);break;case"video":case"audio":for(a=0;a<Xn.length;a++)ne(Xn[a],t);break;case"source":ne("error",t);break;case"img":case"image":case"link":ne("error",t),ne("load",t);break;case"details":ne("toggle",t);break;case"input":ne("invalid",t),lo(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),bs(t);break;case"select":ne("invalid",t);break;case"textarea":ne("invalid",t),so(t,l.value,l.defaultValue,l.children),bs(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||rm(t.textContent,a)?(l.popover!=null&&(ne("beforetoggle",t),ne("toggle",t)),l.onScroll!=null&&ne("scroll",t),l.onScrollEnd!=null&&ne("scrollend",t),l.onClick!=null&&(t.onclick=fi),t=!0):t=!1,t||Wa(e)}function Zo(e){for(Ie=e.return;Ie;)switch(Ie.tag){case 5:case 13:Bt=!1;return;case 27:case 3:Bt=!0;return;default:Ie=Ie.return}}function bn(e){if(e!==Ie)return!1;if(!me)return Zo(e),me=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Ku(e.type,e.memoizedProps)),a=!a),a&&Me&&Wa(e),Zo(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(u(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Me=Ct(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Me=null}}else t===27?(t=Me,Da(e.type)?(e=$u,$u=null,Me=e):Me=t):Me=Ie?Ct(e.stateNode.nextSibling):null;return!0}function pn(){Me=Ie=null,me=!1}function Qo(){var e=Ja;return e!==null&&(it===null?it=e:it.push.apply(it,e),Ja=null),e}function xn(e){Ja===null?Ja=[e]:Ja.push(e)}var Rr=Ye(null),Fa=null,Pt=null;function ya(e,t,a){F(Rr,t._currentValue),t._currentValue=a}function It(e){e._currentValue=Rr.current,je(Rr)}function Cr(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function kr(e,t,a,l){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var i=n.dependencies;if(i!==null){var f=n.child;i=i.firstContext;e:for(;i!==null;){var m=i;i=n;for(var y=0;y<t.length;y++)if(m.context===t[y]){i.lanes|=a,m=i.alternate,m!==null&&(m.lanes|=a),Cr(i.return,a,e),l||(f=null);break e}i=m.next}}else if(n.tag===18){if(f=n.return,f===null)throw Error(u(341));f.lanes|=a,i=f.alternate,i!==null&&(i.lanes|=a),Cr(f,a,e),f=null}else f=n.child;if(f!==null)f.return=n;else for(f=n;f!==null;){if(f===e){f=null;break}if(n=f.sibling,n!==null){n.return=f.return,f=n;break}f=f.return}n=f}}function vn(e,t,a,l){e=null;for(var n=t,i=!1;n!==null;){if(!i){if((n.flags&524288)!==0)i=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var f=n.alternate;if(f===null)throw Error(u(387));if(f=f.memoizedProps,f!==null){var m=n.type;ft(n.pendingProps.value,f.value)||(e!==null?e.push(m):e=[m])}}else if(n===Ee.current){if(f=n.alternate,f===null)throw Error(u(387));f.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Wn):e=[Wn])}n=n.return}e!==null&&kr(t,e,a,l),t.flags|=262144}function Rs(e){for(e=e.firstContext;e!==null;){if(!ft(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function $a(e){Fa=e,Pt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Fe(e){return Vo(Fa,e)}function Cs(e,t){return Fa===null&&$a(e),Vo(e,t)}function Vo(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Pt===null){if(e===null)throw Error(u(308));Pt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Pt=Pt.next=t;return a}var Tg=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},Eg=s.unstable_scheduleCallback,zg=s.unstable_NormalPriority,Be={$$typeof:K,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ur(){return{controller:new Tg,data:new Map,refCount:0}}function Sn(e){e.refCount--,e.refCount===0&&Eg(zg,function(){e.controller.abort()})}var jn=null,_r=0,El=0,zl=null;function Mg(e,t){if(jn===null){var a=jn=[];_r=0,El=qu(),zl={status:"pending",value:void 0,then:function(l){a.push(l)}}}return _r++,t.then(Ko,Ko),t}function Ko(){if(--_r===0&&jn!==null){zl!==null&&(zl.status="fulfilled");var e=jn;jn=null,El=0,zl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Og(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var n=0;n<a.length;n++)(0,a[n])(t)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var Jo=v.S;v.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Mg(e,t),Jo!==null&&Jo(e,t)};var Pa=Ye(null);function Br(){var e=Pa.current;return e!==null?e:Ne.pooledCache}function ks(e,t){t===null?F(Pa,Pa.current):F(Pa,t.pool)}function Wo(){var e=Br();return e===null?null:{parent:Be._currentValue,pool:e}}var wn=Error(u(460)),Fo=Error(u(474)),Us=Error(u(542)),qr={then:function(){}};function $o(e){return e=e.status,e==="fulfilled"||e==="rejected"}function _s(){}function Po(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(_s,_s),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,ef(e),e;default:if(typeof t.status=="string")t.then(_s,_s);else{if(e=Ne,e!==null&&100<e.shellSuspendCounter)throw Error(u(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=l}},function(l){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,ef(e),e}throw Nn=t,wn}}var Nn=null;function Io(){if(Nn===null)throw Error(u(459));var e=Nn;return Nn=null,e}function ef(e){if(e===wn||e===Us)throw Error(u(483))}var ba=!1;function Hr(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Lr(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function pa(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function xa(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(ye&2)!==0){var n=l.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),l.pending=t,t=zs(e),Lo(e,null,a),t}return Es(e,l,t,a),zs(e)}function An(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Vc(e,a)}}function Yr(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,i=null;if(a=a.firstBaseUpdate,a!==null){do{var f={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};i===null?n=i=f:i=i.next=f,a=a.next}while(a!==null);i===null?n=i=t:i=i.next=t}else n=i=t;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Gr=!1;function Tn(){if(Gr){var e=zl;if(e!==null)throw e}}function En(e,t,a,l){Gr=!1;var n=e.updateQueue;ba=!1;var i=n.firstBaseUpdate,f=n.lastBaseUpdate,m=n.shared.pending;if(m!==null){n.shared.pending=null;var y=m,A=y.next;y.next=null,f===null?i=A:f.next=A,f=y;var M=e.alternate;M!==null&&(M=M.updateQueue,m=M.lastBaseUpdate,m!==f&&(m===null?M.firstBaseUpdate=A:m.next=A,M.lastBaseUpdate=y))}if(i!==null){var R=n.baseState;f=0,M=A=y=null,m=i;do{var T=m.lane&-536870913,E=T!==m.lane;if(E?(ue&T)===T:(l&T)===T){T!==0&&T===El&&(Gr=!0),M!==null&&(M=M.next={lane:0,tag:m.tag,payload:m.payload,callback:null,next:null});e:{var W=e,V=m;T=t;var ve=a;switch(V.tag){case 1:if(W=V.payload,typeof W=="function"){R=W.call(ve,R,T);break e}R=W;break e;case 3:W.flags=W.flags&-65537|128;case 0:if(W=V.payload,T=typeof W=="function"?W.call(ve,R,T):W,T==null)break e;R=S({},R,T);break e;case 2:ba=!0}}T=m.callback,T!==null&&(e.flags|=64,E&&(e.flags|=8192),E=n.callbacks,E===null?n.callbacks=[T]:E.push(T))}else E={lane:T,tag:m.tag,payload:m.payload,callback:m.callback,next:null},M===null?(A=M=E,y=R):M=M.next=E,f|=T;if(m=m.next,m===null){if(m=n.shared.pending,m===null)break;E=m,m=E.next,E.next=null,n.lastBaseUpdate=E,n.shared.pending=null}}while(!0);M===null&&(y=R),n.baseState=y,n.firstBaseUpdate=A,n.lastBaseUpdate=M,i===null&&(n.shared.lanes=0),Ea|=f,e.lanes=f,e.memoizedState=R}}function tf(e,t){if(typeof e!="function")throw Error(u(191,e));e.call(t)}function af(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)tf(a[e],t)}var Ml=Ye(null),Bs=Ye(0);function lf(e,t){e=ia,F(Bs,e),F(Ml,t),ia=e|t.baseLanes}function Xr(){F(Bs,ia),F(Ml,Ml.current)}function Zr(){ia=Bs.current,je(Ml),je(Bs)}var va=0,I=null,pe=null,ke=null,qs=!1,Ol=!1,Ia=!1,Hs=0,zn=0,Dl=null,Dg=0;function De(){throw Error(u(321))}function Qr(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!ft(e[a],t[a]))return!1;return!0}function Vr(e,t,a,l,n,i){return va=i,I=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,v.H=e===null||e.memoizedState===null?Yf:Gf,Ia=!1,i=a(l,n),Ia=!1,Ol&&(i=sf(t,a,l,n)),nf(e),i}function nf(e){v.H=Qs;var t=pe!==null&&pe.next!==null;if(va=0,ke=pe=I=null,qs=!1,zn=0,Dl=null,t)throw Error(u(300));e===null||Xe||(e=e.dependencies,e!==null&&Rs(e)&&(Xe=!0))}function sf(e,t,a,l){I=e;var n=0;do{if(Ol&&(Dl=null),zn=0,Ol=!1,25<=n)throw Error(u(301));if(n+=1,ke=pe=null,e.updateQueue!=null){var i=e.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}v.H=qg,i=t(a,l)}while(Ol);return i}function Rg(){var e=v.H,t=e.useState()[0];return t=typeof t.then=="function"?Mn(t):t,e=e.useState()[0],(pe!==null?pe.memoizedState:null)!==e&&(I.flags|=1024),t}function Kr(){var e=Hs!==0;return Hs=0,e}function Jr(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Wr(e){if(qs){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}qs=!1}va=0,ke=pe=I=null,Ol=!1,zn=Hs=0,Dl=null}function nt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ke===null?I.memoizedState=ke=e:ke=ke.next=e,ke}function Ue(){if(pe===null){var e=I.alternate;e=e!==null?e.memoizedState:null}else e=pe.next;var t=ke===null?I.memoizedState:ke.next;if(t!==null)ke=t,pe=e;else{if(e===null)throw I.alternate===null?Error(u(467)):Error(u(310));pe=e,e={memoizedState:pe.memoizedState,baseState:pe.baseState,baseQueue:pe.baseQueue,queue:pe.queue,next:null},ke===null?I.memoizedState=ke=e:ke=ke.next=e}return ke}function Fr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Mn(e){var t=zn;return zn+=1,Dl===null&&(Dl=[]),e=Po(Dl,e,t),t=I,(ke===null?t.memoizedState:ke.next)===null&&(t=t.alternate,v.H=t===null||t.memoizedState===null?Yf:Gf),e}function Ls(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Mn(e);if(e.$$typeof===K)return Fe(e)}throw Error(u(438,String(e)))}function $r(e){var t=null,a=I.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=I.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Fr(),I.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=Et;return t.index++,a}function ea(e,t){return typeof t=="function"?t(e):t}function Ys(e){var t=Ue();return Pr(t,pe,e)}function Pr(e,t,a){var l=e.queue;if(l===null)throw Error(u(311));l.lastRenderedReducer=a;var n=e.baseQueue,i=l.pending;if(i!==null){if(n!==null){var f=n.next;n.next=i.next,i.next=f}t.baseQueue=n=i,l.pending=null}if(i=e.baseState,n===null)e.memoizedState=i;else{t=n.next;var m=f=null,y=null,A=t,M=!1;do{var R=A.lane&-536870913;if(R!==A.lane?(ue&R)===R:(va&R)===R){var T=A.revertLane;if(T===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null}),R===El&&(M=!0);else if((va&T)===T){A=A.next,T===El&&(M=!0);continue}else R={lane:0,revertLane:A.revertLane,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null},y===null?(m=y=R,f=i):y=y.next=R,I.lanes|=T,Ea|=T;R=A.action,Ia&&a(i,R),i=A.hasEagerState?A.eagerState:a(i,R)}else T={lane:R,revertLane:A.revertLane,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null},y===null?(m=y=T,f=i):y=y.next=T,I.lanes|=R,Ea|=R;A=A.next}while(A!==null&&A!==t);if(y===null?f=i:y.next=m,!ft(i,e.memoizedState)&&(Xe=!0,M&&(a=zl,a!==null)))throw a;e.memoizedState=i,e.baseState=f,e.baseQueue=y,l.lastRenderedState=i}return n===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Ir(e){var t=Ue(),a=t.queue;if(a===null)throw Error(u(311));a.lastRenderedReducer=e;var l=a.dispatch,n=a.pending,i=t.memoizedState;if(n!==null){a.pending=null;var f=n=n.next;do i=e(i,f.action),f=f.next;while(f!==n);ft(i,t.memoizedState)||(Xe=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),a.lastRenderedState=i}return[i,l]}function rf(e,t,a){var l=I,n=Ue(),i=me;if(i){if(a===void 0)throw Error(u(407));a=a()}else a=t();var f=!ft((pe||n).memoizedState,a);f&&(n.memoizedState=a,Xe=!0),n=n.queue;var m=of.bind(null,l,n,e);if(On(2048,8,m,[e]),n.getSnapshot!==t||f||ke!==null&&ke.memoizedState.tag&1){if(l.flags|=2048,Rl(9,Gs(),cf.bind(null,l,n,a,t),null),Ne===null)throw Error(u(349));i||(va&124)!==0||uf(l,t,a)}return a}function uf(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=I.updateQueue,t===null?(t=Fr(),I.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function cf(e,t,a,l){t.value=a,t.getSnapshot=l,ff(t)&&df(e)}function of(e,t,a){return a(function(){ff(t)&&df(e)})}function ff(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!ft(e,a)}catch{return!0}}function df(e){var t=wl(e,2);t!==null&&bt(t,e,2)}function eu(e){var t=nt();if(typeof e=="function"){var a=e;if(e=a(),Ia){ma(!0);try{a()}finally{ma(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ea,lastRenderedState:e},t}function mf(e,t,a,l){return e.baseState=a,Pr(e,pe,typeof l=="function"?l:ea)}function Cg(e,t,a,l,n){if(Zs(e))throw Error(u(485));if(e=t.action,e!==null){var i={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){i.listeners.push(f)}};v.T!==null?a(!0):i.isTransition=!1,l(i),a=t.pending,a===null?(i.next=t.pending=i,hf(t,i)):(i.next=a.next,t.pending=a.next=i)}}function hf(e,t){var a=t.action,l=t.payload,n=e.state;if(t.isTransition){var i=v.T,f={};v.T=f;try{var m=a(n,l),y=v.S;y!==null&&y(f,m),gf(e,t,m)}catch(A){tu(e,t,A)}finally{v.T=i}}else try{i=a(n,l),gf(e,t,i)}catch(A){tu(e,t,A)}}function gf(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){yf(e,t,l)},function(l){return tu(e,t,l)}):yf(e,t,a)}function yf(e,t,a){t.status="fulfilled",t.value=a,bf(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,hf(e,a)))}function tu(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,bf(t),t=t.next;while(t!==l)}e.action=null}function bf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function pf(e,t){return t}function xf(e,t){if(me){var a=Ne.formState;if(a!==null){e:{var l=I;if(me){if(Me){t:{for(var n=Me,i=Bt;n.nodeType!==8;){if(!i){n=null;break t}if(n=Ct(n.nextSibling),n===null){n=null;break t}}i=n.data,n=i==="F!"||i==="F"?n:null}if(n){Me=Ct(n.nextSibling),l=n.data==="F!";break e}}Wa(l)}l=!1}l&&(t=a[0])}}return a=nt(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:pf,lastRenderedState:t},a.queue=l,a=qf.bind(null,I,l),l.dispatch=a,l=eu(!1),i=iu.bind(null,I,!1,l.queue),l=nt(),n={state:t,dispatch:null,action:e,pending:null},l.queue=n,a=Cg.bind(null,I,n,i,a),n.dispatch=a,l.memoizedState=e,[t,a,!1]}function vf(e){var t=Ue();return Sf(t,pe,e)}function Sf(e,t,a){if(t=Pr(e,t,pf)[0],e=Ys(ea)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Mn(t)}catch(f){throw f===wn?Us:f}else l=t;t=Ue();var n=t.queue,i=n.dispatch;return a!==t.memoizedState&&(I.flags|=2048,Rl(9,Gs(),kg.bind(null,n,a),null)),[l,i,e]}function kg(e,t){e.action=t}function jf(e){var t=Ue(),a=pe;if(a!==null)return Sf(t,a,e);Ue(),t=t.memoizedState,a=Ue();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function Rl(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=I.updateQueue,t===null&&(t=Fr(),I.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function Gs(){return{destroy:void 0,resource:void 0}}function wf(){return Ue().memoizedState}function Xs(e,t,a,l){var n=nt();l=l===void 0?null:l,I.flags|=e,n.memoizedState=Rl(1|t,Gs(),a,l)}function On(e,t,a,l){var n=Ue();l=l===void 0?null:l;var i=n.memoizedState.inst;pe!==null&&l!==null&&Qr(l,pe.memoizedState.deps)?n.memoizedState=Rl(t,i,a,l):(I.flags|=e,n.memoizedState=Rl(1|t,i,a,l))}function Nf(e,t){Xs(8390656,8,e,t)}function Af(e,t){On(2048,8,e,t)}function Tf(e,t){return On(4,2,e,t)}function Ef(e,t){return On(4,4,e,t)}function zf(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Mf(e,t,a){a=a!=null?a.concat([e]):null,On(4,4,zf.bind(null,t,e),a)}function au(){}function Of(e,t){var a=Ue();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&Qr(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function Df(e,t){var a=Ue();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&Qr(t,l[1]))return l[0];if(l=e(),Ia){ma(!0);try{e()}finally{ma(!1)}}return a.memoizedState=[l,t],l}function lu(e,t,a){return a===void 0||(va&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=kd(),I.lanes|=e,Ea|=e,a)}function Rf(e,t,a,l){return ft(a,t)?a:Ml.current!==null?(e=lu(e,a,l),ft(e,t)||(Xe=!0),e):(va&42)===0?(Xe=!0,e.memoizedState=a):(e=kd(),I.lanes|=e,Ea|=e,t)}function Cf(e,t,a,l,n){var i=C.p;C.p=i!==0&&8>i?i:8;var f=v.T,m={};v.T=m,iu(e,!1,t,a);try{var y=n(),A=v.S;if(A!==null&&A(m,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var M=Og(y,l);Dn(e,t,M,yt(e))}else Dn(e,t,l,yt(e))}catch(R){Dn(e,t,{then:function(){},status:"rejected",reason:R},yt())}finally{C.p=i,v.T=f}}function Ug(){}function nu(e,t,a,l){if(e.tag!==5)throw Error(u(476));var n=kf(e).queue;Cf(e,n,t,U,a===null?Ug:function(){return Uf(e),a(l)})}function kf(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:U,baseState:U,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ea,lastRenderedState:U},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ea,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Uf(e){var t=kf(e).next.queue;Dn(e,t,{},yt())}function su(){return Fe(Wn)}function _f(){return Ue().memoizedState}function Bf(){return Ue().memoizedState}function _g(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=yt();e=pa(a);var l=xa(t,e,a);l!==null&&(bt(l,t,a),An(l,t,a)),t={cache:Ur()},e.payload=t;return}t=t.return}}function Bg(e,t,a){var l=yt();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Zs(e)?Hf(t,a):(a=Ar(e,t,a,l),a!==null&&(bt(a,e,l),Lf(a,t,l)))}function qf(e,t,a){var l=yt();Dn(e,t,a,l)}function Dn(e,t,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Zs(e))Hf(t,n);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var f=t.lastRenderedState,m=i(f,a);if(n.hasEagerState=!0,n.eagerState=m,ft(m,f))return Es(e,t,n,0),Ne===null&&Ts(),!1}catch{}finally{}if(a=Ar(e,t,n,l),a!==null)return bt(a,e,l),Lf(a,t,l),!0}return!1}function iu(e,t,a,l){if(l={lane:2,revertLane:qu(),action:l,hasEagerState:!1,eagerState:null,next:null},Zs(e)){if(t)throw Error(u(479))}else t=Ar(e,a,l,2),t!==null&&bt(t,e,2)}function Zs(e){var t=e.alternate;return e===I||t!==null&&t===I}function Hf(e,t){Ol=qs=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Lf(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Vc(e,a)}}var Qs={readContext:Fe,use:Ls,useCallback:De,useContext:De,useEffect:De,useImperativeHandle:De,useLayoutEffect:De,useInsertionEffect:De,useMemo:De,useReducer:De,useRef:De,useState:De,useDebugValue:De,useDeferredValue:De,useTransition:De,useSyncExternalStore:De,useId:De,useHostTransitionStatus:De,useFormState:De,useActionState:De,useOptimistic:De,useMemoCache:De,useCacheRefresh:De},Yf={readContext:Fe,use:Ls,useCallback:function(e,t){return nt().memoizedState=[e,t===void 0?null:t],e},useContext:Fe,useEffect:Nf,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Xs(4194308,4,zf.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Xs(4194308,4,e,t)},useInsertionEffect:function(e,t){Xs(4,2,e,t)},useMemo:function(e,t){var a=nt();t=t===void 0?null:t;var l=e();if(Ia){ma(!0);try{e()}finally{ma(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=nt();if(a!==void 0){var n=a(t);if(Ia){ma(!0);try{a(t)}finally{ma(!1)}}}else n=t;return l.memoizedState=l.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},l.queue=e,e=e.dispatch=Bg.bind(null,I,e),[l.memoizedState,e]},useRef:function(e){var t=nt();return e={current:e},t.memoizedState=e},useState:function(e){e=eu(e);var t=e.queue,a=qf.bind(null,I,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:au,useDeferredValue:function(e,t){var a=nt();return lu(a,e,t)},useTransition:function(){var e=eu(!1);return e=Cf.bind(null,I,e.queue,!0,!1),nt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=I,n=nt();if(me){if(a===void 0)throw Error(u(407));a=a()}else{if(a=t(),Ne===null)throw Error(u(349));(ue&124)!==0||uf(l,t,a)}n.memoizedState=a;var i={value:a,getSnapshot:t};return n.queue=i,Nf(of.bind(null,l,i,e),[e]),l.flags|=2048,Rl(9,Gs(),cf.bind(null,l,i,a,t),null),a},useId:function(){var e=nt(),t=Ne.identifierPrefix;if(me){var a=$t,l=Ft;a=(l&~(1<<32-ot(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=Hs++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=Dg++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:su,useFormState:xf,useActionState:xf,useOptimistic:function(e){var t=nt();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=iu.bind(null,I,!0,a),a.dispatch=t,[e,t]},useMemoCache:$r,useCacheRefresh:function(){return nt().memoizedState=_g.bind(null,I)}},Gf={readContext:Fe,use:Ls,useCallback:Of,useContext:Fe,useEffect:Af,useImperativeHandle:Mf,useInsertionEffect:Tf,useLayoutEffect:Ef,useMemo:Df,useReducer:Ys,useRef:wf,useState:function(){return Ys(ea)},useDebugValue:au,useDeferredValue:function(e,t){var a=Ue();return Rf(a,pe.memoizedState,e,t)},useTransition:function(){var e=Ys(ea)[0],t=Ue().memoizedState;return[typeof e=="boolean"?e:Mn(e),t]},useSyncExternalStore:rf,useId:_f,useHostTransitionStatus:su,useFormState:vf,useActionState:vf,useOptimistic:function(e,t){var a=Ue();return mf(a,pe,e,t)},useMemoCache:$r,useCacheRefresh:Bf},qg={readContext:Fe,use:Ls,useCallback:Of,useContext:Fe,useEffect:Af,useImperativeHandle:Mf,useInsertionEffect:Tf,useLayoutEffect:Ef,useMemo:Df,useReducer:Ir,useRef:wf,useState:function(){return Ir(ea)},useDebugValue:au,useDeferredValue:function(e,t){var a=Ue();return pe===null?lu(a,e,t):Rf(a,pe.memoizedState,e,t)},useTransition:function(){var e=Ir(ea)[0],t=Ue().memoizedState;return[typeof e=="boolean"?e:Mn(e),t]},useSyncExternalStore:rf,useId:_f,useHostTransitionStatus:su,useFormState:jf,useActionState:jf,useOptimistic:function(e,t){var a=Ue();return pe!==null?mf(a,pe,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:$r,useCacheRefresh:Bf},Cl=null,Rn=0;function Vs(e){var t=Rn;return Rn+=1,Cl===null&&(Cl=[]),Po(Cl,e,t)}function Cn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Ks(e,t){throw t.$$typeof===D?Error(u(525)):(e=Object.prototype.toString.call(t),Error(u(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Xf(e){var t=e._init;return t(e._payload)}function Zf(e){function t(x,p){if(e){var w=x.deletions;w===null?(x.deletions=[p],x.flags|=16):w.push(p)}}function a(x,p){if(!e)return null;for(;p!==null;)t(x,p),p=p.sibling;return null}function l(x){for(var p=new Map;x!==null;)x.key!==null?p.set(x.key,x):p.set(x.index,x),x=x.sibling;return p}function n(x,p){return x=Wt(x,p),x.index=0,x.sibling=null,x}function i(x,p,w){return x.index=w,e?(w=x.alternate,w!==null?(w=w.index,w<p?(x.flags|=67108866,p):w):(x.flags|=67108866,p)):(x.flags|=1048576,p)}function f(x){return e&&x.alternate===null&&(x.flags|=67108866),x}function m(x,p,w,O){return p===null||p.tag!==6?(p=Er(w,x.mode,O),p.return=x,p):(p=n(p,w),p.return=x,p)}function y(x,p,w,O){var H=w.type;return H===_?M(x,p,w.props.children,O,w.key):p!==null&&(p.elementType===H||typeof H=="object"&&H!==null&&H.$$typeof===G&&Xf(H)===p.type)?(p=n(p,w.props),Cn(p,w),p.return=x,p):(p=Ms(w.type,w.key,w.props,null,x.mode,O),Cn(p,w),p.return=x,p)}function A(x,p,w,O){return p===null||p.tag!==4||p.stateNode.containerInfo!==w.containerInfo||p.stateNode.implementation!==w.implementation?(p=zr(w,x.mode,O),p.return=x,p):(p=n(p,w.children||[]),p.return=x,p)}function M(x,p,w,O,H){return p===null||p.tag!==7?(p=Qa(w,x.mode,O,H),p.return=x,p):(p=n(p,w),p.return=x,p)}function R(x,p,w){if(typeof p=="string"&&p!==""||typeof p=="number"||typeof p=="bigint")return p=Er(""+p,x.mode,w),p.return=x,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case k:return w=Ms(p.type,p.key,p.props,null,x.mode,w),Cn(w,p),w.return=x,w;case X:return p=zr(p,x.mode,w),p.return=x,p;case G:var O=p._init;return p=O(p._payload),R(x,p,w)}if(Le(p)||_e(p))return p=Qa(p,x.mode,w,null),p.return=x,p;if(typeof p.then=="function")return R(x,Vs(p),w);if(p.$$typeof===K)return R(x,Cs(x,p),w);Ks(x,p)}return null}function T(x,p,w,O){var H=p!==null?p.key:null;if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return H!==null?null:m(x,p,""+w,O);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case k:return w.key===H?y(x,p,w,O):null;case X:return w.key===H?A(x,p,w,O):null;case G:return H=w._init,w=H(w._payload),T(x,p,w,O)}if(Le(w)||_e(w))return H!==null?null:M(x,p,w,O,null);if(typeof w.then=="function")return T(x,p,Vs(w),O);if(w.$$typeof===K)return T(x,p,Cs(x,w),O);Ks(x,w)}return null}function E(x,p,w,O,H){if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return x=x.get(w)||null,m(p,x,""+O,H);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case k:return x=x.get(O.key===null?w:O.key)||null,y(p,x,O,H);case X:return x=x.get(O.key===null?w:O.key)||null,A(p,x,O,H);case G:var te=O._init;return O=te(O._payload),E(x,p,w,O,H)}if(Le(O)||_e(O))return x=x.get(w)||null,M(p,x,O,H,null);if(typeof O.then=="function")return E(x,p,w,Vs(O),H);if(O.$$typeof===K)return E(x,p,w,Cs(p,O),H);Ks(p,O)}return null}function W(x,p,w,O){for(var H=null,te=null,Z=p,J=p=0,Qe=null;Z!==null&&J<w.length;J++){Z.index>J?(Qe=Z,Z=null):Qe=Z.sibling;var fe=T(x,Z,w[J],O);if(fe===null){Z===null&&(Z=Qe);break}e&&Z&&fe.alternate===null&&t(x,Z),p=i(fe,p,J),te===null?H=fe:te.sibling=fe,te=fe,Z=Qe}if(J===w.length)return a(x,Z),me&&Ka(x,J),H;if(Z===null){for(;J<w.length;J++)Z=R(x,w[J],O),Z!==null&&(p=i(Z,p,J),te===null?H=Z:te.sibling=Z,te=Z);return me&&Ka(x,J),H}for(Z=l(Z);J<w.length;J++)Qe=E(Z,x,J,w[J],O),Qe!==null&&(e&&Qe.alternate!==null&&Z.delete(Qe.key===null?J:Qe.key),p=i(Qe,p,J),te===null?H=Qe:te.sibling=Qe,te=Qe);return e&&Z.forEach(function(_a){return t(x,_a)}),me&&Ka(x,J),H}function V(x,p,w,O){if(w==null)throw Error(u(151));for(var H=null,te=null,Z=p,J=p=0,Qe=null,fe=w.next();Z!==null&&!fe.done;J++,fe=w.next()){Z.index>J?(Qe=Z,Z=null):Qe=Z.sibling;var _a=T(x,Z,fe.value,O);if(_a===null){Z===null&&(Z=Qe);break}e&&Z&&_a.alternate===null&&t(x,Z),p=i(_a,p,J),te===null?H=_a:te.sibling=_a,te=_a,Z=Qe}if(fe.done)return a(x,Z),me&&Ka(x,J),H;if(Z===null){for(;!fe.done;J++,fe=w.next())fe=R(x,fe.value,O),fe!==null&&(p=i(fe,p,J),te===null?H=fe:te.sibling=fe,te=fe);return me&&Ka(x,J),H}for(Z=l(Z);!fe.done;J++,fe=w.next())fe=E(Z,x,J,fe.value,O),fe!==null&&(e&&fe.alternate!==null&&Z.delete(fe.key===null?J:fe.key),p=i(fe,p,J),te===null?H=fe:te.sibling=fe,te=fe);return e&&Z.forEach(function(Hy){return t(x,Hy)}),me&&Ka(x,J),H}function ve(x,p,w,O){if(typeof w=="object"&&w!==null&&w.type===_&&w.key===null&&(w=w.props.children),typeof w=="object"&&w!==null){switch(w.$$typeof){case k:e:{for(var H=w.key;p!==null;){if(p.key===H){if(H=w.type,H===_){if(p.tag===7){a(x,p.sibling),O=n(p,w.props.children),O.return=x,x=O;break e}}else if(p.elementType===H||typeof H=="object"&&H!==null&&H.$$typeof===G&&Xf(H)===p.type){a(x,p.sibling),O=n(p,w.props),Cn(O,w),O.return=x,x=O;break e}a(x,p);break}else t(x,p);p=p.sibling}w.type===_?(O=Qa(w.props.children,x.mode,O,w.key),O.return=x,x=O):(O=Ms(w.type,w.key,w.props,null,x.mode,O),Cn(O,w),O.return=x,x=O)}return f(x);case X:e:{for(H=w.key;p!==null;){if(p.key===H)if(p.tag===4&&p.stateNode.containerInfo===w.containerInfo&&p.stateNode.implementation===w.implementation){a(x,p.sibling),O=n(p,w.children||[]),O.return=x,x=O;break e}else{a(x,p);break}else t(x,p);p=p.sibling}O=zr(w,x.mode,O),O.return=x,x=O}return f(x);case G:return H=w._init,w=H(w._payload),ve(x,p,w,O)}if(Le(w))return W(x,p,w,O);if(_e(w)){if(H=_e(w),typeof H!="function")throw Error(u(150));return w=H.call(w),V(x,p,w,O)}if(typeof w.then=="function")return ve(x,p,Vs(w),O);if(w.$$typeof===K)return ve(x,p,Cs(x,w),O);Ks(x,w)}return typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint"?(w=""+w,p!==null&&p.tag===6?(a(x,p.sibling),O=n(p,w),O.return=x,x=O):(a(x,p),O=Er(w,x.mode,O),O.return=x,x=O),f(x)):a(x,p)}return function(x,p,w,O){try{Rn=0;var H=ve(x,p,w,O);return Cl=null,H}catch(Z){if(Z===wn||Z===Us)throw Z;var te=dt(29,Z,null,x.mode);return te.lanes=O,te.return=x,te}finally{}}}var kl=Zf(!0),Qf=Zf(!1),Nt=Ye(null),qt=null;function Sa(e){var t=e.alternate;F(qe,qe.current&1),F(Nt,e),qt===null&&(t===null||Ml.current!==null||t.memoizedState!==null)&&(qt=e)}function Vf(e){if(e.tag===22){if(F(qe,qe.current),F(Nt,e),qt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(qt=e)}}else ja()}function ja(){F(qe,qe.current),F(Nt,Nt.current)}function ta(e){je(Nt),qt===e&&(qt=null),je(qe)}var qe=Ye(0);function Js(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Fu(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function ru(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:S({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var uu={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=yt(),n=pa(l);n.payload=t,a!=null&&(n.callback=a),t=xa(e,n,l),t!==null&&(bt(t,e,l),An(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=yt(),n=pa(l);n.tag=1,n.payload=t,a!=null&&(n.callback=a),t=xa(e,n,l),t!==null&&(bt(t,e,l),An(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=yt(),l=pa(a);l.tag=2,t!=null&&(l.callback=t),t=xa(e,l,a),t!==null&&(bt(t,e,a),An(t,e,a))}};function Kf(e,t,a,l,n,i,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,i,f):t.prototype&&t.prototype.isPureReactComponent?!gn(a,l)||!gn(n,i):!0}function Jf(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&uu.enqueueReplaceState(t,t.state,null)}function el(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=S({},a));for(var n in e)a[n]===void 0&&(a[n]=e[n])}return a}var Ws=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Wf(e){Ws(e)}function Ff(e){console.error(e)}function $f(e){Ws(e)}function Fs(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Pf(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function cu(e,t,a){return a=pa(a),a.tag=3,a.payload={element:null},a.callback=function(){Fs(e,t)},a}function If(e){return e=pa(e),e.tag=3,e}function ed(e,t,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var i=l.value;e.payload=function(){return n(i)},e.callback=function(){Pf(t,a,l)}}var f=a.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){Pf(t,a,l),typeof n!="function"&&(za===null?za=new Set([this]):za.add(this));var m=l.stack;this.componentDidCatch(l.value,{componentStack:m!==null?m:""})})}function Hg(e,t,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&vn(t,a,n,!0),a=Nt.current,a!==null){switch(a.tag){case 13:return qt===null?Cu():a.alternate===null&&Oe===0&&(Oe=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===qr?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),Uu(e,l,n)),!1;case 22:return a.flags|=65536,l===qr?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),Uu(e,l,n)),!1}throw Error(u(435,a.tag))}return Uu(e,l,n),Cu(),!1}if(me)return t=Nt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,l!==Dr&&(e=Error(u(422),{cause:l}),xn(vt(e,a)))):(l!==Dr&&(t=Error(u(423),{cause:l}),xn(vt(t,a))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,l=vt(l,a),n=cu(e.stateNode,l,n),Yr(e,n),Oe!==4&&(Oe=2)),!1;var i=Error(u(520),{cause:l});if(i=vt(i,a),Ln===null?Ln=[i]:Ln.push(i),Oe!==4&&(Oe=2),t===null)return!0;l=vt(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=n&-n,a.lanes|=e,e=cu(a.stateNode,l,e),Yr(a,e),!1;case 1:if(t=a.type,i=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(za===null||!za.has(i))))return a.flags|=65536,n&=-n,a.lanes|=n,n=If(n),ed(n,e,a,l),Yr(a,n),!1}a=a.return}while(a!==null);return!1}var td=Error(u(461)),Xe=!1;function Ve(e,t,a,l){t.child=e===null?Qf(t,null,a,l):kl(t,e.child,a,l)}function ad(e,t,a,l,n){a=a.render;var i=t.ref;if("ref"in l){var f={};for(var m in l)m!=="ref"&&(f[m]=l[m])}else f=l;return $a(t),l=Vr(e,t,a,f,i,n),m=Kr(),e!==null&&!Xe?(Jr(e,t,n),aa(e,t,n)):(me&&m&&Mr(t),t.flags|=1,Ve(e,t,l,n),t.child)}function ld(e,t,a,l,n){if(e===null){var i=a.type;return typeof i=="function"&&!Tr(i)&&i.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=i,nd(e,t,i,l,n)):(e=Ms(a.type,null,l,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!bu(e,n)){var f=i.memoizedProps;if(a=a.compare,a=a!==null?a:gn,a(f,l)&&e.ref===t.ref)return aa(e,t,n)}return t.flags|=1,e=Wt(i,l),e.ref=t.ref,e.return=t,t.child=e}function nd(e,t,a,l,n){if(e!==null){var i=e.memoizedProps;if(gn(i,l)&&e.ref===t.ref)if(Xe=!1,t.pendingProps=l=i,bu(e,n))(e.flags&131072)!==0&&(Xe=!0);else return t.lanes=e.lanes,aa(e,t,n)}return ou(e,t,a,l,n)}function sd(e,t,a){var l=t.pendingProps,n=l.children,i=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=i!==null?i.baseLanes|a:a,e!==null){for(n=t.child=e.child,i=0;n!==null;)i=i|n.lanes|n.childLanes,n=n.sibling;t.childLanes=i&~l}else t.childLanes=0,t.child=null;return id(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&ks(t,i!==null?i.cachePool:null),i!==null?lf(t,i):Xr(),Vf(t);else return t.lanes=t.childLanes=536870912,id(e,t,i!==null?i.baseLanes|a:a,a)}else i!==null?(ks(t,i.cachePool),lf(t,i),ja(),t.memoizedState=null):(e!==null&&ks(t,null),Xr(),ja());return Ve(e,t,n,a),t.child}function id(e,t,a,l){var n=Br();return n=n===null?null:{parent:Be._currentValue,pool:n},t.memoizedState={baseLanes:a,cachePool:n},e!==null&&ks(t,null),Xr(),Vf(t),e!==null&&vn(e,t,l,!0),null}function $s(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(u(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function ou(e,t,a,l,n){return $a(t),a=Vr(e,t,a,l,void 0,n),l=Kr(),e!==null&&!Xe?(Jr(e,t,n),aa(e,t,n)):(me&&l&&Mr(t),t.flags|=1,Ve(e,t,a,n),t.child)}function rd(e,t,a,l,n,i){return $a(t),t.updateQueue=null,a=sf(t,l,a,n),nf(e),l=Kr(),e!==null&&!Xe?(Jr(e,t,i),aa(e,t,i)):(me&&l&&Mr(t),t.flags|=1,Ve(e,t,a,i),t.child)}function ud(e,t,a,l,n){if($a(t),t.stateNode===null){var i=Nl,f=a.contextType;typeof f=="object"&&f!==null&&(i=Fe(f)),i=new a(l,i),t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=uu,t.stateNode=i,i._reactInternals=t,i=t.stateNode,i.props=l,i.state=t.memoizedState,i.refs={},Hr(t),f=a.contextType,i.context=typeof f=="object"&&f!==null?Fe(f):Nl,i.state=t.memoizedState,f=a.getDerivedStateFromProps,typeof f=="function"&&(ru(t,a,f,l),i.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(f=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),f!==i.state&&uu.enqueueReplaceState(i,i.state,null),En(t,l,i,n),Tn(),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){i=t.stateNode;var m=t.memoizedProps,y=el(a,m);i.props=y;var A=i.context,M=a.contextType;f=Nl,typeof M=="object"&&M!==null&&(f=Fe(M));var R=a.getDerivedStateFromProps;M=typeof R=="function"||typeof i.getSnapshotBeforeUpdate=="function",m=t.pendingProps!==m,M||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(m||A!==f)&&Jf(t,i,l,f),ba=!1;var T=t.memoizedState;i.state=T,En(t,l,i,n),Tn(),A=t.memoizedState,m||T!==A||ba?(typeof R=="function"&&(ru(t,a,R,l),A=t.memoizedState),(y=ba||Kf(t,a,y,l,T,A,f))?(M||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=A),i.props=l,i.state=A,i.context=f,l=y):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{i=t.stateNode,Lr(e,t),f=t.memoizedProps,M=el(a,f),i.props=M,R=t.pendingProps,T=i.context,A=a.contextType,y=Nl,typeof A=="object"&&A!==null&&(y=Fe(A)),m=a.getDerivedStateFromProps,(A=typeof m=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(f!==R||T!==y)&&Jf(t,i,l,y),ba=!1,T=t.memoizedState,i.state=T,En(t,l,i,n),Tn();var E=t.memoizedState;f!==R||T!==E||ba||e!==null&&e.dependencies!==null&&Rs(e.dependencies)?(typeof m=="function"&&(ru(t,a,m,l),E=t.memoizedState),(M=ba||Kf(t,a,M,l,T,E,y)||e!==null&&e.dependencies!==null&&Rs(e.dependencies))?(A||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(l,E,y),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(l,E,y)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||f===e.memoizedProps&&T===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&T===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=E),i.props=l,i.state=E,i.context=y,l=M):(typeof i.componentDidUpdate!="function"||f===e.memoizedProps&&T===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&T===e.memoizedState||(t.flags|=1024),l=!1)}return i=l,$s(e,t),l=(t.flags&128)!==0,i||l?(i=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:i.render(),t.flags|=1,e!==null&&l?(t.child=kl(t,e.child,null,n),t.child=kl(t,null,a,n)):Ve(e,t,a,n),t.memoizedState=i.state,e=t.child):e=aa(e,t,n),e}function cd(e,t,a,l){return pn(),t.flags|=256,Ve(e,t,a,l),t.child}var fu={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function du(e){return{baseLanes:e,cachePool:Wo()}}function mu(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=At),e}function od(e,t,a){var l=t.pendingProps,n=!1,i=(t.flags&128)!==0,f;if((f=i)||(f=e!==null&&e.memoizedState===null?!1:(qe.current&2)!==0),f&&(n=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(me){if(n?Sa(t):ja(),me){var m=Me,y;if(y=m){e:{for(y=m,m=Bt;y.nodeType!==8;){if(!m){m=null;break e}if(y=Ct(y.nextSibling),y===null){m=null;break e}}m=y}m!==null?(t.memoizedState={dehydrated:m,treeContext:Va!==null?{id:Ft,overflow:$t}:null,retryLane:536870912,hydrationErrors:null},y=dt(18,null,null,0),y.stateNode=m,y.return=t,t.child=y,Ie=t,Me=null,y=!0):y=!1}y||Wa(t)}if(m=t.memoizedState,m!==null&&(m=m.dehydrated,m!==null))return Fu(m)?t.lanes=32:t.lanes=536870912,null;ta(t)}return m=l.children,l=l.fallback,n?(ja(),n=t.mode,m=Ps({mode:"hidden",children:m},n),l=Qa(l,n,a,null),m.return=t,l.return=t,m.sibling=l,t.child=m,n=t.child,n.memoizedState=du(a),n.childLanes=mu(e,f,a),t.memoizedState=fu,l):(Sa(t),hu(t,m))}if(y=e.memoizedState,y!==null&&(m=y.dehydrated,m!==null)){if(i)t.flags&256?(Sa(t),t.flags&=-257,t=gu(e,t,a)):t.memoizedState!==null?(ja(),t.child=e.child,t.flags|=128,t=null):(ja(),n=l.fallback,m=t.mode,l=Ps({mode:"visible",children:l.children},m),n=Qa(n,m,a,null),n.flags|=2,l.return=t,n.return=t,l.sibling=n,t.child=l,kl(t,e.child,null,a),l=t.child,l.memoizedState=du(a),l.childLanes=mu(e,f,a),t.memoizedState=fu,t=n);else if(Sa(t),Fu(m)){if(f=m.nextSibling&&m.nextSibling.dataset,f)var A=f.dgst;f=A,l=Error(u(419)),l.stack="",l.digest=f,xn({value:l,source:null,stack:null}),t=gu(e,t,a)}else if(Xe||vn(e,t,a,!1),f=(a&e.childLanes)!==0,Xe||f){if(f=Ne,f!==null&&(l=a&-a,l=(l&42)!==0?1:$i(l),l=(l&(f.suspendedLanes|a))!==0?0:l,l!==0&&l!==y.retryLane))throw y.retryLane=l,wl(e,l),bt(f,e,l),td;m.data==="$?"||Cu(),t=gu(e,t,a)}else m.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=y.treeContext,Me=Ct(m.nextSibling),Ie=t,me=!0,Ja=null,Bt=!1,e!==null&&(jt[wt++]=Ft,jt[wt++]=$t,jt[wt++]=Va,Ft=e.id,$t=e.overflow,Va=t),t=hu(t,l.children),t.flags|=4096);return t}return n?(ja(),n=l.fallback,m=t.mode,y=e.child,A=y.sibling,l=Wt(y,{mode:"hidden",children:l.children}),l.subtreeFlags=y.subtreeFlags&65011712,A!==null?n=Wt(A,n):(n=Qa(n,m,a,null),n.flags|=2),n.return=t,l.return=t,l.sibling=n,t.child=l,l=n,n=t.child,m=e.child.memoizedState,m===null?m=du(a):(y=m.cachePool,y!==null?(A=Be._currentValue,y=y.parent!==A?{parent:A,pool:A}:y):y=Wo(),m={baseLanes:m.baseLanes|a,cachePool:y}),n.memoizedState=m,n.childLanes=mu(e,f,a),t.memoizedState=fu,l):(Sa(t),a=e.child,e=a.sibling,a=Wt(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=a,t.memoizedState=null,a)}function hu(e,t){return t=Ps({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Ps(e,t){return e=dt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function gu(e,t,a){return kl(t,e.child,null,a),e=hu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function fd(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Cr(e.return,t,a)}function yu(e,t,a,l,n){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=l,i.tail=a,i.tailMode=n)}function dd(e,t,a){var l=t.pendingProps,n=l.revealOrder,i=l.tail;if(Ve(e,t,l.children,a),l=qe.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&fd(e,a,t);else if(e.tag===19)fd(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(F(qe,l),n){case"forwards":for(a=t.child,n=null;a!==null;)e=a.alternate,e!==null&&Js(e)===null&&(n=a),a=a.sibling;a=n,a===null?(n=t.child,t.child=null):(n=a.sibling,a.sibling=null),yu(t,!1,n,a,i);break;case"backwards":for(a=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Js(e)===null){t.child=n;break}e=n.sibling,n.sibling=a,a=n,n=e}yu(t,!0,a,null,i);break;case"together":yu(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function aa(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Ea|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(vn(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(u(153));if(t.child!==null){for(e=t.child,a=Wt(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Wt(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function bu(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Rs(e)))}function Lg(e,t,a){switch(t.tag){case 3:Ha(t,t.stateNode.containerInfo),ya(t,Be,e.memoizedState.cache),pn();break;case 27:case 5:oa(t);break;case 4:Ha(t,t.stateNode.containerInfo);break;case 10:ya(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(Sa(t),t.flags|=128,null):(a&t.child.childLanes)!==0?od(e,t,a):(Sa(t),e=aa(e,t,a),e!==null?e.sibling:null);Sa(t);break;case 19:var n=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(vn(e,t,a,!1),l=(a&t.childLanes)!==0),n){if(l)return dd(e,t,a);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),F(qe,qe.current),l)break;return null;case 22:case 23:return t.lanes=0,sd(e,t,a);case 24:ya(t,Be,e.memoizedState.cache)}return aa(e,t,a)}function md(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)Xe=!0;else{if(!bu(e,a)&&(t.flags&128)===0)return Xe=!1,Lg(e,t,a);Xe=(e.flags&131072)!==0}else Xe=!1,me&&(t.flags&1048576)!==0&&Go(t,Ds,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,n=l._init;if(l=n(l._payload),t.type=l,typeof l=="function")Tr(l)?(e=el(l,e),t.tag=1,t=ud(null,t,l,e,a)):(t.tag=0,t=ou(null,t,l,e,a));else{if(l!=null){if(n=l.$$typeof,n===de){t.tag=11,t=ad(null,t,l,e,a);break e}else if(n===Se){t.tag=14,t=ld(null,t,l,e,a);break e}}throw t=zt(l)||l,Error(u(306,t,""))}}return t;case 0:return ou(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,n=el(l,t.pendingProps),ud(e,t,l,n,a);case 3:e:{if(Ha(t,t.stateNode.containerInfo),e===null)throw Error(u(387));l=t.pendingProps;var i=t.memoizedState;n=i.element,Lr(e,t),En(t,l,null,a);var f=t.memoizedState;if(l=f.cache,ya(t,Be,l),l!==i.cache&&kr(t,[Be],a,!0),Tn(),l=f.element,i.isDehydrated)if(i={element:l,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){t=cd(e,t,l,a);break e}else if(l!==n){n=vt(Error(u(424)),t),xn(n),t=cd(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Me=Ct(e.firstChild),Ie=t,me=!0,Ja=null,Bt=!0,a=Qf(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(pn(),l===n){t=aa(e,t,a);break e}Ve(e,t,l,a)}t=t.child}return t;case 26:return $s(e,t),e===null?(a=bm(t.type,null,t.pendingProps,null))?t.memoizedState=a:me||(a=t.type,e=t.pendingProps,l=di(Mt.current).createElement(a),l[We]=t,l[at]=e,Je(l,a,e),Ge(l),t.stateNode=l):t.memoizedState=bm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return oa(t),e===null&&me&&(l=t.stateNode=hm(t.type,t.pendingProps,Mt.current),Ie=t,Bt=!0,n=Me,Da(t.type)?($u=n,Me=Ct(l.firstChild)):Me=n),Ve(e,t,t.pendingProps.children,a),$s(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&me&&((n=l=Me)&&(l=hy(l,t.type,t.pendingProps,Bt),l!==null?(t.stateNode=l,Ie=t,Me=Ct(l.firstChild),Bt=!1,n=!0):n=!1),n||Wa(t)),oa(t),n=t.type,i=t.pendingProps,f=e!==null?e.memoizedProps:null,l=i.children,Ku(n,i)?l=null:f!==null&&Ku(n,f)&&(t.flags|=32),t.memoizedState!==null&&(n=Vr(e,t,Rg,null,null,a),Wn._currentValue=n),$s(e,t),Ve(e,t,l,a),t.child;case 6:return e===null&&me&&((e=a=Me)&&(a=gy(a,t.pendingProps,Bt),a!==null?(t.stateNode=a,Ie=t,Me=null,e=!0):e=!1),e||Wa(t)),null;case 13:return od(e,t,a);case 4:return Ha(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=kl(t,null,l,a):Ve(e,t,l,a),t.child;case 11:return ad(e,t,t.type,t.pendingProps,a);case 7:return Ve(e,t,t.pendingProps,a),t.child;case 8:return Ve(e,t,t.pendingProps.children,a),t.child;case 12:return Ve(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,ya(t,t.type,l.value),Ve(e,t,l.children,a),t.child;case 9:return n=t.type._context,l=t.pendingProps.children,$a(t),n=Fe(n),l=l(n),t.flags|=1,Ve(e,t,l,a),t.child;case 14:return ld(e,t,t.type,t.pendingProps,a);case 15:return nd(e,t,t.type,t.pendingProps,a);case 19:return dd(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=Ps(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Wt(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return sd(e,t,a);case 24:return $a(t),l=Fe(Be),e===null?(n=Br(),n===null&&(n=Ne,i=Ur(),n.pooledCache=i,i.refCount++,i!==null&&(n.pooledCacheLanes|=a),n=i),t.memoizedState={parent:l,cache:n},Hr(t),ya(t,Be,n)):((e.lanes&a)!==0&&(Lr(e,t),En(t,null,null,a),Tn()),n=e.memoizedState,i=t.memoizedState,n.parent!==l?(n={parent:l,cache:l},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),ya(t,Be,l)):(l=i.cache,ya(t,Be,l),l!==n.cache&&kr(t,[Be],a,!0))),Ve(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(u(156,t.tag))}function la(e){e.flags|=4}function hd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!jm(t)){if(t=Nt.current,t!==null&&((ue&4194048)===ue?qt!==null:(ue&62914560)!==ue&&(ue&536870912)===0||t!==qt))throw Nn=qr,Fo;e.flags|=8192}}function Is(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Zc():536870912,e.lanes|=t,ql|=t)}function kn(e,t){if(!me)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function ze(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function Yg(e,t,a){var l=t.pendingProps;switch(Or(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ze(t),null;case 1:return ze(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),It(Be),Ot(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(bn(t)?la(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Qo())),ze(t),null;case 26:return a=t.memoizedState,e===null?(la(t),a!==null?(ze(t),hd(t,a)):(ze(t),t.flags&=-16777217)):a?a!==e.memoizedState?(la(t),ze(t),hd(t,a)):(ze(t),t.flags&=-16777217):(e.memoizedProps!==l&&la(t),ze(t),t.flags&=-16777217),null;case 27:fa(t),a=Mt.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&la(t);else{if(!l){if(t.stateNode===null)throw Error(u(166));return ze(t),null}e=oe.current,bn(t)?Xo(t):(e=hm(n,l,a),t.stateNode=e,la(t))}return ze(t),null;case 5:if(fa(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&la(t);else{if(!l){if(t.stateNode===null)throw Error(u(166));return ze(t),null}if(e=oe.current,bn(t))Xo(t);else{switch(n=di(Mt.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}e[We]=t,e[at]=l;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(Je(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&la(t)}}return ze(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&la(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(u(166));if(e=Mt.current,bn(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,n=Ie,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}e[We]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||rm(e.nodeValue,a)),e||Wa(t)}else e=di(e).createTextNode(l),e[We]=t,t.stateNode=e}return ze(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=bn(t),l!==null&&l.dehydrated!==null){if(e===null){if(!n)throw Error(u(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(u(317));n[We]=t}else pn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;ze(t),n=!1}else n=Qo(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(ta(t),t):(ta(t),null)}if(ta(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var i=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(i=l.memoizedState.cachePool.pool),i!==n&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),Is(t,t.updateQueue),ze(t),null;case 4:return Ot(),e===null&&Gu(t.stateNode.containerInfo),ze(t),null;case 10:return It(t.type),ze(t),null;case 19:if(je(qe),n=t.memoizedState,n===null)return ze(t),null;if(l=(t.flags&128)!==0,i=n.rendering,i===null)if(l)kn(n,!1);else{if(Oe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(i=Js(e),i!==null){for(t.flags|=128,kn(n,!1),e=i.updateQueue,t.updateQueue=e,Is(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Yo(a,e),a=a.sibling;return F(qe,qe.current&1|2),t.child}e=e.sibling}n.tail!==null&&_t()>ai&&(t.flags|=128,l=!0,kn(n,!1),t.lanes=4194304)}else{if(!l)if(e=Js(i),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,Is(t,e),kn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!i.alternate&&!me)return ze(t),null}else 2*_t()-n.renderingStartTime>ai&&a!==536870912&&(t.flags|=128,l=!0,kn(n,!1),t.lanes=4194304);n.isBackwards?(i.sibling=t.child,t.child=i):(e=n.last,e!==null?e.sibling=i:t.child=i,n.last=i)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=_t(),t.sibling=null,e=qe.current,F(qe,l?e&1|2:e&1),t):(ze(t),null);case 22:case 23:return ta(t),Zr(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(ze(t),t.subtreeFlags&6&&(t.flags|=8192)):ze(t),a=t.updateQueue,a!==null&&Is(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&je(Pa),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),It(Be),ze(t),null;case 25:return null;case 30:return null}throw Error(u(156,t.tag))}function Gg(e,t){switch(Or(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return It(Be),Ot(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return fa(t),null;case 13:if(ta(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(u(340));pn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return je(qe),null;case 4:return Ot(),null;case 10:return It(t.type),null;case 22:case 23:return ta(t),Zr(),e!==null&&je(Pa),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return It(Be),null;case 25:return null;default:return null}}function gd(e,t){switch(Or(t),t.tag){case 3:It(Be),Ot();break;case 26:case 27:case 5:fa(t);break;case 4:Ot();break;case 13:ta(t);break;case 19:je(qe);break;case 10:It(t.type);break;case 22:case 23:ta(t),Zr(),e!==null&&je(Pa);break;case 24:It(Be)}}function Un(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&e)===e){l=void 0;var i=a.create,f=a.inst;l=i(),f.destroy=l}a=a.next}while(a!==n)}}catch(m){we(t,t.return,m)}}function wa(e,t,a){try{var l=t.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var i=n.next;l=i;do{if((l.tag&e)===e){var f=l.inst,m=f.destroy;if(m!==void 0){f.destroy=void 0,n=t;var y=a,A=m;try{A()}catch(M){we(n,y,M)}}}l=l.next}while(l!==i)}}catch(M){we(t,t.return,M)}}function yd(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{af(t,a)}catch(l){we(e,e.return,l)}}}function bd(e,t,a){a.props=el(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){we(e,t,l)}}function _n(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(n){we(e,t,n)}}function Ht(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){we(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){we(e,t,n)}else a.current=null}function pd(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){we(e,e.return,n)}}function pu(e,t,a){try{var l=e.stateNode;cy(l,e.type,a,t),l[at]=t}catch(n){we(e,e.return,n)}}function xd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Da(e.type)||e.tag===4}function xu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||xd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Da(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function vu(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=fi));else if(l!==4&&(l===27&&Da(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(vu(e,t,a),e=e.sibling;e!==null;)vu(e,t,a),e=e.sibling}function ei(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&Da(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(ei(e,t,a),e=e.sibling;e!==null;)ei(e,t,a),e=e.sibling}function vd(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);Je(t,l,a),t[We]=e,t[at]=a}catch(i){we(e,e.return,i)}}var na=!1,Re=!1,Su=!1,Sd=typeof WeakSet=="function"?WeakSet:Set,Ze=null;function Xg(e,t){if(e=e.containerInfo,Qu=pi,e=Do(e),xr(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,i=l.focusNode;l=l.focusOffset;try{a.nodeType,i.nodeType}catch{a=null;break e}var f=0,m=-1,y=-1,A=0,M=0,R=e,T=null;t:for(;;){for(var E;R!==a||n!==0&&R.nodeType!==3||(m=f+n),R!==i||l!==0&&R.nodeType!==3||(y=f+l),R.nodeType===3&&(f+=R.nodeValue.length),(E=R.firstChild)!==null;)T=R,R=E;for(;;){if(R===e)break t;if(T===a&&++A===n&&(m=f),T===i&&++M===l&&(y=f),(E=R.nextSibling)!==null)break;R=T,T=R.parentNode}R=E}a=m===-1||y===-1?null:{start:m,end:y}}else a=null}a=a||{start:0,end:0}}else a=null;for(Vu={focusedElem:e,selectionRange:a},pi=!1,Ze=t;Ze!==null;)if(t=Ze,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ze=e;else for(;Ze!==null;){switch(t=Ze,i=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&i!==null){e=void 0,a=t,n=i.memoizedProps,i=i.memoizedState,l=a.stateNode;try{var W=el(a.type,n,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(W,i),l.__reactInternalSnapshotBeforeUpdate=e}catch(V){we(a,a.return,V)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Wu(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Wu(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(u(163))}if(e=t.sibling,e!==null){e.return=t.return,Ze=e;break}Ze=t.return}}function jd(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:Na(e,a),l&4&&Un(5,a);break;case 1:if(Na(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(f){we(a,a.return,f)}else{var n=el(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){we(a,a.return,f)}}l&64&&yd(a),l&512&&_n(a,a.return);break;case 3:if(Na(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{af(e,t)}catch(f){we(a,a.return,f)}}break;case 27:t===null&&l&4&&vd(a);case 26:case 5:Na(e,a),t===null&&l&4&&pd(a),l&512&&_n(a,a.return);break;case 12:Na(e,a);break;case 13:Na(e,a),l&4&&Ad(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Pg.bind(null,a),yy(e,a))));break;case 22:if(l=a.memoizedState!==null||na,!l){t=t!==null&&t.memoizedState!==null||Re,n=na;var i=Re;na=l,(Re=t)&&!i?Aa(e,a,(a.subtreeFlags&8772)!==0):Na(e,a),na=n,Re=i}break;case 30:break;default:Na(e,a)}}function wd(e){var t=e.alternate;t!==null&&(e.alternate=null,wd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&er(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ae=null,st=!1;function sa(e,t,a){for(a=a.child;a!==null;)Nd(e,t,a),a=a.sibling}function Nd(e,t,a){if(ct&&typeof ct.onCommitFiberUnmount=="function")try{ct.onCommitFiberUnmount(an,a)}catch{}switch(a.tag){case 26:Re||Ht(a,t),sa(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Re||Ht(a,t);var l=Ae,n=st;Da(a.type)&&(Ae=a.stateNode,st=!1),sa(e,t,a),Qn(a.stateNode),Ae=l,st=n;break;case 5:Re||Ht(a,t);case 6:if(l=Ae,n=st,Ae=null,sa(e,t,a),Ae=l,st=n,Ae!==null)if(st)try{(Ae.nodeType===9?Ae.body:Ae.nodeName==="HTML"?Ae.ownerDocument.body:Ae).removeChild(a.stateNode)}catch(i){we(a,t,i)}else try{Ae.removeChild(a.stateNode)}catch(i){we(a,t,i)}break;case 18:Ae!==null&&(st?(e=Ae,dm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),In(e)):dm(Ae,a.stateNode));break;case 4:l=Ae,n=st,Ae=a.stateNode.containerInfo,st=!0,sa(e,t,a),Ae=l,st=n;break;case 0:case 11:case 14:case 15:Re||wa(2,a,t),Re||wa(4,a,t),sa(e,t,a);break;case 1:Re||(Ht(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&bd(a,t,l)),sa(e,t,a);break;case 21:sa(e,t,a);break;case 22:Re=(l=Re)||a.memoizedState!==null,sa(e,t,a),Re=l;break;default:sa(e,t,a)}}function Ad(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{In(e)}catch(a){we(t,t.return,a)}}function Zg(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Sd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Sd),t;default:throw Error(u(435,e.tag))}}function ju(e,t){var a=Zg(e);t.forEach(function(l){var n=Ig.bind(null,e,l);a.has(l)||(a.add(l),l.then(n,n))})}function mt(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],i=e,f=t,m=f;e:for(;m!==null;){switch(m.tag){case 27:if(Da(m.type)){Ae=m.stateNode,st=!1;break e}break;case 5:Ae=m.stateNode,st=!1;break e;case 3:case 4:Ae=m.stateNode.containerInfo,st=!0;break e}m=m.return}if(Ae===null)throw Error(u(160));Nd(i,f,n),Ae=null,st=!1,i=n.alternate,i!==null&&(i.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Td(t,e),t=t.sibling}var Rt=null;function Td(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:mt(t,e),ht(e),l&4&&(wa(3,e,e.return),Un(3,e),wa(5,e,e.return));break;case 1:mt(t,e),ht(e),l&512&&(Re||a===null||Ht(a,a.return)),l&64&&na&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=Rt;if(mt(t,e),ht(e),l&512&&(Re||a===null||Ht(a,a.return)),l&4){var i=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,n=n.ownerDocument||n;t:switch(l){case"title":i=n.getElementsByTagName("title")[0],(!i||i[sn]||i[We]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=n.createElement(l),n.head.insertBefore(i,n.querySelector("head > title"))),Je(i,l,a),i[We]=e,Ge(i),l=i;break e;case"link":var f=vm("link","href",n).get(l+(a.href||""));if(f){for(var m=0;m<f.length;m++)if(i=f[m],i.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&i.getAttribute("rel")===(a.rel==null?null:a.rel)&&i.getAttribute("title")===(a.title==null?null:a.title)&&i.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){f.splice(m,1);break t}}i=n.createElement(l),Je(i,l,a),n.head.appendChild(i);break;case"meta":if(f=vm("meta","content",n).get(l+(a.content||""))){for(m=0;m<f.length;m++)if(i=f[m],i.getAttribute("content")===(a.content==null?null:""+a.content)&&i.getAttribute("name")===(a.name==null?null:a.name)&&i.getAttribute("property")===(a.property==null?null:a.property)&&i.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&i.getAttribute("charset")===(a.charSet==null?null:a.charSet)){f.splice(m,1);break t}}i=n.createElement(l),Je(i,l,a),n.head.appendChild(i);break;default:throw Error(u(468,l))}i[We]=e,Ge(i),l=i}e.stateNode=l}else Sm(n,e.type,e.stateNode);else e.stateNode=xm(n,l,e.memoizedProps);else i!==l?(i===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):i.count--,l===null?Sm(n,e.type,e.stateNode):xm(n,l,e.memoizedProps)):l===null&&e.stateNode!==null&&pu(e,e.memoizedProps,a.memoizedProps)}break;case 27:mt(t,e),ht(e),l&512&&(Re||a===null||Ht(a,a.return)),a!==null&&l&4&&pu(e,e.memoizedProps,a.memoizedProps);break;case 5:if(mt(t,e),ht(e),l&512&&(Re||a===null||Ht(a,a.return)),e.flags&32){n=e.stateNode;try{yl(n,"")}catch(E){we(e,e.return,E)}}l&4&&e.stateNode!=null&&(n=e.memoizedProps,pu(e,n,a!==null?a.memoizedProps:n)),l&1024&&(Su=!0);break;case 6:if(mt(t,e),ht(e),l&4){if(e.stateNode===null)throw Error(u(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(E){we(e,e.return,E)}}break;case 3:if(gi=null,n=Rt,Rt=mi(t.containerInfo),mt(t,e),Rt=n,ht(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{In(t.containerInfo)}catch(E){we(e,e.return,E)}Su&&(Su=!1,Ed(e));break;case 4:l=Rt,Rt=mi(e.stateNode.containerInfo),mt(t,e),ht(e),Rt=l;break;case 12:mt(t,e),ht(e);break;case 13:mt(t,e),ht(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(zu=_t()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,ju(e,l)));break;case 22:n=e.memoizedState!==null;var y=a!==null&&a.memoizedState!==null,A=na,M=Re;if(na=A||n,Re=M||y,mt(t,e),Re=M,na=A,ht(e),l&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(a===null||y||na||Re||tl(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){y=a=t;try{if(i=y.stateNode,n)f=i.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{m=y.stateNode;var R=y.memoizedProps.style,T=R!=null&&R.hasOwnProperty("display")?R.display:null;m.style.display=T==null||typeof T=="boolean"?"":(""+T).trim()}}catch(E){we(y,y.return,E)}}}else if(t.tag===6){if(a===null){y=t;try{y.stateNode.nodeValue=n?"":y.memoizedProps}catch(E){we(y,y.return,E)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,ju(e,a))));break;case 19:mt(t,e),ht(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,ju(e,l)));break;case 30:break;case 21:break;default:mt(t,e),ht(e)}}function ht(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(xd(l)){a=l;break}l=l.return}if(a==null)throw Error(u(160));switch(a.tag){case 27:var n=a.stateNode,i=xu(e);ei(e,i,n);break;case 5:var f=a.stateNode;a.flags&32&&(yl(f,""),a.flags&=-33);var m=xu(e);ei(e,m,f);break;case 3:case 4:var y=a.stateNode.containerInfo,A=xu(e);vu(e,A,y);break;default:throw Error(u(161))}}catch(M){we(e,e.return,M)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Ed(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Ed(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Na(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)jd(e,t.alternate,t),t=t.sibling}function tl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:wa(4,t,t.return),tl(t);break;case 1:Ht(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&bd(t,t.return,a),tl(t);break;case 27:Qn(t.stateNode);case 26:case 5:Ht(t,t.return),tl(t);break;case 22:t.memoizedState===null&&tl(t);break;case 30:tl(t);break;default:tl(t)}e=e.sibling}}function Aa(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,n=e,i=t,f=i.flags;switch(i.tag){case 0:case 11:case 15:Aa(n,i,a),Un(4,i);break;case 1:if(Aa(n,i,a),l=i,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(A){we(l,l.return,A)}if(l=i,n=l.updateQueue,n!==null){var m=l.stateNode;try{var y=n.shared.hiddenCallbacks;if(y!==null)for(n.shared.hiddenCallbacks=null,n=0;n<y.length;n++)tf(y[n],m)}catch(A){we(l,l.return,A)}}a&&f&64&&yd(i),_n(i,i.return);break;case 27:vd(i);case 26:case 5:Aa(n,i,a),a&&l===null&&f&4&&pd(i),_n(i,i.return);break;case 12:Aa(n,i,a);break;case 13:Aa(n,i,a),a&&f&4&&Ad(n,i);break;case 22:i.memoizedState===null&&Aa(n,i,a),_n(i,i.return);break;case 30:break;default:Aa(n,i,a)}t=t.sibling}}function wu(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Sn(a))}function Nu(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Sn(e))}function Lt(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)zd(e,t,a,l),t=t.sibling}function zd(e,t,a,l){var n=t.flags;switch(t.tag){case 0:case 11:case 15:Lt(e,t,a,l),n&2048&&Un(9,t);break;case 1:Lt(e,t,a,l);break;case 3:Lt(e,t,a,l),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Sn(e)));break;case 12:if(n&2048){Lt(e,t,a,l),e=t.stateNode;try{var i=t.memoizedProps,f=i.id,m=i.onPostCommit;typeof m=="function"&&m(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(y){we(t,t.return,y)}}else Lt(e,t,a,l);break;case 13:Lt(e,t,a,l);break;case 23:break;case 22:i=t.stateNode,f=t.alternate,t.memoizedState!==null?i._visibility&2?Lt(e,t,a,l):Bn(e,t):i._visibility&2?Lt(e,t,a,l):(i._visibility|=2,Ul(e,t,a,l,(t.subtreeFlags&10256)!==0)),n&2048&&wu(f,t);break;case 24:Lt(e,t,a,l),n&2048&&Nu(t.alternate,t);break;default:Lt(e,t,a,l)}}function Ul(e,t,a,l,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var i=e,f=t,m=a,y=l,A=f.flags;switch(f.tag){case 0:case 11:case 15:Ul(i,f,m,y,n),Un(8,f);break;case 23:break;case 22:var M=f.stateNode;f.memoizedState!==null?M._visibility&2?Ul(i,f,m,y,n):Bn(i,f):(M._visibility|=2,Ul(i,f,m,y,n)),n&&A&2048&&wu(f.alternate,f);break;case 24:Ul(i,f,m,y,n),n&&A&2048&&Nu(f.alternate,f);break;default:Ul(i,f,m,y,n)}t=t.sibling}}function Bn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,n=l.flags;switch(l.tag){case 22:Bn(a,l),n&2048&&wu(l.alternate,l);break;case 24:Bn(a,l),n&2048&&Nu(l.alternate,l);break;default:Bn(a,l)}t=t.sibling}}var qn=8192;function _l(e){if(e.subtreeFlags&qn)for(e=e.child;e!==null;)Md(e),e=e.sibling}function Md(e){switch(e.tag){case 26:_l(e),e.flags&qn&&e.memoizedState!==null&&My(Rt,e.memoizedState,e.memoizedProps);break;case 5:_l(e);break;case 3:case 4:var t=Rt;Rt=mi(e.stateNode.containerInfo),_l(e),Rt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=qn,qn=16777216,_l(e),qn=t):_l(e));break;default:_l(e)}}function Od(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Hn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Ze=l,Rd(l,e)}Od(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Dd(e),e=e.sibling}function Dd(e){switch(e.tag){case 0:case 11:case 15:Hn(e),e.flags&2048&&wa(9,e,e.return);break;case 3:Hn(e);break;case 12:Hn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,ti(e)):Hn(e);break;default:Hn(e)}}function ti(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Ze=l,Rd(l,e)}Od(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:wa(8,t,t.return),ti(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,ti(t));break;default:ti(t)}e=e.sibling}}function Rd(e,t){for(;Ze!==null;){var a=Ze;switch(a.tag){case 0:case 11:case 15:wa(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Sn(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Ze=l;else e:for(a=e;Ze!==null;){l=Ze;var n=l.sibling,i=l.return;if(wd(l),l===a){Ze=null;break e}if(n!==null){n.return=i,Ze=n;break e}Ze=i}}}var Qg={getCacheForType:function(e){var t=Fe(Be),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},Vg=typeof WeakMap=="function"?WeakMap:Map,ye=0,Ne=null,le=null,ue=0,be=0,gt=null,Ta=!1,Bl=!1,Au=!1,ia=0,Oe=0,Ea=0,al=0,Tu=0,At=0,ql=0,Ln=null,it=null,Eu=!1,zu=0,ai=1/0,li=null,za=null,Ke=0,Ma=null,Hl=null,Ll=0,Mu=0,Ou=null,Cd=null,Yn=0,Du=null;function yt(){if((ye&2)!==0&&ue!==0)return ue&-ue;if(v.T!==null){var e=El;return e!==0?e:qu()}return Kc()}function kd(){At===0&&(At=(ue&536870912)===0||me?Xc():536870912);var e=Nt.current;return e!==null&&(e.flags|=32),At}function bt(e,t,a){(e===Ne&&(be===2||be===9)||e.cancelPendingCommit!==null)&&(Yl(e,0),Oa(e,ue,At,!1)),nn(e,a),((ye&2)===0||e!==Ne)&&(e===Ne&&((ye&2)===0&&(al|=a),Oe===4&&Oa(e,ue,At,!1)),Yt(e))}function Ud(e,t,a){if((ye&6)!==0)throw Error(u(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||ln(e,t),n=l?Wg(e,t):ku(e,t,!0),i=l;do{if(n===0){Bl&&!l&&Oa(e,t,0,!1);break}else{if(a=e.current.alternate,i&&!Kg(a)){n=ku(e,t,!1),i=!1;continue}if(n===2){if(i=t,e.errorRecoveryDisabledLanes&i)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var m=e;n=Ln;var y=m.current.memoizedState.isDehydrated;if(y&&(Yl(m,f).flags|=256),f=ku(m,f,!1),f!==2){if(Au&&!y){m.errorRecoveryDisabledLanes|=i,al|=i,n=4;break e}i=it,it=n,i!==null&&(it===null?it=i:it.push.apply(it,i))}n=f}if(i=!1,n!==2)continue}}if(n===1){Yl(e,0),Oa(e,t,0,!0);break}e:{switch(l=e,i=n,i){case 0:case 1:throw Error(u(345));case 4:if((t&4194048)!==t)break;case 6:Oa(l,t,At,!Ta);break e;case 2:it=null;break;case 3:case 5:break;default:throw Error(u(329))}if((t&62914560)===t&&(n=zu+300-_t(),10<n)){if(Oa(l,t,At,!Ta),hs(l,0,!0)!==0)break e;l.timeoutHandle=om(_d.bind(null,l,a,it,li,Eu,t,At,al,ql,Ta,i,2,-0,0),n);break e}_d(l,a,it,li,Eu,t,At,al,ql,Ta,i,0,-0,0)}}break}while(!0);Yt(e)}function _d(e,t,a,l,n,i,f,m,y,A,M,R,T,E){if(e.timeoutHandle=-1,R=t.subtreeFlags,(R&8192||(R&16785408)===16785408)&&(Jn={stylesheets:null,count:0,unsuspend:zy},Md(t),R=Oy(),R!==null)){e.cancelPendingCommit=R(Xd.bind(null,e,t,i,a,l,n,f,m,y,M,1,T,E)),Oa(e,i,f,!A);return}Xd(e,t,i,a,l,n,f,m,y)}function Kg(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],i=n.getSnapshot;n=n.value;try{if(!ft(i(),n))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Oa(e,t,a,l){t&=~Tu,t&=~al,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var n=t;0<n;){var i=31-ot(n),f=1<<i;l[i]=-1,n&=~f}a!==0&&Qc(e,a,t)}function ni(){return(ye&6)===0?(Gn(0),!1):!0}function Ru(){if(le!==null){if(be===0)var e=le.return;else e=le,Pt=Fa=null,Wr(e),Cl=null,Rn=0,e=le;for(;e!==null;)gd(e.alternate,e),e=e.return;le=null}}function Yl(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,fy(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Ru(),Ne=e,le=a=Wt(e.current,null),ue=t,be=0,gt=null,Ta=!1,Bl=ln(e,t),Au=!1,ql=At=Tu=al=Ea=Oe=0,it=Ln=null,Eu=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var n=31-ot(l),i=1<<n;t|=e[n],l&=~i}return ia=t,Ts(),a}function Bd(e,t){I=null,v.H=Qs,t===wn||t===Us?(t=Io(),be=3):t===Fo?(t=Io(),be=4):be=t===td?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,gt=t,le===null&&(Oe=1,Fs(e,vt(t,e.current)))}function qd(){var e=v.H;return v.H=Qs,e===null?Qs:e}function Hd(){var e=v.A;return v.A=Qg,e}function Cu(){Oe=4,Ta||(ue&4194048)!==ue&&Nt.current!==null||(Bl=!0),(Ea&134217727)===0&&(al&134217727)===0||Ne===null||Oa(Ne,ue,At,!1)}function ku(e,t,a){var l=ye;ye|=2;var n=qd(),i=Hd();(Ne!==e||ue!==t)&&(li=null,Yl(e,t)),t=!1;var f=Oe;e:do try{if(be!==0&&le!==null){var m=le,y=gt;switch(be){case 8:Ru(),f=6;break e;case 3:case 2:case 9:case 6:Nt.current===null&&(t=!0);var A=be;if(be=0,gt=null,Gl(e,m,y,A),a&&Bl){f=0;break e}break;default:A=be,be=0,gt=null,Gl(e,m,y,A)}}Jg(),f=Oe;break}catch(M){Bd(e,M)}while(!0);return t&&e.shellSuspendCounter++,Pt=Fa=null,ye=l,v.H=n,v.A=i,le===null&&(Ne=null,ue=0,Ts()),f}function Jg(){for(;le!==null;)Ld(le)}function Wg(e,t){var a=ye;ye|=2;var l=qd(),n=Hd();Ne!==e||ue!==t?(li=null,ai=_t()+500,Yl(e,t)):Bl=ln(e,t);e:do try{if(be!==0&&le!==null){t=le;var i=gt;t:switch(be){case 1:be=0,gt=null,Gl(e,t,i,1);break;case 2:case 9:if($o(i)){be=0,gt=null,Yd(t);break}t=function(){be!==2&&be!==9||Ne!==e||(be=7),Yt(e)},i.then(t,t);break e;case 3:be=7;break e;case 4:be=5;break e;case 7:$o(i)?(be=0,gt=null,Yd(t)):(be=0,gt=null,Gl(e,t,i,7));break;case 5:var f=null;switch(le.tag){case 26:f=le.memoizedState;case 5:case 27:var m=le;if(!f||jm(f)){be=0,gt=null;var y=m.sibling;if(y!==null)le=y;else{var A=m.return;A!==null?(le=A,si(A)):le=null}break t}}be=0,gt=null,Gl(e,t,i,5);break;case 6:be=0,gt=null,Gl(e,t,i,6);break;case 8:Ru(),Oe=6;break e;default:throw Error(u(462))}}Fg();break}catch(M){Bd(e,M)}while(!0);return Pt=Fa=null,v.H=l,v.A=n,ye=a,le!==null?0:(Ne=null,ue=0,Ts(),Oe)}function Fg(){for(;le!==null&&!ph();)Ld(le)}function Ld(e){var t=md(e.alternate,e,ia);e.memoizedProps=e.pendingProps,t===null?si(e):le=t}function Yd(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=rd(a,t,t.pendingProps,t.type,void 0,ue);break;case 11:t=rd(a,t,t.pendingProps,t.type.render,t.ref,ue);break;case 5:Wr(t);default:gd(a,t),t=le=Yo(t,ia),t=md(a,t,ia)}e.memoizedProps=e.pendingProps,t===null?si(e):le=t}function Gl(e,t,a,l){Pt=Fa=null,Wr(t),Cl=null,Rn=0;var n=t.return;try{if(Hg(e,n,t,a,ue)){Oe=1,Fs(e,vt(a,e.current)),le=null;return}}catch(i){if(n!==null)throw le=n,i;Oe=1,Fs(e,vt(a,e.current)),le=null;return}t.flags&32768?(me||l===1?e=!0:Bl||(ue&536870912)!==0?e=!1:(Ta=e=!0,(l===2||l===9||l===3||l===6)&&(l=Nt.current,l!==null&&l.tag===13&&(l.flags|=16384))),Gd(t,e)):si(t)}function si(e){var t=e;do{if((t.flags&32768)!==0){Gd(t,Ta);return}e=t.return;var a=Yg(t.alternate,t,ia);if(a!==null){le=a;return}if(t=t.sibling,t!==null){le=t;return}le=t=e}while(t!==null);Oe===0&&(Oe=5)}function Gd(e,t){do{var a=Gg(e.alternate,e);if(a!==null){a.flags&=32767,le=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){le=e;return}le=e=a}while(e!==null);Oe=6,le=null}function Xd(e,t,a,l,n,i,f,m,y){e.cancelPendingCommit=null;do ii();while(Ke!==0);if((ye&6)!==0)throw Error(u(327));if(t!==null){if(t===e.current)throw Error(u(177));if(i=t.lanes|t.childLanes,i|=Nr,zh(e,a,i,f,m,y),e===Ne&&(le=Ne=null,ue=0),Hl=t,Ma=e,Ll=a,Mu=i,Ou=n,Cd=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,ey(fs,function(){return Jd(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=v.T,v.T=null,n=C.p,C.p=2,f=ye,ye|=4;try{Xg(e,t,a)}finally{ye=f,C.p=n,v.T=l}}Ke=1,Zd(),Qd(),Vd()}}function Zd(){if(Ke===1){Ke=0;var e=Ma,t=Hl,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=v.T,v.T=null;var l=C.p;C.p=2;var n=ye;ye|=4;try{Td(t,e);var i=Vu,f=Do(e.containerInfo),m=i.focusedElem,y=i.selectionRange;if(f!==m&&m&&m.ownerDocument&&Oo(m.ownerDocument.documentElement,m)){if(y!==null&&xr(m)){var A=y.start,M=y.end;if(M===void 0&&(M=A),"selectionStart"in m)m.selectionStart=A,m.selectionEnd=Math.min(M,m.value.length);else{var R=m.ownerDocument||document,T=R&&R.defaultView||window;if(T.getSelection){var E=T.getSelection(),W=m.textContent.length,V=Math.min(y.start,W),ve=y.end===void 0?V:Math.min(y.end,W);!E.extend&&V>ve&&(f=ve,ve=V,V=f);var x=Mo(m,V),p=Mo(m,ve);if(x&&p&&(E.rangeCount!==1||E.anchorNode!==x.node||E.anchorOffset!==x.offset||E.focusNode!==p.node||E.focusOffset!==p.offset)){var w=R.createRange();w.setStart(x.node,x.offset),E.removeAllRanges(),V>ve?(E.addRange(w),E.extend(p.node,p.offset)):(w.setEnd(p.node,p.offset),E.addRange(w))}}}}for(R=[],E=m;E=E.parentNode;)E.nodeType===1&&R.push({element:E,left:E.scrollLeft,top:E.scrollTop});for(typeof m.focus=="function"&&m.focus(),m=0;m<R.length;m++){var O=R[m];O.element.scrollLeft=O.left,O.element.scrollTop=O.top}}pi=!!Qu,Vu=Qu=null}finally{ye=n,C.p=l,v.T=a}}e.current=t,Ke=2}}function Qd(){if(Ke===2){Ke=0;var e=Ma,t=Hl,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=v.T,v.T=null;var l=C.p;C.p=2;var n=ye;ye|=4;try{jd(e,t.alternate,t)}finally{ye=n,C.p=l,v.T=a}}Ke=3}}function Vd(){if(Ke===4||Ke===3){Ke=0,xh();var e=Ma,t=Hl,a=Ll,l=Cd;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ke=5:(Ke=0,Hl=Ma=null,Kd(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(za=null),Pi(a),t=t.stateNode,ct&&typeof ct.onCommitFiberRoot=="function")try{ct.onCommitFiberRoot(an,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=v.T,n=C.p,C.p=2,v.T=null;try{for(var i=e.onRecoverableError,f=0;f<l.length;f++){var m=l[f];i(m.value,{componentStack:m.stack})}}finally{v.T=t,C.p=n}}(Ll&3)!==0&&ii(),Yt(e),n=e.pendingLanes,(a&4194090)!==0&&(n&42)!==0?e===Du?Yn++:(Yn=0,Du=e):Yn=0,Gn(0)}}function Kd(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Sn(t)))}function ii(e){return Zd(),Qd(),Vd(),Jd()}function Jd(){if(Ke!==5)return!1;var e=Ma,t=Mu;Mu=0;var a=Pi(Ll),l=v.T,n=C.p;try{C.p=32>a?32:a,v.T=null,a=Ou,Ou=null;var i=Ma,f=Ll;if(Ke=0,Hl=Ma=null,Ll=0,(ye&6)!==0)throw Error(u(331));var m=ye;if(ye|=4,Dd(i.current),zd(i,i.current,f,a),ye=m,Gn(0,!1),ct&&typeof ct.onPostCommitFiberRoot=="function")try{ct.onPostCommitFiberRoot(an,i)}catch{}return!0}finally{C.p=n,v.T=l,Kd(e,t)}}function Wd(e,t,a){t=vt(a,t),t=cu(e.stateNode,t,2),e=xa(e,t,2),e!==null&&(nn(e,2),Yt(e))}function we(e,t,a){if(e.tag===3)Wd(e,e,a);else for(;t!==null;){if(t.tag===3){Wd(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(za===null||!za.has(l))){e=vt(a,e),a=If(2),l=xa(t,a,2),l!==null&&(ed(a,l,t,e),nn(l,2),Yt(l));break}}t=t.return}}function Uu(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new Vg;var n=new Set;l.set(t,n)}else n=l.get(t),n===void 0&&(n=new Set,l.set(t,n));n.has(a)||(Au=!0,n.add(a),e=$g.bind(null,e,t,a),t.then(e,e))}function $g(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Ne===e&&(ue&a)===a&&(Oe===4||Oe===3&&(ue&62914560)===ue&&300>_t()-zu?(ye&2)===0&&Yl(e,0):Tu|=a,ql===ue&&(ql=0)),Yt(e)}function Fd(e,t){t===0&&(t=Zc()),e=wl(e,t),e!==null&&(nn(e,t),Yt(e))}function Pg(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Fd(e,a)}function Ig(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,n=e.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(u(314))}l!==null&&l.delete(t),Fd(e,a)}function ey(e,t){return Ji(e,t)}var ri=null,Xl=null,_u=!1,ui=!1,Bu=!1,ll=0;function Yt(e){e!==Xl&&e.next===null&&(Xl===null?ri=Xl=e:Xl=Xl.next=e),ui=!0,_u||(_u=!0,ay())}function Gn(e,t){if(!Bu&&ui){Bu=!0;do for(var a=!1,l=ri;l!==null;){if(e!==0){var n=l.pendingLanes;if(n===0)var i=0;else{var f=l.suspendedLanes,m=l.pingedLanes;i=(1<<31-ot(42|e)+1)-1,i&=n&~(f&~m),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(a=!0,em(l,i))}else i=ue,i=hs(l,l===Ne?i:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(i&3)===0||ln(l,i)||(a=!0,em(l,i));l=l.next}while(a);Bu=!1}}function ty(){$d()}function $d(){ui=_u=!1;var e=0;ll!==0&&(oy()&&(e=ll),ll=0);for(var t=_t(),a=null,l=ri;l!==null;){var n=l.next,i=Pd(l,t);i===0?(l.next=null,a===null?ri=n:a.next=n,n===null&&(Xl=a)):(a=l,(e!==0||(i&3)!==0)&&(ui=!0)),l=n}Gn(e)}function Pd(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,n=e.expirationTimes,i=e.pendingLanes&-62914561;0<i;){var f=31-ot(i),m=1<<f,y=n[f];y===-1?((m&a)===0||(m&l)!==0)&&(n[f]=Eh(m,t)):y<=t&&(e.expiredLanes|=m),i&=~m}if(t=Ne,a=ue,a=hs(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(be===2||be===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&Wi(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||ln(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&Wi(l),Pi(a)){case 2:case 8:a=Yc;break;case 32:a=fs;break;case 268435456:a=Gc;break;default:a=fs}return l=Id.bind(null,e),a=Ji(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&Wi(l),e.callbackPriority=2,e.callbackNode=null,2}function Id(e,t){if(Ke!==0&&Ke!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(ii()&&e.callbackNode!==a)return null;var l=ue;return l=hs(e,e===Ne?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Ud(e,l,t),Pd(e,_t()),e.callbackNode!=null&&e.callbackNode===a?Id.bind(null,e):null)}function em(e,t){if(ii())return null;Ud(e,t,!0)}function ay(){dy(function(){(ye&6)!==0?Ji(Lc,ty):$d()})}function qu(){return ll===0&&(ll=Xc()),ll}function tm(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:xs(""+e)}function am(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function ly(e,t,a,l,n){if(t==="submit"&&a&&a.stateNode===n){var i=tm((n[at]||null).action),f=l.submitter;f&&(t=(t=f[at]||null)?tm(t.formAction):f.getAttribute("formAction"),t!==null&&(i=t,f=null));var m=new ws("action","action",null,l,n);e.push({event:m,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(ll!==0){var y=f?am(n,f):new FormData(n);nu(a,{pending:!0,data:y,method:n.method,action:i},null,y)}}else typeof i=="function"&&(m.preventDefault(),y=f?am(n,f):new FormData(n),nu(a,{pending:!0,data:y,method:n.method,action:i},i,y))},currentTarget:n}]})}}for(var Hu=0;Hu<wr.length;Hu++){var Lu=wr[Hu],ny=Lu.toLowerCase(),sy=Lu[0].toUpperCase()+Lu.slice(1);Dt(ny,"on"+sy)}Dt(ko,"onAnimationEnd"),Dt(Uo,"onAnimationIteration"),Dt(_o,"onAnimationStart"),Dt("dblclick","onDoubleClick"),Dt("focusin","onFocus"),Dt("focusout","onBlur"),Dt(jg,"onTransitionRun"),Dt(wg,"onTransitionStart"),Dt(Ng,"onTransitionCancel"),Dt(Bo,"onTransitionEnd"),ml("onMouseEnter",["mouseout","mouseover"]),ml("onMouseLeave",["mouseout","mouseover"]),ml("onPointerEnter",["pointerout","pointerover"]),ml("onPointerLeave",["pointerout","pointerover"]),Ya("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ya("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ya("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ya("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ya("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ya("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Xn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),iy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Xn));function lm(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],n=l.event;l=l.listeners;e:{var i=void 0;if(t)for(var f=l.length-1;0<=f;f--){var m=l[f],y=m.instance,A=m.currentTarget;if(m=m.listener,y!==i&&n.isPropagationStopped())break e;i=m,n.currentTarget=A;try{i(n)}catch(M){Ws(M)}n.currentTarget=null,i=y}else for(f=0;f<l.length;f++){if(m=l[f],y=m.instance,A=m.currentTarget,m=m.listener,y!==i&&n.isPropagationStopped())break e;i=m,n.currentTarget=A;try{i(n)}catch(M){Ws(M)}n.currentTarget=null,i=y}}}}function ne(e,t){var a=t[Ii];a===void 0&&(a=t[Ii]=new Set);var l=e+"__bubble";a.has(l)||(nm(t,e,2,!1),a.add(l))}function Yu(e,t,a){var l=0;t&&(l|=4),nm(a,e,l,t)}var ci="_reactListening"+Math.random().toString(36).slice(2);function Gu(e){if(!e[ci]){e[ci]=!0,Wc.forEach(function(a){a!=="selectionchange"&&(iy.has(a)||Yu(a,!1,e),Yu(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ci]||(t[ci]=!0,Yu("selectionchange",!1,t))}}function nm(e,t,a,l){switch(zm(t)){case 2:var n=Cy;break;case 8:n=ky;break;default:n=ac}a=n.bind(null,t,a,e),n=void 0,!or||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),l?n!==void 0?e.addEventListener(t,a,{capture:!0,passive:n}):e.addEventListener(t,a,!0):n!==void 0?e.addEventListener(t,a,{passive:n}):e.addEventListener(t,a,!1)}function Xu(e,t,a,l,n){var i=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var m=l.stateNode.containerInfo;if(m===n)break;if(f===4)for(f=l.return;f!==null;){var y=f.tag;if((y===3||y===4)&&f.stateNode.containerInfo===n)return;f=f.return}for(;m!==null;){if(f=ol(m),f===null)return;if(y=f.tag,y===5||y===6||y===26||y===27){l=i=f;continue e}m=m.parentNode}}l=l.return}co(function(){var A=i,M=ur(a),R=[];e:{var T=qo.get(e);if(T!==void 0){var E=ws,W=e;switch(e){case"keypress":if(Ss(a)===0)break e;case"keydown":case"keyup":E=eg;break;case"focusin":W="focus",E=hr;break;case"focusout":W="blur",E=hr;break;case"beforeblur":case"afterblur":E=hr;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":E=mo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":E=Gh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":E=lg;break;case ko:case Uo:case _o:E=Qh;break;case Bo:E=sg;break;case"scroll":case"scrollend":E=Lh;break;case"wheel":E=rg;break;case"copy":case"cut":case"paste":E=Kh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":E=go;break;case"toggle":case"beforetoggle":E=cg}var V=(t&4)!==0,ve=!V&&(e==="scroll"||e==="scrollend"),x=V?T!==null?T+"Capture":null:T;V=[];for(var p=A,w;p!==null;){var O=p;if(w=O.stateNode,O=O.tag,O!==5&&O!==26&&O!==27||w===null||x===null||(O=un(p,x),O!=null&&V.push(Zn(p,O,w))),ve)break;p=p.return}0<V.length&&(T=new E(T,W,null,a,M),R.push({event:T,listeners:V}))}}if((t&7)===0){e:{if(T=e==="mouseover"||e==="pointerover",E=e==="mouseout"||e==="pointerout",T&&a!==rr&&(W=a.relatedTarget||a.fromElement)&&(ol(W)||W[cl]))break e;if((E||T)&&(T=M.window===M?M:(T=M.ownerDocument)?T.defaultView||T.parentWindow:window,E?(W=a.relatedTarget||a.toElement,E=A,W=W?ol(W):null,W!==null&&(ve=h(W),V=W.tag,W!==ve||V!==5&&V!==27&&V!==6)&&(W=null)):(E=null,W=A),E!==W)){if(V=mo,O="onMouseLeave",x="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(V=go,O="onPointerLeave",x="onPointerEnter",p="pointer"),ve=E==null?T:rn(E),w=W==null?T:rn(W),T=new V(O,p+"leave",E,a,M),T.target=ve,T.relatedTarget=w,O=null,ol(M)===A&&(V=new V(x,p+"enter",W,a,M),V.target=w,V.relatedTarget=ve,O=V),ve=O,E&&W)t:{for(V=E,x=W,p=0,w=V;w;w=Zl(w))p++;for(w=0,O=x;O;O=Zl(O))w++;for(;0<p-w;)V=Zl(V),p--;for(;0<w-p;)x=Zl(x),w--;for(;p--;){if(V===x||x!==null&&V===x.alternate)break t;V=Zl(V),x=Zl(x)}V=null}else V=null;E!==null&&sm(R,T,E,V,!1),W!==null&&ve!==null&&sm(R,ve,W,V,!0)}}e:{if(T=A?rn(A):window,E=T.nodeName&&T.nodeName.toLowerCase(),E==="select"||E==="input"&&T.type==="file")var H=wo;else if(So(T))if(No)H=xg;else{H=bg;var te=yg}else E=T.nodeName,!E||E.toLowerCase()!=="input"||T.type!=="checkbox"&&T.type!=="radio"?A&&ir(A.elementType)&&(H=wo):H=pg;if(H&&(H=H(e,A))){jo(R,H,a,M);break e}te&&te(e,T,A),e==="focusout"&&A&&T.type==="number"&&A.memoizedProps.value!=null&&sr(T,"number",T.value)}switch(te=A?rn(A):window,e){case"focusin":(So(te)||te.contentEditable==="true")&&(vl=te,vr=A,yn=null);break;case"focusout":yn=vr=vl=null;break;case"mousedown":Sr=!0;break;case"contextmenu":case"mouseup":case"dragend":Sr=!1,Ro(R,a,M);break;case"selectionchange":if(Sg)break;case"keydown":case"keyup":Ro(R,a,M)}var Z;if(yr)e:{switch(e){case"compositionstart":var J="onCompositionStart";break e;case"compositionend":J="onCompositionEnd";break e;case"compositionupdate":J="onCompositionUpdate";break e}J=void 0}else xl?xo(e,a)&&(J="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(J="onCompositionStart");J&&(yo&&a.locale!=="ko"&&(xl||J!=="onCompositionStart"?J==="onCompositionEnd"&&xl&&(Z=oo()):(ga=M,fr="value"in ga?ga.value:ga.textContent,xl=!0)),te=oi(A,J),0<te.length&&(J=new ho(J,e,null,a,M),R.push({event:J,listeners:te}),Z?J.data=Z:(Z=vo(a),Z!==null&&(J.data=Z)))),(Z=fg?dg(e,a):mg(e,a))&&(J=oi(A,"onBeforeInput"),0<J.length&&(te=new ho("onBeforeInput","beforeinput",null,a,M),R.push({event:te,listeners:J}),te.data=Z)),ly(R,e,A,a,M)}lm(R,t)})}function Zn(e,t,a){return{instance:e,listener:t,currentTarget:a}}function oi(e,t){for(var a=t+"Capture",l=[];e!==null;){var n=e,i=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||i===null||(n=un(e,a),n!=null&&l.unshift(Zn(e,n,i)),n=un(e,t),n!=null&&l.push(Zn(e,n,i))),e.tag===3)return l;e=e.return}return[]}function Zl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function sm(e,t,a,l,n){for(var i=t._reactName,f=[];a!==null&&a!==l;){var m=a,y=m.alternate,A=m.stateNode;if(m=m.tag,y!==null&&y===l)break;m!==5&&m!==26&&m!==27||A===null||(y=A,n?(A=un(a,i),A!=null&&f.unshift(Zn(a,A,y))):n||(A=un(a,i),A!=null&&f.push(Zn(a,A,y)))),a=a.return}f.length!==0&&e.push({event:t,listeners:f})}var ry=/\r\n?/g,uy=/\u0000|\uFFFD/g;function im(e){return(typeof e=="string"?e:""+e).replace(ry,`
`).replace(uy,"")}function rm(e,t){return t=im(t),im(e)===t}function fi(){}function xe(e,t,a,l,n,i){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||yl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&yl(e,""+l);break;case"className":ys(e,"class",l);break;case"tabIndex":ys(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":ys(e,a,l);break;case"style":ro(e,l,i);break;case"data":if(t!=="object"){ys(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=xs(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(a==="formAction"?(t!=="input"&&xe(e,t,"name",n.name,n,null),xe(e,t,"formEncType",n.formEncType,n,null),xe(e,t,"formMethod",n.formMethod,n,null),xe(e,t,"formTarget",n.formTarget,n,null)):(xe(e,t,"encType",n.encType,n,null),xe(e,t,"method",n.method,n,null),xe(e,t,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=xs(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=fi);break;case"onScroll":l!=null&&ne("scroll",e);break;case"onScrollEnd":l!=null&&ne("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(u(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=xs(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":ne("beforetoggle",e),ne("toggle",e),gs(e,"popover",l);break;case"xlinkActuate":Kt(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Kt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Kt(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Kt(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Kt(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Kt(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Kt(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Kt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Kt(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":gs(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=qh.get(a)||a,gs(e,a,l))}}function Zu(e,t,a,l,n,i){switch(a){case"style":ro(e,l,i);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(u(60));e.innerHTML=a}}break;case"children":typeof l=="string"?yl(e,l):(typeof l=="number"||typeof l=="bigint")&&yl(e,""+l);break;case"onScroll":l!=null&&ne("scroll",e);break;case"onScrollEnd":l!=null&&ne("scrollend",e);break;case"onClick":l!=null&&(e.onclick=fi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Fc.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),t=a.slice(2,n?a.length-7:void 0),i=e[at]||null,i=i!=null?i[a]:null,typeof i=="function"&&e.removeEventListener(t,i,n),typeof l=="function")){typeof i!="function"&&i!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,n);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):gs(e,a,l)}}}function Je(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ne("error",e),ne("load",e);var l=!1,n=!1,i;for(i in a)if(a.hasOwnProperty(i)){var f=a[i];if(f!=null)switch(i){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:xe(e,t,i,f,a,null)}}n&&xe(e,t,"srcSet",a.srcSet,a,null),l&&xe(e,t,"src",a.src,a,null);return;case"input":ne("invalid",e);var m=i=f=n=null,y=null,A=null;for(l in a)if(a.hasOwnProperty(l)){var M=a[l];if(M!=null)switch(l){case"name":n=M;break;case"type":f=M;break;case"checked":y=M;break;case"defaultChecked":A=M;break;case"value":i=M;break;case"defaultValue":m=M;break;case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(u(137,t));break;default:xe(e,t,l,M,a,null)}}lo(e,i,m,y,A,f,n,!1),bs(e);return;case"select":ne("invalid",e),l=f=i=null;for(n in a)if(a.hasOwnProperty(n)&&(m=a[n],m!=null))switch(n){case"value":i=m;break;case"defaultValue":f=m;break;case"multiple":l=m;default:xe(e,t,n,m,a,null)}t=i,a=f,e.multiple=!!l,t!=null?gl(e,!!l,t,!1):a!=null&&gl(e,!!l,a,!0);return;case"textarea":ne("invalid",e),i=n=l=null;for(f in a)if(a.hasOwnProperty(f)&&(m=a[f],m!=null))switch(f){case"value":l=m;break;case"defaultValue":n=m;break;case"children":i=m;break;case"dangerouslySetInnerHTML":if(m!=null)throw Error(u(91));break;default:xe(e,t,f,m,a,null)}so(e,l,n,i),bs(e);return;case"option":for(y in a)if(a.hasOwnProperty(y)&&(l=a[y],l!=null))switch(y){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:xe(e,t,y,l,a,null)}return;case"dialog":ne("beforetoggle",e),ne("toggle",e),ne("cancel",e),ne("close",e);break;case"iframe":case"object":ne("load",e);break;case"video":case"audio":for(l=0;l<Xn.length;l++)ne(Xn[l],e);break;case"image":ne("error",e),ne("load",e);break;case"details":ne("toggle",e);break;case"embed":case"source":case"link":ne("error",e),ne("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(A in a)if(a.hasOwnProperty(A)&&(l=a[A],l!=null))switch(A){case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:xe(e,t,A,l,a,null)}return;default:if(ir(t)){for(M in a)a.hasOwnProperty(M)&&(l=a[M],l!==void 0&&Zu(e,t,M,l,a,void 0));return}}for(m in a)a.hasOwnProperty(m)&&(l=a[m],l!=null&&xe(e,t,m,l,a,null))}function cy(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,i=null,f=null,m=null,y=null,A=null,M=null;for(E in a){var R=a[E];if(a.hasOwnProperty(E)&&R!=null)switch(E){case"checked":break;case"value":break;case"defaultValue":y=R;default:l.hasOwnProperty(E)||xe(e,t,E,null,l,R)}}for(var T in l){var E=l[T];if(R=a[T],l.hasOwnProperty(T)&&(E!=null||R!=null))switch(T){case"type":i=E;break;case"name":n=E;break;case"checked":A=E;break;case"defaultChecked":M=E;break;case"value":f=E;break;case"defaultValue":m=E;break;case"children":case"dangerouslySetInnerHTML":if(E!=null)throw Error(u(137,t));break;default:E!==R&&xe(e,t,T,E,l,R)}}nr(e,f,m,y,A,M,i,n);return;case"select":E=f=m=T=null;for(i in a)if(y=a[i],a.hasOwnProperty(i)&&y!=null)switch(i){case"value":break;case"multiple":E=y;default:l.hasOwnProperty(i)||xe(e,t,i,null,l,y)}for(n in l)if(i=l[n],y=a[n],l.hasOwnProperty(n)&&(i!=null||y!=null))switch(n){case"value":T=i;break;case"defaultValue":m=i;break;case"multiple":f=i;default:i!==y&&xe(e,t,n,i,l,y)}t=m,a=f,l=E,T!=null?gl(e,!!a,T,!1):!!l!=!!a&&(t!=null?gl(e,!!a,t,!0):gl(e,!!a,a?[]:"",!1));return;case"textarea":E=T=null;for(m in a)if(n=a[m],a.hasOwnProperty(m)&&n!=null&&!l.hasOwnProperty(m))switch(m){case"value":break;case"children":break;default:xe(e,t,m,null,l,n)}for(f in l)if(n=l[f],i=a[f],l.hasOwnProperty(f)&&(n!=null||i!=null))switch(f){case"value":T=n;break;case"defaultValue":E=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(u(91));break;default:n!==i&&xe(e,t,f,n,l,i)}no(e,T,E);return;case"option":for(var W in a)if(T=a[W],a.hasOwnProperty(W)&&T!=null&&!l.hasOwnProperty(W))switch(W){case"selected":e.selected=!1;break;default:xe(e,t,W,null,l,T)}for(y in l)if(T=l[y],E=a[y],l.hasOwnProperty(y)&&T!==E&&(T!=null||E!=null))switch(y){case"selected":e.selected=T&&typeof T!="function"&&typeof T!="symbol";break;default:xe(e,t,y,T,l,E)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var V in a)T=a[V],a.hasOwnProperty(V)&&T!=null&&!l.hasOwnProperty(V)&&xe(e,t,V,null,l,T);for(A in l)if(T=l[A],E=a[A],l.hasOwnProperty(A)&&T!==E&&(T!=null||E!=null))switch(A){case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(u(137,t));break;default:xe(e,t,A,T,l,E)}return;default:if(ir(t)){for(var ve in a)T=a[ve],a.hasOwnProperty(ve)&&T!==void 0&&!l.hasOwnProperty(ve)&&Zu(e,t,ve,void 0,l,T);for(M in l)T=l[M],E=a[M],!l.hasOwnProperty(M)||T===E||T===void 0&&E===void 0||Zu(e,t,M,T,l,E);return}}for(var x in a)T=a[x],a.hasOwnProperty(x)&&T!=null&&!l.hasOwnProperty(x)&&xe(e,t,x,null,l,T);for(R in l)T=l[R],E=a[R],!l.hasOwnProperty(R)||T===E||T==null&&E==null||xe(e,t,R,T,l,E)}var Qu=null,Vu=null;function di(e){return e.nodeType===9?e:e.ownerDocument}function um(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function cm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Ku(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ju=null;function oy(){var e=window.event;return e&&e.type==="popstate"?e===Ju?!1:(Ju=e,!0):(Ju=null,!1)}var om=typeof setTimeout=="function"?setTimeout:void 0,fy=typeof clearTimeout=="function"?clearTimeout:void 0,fm=typeof Promise=="function"?Promise:void 0,dy=typeof queueMicrotask=="function"?queueMicrotask:typeof fm<"u"?function(e){return fm.resolve(null).then(e).catch(my)}:om;function my(e){setTimeout(function(){throw e})}function Da(e){return e==="head"}function dm(e,t){var a=t,l=0,n=0;do{var i=a.nextSibling;if(e.removeChild(a),i&&i.nodeType===8)if(a=i.data,a==="/$"){if(0<l&&8>l){a=l;var f=e.ownerDocument;if(a&1&&Qn(f.documentElement),a&2&&Qn(f.body),a&4)for(a=f.head,Qn(a),f=a.firstChild;f;){var m=f.nextSibling,y=f.nodeName;f[sn]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&f.rel.toLowerCase()==="stylesheet"||a.removeChild(f),f=m}}if(n===0){e.removeChild(i),In(t);return}n--}else a==="$"||a==="$?"||a==="$!"?n++:l=a.charCodeAt(0)-48;else l=0;a=i}while(a);In(t)}function Wu(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Wu(a),er(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function hy(e,t,a,l){for(;e.nodeType===1;){var n=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[sn])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(i=e.getAttribute("rel"),i==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(i!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(i=e.getAttribute("src"),(i!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&i&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var i=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===i)return e}else return e;if(e=Ct(e.nextSibling),e===null)break}return null}function gy(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Ct(e.nextSibling),e===null))return null;return e}function Fu(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function yy(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Ct(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var $u=null;function mm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function hm(e,t,a){switch(t=di(a),e){case"html":if(e=t.documentElement,!e)throw Error(u(452));return e;case"head":if(e=t.head,!e)throw Error(u(453));return e;case"body":if(e=t.body,!e)throw Error(u(454));return e;default:throw Error(u(451))}}function Qn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);er(e)}var Tt=new Map,gm=new Set;function mi(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ra=C.d;C.d={f:by,r:py,D:xy,C:vy,L:Sy,m:jy,X:Ny,S:wy,M:Ay};function by(){var e=ra.f(),t=ni();return e||t}function py(e){var t=fl(e);t!==null&&t.tag===5&&t.type==="form"?Uf(t):ra.r(e)}var Ql=typeof document>"u"?null:document;function ym(e,t,a){var l=Ql;if(l&&typeof t=="string"&&t){var n=xt(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),gm.has(n)||(gm.add(n),e={rel:e,crossOrigin:a,href:t},l.querySelector(n)===null&&(t=l.createElement("link"),Je(t,"link",e),Ge(t),l.head.appendChild(t)))}}function xy(e){ra.D(e),ym("dns-prefetch",e,null)}function vy(e,t){ra.C(e,t),ym("preconnect",e,t)}function Sy(e,t,a){ra.L(e,t,a);var l=Ql;if(l&&e&&t){var n='link[rel="preload"][as="'+xt(t)+'"]';t==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+xt(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+xt(a.imageSizes)+'"]')):n+='[href="'+xt(e)+'"]';var i=n;switch(t){case"style":i=Vl(e);break;case"script":i=Kl(e)}Tt.has(i)||(e=S({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Tt.set(i,e),l.querySelector(n)!==null||t==="style"&&l.querySelector(Vn(i))||t==="script"&&l.querySelector(Kn(i))||(t=l.createElement("link"),Je(t,"link",e),Ge(t),l.head.appendChild(t)))}}function jy(e,t){ra.m(e,t);var a=Ql;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+xt(l)+'"][href="'+xt(e)+'"]',i=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Kl(e)}if(!Tt.has(i)&&(e=S({rel:"modulepreload",href:e},t),Tt.set(i,e),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Kn(i)))return}l=a.createElement("link"),Je(l,"link",e),Ge(l),a.head.appendChild(l)}}}function wy(e,t,a){ra.S(e,t,a);var l=Ql;if(l&&e){var n=dl(l).hoistableStyles,i=Vl(e);t=t||"default";var f=n.get(i);if(!f){var m={loading:0,preload:null};if(f=l.querySelector(Vn(i)))m.loading=5;else{e=S({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Tt.get(i))&&Pu(e,a);var y=f=l.createElement("link");Ge(y),Je(y,"link",e),y._p=new Promise(function(A,M){y.onload=A,y.onerror=M}),y.addEventListener("load",function(){m.loading|=1}),y.addEventListener("error",function(){m.loading|=2}),m.loading|=4,hi(f,t,l)}f={type:"stylesheet",instance:f,count:1,state:m},n.set(i,f)}}}function Ny(e,t){ra.X(e,t);var a=Ql;if(a&&e){var l=dl(a).hoistableScripts,n=Kl(e),i=l.get(n);i||(i=a.querySelector(Kn(n)),i||(e=S({src:e,async:!0},t),(t=Tt.get(n))&&Iu(e,t),i=a.createElement("script"),Ge(i),Je(i,"link",e),a.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},l.set(n,i))}}function Ay(e,t){ra.M(e,t);var a=Ql;if(a&&e){var l=dl(a).hoistableScripts,n=Kl(e),i=l.get(n);i||(i=a.querySelector(Kn(n)),i||(e=S({src:e,async:!0,type:"module"},t),(t=Tt.get(n))&&Iu(e,t),i=a.createElement("script"),Ge(i),Je(i,"link",e),a.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},l.set(n,i))}}function bm(e,t,a,l){var n=(n=Mt.current)?mi(n):null;if(!n)throw Error(u(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Vl(a.href),a=dl(n).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Vl(a.href);var i=dl(n).hoistableStyles,f=i.get(e);if(f||(n=n.ownerDocument||n,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(e,f),(i=n.querySelector(Vn(e)))&&!i._p&&(f.instance=i,f.state.loading=5),Tt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Tt.set(e,a),i||Ty(n,e,a,f.state))),t&&l===null)throw Error(u(528,""));return f}if(t&&l!==null)throw Error(u(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Kl(a),a=dl(n).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(u(444,e))}}function Vl(e){return'href="'+xt(e)+'"'}function Vn(e){return'link[rel="stylesheet"]['+e+"]"}function pm(e){return S({},e,{"data-precedence":e.precedence,precedence:null})}function Ty(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),Je(t,"link",a),Ge(t),e.head.appendChild(t))}function Kl(e){return'[src="'+xt(e)+'"]'}function Kn(e){return"script[async]"+e}function xm(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+xt(a.href)+'"]');if(l)return t.instance=l,Ge(l),l;var n=S({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Ge(l),Je(l,"style",n),hi(l,a.precedence,e),t.instance=l;case"stylesheet":n=Vl(a.href);var i=e.querySelector(Vn(n));if(i)return t.state.loading|=4,t.instance=i,Ge(i),i;l=pm(a),(n=Tt.get(n))&&Pu(l,n),i=(e.ownerDocument||e).createElement("link"),Ge(i);var f=i;return f._p=new Promise(function(m,y){f.onload=m,f.onerror=y}),Je(i,"link",l),t.state.loading|=4,hi(i,a.precedence,e),t.instance=i;case"script":return i=Kl(a.src),(n=e.querySelector(Kn(i)))?(t.instance=n,Ge(n),n):(l=a,(n=Tt.get(i))&&(l=S({},a),Iu(l,n)),e=e.ownerDocument||e,n=e.createElement("script"),Ge(n),Je(n,"link",l),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(u(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,hi(l,a.precedence,e));return t.instance}function hi(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,i=n,f=0;f<l.length;f++){var m=l[f];if(m.dataset.precedence===t)i=m;else if(i!==n)break}i?i.parentNode.insertBefore(e,i.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Pu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Iu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var gi=null;function vm(e,t,a){if(gi===null){var l=new Map,n=gi=new Map;n.set(a,l)}else n=gi,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),n=0;n<a.length;n++){var i=a[n];if(!(i[sn]||i[We]||e==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var f=i.getAttribute(t)||"";f=e+f;var m=l.get(f);m?m.push(i):l.set(f,[i])}}return l}function Sm(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function Ey(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function jm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Jn=null;function zy(){}function My(e,t,a){if(Jn===null)throw Error(u(475));var l=Jn;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=Vl(a.href),i=e.querySelector(Vn(n));if(i){e=i._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=yi.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=i,Ge(i);return}i=e.ownerDocument||e,a=pm(a),(n=Tt.get(n))&&Pu(a,n),i=i.createElement("link"),Ge(i);var f=i;f._p=new Promise(function(m,y){f.onload=m,f.onerror=y}),Je(i,"link",a),t.instance=i}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=yi.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function Oy(){if(Jn===null)throw Error(u(475));var e=Jn;return e.stylesheets&&e.count===0&&ec(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&ec(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function yi(){if(this.count--,this.count===0){if(this.stylesheets)ec(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var bi=null;function ec(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,bi=new Map,t.forEach(Dy,e),bi=null,yi.call(e))}function Dy(e,t){if(!(t.state.loading&4)){var a=bi.get(e);if(a)var l=a.get(null);else{a=new Map,bi.set(e,a);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<n.length;i++){var f=n[i];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(a.set(f.dataset.precedence,f),l=f)}l&&a.set(null,l)}n=t.instance,f=n.getAttribute("data-precedence"),i=a.get(f)||l,i===l&&a.set(null,n),a.set(f,n),this.count++,l=yi.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),i?i.parentNode.insertBefore(n,i.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var Wn={$$typeof:K,Provider:null,Consumer:null,_currentValue:U,_currentValue2:U,_threadCount:0};function Ry(e,t,a,l,n,i,f,m){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Fi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Fi(0),this.hiddenUpdates=Fi(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=i,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=m,this.incompleteTransitions=new Map}function wm(e,t,a,l,n,i,f,m,y,A,M,R){return e=new Ry(e,t,a,f,m,y,A,R),t=1,i===!0&&(t|=24),i=dt(3,null,null,t),e.current=i,i.stateNode=e,t=Ur(),t.refCount++,e.pooledCache=t,t.refCount++,i.memoizedState={element:l,isDehydrated:a,cache:t},Hr(i),e}function Nm(e){return e?(e=Nl,e):Nl}function Am(e,t,a,l,n,i){n=Nm(n),l.context===null?l.context=n:l.pendingContext=n,l=pa(t),l.payload={element:a},i=i===void 0?null:i,i!==null&&(l.callback=i),a=xa(e,l,t),a!==null&&(bt(a,e,t),An(a,e,t))}function Tm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function tc(e,t){Tm(e,t),(e=e.alternate)&&Tm(e,t)}function Em(e){if(e.tag===13){var t=wl(e,67108864);t!==null&&bt(t,e,67108864),tc(e,67108864)}}var pi=!0;function Cy(e,t,a,l){var n=v.T;v.T=null;var i=C.p;try{C.p=2,ac(e,t,a,l)}finally{C.p=i,v.T=n}}function ky(e,t,a,l){var n=v.T;v.T=null;var i=C.p;try{C.p=8,ac(e,t,a,l)}finally{C.p=i,v.T=n}}function ac(e,t,a,l){if(pi){var n=lc(l);if(n===null)Xu(e,t,l,xi,a),Mm(e,l);else if(_y(n,e,t,a,l))l.stopPropagation();else if(Mm(e,l),t&4&&-1<Uy.indexOf(e)){for(;n!==null;){var i=fl(n);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var f=La(i.pendingLanes);if(f!==0){var m=i;for(m.pendingLanes|=2,m.entangledLanes|=2;f;){var y=1<<31-ot(f);m.entanglements[1]|=y,f&=~y}Yt(i),(ye&6)===0&&(ai=_t()+500,Gn(0))}}break;case 13:m=wl(i,2),m!==null&&bt(m,i,2),ni(),tc(i,2)}if(i=lc(l),i===null&&Xu(e,t,l,xi,a),i===n)break;n=i}n!==null&&l.stopPropagation()}else Xu(e,t,l,null,a)}}function lc(e){return e=ur(e),nc(e)}var xi=null;function nc(e){if(xi=null,e=ol(e),e!==null){var t=h(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=g(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return xi=e,null}function zm(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(vh()){case Lc:return 2;case Yc:return 8;case fs:case Sh:return 32;case Gc:return 268435456;default:return 32}default:return 32}}var sc=!1,Ra=null,Ca=null,ka=null,Fn=new Map,$n=new Map,Ua=[],Uy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Mm(e,t){switch(e){case"focusin":case"focusout":Ra=null;break;case"dragenter":case"dragleave":Ca=null;break;case"mouseover":case"mouseout":ka=null;break;case"pointerover":case"pointerout":Fn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":$n.delete(t.pointerId)}}function Pn(e,t,a,l,n,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:i,targetContainers:[n]},t!==null&&(t=fl(t),t!==null&&Em(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function _y(e,t,a,l,n){switch(t){case"focusin":return Ra=Pn(Ra,e,t,a,l,n),!0;case"dragenter":return Ca=Pn(Ca,e,t,a,l,n),!0;case"mouseover":return ka=Pn(ka,e,t,a,l,n),!0;case"pointerover":var i=n.pointerId;return Fn.set(i,Pn(Fn.get(i)||null,e,t,a,l,n)),!0;case"gotpointercapture":return i=n.pointerId,$n.set(i,Pn($n.get(i)||null,e,t,a,l,n)),!0}return!1}function Om(e){var t=ol(e.target);if(t!==null){var a=h(t);if(a!==null){if(t=a.tag,t===13){if(t=g(a),t!==null){e.blockedOn=t,Mh(e.priority,function(){if(a.tag===13){var l=yt();l=$i(l);var n=wl(a,l);n!==null&&bt(n,a,l),tc(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function vi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=lc(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);rr=l,a.target.dispatchEvent(l),rr=null}else return t=fl(a),t!==null&&Em(t),e.blockedOn=a,!1;t.shift()}return!0}function Dm(e,t,a){vi(e)&&a.delete(t)}function By(){sc=!1,Ra!==null&&vi(Ra)&&(Ra=null),Ca!==null&&vi(Ca)&&(Ca=null),ka!==null&&vi(ka)&&(ka=null),Fn.forEach(Dm),$n.forEach(Dm)}function Si(e,t){e.blockedOn===t&&(e.blockedOn=null,sc||(sc=!0,s.unstable_scheduleCallback(s.unstable_NormalPriority,By)))}var ji=null;function Rm(e){ji!==e&&(ji=e,s.unstable_scheduleCallback(s.unstable_NormalPriority,function(){ji===e&&(ji=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],n=e[t+2];if(typeof l!="function"){if(nc(l||a)===null)continue;break}var i=fl(a);i!==null&&(e.splice(t,3),t-=3,nu(i,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function In(e){function t(y){return Si(y,e)}Ra!==null&&Si(Ra,e),Ca!==null&&Si(Ca,e),ka!==null&&Si(ka,e),Fn.forEach(t),$n.forEach(t);for(var a=0;a<Ua.length;a++){var l=Ua[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Ua.length&&(a=Ua[0],a.blockedOn===null);)Om(a),a.blockedOn===null&&Ua.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],i=a[l+1],f=n[at]||null;if(typeof i=="function")f||Rm(a);else if(f){var m=null;if(i&&i.hasAttribute("formAction")){if(n=i,f=i[at]||null)m=f.formAction;else if(nc(n)!==null)continue}else m=f.action;typeof m=="function"?a[l+1]=m:(a.splice(l,3),l-=3),Rm(a)}}}function ic(e){this._internalRoot=e}wi.prototype.render=ic.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(u(409));var a=t.current,l=yt();Am(a,l,e,t,null,null)},wi.prototype.unmount=ic.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Am(e.current,2,null,e,null,null),ni(),t[cl]=null}};function wi(e){this._internalRoot=e}wi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Kc();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Ua.length&&t!==0&&t<Ua[a].priority;a++);Ua.splice(a,0,e),a===0&&Om(e)}};var Cm=r.version;if(Cm!=="19.1.0")throw Error(u(527,Cm,"19.1.0"));C.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(u(188)):(e=Object.keys(e).join(","),Error(u(268,e)));return e=N(t),e=e!==null?j(e):null,e=e===null?null:e.stateNode,e};var qy={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:v,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ni=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ni.isDisabled&&Ni.supportsFiber)try{an=Ni.inject(qy),ct=Ni}catch{}}return ts.createRoot=function(e,t){if(!d(e))throw Error(u(299));var a=!1,l="",n=Wf,i=Ff,f=$f,m=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(i=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(m=t.unstable_transitionCallbacks)),t=wm(e,1,!1,null,null,a,l,n,i,f,m,null),e[cl]=t.current,Gu(e),new ic(t)},ts.hydrateRoot=function(e,t,a){if(!d(e))throw Error(u(299));var l=!1,n="",i=Wf,f=Ff,m=$f,y=null,A=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(i=a.onUncaughtError),a.onCaughtError!==void 0&&(f=a.onCaughtError),a.onRecoverableError!==void 0&&(m=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(y=a.unstable_transitionCallbacks),a.formState!==void 0&&(A=a.formState)),t=wm(e,1,!0,t,a??null,l,n,i,f,m,y,A),t.context=Nm(null),a=t.current,l=yt(),l=$i(l),n=pa(l),n.callback=null,xa(a,n,l),a=l,t.current.lanes=a,nn(t,a),Yt(t),e[cl]=t.current,Gu(e),new wi(t)},ts.version="19.1.0",ts}var Lm;function xb(){if(Lm)return rc.exports;Lm=1;function s(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(s)}catch(r){console.error(r)}}return s(),rc.exports=pb(),rc.exports}var vb=xb();const w0=6e4,N0=36e5,Ai=43200,Ym=1440,Gm=Symbol.for("constructDateFrom");function Hi(s,r){return typeof s=="function"?s(r):s&&typeof s=="object"&&Gm in s?s[Gm](r):s instanceof Date?new s.constructor(r):new Date(r)}function kt(s,r){return Hi(s,s)}let Sb={};function jb(){return Sb}function Xm(s){const r=kt(s),c=new Date(Date.UTC(r.getFullYear(),r.getMonth(),r.getDate(),r.getHours(),r.getMinutes(),r.getSeconds(),r.getMilliseconds()));return c.setUTCFullYear(r.getFullYear()),+s-+c}function Rc(s,...r){const c=Hi.bind(null,s||r.find(u=>typeof u=="object"));return r.map(c)}function Oi(s,r){const c=+kt(s)-+kt(r);return c<0?-1:c>0?1:c}function wb(s){return Hi(s,Date.now())}function Nb(s){return s instanceof Date||typeof s=="object"&&Object.prototype.toString.call(s)==="[object Date]"}function Ab(s){return!(!Nb(s)&&typeof s!="number"||isNaN(+kt(s)))}function Tb(s,r,c){const[u,d]=Rc(c==null?void 0:c.in,s,r),h=u.getFullYear()-d.getFullYear(),g=u.getMonth()-d.getMonth();return h*12+g}function Eb(s){return r=>{const u=(s?Math[s]:Math.trunc)(r);return u===0?0:u}}function zb(s,r){return+kt(s)-+kt(r)}function Mb(s,r){const c=kt(s);return c.setHours(23,59,59,999),c}function Ob(s,r){const c=kt(s),u=c.getMonth();return c.setFullYear(c.getFullYear(),u+1,0),c.setHours(23,59,59,999),c}function Db(s,r){const c=kt(s);return+Mb(c)==+Ob(c)}function Rb(s,r,c){const[u,d,h]=Rc(c==null?void 0:c.in,s,s,r),g=Oi(d,h),b=Math.abs(Tb(d,h));if(b<1)return 0;d.getMonth()===1&&d.getDate()>27&&d.setDate(30),d.setMonth(d.getMonth()-g*b);let N=Oi(d,h)===-g;Db(u)&&b===1&&Oi(u,h)===1&&(N=!1);const j=g*(b-+N);return j===0?0:j}function Cb(s,r,c){const u=zb(s,r)/1e3;return Eb(c==null?void 0:c.roundingMethod)(u)}const kb={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Ub=(s,r,c)=>{let u;const d=kb[s];return typeof d=="string"?u=d:r===1?u=d.one:u=d.other.replace("{{count}}",r.toString()),c!=null&&c.addSuffix?c.comparison&&c.comparison>0?"in "+u:u+" ago":u};function Wl(s){return(r={})=>{const c=r.width?String(r.width):s.defaultWidth;return s.formats[c]||s.formats[s.defaultWidth]}}const _b={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Bb={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},qb={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Hb={date:Wl({formats:_b,defaultWidth:"full"}),time:Wl({formats:Bb,defaultWidth:"full"}),dateTime:Wl({formats:qb,defaultWidth:"full"})},Lb={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Yb=(s,r,c,u)=>Lb[s];function Xt(s){return(r,c)=>{const u=c!=null&&c.context?String(c.context):"standalone";let d;if(u==="formatting"&&s.formattingValues){const g=s.defaultFormattingWidth||s.defaultWidth,b=c!=null&&c.width?String(c.width):g;d=s.formattingValues[b]||s.formattingValues[g]}else{const g=s.defaultWidth,b=c!=null&&c.width?String(c.width):s.defaultWidth;d=s.values[b]||s.values[g]}const h=s.argumentCallback?s.argumentCallback(r):r;return d[h]}}const Gb={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Xb={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Zb={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Qb={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Vb={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Kb={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Jb=(s,r)=>{const c=Number(s),u=c%100;if(u>20||u<10)switch(u%10){case 1:return c+"st";case 2:return c+"nd";case 3:return c+"rd"}return c+"th"},Wb={ordinalNumber:Jb,era:Xt({values:Gb,defaultWidth:"wide"}),quarter:Xt({values:Xb,defaultWidth:"wide",argumentCallback:s=>s-1}),month:Xt({values:Zb,defaultWidth:"wide"}),day:Xt({values:Qb,defaultWidth:"wide"}),dayPeriod:Xt({values:Vb,defaultWidth:"wide",formattingValues:Kb,defaultFormattingWidth:"wide"})};function Zt(s){return(r,c={})=>{const u=c.width,d=u&&s.matchPatterns[u]||s.matchPatterns[s.defaultMatchWidth],h=r.match(d);if(!h)return null;const g=h[0],b=u&&s.parsePatterns[u]||s.parsePatterns[s.defaultParseWidth],N=Array.isArray(b)?$b(b,D=>D.test(g)):Fb(b,D=>D.test(g));let j;j=s.valueCallback?s.valueCallback(N):N,j=c.valueCallback?c.valueCallback(j):j;const S=r.slice(g.length);return{value:j,rest:S}}}function Fb(s,r){for(const c in s)if(Object.prototype.hasOwnProperty.call(s,c)&&r(s[c]))return c}function $b(s,r){for(let c=0;c<s.length;c++)if(r(s[c]))return c}function A0(s){return(r,c={})=>{const u=r.match(s.matchPattern);if(!u)return null;const d=u[0],h=r.match(s.parsePattern);if(!h)return null;let g=s.valueCallback?s.valueCallback(h[0]):h[0];g=c.valueCallback?c.valueCallback(g):g;const b=r.slice(d.length);return{value:g,rest:b}}}const Pb=/^(\d+)(th|st|nd|rd)?/i,Ib=/\d+/i,ep={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},tp={any:[/^b/i,/^(a|c)/i]},ap={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},lp={any:[/1/i,/2/i,/3/i,/4/i]},np={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},sp={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},ip={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},rp={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},up={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},cp={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},op={ordinalNumber:A0({matchPattern:Pb,parsePattern:Ib,valueCallback:s=>parseInt(s,10)}),era:Zt({matchPatterns:ep,defaultMatchWidth:"wide",parsePatterns:tp,defaultParseWidth:"any"}),quarter:Zt({matchPatterns:ap,defaultMatchWidth:"wide",parsePatterns:lp,defaultParseWidth:"any",valueCallback:s=>s+1}),month:Zt({matchPatterns:np,defaultMatchWidth:"wide",parsePatterns:sp,defaultParseWidth:"any"}),day:Zt({matchPatterns:ip,defaultMatchWidth:"wide",parsePatterns:rp,defaultParseWidth:"any"}),dayPeriod:Zt({matchPatterns:up,defaultMatchWidth:"any",parsePatterns:cp,defaultParseWidth:"any"})},fp={code:"en-US",formatDistance:Ub,formatLong:Hb,formatRelative:Yb,localize:Wb,match:op,options:{weekStartsOn:0,firstWeekContainsDate:1}};function dp(s,r,c){const u=jb(),d=(c==null?void 0:c.locale)??u.locale??fp,h=2520,g=Oi(s,r);if(isNaN(g))throw new RangeError("Invalid time value");const b=Object.assign({},c,{addSuffix:c==null?void 0:c.addSuffix,comparison:g}),[N,j]=Rc(c==null?void 0:c.in,...g>0?[r,s]:[s,r]),S=Cb(j,N),D=(Xm(j)-Xm(N))/1e3,k=Math.round((S-D)/60);let X;if(k<2)return c!=null&&c.includeSeconds?S<5?d.formatDistance("lessThanXSeconds",5,b):S<10?d.formatDistance("lessThanXSeconds",10,b):S<20?d.formatDistance("lessThanXSeconds",20,b):S<40?d.formatDistance("halfAMinute",0,b):S<60?d.formatDistance("lessThanXMinutes",1,b):d.formatDistance("xMinutes",1,b):k===0?d.formatDistance("lessThanXMinutes",1,b):d.formatDistance("xMinutes",k,b);if(k<45)return d.formatDistance("xMinutes",k,b);if(k<90)return d.formatDistance("aboutXHours",1,b);if(k<Ym){const _=Math.round(k/60);return d.formatDistance("aboutXHours",_,b)}else{if(k<h)return d.formatDistance("xDays",1,b);if(k<Ai){const _=Math.round(k/Ym);return d.formatDistance("xDays",_,b)}else if(k<Ai*2)return X=Math.round(k/Ai),d.formatDistance("aboutXMonths",X,b)}if(X=Rb(j,N),X<12){const _=Math.round(k/Ai);return d.formatDistance("xMonths",_,b)}else{const _=X%12,B=Math.trunc(X/12);return _<3?d.formatDistance("aboutXYears",B,b):_<9?d.formatDistance("overXYears",B,b):d.formatDistance("almostXYears",B+1,b)}}function mp(s,r){return dp(s,wb(s),r)}function hp(s,r){const c=()=>Hi(r==null?void 0:r.in,NaN),d=pp(s);let h;if(d.date){const j=xp(d.date,2);h=vp(j.restDateString,j.year)}if(!h||isNaN(+h))return c();const g=+h;let b=0,N;if(d.time&&(b=Sp(d.time),isNaN(b)))return c();if(d.timezone){if(N=jp(d.timezone),isNaN(N))return c()}else{const j=new Date(g+b),S=kt(0);return S.setFullYear(j.getUTCFullYear(),j.getUTCMonth(),j.getUTCDate()),S.setHours(j.getUTCHours(),j.getUTCMinutes(),j.getUTCSeconds(),j.getUTCMilliseconds()),S}return kt(g+b+N)}const Ti={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},gp=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,yp=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,bp=/^([+-])(\d{2})(?::?(\d{2}))?$/;function pp(s){const r={},c=s.split(Ti.dateTimeDelimiter);let u;if(c.length>2)return r;if(/:/.test(c[0])?u=c[0]:(r.date=c[0],u=c[1],Ti.timeZoneDelimiter.test(r.date)&&(r.date=s.split(Ti.timeZoneDelimiter)[0],u=s.substr(r.date.length,s.length))),u){const d=Ti.timezone.exec(u);d?(r.time=u.replace(d[1],""),r.timezone=d[1]):r.time=u}return r}function xp(s,r){const c=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+r)+"})|(\\d{2}|[+-]\\d{"+(2+r)+"})$)"),u=s.match(c);if(!u)return{year:NaN,restDateString:""};const d=u[1]?parseInt(u[1]):null,h=u[2]?parseInt(u[2]):null;return{year:h===null?d:h*100,restDateString:s.slice((u[1]||u[2]).length)}}function vp(s,r){if(r===null)return new Date(NaN);const c=s.match(gp);if(!c)return new Date(NaN);const u=!!c[4],d=as(c[1]),h=as(c[2])-1,g=as(c[3]),b=as(c[4]),N=as(c[5])-1;if(u)return Ep(r,b,N)?wp(r,b,N):new Date(NaN);{const j=new Date(0);return!Ap(r,h,g)||!Tp(r,d)?new Date(NaN):(j.setUTCFullYear(r,h,Math.max(d,g)),j)}}function as(s){return s?parseInt(s):1}function Sp(s){const r=s.match(yp);if(!r)return NaN;const c=oc(r[1]),u=oc(r[2]),d=oc(r[3]);return zp(c,u,d)?c*N0+u*w0+d*1e3:NaN}function oc(s){return s&&parseFloat(s.replace(",","."))||0}function jp(s){if(s==="Z")return 0;const r=s.match(bp);if(!r)return 0;const c=r[1]==="+"?-1:1,u=parseInt(r[2]),d=r[3]&&parseInt(r[3])||0;return Mp(u,d)?c*(u*N0+d*w0):NaN}function wp(s,r,c){const u=new Date(0);u.setUTCFullYear(s,0,4);const d=u.getUTCDay()||7,h=(r-1)*7+c+1-d;return u.setUTCDate(u.getUTCDate()+h),u}const Np=[31,null,31,30,31,30,31,31,30,31,30,31];function T0(s){return s%400===0||s%4===0&&s%100!==0}function Ap(s,r,c){return r>=0&&r<=11&&c>=1&&c<=(Np[r]||(T0(s)?29:28))}function Tp(s,r){return r>=1&&r<=(T0(s)?366:365)}function Ep(s,r,c){return r>=1&&r<=53&&c>=0&&c<=6}function zp(s,r,c){return s===24?r===0&&c===0:c>=0&&c<60&&r>=0&&r<60&&s>=0&&s<25}function Mp(s,r){return r>=0&&r<=59}const Op={lessThanXSeconds:{one:"menos de um segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"meio minuto",lessThanXMinutes:{one:"menos de um minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"cerca de 1 hora",other:"cerca de {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 dia",other:"{{count}} dias"},aboutXWeeks:{one:"cerca de 1 semana",other:"cerca de {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"cerca de 1 mês",other:"cerca de {{count}} meses"},xMonths:{one:"1 mês",other:"{{count}} meses"},aboutXYears:{one:"cerca de 1 ano",other:"cerca de {{count}} anos"},xYears:{one:"1 ano",other:"{{count}} anos"},overXYears:{one:"mais de 1 ano",other:"mais de {{count}} anos"},almostXYears:{one:"quase 1 ano",other:"quase {{count}} anos"}},Dp=(s,r,c)=>{let u;const d=Op[s];return typeof d=="string"?u=d:r===1?u=d.one:u=d.other.replace("{{count}}",String(r)),c!=null&&c.addSuffix?c.comparison&&c.comparison>0?"em "+u:"há "+u:u},Rp={full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"d MMM y",short:"dd/MM/yyyy"},Cp={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},kp={full:"{{date}} 'às' {{time}}",long:"{{date}} 'às' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Up={date:Wl({formats:Rp,defaultWidth:"full"}),time:Wl({formats:Cp,defaultWidth:"full"}),dateTime:Wl({formats:kp,defaultWidth:"full"})},_p={lastWeek:s=>{const r=s.getDay();return"'"+(r===0||r===6?"último":"última")+"' eeee 'às' p"},yesterday:"'ontem às' p",today:"'hoje às' p",tomorrow:"'amanhã às' p",nextWeek:"eeee 'às' p",other:"P"},Bp=(s,r,c,u)=>{const d=_p[s];return typeof d=="function"?d(r):d},qp={narrow:["AC","DC"],abbreviated:["AC","DC"],wide:["antes de cristo","depois de cristo"]},Hp={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1º trimestre","2º trimestre","3º trimestre","4º trimestre"]},Lp={narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez"],wide:["janeiro","fevereiro","março","abril","maio","junho","julho","agosto","setembro","outubro","novembro","dezembro"]},Yp={narrow:["D","S","T","Q","Q","S","S"],short:["dom","seg","ter","qua","qui","sex","sab"],abbreviated:["domingo","segunda","terça","quarta","quinta","sexta","sábado"],wide:["domingo","segunda-feira","terça-feira","quarta-feira","quinta-feira","sexta-feira","sábado"]},Gp={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"manhã",afternoon:"tarde",evening:"tarde",night:"noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"manhã",afternoon:"tarde",evening:"tarde",night:"noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"manhã",afternoon:"tarde",evening:"tarde",night:"noite"}},Xp={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"da manhã",afternoon:"da tarde",evening:"da tarde",night:"da noite"},abbreviated:{am:"AM",pm:"PM",midnight:"meia-noite",noon:"meio-dia",morning:"da manhã",afternoon:"da tarde",evening:"da tarde",night:"da noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"meia-noite",noon:"meio-dia",morning:"da manhã",afternoon:"da tarde",evening:"da tarde",night:"da noite"}},Zp=(s,r)=>{const c=Number(s);return(r==null?void 0:r.unit)==="week"?c+"ª":c+"º"},Qp={ordinalNumber:Zp,era:Xt({values:qp,defaultWidth:"wide"}),quarter:Xt({values:Hp,defaultWidth:"wide",argumentCallback:s=>s-1}),month:Xt({values:Lp,defaultWidth:"wide"}),day:Xt({values:Yp,defaultWidth:"wide"}),dayPeriod:Xt({values:Gp,defaultWidth:"wide",formattingValues:Xp,defaultFormattingWidth:"wide"})},Vp=/^(\d+)[ºªo]?/i,Kp=/\d+/i,Jp={narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|d\.?\s?c\.?)/i,wide:/^(antes de cristo|depois de cristo)/i},Wp={any:[/^ac/i,/^dc/i],wide:[/^antes de cristo/i,/^depois de cristo/i]},Fp={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º)? trimestre/i},$p={any:[/1/i,/2/i,/3/i,/4/i]},Pp={narrow:/^[jfmajsond]/i,abbreviated:/^(jan|fev|mar|abr|mai|jun|jul|ago|set|out|nov|dez)/i,wide:/^(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)/i},Ip={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^fev/i,/^mar/i,/^abr/i,/^mai/i,/^jun/i,/^jul/i,/^ago/i,/^set/i,/^out/i,/^nov/i,/^dez/i]},ex={narrow:/^(dom|[23456]ª?|s[aá]b)/i,short:/^(dom|[23456]ª?|s[aá]b)/i,abbreviated:/^(dom|seg|ter|qua|qui|sex|s[aá]b)/i,wide:/^(domingo|(segunda|ter[cç]a|quarta|quinta|sexta)([- ]feira)?|s[aá]bado)/i},tx={short:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],narrow:[/^d/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^s[aá]/i],any:[/^d/i,/^seg/i,/^t/i,/^qua/i,/^qui/i,/^sex/i,/^s[aá]b/i]},ax={narrow:/^(a|p|mn|md|(da) (manhã|tarde|noite))/i,any:/^([ap]\.?\s?m\.?|meia[-\s]noite|meio[-\s]dia|(da) (manhã|tarde|noite))/i},lx={any:{am:/^a/i,pm:/^p/i,midnight:/^mn|^meia[-\s]noite/i,noon:/^md|^meio[-\s]dia/i,morning:/manhã/i,afternoon:/tarde/i,evening:/tarde/i,night:/noite/i}},nx={ordinalNumber:A0({matchPattern:Vp,parsePattern:Kp,valueCallback:s=>parseInt(s,10)}),era:Zt({matchPatterns:Jp,defaultMatchWidth:"wide",parsePatterns:Wp,defaultParseWidth:"any"}),quarter:Zt({matchPatterns:Fp,defaultMatchWidth:"wide",parsePatterns:$p,defaultParseWidth:"any",valueCallback:s=>s+1}),month:Zt({matchPatterns:Pp,defaultMatchWidth:"wide",parsePatterns:Ip,defaultParseWidth:"any"}),day:Zt({matchPatterns:ex,defaultMatchWidth:"wide",parsePatterns:tx,defaultParseWidth:"any"}),dayPeriod:Zt({matchPatterns:ax,defaultMatchWidth:"any",parsePatterns:lx,defaultParseWidth:"any"})},sx={code:"pt-BR",formatDistance:Dp,formatLong:Up,formatRelative:Bp,localize:Qp,match:nx,options:{weekStartsOn:0,firstWeekContainsDate:1}},Fl=(s,r={})=>{const{minimumFractionDigits:c=2,maximumFractionDigits:u=2,showSymbol:d=!0}=r;return s==null||isNaN(s)?d?"R$ 0,00":"0,00":new Intl.NumberFormat("pt-BR",{style:d?"currency":"decimal",currency:"BRL",minimumFractionDigits:c,maximumFractionDigits:u}).format(s)},$l=(s,r={})=>{const{minimumFractionDigits:c=0,maximumFractionDigits:u=0}=r;return s==null||isNaN(s)?"0":new Intl.NumberFormat("pt-BR",{minimumFractionDigits:c,maximumFractionDigits:u}).format(s)},is=(s,r={})=>{const{minimumFractionDigits:c=1,maximumFractionDigits:u=1,showSymbol:d=!0}=r;if(s==null||isNaN(s))return d?"0,0%":"0,0";const h=new Intl.NumberFormat("pt-BR",{style:d?"percent":"decimal",minimumFractionDigits:c,maximumFractionDigits:u}),g=d?s/100:s;return h.format(g)},E0=s=>{if(!s)return"";let r;return typeof s=="string"?r=hp(s):r=s,Ab(r)?mp(r,{addSuffix:!0,locale:sx}):""},ix=s=>{if(!s)return"";const r=s.replace(/\D/g,"");return r.length===11?r.replace(/(\d{2})(\d{5})(\d{4})/,"($1) $2-$3"):r.length===10?r.replace(/(\d{2})(\d{4})(\d{4})/,"($1) $2-$3"):r.length===13&&r.startsWith("55")?r.replace(/55(\d{2})(\d{5})(\d{4})/,"+55 ($1) $2-$3"):s},rx=s=>s?s.toLowerCase().split(" ").map(r=>r.charAt(0).toUpperCase()+r.slice(1)).join(" "):"",z0=s=>({online:"var(--success)",offline:"var(--gray-400)",quente:"var(--danger)",morno:"var(--warning)",frio:"var(--primary-blue)",ativo:"var(--success)",inativo:"var(--gray-400)",pendente:"var(--warning)",concluído:"var(--success)",cancelado:"var(--danger)"})[s==null?void 0:s.toLowerCase()]||"var(--gray-500)",Cc=s=>({WhatsApp:"💬",Instagram:"📷",Facebook:"👥",Site:"🌐",Telegram:"✈️",Email:"📧"})[s]||"💬",ux=s=>({orçamento:"Solicitação de Orçamento",suporte:"Suporte Técnico",agendamento:"Agendamento",informação:"Informações Gerais",reclamação:"Reclamação",elogio:"Elogio",dúvida:"Dúvida"})[s==null?void 0:s.toLowerCase()]||rx(s||""),kc="-",cx=s=>{const r=fx(s),{conflictingClassGroups:c,conflictingClassGroupModifiers:u}=s;return{getClassGroupId:g=>{const b=g.split(kc);return b[0]===""&&b.length!==1&&b.shift(),M0(b,r)||ox(g)},getConflictingClassGroupIds:(g,b)=>{const N=c[g]||[];return b&&u[g]?[...N,...u[g]]:N}}},M0=(s,r)=>{var g;if(s.length===0)return r.classGroupId;const c=s[0],u=r.nextPart.get(c),d=u?M0(s.slice(1),u):void 0;if(d)return d;if(r.validators.length===0)return;const h=s.join(kc);return(g=r.validators.find(({validator:b})=>b(h)))==null?void 0:g.classGroupId},Zm=/^\[(.+)\]$/,ox=s=>{if(Zm.test(s)){const r=Zm.exec(s)[1],c=r==null?void 0:r.substring(0,r.indexOf(":"));if(c)return"arbitrary.."+c}},fx=s=>{const{theme:r,classGroups:c}=s,u={nextPart:new Map,validators:[]};for(const d in c)xc(c[d],u,d,r);return u},xc=(s,r,c,u)=>{s.forEach(d=>{if(typeof d=="string"){const h=d===""?r:Qm(r,d);h.classGroupId=c;return}if(typeof d=="function"){if(dx(d)){xc(d(u),r,c,u);return}r.validators.push({validator:d,classGroupId:c});return}Object.entries(d).forEach(([h,g])=>{xc(g,Qm(r,h),c,u)})})},Qm=(s,r)=>{let c=s;return r.split(kc).forEach(u=>{c.nextPart.has(u)||c.nextPart.set(u,{nextPart:new Map,validators:[]}),c=c.nextPart.get(u)}),c},dx=s=>s.isThemeGetter,mx=s=>{if(s<1)return{get:()=>{},set:()=>{}};let r=0,c=new Map,u=new Map;const d=(h,g)=>{c.set(h,g),r++,r>s&&(r=0,u=c,c=new Map)};return{get(h){let g=c.get(h);if(g!==void 0)return g;if((g=u.get(h))!==void 0)return d(h,g),g},set(h,g){c.has(h)?c.set(h,g):d(h,g)}}},vc="!",Sc=":",hx=Sc.length,gx=s=>{const{prefix:r,experimentalParseClassName:c}=s;let u=d=>{const h=[];let g=0,b=0,N=0,j;for(let _=0;_<d.length;_++){let B=d[_];if(g===0&&b===0){if(B===Sc){h.push(d.slice(N,_)),N=_+hx;continue}if(B==="/"){j=_;continue}}B==="["?g++:B==="]"?g--:B==="("?b++:B===")"&&b--}const S=h.length===0?d:d.substring(N),D=yx(S),k=D!==S,X=j&&j>N?j-N:void 0;return{modifiers:h,hasImportantModifier:k,baseClassName:D,maybePostfixModifierPosition:X}};if(r){const d=r+Sc,h=u;u=g=>g.startsWith(d)?h(g.substring(d.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:g,maybePostfixModifierPosition:void 0}}if(c){const d=u;u=h=>c({className:h,parseClassName:d})}return u},yx=s=>s.endsWith(vc)?s.substring(0,s.length-1):s.startsWith(vc)?s.substring(1):s,bx=s=>{const r=Object.fromEntries(s.orderSensitiveModifiers.map(u=>[u,!0]));return u=>{if(u.length<=1)return u;const d=[];let h=[];return u.forEach(g=>{g[0]==="["||r[g]?(d.push(...h.sort(),g),h=[]):h.push(g)}),d.push(...h.sort()),d}},px=s=>({cache:mx(s.cacheSize),parseClassName:gx(s),sortModifiers:bx(s),...cx(s)}),xx=/\s+/,vx=(s,r)=>{const{parseClassName:c,getClassGroupId:u,getConflictingClassGroupIds:d,sortModifiers:h}=r,g=[],b=s.trim().split(xx);let N="";for(let j=b.length-1;j>=0;j-=1){const S=b[j],{isExternal:D,modifiers:k,hasImportantModifier:X,baseClassName:_,maybePostfixModifierPosition:B}=c(S);if(D){N=S+(N.length>0?" "+N:N);continue}let q=!!B,Q=u(q?_.substring(0,B):_);if(!Q){if(!q){N=S+(N.length>0?" "+N:N);continue}if(Q=u(_),!Q){N=S+(N.length>0?" "+N:N);continue}q=!1}const P=h(k).join(":"),K=X?P+vc:P,de=K+Q;if(g.includes(de))continue;g.push(de);const re=d(Q,q);for(let ce=0;ce<re.length;++ce){const Se=re[ce];g.push(K+Se)}N=S+(N.length>0?" "+N:N)}return N};function Sx(){let s=0,r,c,u="";for(;s<arguments.length;)(r=arguments[s++])&&(c=O0(r))&&(u&&(u+=" "),u+=c);return u}const O0=s=>{if(typeof s=="string")return s;let r,c="";for(let u=0;u<s.length;u++)s[u]&&(r=O0(s[u]))&&(c&&(c+=" "),c+=r);return c};function jx(s,...r){let c,u,d,h=g;function g(N){const j=r.reduce((S,D)=>D(S),s());return c=px(j),u=c.cache.get,d=c.cache.set,h=b,b(N)}function b(N){const j=u(N);if(j)return j;const S=vx(N,c);return d(N,S),S}return function(){return h(Sx.apply(null,arguments))}}const He=s=>{const r=c=>c[s]||[];return r.isThemeGetter=!0,r},D0=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,R0=/^\((?:(\w[\w-]*):)?(.+)\)$/i,wx=/^\d+\/\d+$/,Nx=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ax=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Tx=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Ex=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,zx=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Jl=s=>wx.test(s),ee=s=>!!s&&!Number.isNaN(Number(s)),Ba=s=>!!s&&Number.isInteger(Number(s)),fc=s=>s.endsWith("%")&&ee(s.slice(0,-1)),ua=s=>Nx.test(s),Mx=()=>!0,Ox=s=>Ax.test(s)&&!Tx.test(s),C0=()=>!1,Dx=s=>Ex.test(s),Rx=s=>zx.test(s),Cx=s=>!L(s)&&!Y(s),kx=s=>Pl(s,_0,C0),L=s=>D0.test(s),nl=s=>Pl(s,B0,Ox),dc=s=>Pl(s,Hx,ee),Vm=s=>Pl(s,k0,C0),Ux=s=>Pl(s,U0,Rx),Ei=s=>Pl(s,q0,Dx),Y=s=>R0.test(s),ls=s=>Il(s,B0),_x=s=>Il(s,Lx),Km=s=>Il(s,k0),Bx=s=>Il(s,_0),qx=s=>Il(s,U0),zi=s=>Il(s,q0,!0),Pl=(s,r,c)=>{const u=D0.exec(s);return u?u[1]?r(u[1]):c(u[2]):!1},Il=(s,r,c=!1)=>{const u=R0.exec(s);return u?u[1]?r(u[1]):c:!1},k0=s=>s==="position"||s==="percentage",U0=s=>s==="image"||s==="url",_0=s=>s==="length"||s==="size"||s==="bg-size",B0=s=>s==="length",Hx=s=>s==="number",Lx=s=>s==="family-name",q0=s=>s==="shadow",Yx=()=>{const s=He("color"),r=He("font"),c=He("text"),u=He("font-weight"),d=He("tracking"),h=He("leading"),g=He("breakpoint"),b=He("container"),N=He("spacing"),j=He("radius"),S=He("shadow"),D=He("inset-shadow"),k=He("text-shadow"),X=He("drop-shadow"),_=He("blur"),B=He("perspective"),q=He("aspect"),Q=He("ease"),P=He("animate"),K=()=>["auto","avoid","all","avoid-page","page","left","right","column"],de=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],re=()=>[...de(),Y,L],ce=()=>["auto","hidden","clip","visible","scroll"],Se=()=>["auto","contain","none"],G=()=>[Y,L,N],Te=()=>[Jl,"full","auto",...G()],Et=()=>[Ba,"none","subgrid",Y,L],ge=()=>["auto",{span:["full",Ba,Y,L]},Ba,Y,L],_e=()=>[Ba,"auto",Y,L],Vt=()=>["auto","min","max","fr",Y,L],zt=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Le=()=>["start","end","center","stretch","center-safe","end-safe"],v=()=>["auto",...G()],C=()=>[Jl,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...G()],U=()=>[s,Y,L],se=()=>[...de(),Km,Vm,{position:[Y,L]}],ae=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Ye=()=>["auto","cover","contain",Bx,kx,{size:[Y,L]}],je=()=>[fc,ls,nl],F=()=>["","none","full",j,Y,L],oe=()=>["",ee,ls,nl],Pe=()=>["solid","dashed","dotted","double"],Mt=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Ee=()=>[ee,fc,Km,Vm],Ha=()=>["","none",_,Y,L],Ot=()=>["none",ee,Y,L],oa=()=>["none",ee,Y,L],fa=()=>[ee,Y,L],da=()=>[Jl,"full",...G()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[ua],breakpoint:[ua],color:[Mx],container:[ua],"drop-shadow":[ua],ease:["in","out","in-out"],font:[Cx],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[ua],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[ua],shadow:[ua],spacing:["px",ee],text:[ua],"text-shadow":[ua],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Jl,L,Y,q]}],container:["container"],columns:[{columns:[ee,L,Y,b]}],"break-after":[{"break-after":K()}],"break-before":[{"break-before":K()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:re()}],overflow:[{overflow:ce()}],"overflow-x":[{"overflow-x":ce()}],"overflow-y":[{"overflow-y":ce()}],overscroll:[{overscroll:Se()}],"overscroll-x":[{"overscroll-x":Se()}],"overscroll-y":[{"overscroll-y":Se()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Te()}],"inset-x":[{"inset-x":Te()}],"inset-y":[{"inset-y":Te()}],start:[{start:Te()}],end:[{end:Te()}],top:[{top:Te()}],right:[{right:Te()}],bottom:[{bottom:Te()}],left:[{left:Te()}],visibility:["visible","invisible","collapse"],z:[{z:[Ba,"auto",Y,L]}],basis:[{basis:[Jl,"full","auto",b,...G()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ee,Jl,"auto","initial","none",L]}],grow:[{grow:["",ee,Y,L]}],shrink:[{shrink:["",ee,Y,L]}],order:[{order:[Ba,"first","last","none",Y,L]}],"grid-cols":[{"grid-cols":Et()}],"col-start-end":[{col:ge()}],"col-start":[{"col-start":_e()}],"col-end":[{"col-end":_e()}],"grid-rows":[{"grid-rows":Et()}],"row-start-end":[{row:ge()}],"row-start":[{"row-start":_e()}],"row-end":[{"row-end":_e()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Vt()}],"auto-rows":[{"auto-rows":Vt()}],gap:[{gap:G()}],"gap-x":[{"gap-x":G()}],"gap-y":[{"gap-y":G()}],"justify-content":[{justify:[...zt(),"normal"]}],"justify-items":[{"justify-items":[...Le(),"normal"]}],"justify-self":[{"justify-self":["auto",...Le()]}],"align-content":[{content:["normal",...zt()]}],"align-items":[{items:[...Le(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Le(),{baseline:["","last"]}]}],"place-content":[{"place-content":zt()}],"place-items":[{"place-items":[...Le(),"baseline"]}],"place-self":[{"place-self":["auto",...Le()]}],p:[{p:G()}],px:[{px:G()}],py:[{py:G()}],ps:[{ps:G()}],pe:[{pe:G()}],pt:[{pt:G()}],pr:[{pr:G()}],pb:[{pb:G()}],pl:[{pl:G()}],m:[{m:v()}],mx:[{mx:v()}],my:[{my:v()}],ms:[{ms:v()}],me:[{me:v()}],mt:[{mt:v()}],mr:[{mr:v()}],mb:[{mb:v()}],ml:[{ml:v()}],"space-x":[{"space-x":G()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":G()}],"space-y-reverse":["space-y-reverse"],size:[{size:C()}],w:[{w:[b,"screen",...C()]}],"min-w":[{"min-w":[b,"screen","none",...C()]}],"max-w":[{"max-w":[b,"screen","none","prose",{screen:[g]},...C()]}],h:[{h:["screen","lh",...C()]}],"min-h":[{"min-h":["screen","lh","none",...C()]}],"max-h":[{"max-h":["screen","lh",...C()]}],"font-size":[{text:["base",c,ls,nl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[u,Y,dc]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",fc,L]}],"font-family":[{font:[_x,L,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[d,Y,L]}],"line-clamp":[{"line-clamp":[ee,"none",Y,dc]}],leading:[{leading:[h,...G()]}],"list-image":[{"list-image":["none",Y,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Y,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:U()}],"text-color":[{text:U()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Pe(),"wavy"]}],"text-decoration-thickness":[{decoration:[ee,"from-font","auto",Y,nl]}],"text-decoration-color":[{decoration:U()}],"underline-offset":[{"underline-offset":[ee,"auto",Y,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:G()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Y,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Y,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:se()}],"bg-repeat":[{bg:ae()}],"bg-size":[{bg:Ye()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Ba,Y,L],radial:["",Y,L],conic:[Ba,Y,L]},qx,Ux]}],"bg-color":[{bg:U()}],"gradient-from-pos":[{from:je()}],"gradient-via-pos":[{via:je()}],"gradient-to-pos":[{to:je()}],"gradient-from":[{from:U()}],"gradient-via":[{via:U()}],"gradient-to":[{to:U()}],rounded:[{rounded:F()}],"rounded-s":[{"rounded-s":F()}],"rounded-e":[{"rounded-e":F()}],"rounded-t":[{"rounded-t":F()}],"rounded-r":[{"rounded-r":F()}],"rounded-b":[{"rounded-b":F()}],"rounded-l":[{"rounded-l":F()}],"rounded-ss":[{"rounded-ss":F()}],"rounded-se":[{"rounded-se":F()}],"rounded-ee":[{"rounded-ee":F()}],"rounded-es":[{"rounded-es":F()}],"rounded-tl":[{"rounded-tl":F()}],"rounded-tr":[{"rounded-tr":F()}],"rounded-br":[{"rounded-br":F()}],"rounded-bl":[{"rounded-bl":F()}],"border-w":[{border:oe()}],"border-w-x":[{"border-x":oe()}],"border-w-y":[{"border-y":oe()}],"border-w-s":[{"border-s":oe()}],"border-w-e":[{"border-e":oe()}],"border-w-t":[{"border-t":oe()}],"border-w-r":[{"border-r":oe()}],"border-w-b":[{"border-b":oe()}],"border-w-l":[{"border-l":oe()}],"divide-x":[{"divide-x":oe()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":oe()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...Pe(),"hidden","none"]}],"divide-style":[{divide:[...Pe(),"hidden","none"]}],"border-color":[{border:U()}],"border-color-x":[{"border-x":U()}],"border-color-y":[{"border-y":U()}],"border-color-s":[{"border-s":U()}],"border-color-e":[{"border-e":U()}],"border-color-t":[{"border-t":U()}],"border-color-r":[{"border-r":U()}],"border-color-b":[{"border-b":U()}],"border-color-l":[{"border-l":U()}],"divide-color":[{divide:U()}],"outline-style":[{outline:[...Pe(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ee,Y,L]}],"outline-w":[{outline:["",ee,ls,nl]}],"outline-color":[{outline:U()}],shadow:[{shadow:["","none",S,zi,Ei]}],"shadow-color":[{shadow:U()}],"inset-shadow":[{"inset-shadow":["none",D,zi,Ei]}],"inset-shadow-color":[{"inset-shadow":U()}],"ring-w":[{ring:oe()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:U()}],"ring-offset-w":[{"ring-offset":[ee,nl]}],"ring-offset-color":[{"ring-offset":U()}],"inset-ring-w":[{"inset-ring":oe()}],"inset-ring-color":[{"inset-ring":U()}],"text-shadow":[{"text-shadow":["none",k,zi,Ei]}],"text-shadow-color":[{"text-shadow":U()}],opacity:[{opacity:[ee,Y,L]}],"mix-blend":[{"mix-blend":[...Mt(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Mt()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ee]}],"mask-image-linear-from-pos":[{"mask-linear-from":Ee()}],"mask-image-linear-to-pos":[{"mask-linear-to":Ee()}],"mask-image-linear-from-color":[{"mask-linear-from":U()}],"mask-image-linear-to-color":[{"mask-linear-to":U()}],"mask-image-t-from-pos":[{"mask-t-from":Ee()}],"mask-image-t-to-pos":[{"mask-t-to":Ee()}],"mask-image-t-from-color":[{"mask-t-from":U()}],"mask-image-t-to-color":[{"mask-t-to":U()}],"mask-image-r-from-pos":[{"mask-r-from":Ee()}],"mask-image-r-to-pos":[{"mask-r-to":Ee()}],"mask-image-r-from-color":[{"mask-r-from":U()}],"mask-image-r-to-color":[{"mask-r-to":U()}],"mask-image-b-from-pos":[{"mask-b-from":Ee()}],"mask-image-b-to-pos":[{"mask-b-to":Ee()}],"mask-image-b-from-color":[{"mask-b-from":U()}],"mask-image-b-to-color":[{"mask-b-to":U()}],"mask-image-l-from-pos":[{"mask-l-from":Ee()}],"mask-image-l-to-pos":[{"mask-l-to":Ee()}],"mask-image-l-from-color":[{"mask-l-from":U()}],"mask-image-l-to-color":[{"mask-l-to":U()}],"mask-image-x-from-pos":[{"mask-x-from":Ee()}],"mask-image-x-to-pos":[{"mask-x-to":Ee()}],"mask-image-x-from-color":[{"mask-x-from":U()}],"mask-image-x-to-color":[{"mask-x-to":U()}],"mask-image-y-from-pos":[{"mask-y-from":Ee()}],"mask-image-y-to-pos":[{"mask-y-to":Ee()}],"mask-image-y-from-color":[{"mask-y-from":U()}],"mask-image-y-to-color":[{"mask-y-to":U()}],"mask-image-radial":[{"mask-radial":[Y,L]}],"mask-image-radial-from-pos":[{"mask-radial-from":Ee()}],"mask-image-radial-to-pos":[{"mask-radial-to":Ee()}],"mask-image-radial-from-color":[{"mask-radial-from":U()}],"mask-image-radial-to-color":[{"mask-radial-to":U()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":de()}],"mask-image-conic-pos":[{"mask-conic":[ee]}],"mask-image-conic-from-pos":[{"mask-conic-from":Ee()}],"mask-image-conic-to-pos":[{"mask-conic-to":Ee()}],"mask-image-conic-from-color":[{"mask-conic-from":U()}],"mask-image-conic-to-color":[{"mask-conic-to":U()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:se()}],"mask-repeat":[{mask:ae()}],"mask-size":[{mask:Ye()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Y,L]}],filter:[{filter:["","none",Y,L]}],blur:[{blur:Ha()}],brightness:[{brightness:[ee,Y,L]}],contrast:[{contrast:[ee,Y,L]}],"drop-shadow":[{"drop-shadow":["","none",X,zi,Ei]}],"drop-shadow-color":[{"drop-shadow":U()}],grayscale:[{grayscale:["",ee,Y,L]}],"hue-rotate":[{"hue-rotate":[ee,Y,L]}],invert:[{invert:["",ee,Y,L]}],saturate:[{saturate:[ee,Y,L]}],sepia:[{sepia:["",ee,Y,L]}],"backdrop-filter":[{"backdrop-filter":["","none",Y,L]}],"backdrop-blur":[{"backdrop-blur":Ha()}],"backdrop-brightness":[{"backdrop-brightness":[ee,Y,L]}],"backdrop-contrast":[{"backdrop-contrast":[ee,Y,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ee,Y,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ee,Y,L]}],"backdrop-invert":[{"backdrop-invert":["",ee,Y,L]}],"backdrop-opacity":[{"backdrop-opacity":[ee,Y,L]}],"backdrop-saturate":[{"backdrop-saturate":[ee,Y,L]}],"backdrop-sepia":[{"backdrop-sepia":["",ee,Y,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":G()}],"border-spacing-x":[{"border-spacing-x":G()}],"border-spacing-y":[{"border-spacing-y":G()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Y,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ee,"initial",Y,L]}],ease:[{ease:["linear","initial",Q,Y,L]}],delay:[{delay:[ee,Y,L]}],animate:[{animate:["none",P,Y,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[B,Y,L]}],"perspective-origin":[{"perspective-origin":re()}],rotate:[{rotate:Ot()}],"rotate-x":[{"rotate-x":Ot()}],"rotate-y":[{"rotate-y":Ot()}],"rotate-z":[{"rotate-z":Ot()}],scale:[{scale:oa()}],"scale-x":[{"scale-x":oa()}],"scale-y":[{"scale-y":oa()}],"scale-z":[{"scale-z":oa()}],"scale-3d":["scale-3d"],skew:[{skew:fa()}],"skew-x":[{"skew-x":fa()}],"skew-y":[{"skew-y":fa()}],transform:[{transform:[Y,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:re()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:da()}],"translate-x":[{"translate-x":da()}],"translate-y":[{"translate-y":da()}],"translate-z":[{"translate-z":da()}],"translate-none":["translate-none"],accent:[{accent:U()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:U()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Y,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":G()}],"scroll-mx":[{"scroll-mx":G()}],"scroll-my":[{"scroll-my":G()}],"scroll-ms":[{"scroll-ms":G()}],"scroll-me":[{"scroll-me":G()}],"scroll-mt":[{"scroll-mt":G()}],"scroll-mr":[{"scroll-mr":G()}],"scroll-mb":[{"scroll-mb":G()}],"scroll-ml":[{"scroll-ml":G()}],"scroll-p":[{"scroll-p":G()}],"scroll-px":[{"scroll-px":G()}],"scroll-py":[{"scroll-py":G()}],"scroll-ps":[{"scroll-ps":G()}],"scroll-pe":[{"scroll-pe":G()}],"scroll-pt":[{"scroll-pt":G()}],"scroll-pr":[{"scroll-pr":G()}],"scroll-pb":[{"scroll-pb":G()}],"scroll-pl":[{"scroll-pl":G()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Y,L]}],fill:[{fill:["none",...U()]}],"stroke-w":[{stroke:[ee,ls,nl,dc]}],stroke:[{stroke:["none",...U()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Gx=jx(Yx);function tt(...s){return Gx(fb(s))}const Uc={button:{primary:"bg-primary-600 hover:bg-primary-700 text-white shadow-soft hover:shadow-medium",secondary:"bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 shadow-soft hover:shadow-medium",success:"bg-success-600 hover:bg-success-700 text-white shadow-soft hover:shadow-medium",warning:"bg-warning-600 hover:bg-warning-700 text-white shadow-soft hover:shadow-medium",danger:"bg-danger-600 hover:bg-danger-700 text-white shadow-soft hover:shadow-medium",ghost:"hover:bg-gray-100 text-gray-700",outline:"border-2 border-primary-600 text-primary-600 hover:bg-primary-50"},card:{default:"bg-white border border-gray-200 shadow-soft hover:shadow-medium",elevated:"bg-white border border-gray-200 shadow-medium hover:shadow-large",interactive:"bg-white border border-gray-200 shadow-soft hover:shadow-medium hover:border-primary-300 cursor-pointer",gradient:"bg-gradient-to-br from-white to-gray-50 border border-gray-200 shadow-soft"},badge:{primary:"bg-primary-100 text-primary-800 border border-primary-200",success:"bg-success-100 text-success-800 border border-success-200",warning:"bg-warning-100 text-warning-800 border border-warning-200",danger:"bg-danger-100 text-danger-800 border border-danger-200",gray:"bg-gray-100 text-gray-800 border border-gray-200"}},Xx={button:{xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base",xl:"px-8 py-4 text-lg"}},_c={default:"transition-all duration-200 ease-in-out"},ca=ie.forwardRef(({children:s,variant:r="default",padding:c="default",hover:u=!1,animate:d=!0,className:h,onClick:g,...b},N)=>{const j=tt("rounded-2xl overflow-hidden","dark:bg-gray-800 dark:border-gray-700",_c.default,Uc.card[r],{"p-6":c==="default","p-4":c==="sm","p-8":c==="lg","p-0":c==="none"},u&&"hover:scale-[1.02] hover:-translate-y-1",g&&"cursor-pointer",h),S=s;return d?o.jsx(he.div,{ref:N,className:j,onClick:g,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,ease:"easeOut"},whileHover:u?{scale:1.02,y:-4}:{},...b,children:S}):o.jsx("div",{ref:N,className:j,onClick:g,...b,children:S})});ca.displayName="Card";const Mi=({className:s,variant:r="default",animation:c="pulse",...u})=>{const d=tt("bg-gray-200 dark:bg-gray-700 rounded",{"animate-pulse":c==="pulse","animate-shimmer bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 bg-[length:200%_100%]":c==="shimmer"},s);return o.jsx("div",{className:d,...u})},H0=({className:s,...r})=>o.jsxs("div",{className:tt("p-6 bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700",s),...r,children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsx(Mi,{className:"w-12 h-12 rounded-xl"}),o.jsx(Mi,{className:"h-4 w-16"})]}),o.jsx(Mi,{className:"h-8 w-24 mb-2"}),o.jsx(Mi,{className:"h-4 w-32"})]}),Zx=({title:s,value:r,type:c="number",trend:u=null,trendValue:d=null,icon:h,color:g="blue",loading:b=!1,subtitle:N=null,onClick:j=null,className:S,...D})=>{const k=(Q,P)=>{if(Q==null)return"-";switch(P){case"currency":return Fl(Q);case"percentage":return is(Q);case"number":return $l(Q);default:return Q.toString()}},X=Q=>{switch(Q){case"up":return ss;case"down":return Yy;case"neutral":return Ly;default:return null}},B=(Q=>{const P={blue:{icon:"text-primary-600 dark:text-primary-400",bg:"bg-primary-50 dark:bg-primary-900/20",trend:{up:"text-success-600 bg-success-50 dark:bg-success-900/20",down:"text-danger-600 bg-danger-50 dark:bg-danger-900/20",neutral:"text-gray-600 bg-gray-50 dark:bg-gray-900/20"}},green:{icon:"text-success-600 dark:text-success-400",bg:"bg-success-50 dark:bg-success-900/20",trend:{up:"text-success-600 bg-success-50 dark:bg-success-900/20",down:"text-danger-600 bg-danger-50 dark:bg-danger-900/20",neutral:"text-gray-600 bg-gray-50 dark:bg-gray-900/20"}},yellow:{icon:"text-warning-600 dark:text-warning-400",bg:"bg-warning-50 dark:bg-warning-900/20",trend:{up:"text-success-600 bg-success-50 dark:bg-success-900/20",down:"text-danger-600 bg-danger-50 dark:bg-danger-900/20",neutral:"text-gray-600 bg-gray-50 dark:bg-gray-900/20"}},red:{icon:"text-danger-600 dark:text-danger-400",bg:"bg-danger-50 dark:bg-danger-900/20",trend:{up:"text-success-600 bg-success-50 dark:bg-success-900/20",down:"text-danger-600 bg-danger-50 dark:bg-danger-900/20",neutral:"text-gray-600 bg-gray-50 dark:bg-gray-900/20"}},gray:{icon:"text-gray-600 dark:text-gray-400",bg:"bg-gray-50 dark:bg-gray-900/20",trend:{up:"text-success-600 bg-success-50 dark:bg-success-900/20",down:"text-danger-600 bg-danger-50 dark:bg-danger-900/20",neutral:"text-gray-600 bg-gray-50 dark:bg-gray-900/20"}}};return P[Q]||P.blue})(g),q=X(u);return b?o.jsx(H0,{className:S}):o.jsxs(ca,{variant:j?"interactive":"default",hover:!!j,onClick:j,className:tt("group",S),...D,children:[o.jsxs("div",{className:"flex items-center justify-between mb-6",children:[h&&o.jsx(he.div,{className:tt("w-14 h-14 rounded-2xl flex items-center justify-center",B.bg,B.icon,"group-hover:scale-110 transition-transform duration-200"),whileHover:{rotate:5},transition:{type:"spring",stiffness:300},children:o.jsx(h,{size:28})}),u&&d&&o.jsxs(he.div,{className:tt("flex items-center gap-1.5 px-2.5 py-1.5 rounded-full text-sm font-medium",B.trend[u]),initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:.2},children:[q&&o.jsx(q,{size:14}),o.jsx("span",{children:is(Math.abs(d))})]})]}),o.jsx(he.div,{className:"mb-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:o.jsx("div",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-1 gradient-text",children:k(r,c)})}),o.jsxs(he.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[o.jsx("h3",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-1",children:s}),N&&o.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:N})]}),u&&d&&o.jsx(he.div,{className:"mt-4 pt-4 border-t border-gray-100 dark:border-gray-700",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:o.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1",children:[o.jsx("span",{className:tt("w-2 h-2 rounded-full",u==="up"&&"bg-success-500",u==="down"&&"bg-danger-500",u==="neutral"&&"bg-gray-400")}),u==="up"&&"Aumento de ",u==="down"&&"Redução de ",u==="neutral"&&"Estável ",d&&is(Math.abs(d))," vs. período anterior"]})})]})},Qx=({count:s=4,className:r})=>o.jsx(o.Fragment,{children:Array.from({length:s}).map((c,u)=>o.jsx(H0,{className:r},u))}),Vx=({kpis:s,loading:r=!1,className:c=""})=>r?o.jsx(he.div,{className:tt("grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",c),initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:o.jsx(Qx,{count:4})}):o.jsx(he.div,{className:tt("grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6 lg:gap-8",c),initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:s.map((u,d)=>o.jsx(he.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:d*.1,duration:.5},children:o.jsx(Zx,{...u})},d))}),L0=({active:s,payload:r,label:c,type:u="default"})=>s&&r&&r.length?o.jsxs("div",{className:"bg-white p-3 border border-gray-200 rounded-lg shadow-lg",children:[c&&o.jsx("p",{className:"text-sm font-medium text-gray-900 mb-1",children:c}),r.map((d,h)=>o.jsxs("p",{className:"text-sm",style:{color:d.color},children:[`${d.name}: `,u==="currency"&&Fl(d.value),u==="number"&&$l(d.value),u==="percentage"&&`${d.value}%`,u==="default"&&d.value]},h))]}):null,Kx=({data:s,loading:r=!1})=>{if(r)return o.jsxs("div",{className:"card",children:[o.jsx("div",{className:"card-header",children:o.jsx("h3",{className:"text-lg font-semibold",children:"Conversões por Canal"})}),o.jsx("div",{className:"card-body",children:o.jsx("div",{className:"h-64 bg-gray-100 rounded-lg animate-pulse"})})]});const c=["#3b82f6","#10b981","#f59e0b","#ef4444","#8b5cf6"];return o.jsxs("div",{className:"card",children:[o.jsx("div",{className:"card-header",children:o.jsx("h3",{className:"text-lg font-semibold",children:"Conversões por Canal"})}),o.jsxs("div",{className:"card-body",children:[o.jsx(Oc,{width:"100%",height:300,children:o.jsxs(db,{children:[o.jsx(mb,{data:s,cx:"50%",cy:"50%",labelLine:!1,label:({name:u,percent:d})=>`${u} ${(d*100).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:s.map((u,d)=>o.jsx(hb,{fill:u.color||c[d%c.length]},`cell-${d}`))}),o.jsx(Dc,{content:o.jsx(L0,{type:"percentage"})})]})}),o.jsx("div",{className:"mt-4 grid grid-cols-2 gap-2",children:s.map((u,d)=>o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:u.color||c[d%c.length]}}),o.jsx("span",{className:"text-sm text-gray-600",children:u.name})]},u.name))})]})]})},Jx=({data:s,loading:r=!1})=>r?o.jsxs("div",{className:"card",children:[o.jsx("div",{className:"card-header",children:o.jsx("h3",{className:"text-lg font-semibold",children:"Receita ao Longo do Tempo"})}),o.jsx("div",{className:"card-body",children:o.jsx("div",{className:"h-64 bg-gray-100 rounded-lg animate-pulse"})})]}):o.jsxs("div",{className:"card",children:[o.jsx("div",{className:"card-header",children:o.jsx("h3",{className:"text-lg font-semibold",children:"Receita ao Longo do Tempo"})}),o.jsx("div",{className:"card-body",children:o.jsx(Oc,{width:"100%",height:300,children:o.jsxs(v0,{data:s,children:[o.jsx(S0,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),o.jsx(j0,{dataKey:"month",stroke:"#64748b",fontSize:12}),o.jsx(bc,{stroke:"#64748b",fontSize:12,tickFormatter:c=>Fl(c,{showSymbol:!1})}),o.jsx(Dc,{content:o.jsx(L0,{type:"currency"})}),o.jsx(pc,{type:"monotone",dataKey:"revenue",stroke:"#3b82f6",strokeWidth:3,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2}})]})})})]}),Wx=({revenueData:s,leadsData:r,loading:c=!1})=>{if(c)return o.jsxs("div",{className:"card",children:[o.jsx("div",{className:"card-header",children:o.jsx("h3",{className:"text-lg font-semibold",children:"Performance Geral"})}),o.jsx("div",{className:"card-body",children:o.jsx("div",{className:"h-64 bg-gray-100 rounded-lg animate-pulse"})})]});const u=s.map((d,h)=>{var g;return{month:d.month,revenue:d.revenue,leads:((g=r[h])==null?void 0:g.leads)||0}});return o.jsxs("div",{className:"card",children:[o.jsx("div",{className:"card-header",children:o.jsx("h3",{className:"text-lg font-semibold",children:"Performance Geral"})}),o.jsx("div",{className:"card-body",children:o.jsx(Oc,{width:"100%",height:300,children:o.jsxs(v0,{data:u,children:[o.jsx(S0,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),o.jsx(j0,{dataKey:"month",stroke:"#64748b",fontSize:12}),o.jsx(bc,{yAxisId:"left",stroke:"#64748b",fontSize:12,tickFormatter:d=>Fl(d,{showSymbol:!1})}),o.jsx(bc,{yAxisId:"right",orientation:"right",stroke:"#64748b",fontSize:12}),o.jsx(Dc,{content:({active:d,payload:h,label:g})=>{var b,N;return d&&h&&h.length?o.jsxs("div",{className:"bg-white p-3 border border-gray-200 rounded-lg shadow-lg",children:[o.jsx("p",{className:"text-sm font-medium text-gray-900 mb-1",children:g}),o.jsxs("p",{className:"text-sm",style:{color:"#3b82f6"},children:["Receita: ",Fl((b=h[0])==null?void 0:b.value)]}),o.jsxs("p",{className:"text-sm",style:{color:"#10b981"},children:["Leads: ",$l((N=h[1])==null?void 0:N.value)]})]}):null}}),o.jsx(gb,{}),o.jsx(pc,{yAxisId:"left",type:"monotone",dataKey:"revenue",stroke:"#3b82f6",strokeWidth:3,name:"Receita",dot:{fill:"#3b82f6",strokeWidth:2,r:4}}),o.jsx(pc,{yAxisId:"right",type:"monotone",dataKey:"leads",stroke:"#10b981",strokeWidth:3,name:"Leads",dot:{fill:"#10b981",strokeWidth:2,r:4}})]})})})]})},Fx=({chartData:s,loading:r=!1})=>o.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[o.jsx(Kx,{data:(s==null?void 0:s.conversionByChannel)||[],loading:r}),o.jsx(Jx,{data:(s==null?void 0:s.revenueOverTime)||[],loading:r}),o.jsx("div",{className:"lg:col-span-2",children:o.jsx(Wx,{revenueData:(s==null?void 0:s.revenueOverTime)||[],leadsData:(s==null?void 0:s.leadsOverTime)||[],loading:r})})]}),Y0=({agent:s,onViewDetails:r,onToggleStatus:c,onSettings:u,onEdit:d,loading:h=!1})=>{if(h)return o.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden animate-pulse",children:o.jsxs("div",{className:"p-6",children:[o.jsxs("div",{className:"flex items-center justify-between mb-6",children:[o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("div",{className:"w-14 h-14 bg-gray-200 dark:bg-gray-700 rounded-xl"}),o.jsxs("div",{children:[o.jsx("div",{className:"w-28 h-5 bg-gray-200 dark:bg-gray-700 rounded mb-2"}),o.jsx("div",{className:"w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded"})]})]}),o.jsx("div",{className:"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg"})]}),o.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[o.jsx("div",{className:"w-full h-20 bg-gray-200 dark:bg-gray-700 rounded-xl"}),o.jsx("div",{className:"w-full h-20 bg-gray-200 dark:bg-gray-700 rounded-xl"})]}),o.jsxs("div",{className:"mb-6",children:[o.jsx("div",{className:"w-full h-4 bg-gray-200 dark:bg-gray-700 rounded mb-3"}),o.jsx("div",{className:"w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-full"})]}),o.jsxs("div",{className:"flex items-center justify-between mb-6",children:[o.jsx("div",{className:"w-24 h-4 bg-gray-200 dark:bg-gray-700 rounded"}),o.jsx("div",{className:"w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded"})]}),o.jsx("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-700",children:o.jsx("div",{className:"w-full h-12 bg-gray-200 dark:bg-gray-700 rounded-xl"})})]})});const{id:g,name:b,channel:N,leadsAttended:j,activeConversations:S,responseRate:D,status:k,lastActivity:X}=s,_=z0(k),B=Cc(N);return o.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100 dark:border-gray-700 overflow-hidden",children:o.jsxs("div",{className:"p-6",children:[o.jsxs("div",{className:"flex items-center justify-between mb-6",children:[o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-2xl text-white shadow-lg",children:B}),o.jsx("div",{className:"absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-3 border-white dark:border-gray-800 shadow-sm",style:{backgroundColor:_},title:k==="online"?"Online":"Offline"})]}),o.jsxs("div",{children:[o.jsx("h3",{className:"font-bold text-gray-900 dark:text-white text-lg",children:b}),o.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 font-medium",children:N})]})]}),o.jsxs("div",{className:"relative group",children:[o.jsx("button",{className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:o.jsx(Gy,{size:18,className:"text-gray-400 dark:text-gray-500"})}),o.jsxs("div",{className:"absolute right-0 top-8 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 opacity-0 group-hover:opacity-100 transition-opacity z-10 min-w-[140px]",children:[o.jsxs("button",{onClick:()=>d==null?void 0:d(s),className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300",children:[o.jsx(d0,{size:14}),"Editar"]}),o.jsxs("button",{onClick:()=>r==null?void 0:r(s),className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300",children:[o.jsx(Xy,{size:14}),"Ver detalhes"]}),o.jsxs("button",{onClick:()=>u==null?void 0:u(s),className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300",children:[o.jsx(qi,{size:14}),"Configurar"]}),o.jsxs("button",{onClick:()=>c==null?void 0:c(s),className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 text-gray-700 dark:text-gray-300",children:[o.jsx(Zy,{size:14}),k==="online"?"Desativar":"Ativar"]})]})]})]}),o.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[o.jsxs("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[o.jsx(rl,{size:18,className:"text-blue-600 dark:text-blue-400"}),o.jsx("span",{className:"text-xs text-blue-600 dark:text-blue-400 font-medium",children:"Leads Atendidos"})]}),o.jsx("div",{className:"text-xl font-bold text-blue-900 dark:text-blue-100",children:$l(j)})]}),o.jsxs("div",{className:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-800",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[o.jsx(ki,{size:18,className:"text-green-600 dark:text-green-400"}),o.jsx("span",{className:"text-xs text-green-600 dark:text-green-400 font-medium",children:"Conversas Ativas"})]}),o.jsx("div",{className:"text-xl font-bold text-green-900 dark:text-green-100",children:$l(S)})]})]}),o.jsxs("div",{className:"mb-6",children:[o.jsxs("div",{className:"flex items-center justify-between mb-3",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(ss,{size:18,className:"text-purple-600 dark:text-purple-400"}),o.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300 font-medium",children:"Taxa de Resposta"})]}),o.jsx("span",{className:"text-lg font-bold text-purple-900 dark:text-purple-100",children:is(D)})]}),o.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 shadow-inner",children:o.jsx("div",{className:"h-3 rounded-full transition-all duration-500 shadow-sm",style:{width:`${Math.min(D,100)}%`,background:D>=90?"linear-gradient(90deg, #10b981, #059669)":D>=70?"linear-gradient(90deg, #f59e0b, #d97706)":"linear-gradient(90deg, #ef4444, #dc2626)"}})})]}),o.jsxs("div",{className:"flex items-center justify-between text-sm mb-6",children:[o.jsxs("div",{className:"flex items-center gap-2 text-gray-500 dark:text-gray-400",children:[o.jsx(Qy,{size:16}),o.jsx("span",{className:"font-medium",children:"Última atividade"})]}),o.jsx("span",{className:"text-gray-700 dark:text-gray-300 font-semibold",children:E0(X)})]}),o.jsx("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-700",children:o.jsx("button",{onClick:()=>r==null?void 0:r(s),className:"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:"Ver Conversas"})})]})})},$x=({count:s=3})=>o.jsx(o.Fragment,{children:Array.from({length:s}).map((r,c)=>o.jsx(Y0,{loading:!0},c))}),G0=({agents:s,loading:r=!1,onViewDetails:c,onToggleStatus:u,onSettings:d,onEdit:h,className:g=""})=>r?o.jsx("div",{className:`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${g}`,children:o.jsx($x,{count:6})}):!s||s.length===0?o.jsxs("div",{className:"text-center py-12",children:[o.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:o.jsx(rl,{size:32,className:"text-gray-400"})}),o.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Nenhum agente encontrado"}),o.jsx("p",{className:"text-gray-500",children:"Configure seus primeiros agentes de IA para começar."})]}):o.jsx("div",{className:`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 ${g}`,children:s.map(b=>o.jsx(Y0,{agent:b,onViewDetails:c,onToggleStatus:u,onSettings:d,onEdit:h},b.id))}),et=ie.forwardRef(({children:s,variant:r="primary",size:c="md",disabled:u=!1,loading:d=!1,leftIcon:h,rightIcon:g,fullWidth:b=!1,animate:N=!0,className:j,onClick:S,...D},k)=>{const X=tt("inline-flex items-center justify-center gap-2 font-medium rounded-xl","focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500","disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none",_c.default,Uc.button[r],Xx.button[c],b&&"w-full",j),_=o.jsxs(o.Fragment,{children:[d&&o.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[o.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),o.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!d&&h&&o.jsx("span",{className:"flex-shrink-0",children:h}),o.jsx("span",{children:s}),!d&&g&&o.jsx("span",{className:"flex-shrink-0",children:g})]}),B=q=>{u||d||S==null||S(q)};return N?o.jsx(he.button,{ref:k,className:X,onClick:B,disabled:u||d,whileHover:{scale:u||d?1:1.02},whileTap:{scale:u||d?1:.98},transition:{type:"spring",stiffness:400,damping:17},...D,children:_}):o.jsx("button",{ref:k,className:X,onClick:B,disabled:u||d,...D,children:_})});et.displayName="Button";function X0(s,r){return function(){return s.apply(r,arguments)}}const{toString:Px}=Object.prototype,{getPrototypeOf:Bc}=Object,{iterator:Li,toStringTag:Z0}=Symbol,Yi=(s=>r=>{const c=Px.call(r);return s[c]||(s[c]=c.slice(8,-1).toLowerCase())})(Object.create(null)),Ut=s=>(s=s.toLowerCase(),r=>Yi(r)===s),Gi=s=>r=>typeof r===s,{isArray:en}=Array,us=Gi("undefined");function Ix(s){return s!==null&&!us(s)&&s.constructor!==null&&!us(s.constructor)&&rt(s.constructor.isBuffer)&&s.constructor.isBuffer(s)}const Q0=Ut("ArrayBuffer");function ev(s){let r;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?r=ArrayBuffer.isView(s):r=s&&s.buffer&&Q0(s.buffer),r}const tv=Gi("string"),rt=Gi("function"),V0=Gi("number"),Xi=s=>s!==null&&typeof s=="object",av=s=>s===!0||s===!1,Di=s=>{if(Yi(s)!=="object")return!1;const r=Bc(s);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Z0 in s)&&!(Li in s)},lv=Ut("Date"),nv=Ut("File"),sv=Ut("Blob"),iv=Ut("FileList"),rv=s=>Xi(s)&&rt(s.pipe),uv=s=>{let r;return s&&(typeof FormData=="function"&&s instanceof FormData||rt(s.append)&&((r=Yi(s))==="formdata"||r==="object"&&rt(s.toString)&&s.toString()==="[object FormData]"))},cv=Ut("URLSearchParams"),[ov,fv,dv,mv]=["ReadableStream","Request","Response","Headers"].map(Ut),hv=s=>s.trim?s.trim():s.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function cs(s,r,{allOwnKeys:c=!1}={}){if(s===null||typeof s>"u")return;let u,d;if(typeof s!="object"&&(s=[s]),en(s))for(u=0,d=s.length;u<d;u++)r.call(null,s[u],u,s);else{const h=c?Object.getOwnPropertyNames(s):Object.keys(s),g=h.length;let b;for(u=0;u<g;u++)b=h[u],r.call(null,s[b],b,s)}}function K0(s,r){r=r.toLowerCase();const c=Object.keys(s);let u=c.length,d;for(;u-- >0;)if(d=c[u],r===d.toLowerCase())return d;return null}const sl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,J0=s=>!us(s)&&s!==sl;function jc(){const{caseless:s}=J0(this)&&this||{},r={},c=(u,d)=>{const h=s&&K0(r,d)||d;Di(r[h])&&Di(u)?r[h]=jc(r[h],u):Di(u)?r[h]=jc({},u):en(u)?r[h]=u.slice():r[h]=u};for(let u=0,d=arguments.length;u<d;u++)arguments[u]&&cs(arguments[u],c);return r}const gv=(s,r,c,{allOwnKeys:u}={})=>(cs(r,(d,h)=>{c&&rt(d)?s[h]=X0(d,c):s[h]=d},{allOwnKeys:u}),s),yv=s=>(s.charCodeAt(0)===65279&&(s=s.slice(1)),s),bv=(s,r,c,u)=>{s.prototype=Object.create(r.prototype,u),s.prototype.constructor=s,Object.defineProperty(s,"super",{value:r.prototype}),c&&Object.assign(s.prototype,c)},pv=(s,r,c,u)=>{let d,h,g;const b={};if(r=r||{},s==null)return r;do{for(d=Object.getOwnPropertyNames(s),h=d.length;h-- >0;)g=d[h],(!u||u(g,s,r))&&!b[g]&&(r[g]=s[g],b[g]=!0);s=c!==!1&&Bc(s)}while(s&&(!c||c(s,r))&&s!==Object.prototype);return r},xv=(s,r,c)=>{s=String(s),(c===void 0||c>s.length)&&(c=s.length),c-=r.length;const u=s.indexOf(r,c);return u!==-1&&u===c},vv=s=>{if(!s)return null;if(en(s))return s;let r=s.length;if(!V0(r))return null;const c=new Array(r);for(;r-- >0;)c[r]=s[r];return c},Sv=(s=>r=>s&&r instanceof s)(typeof Uint8Array<"u"&&Bc(Uint8Array)),jv=(s,r)=>{const u=(s&&s[Li]).call(s);let d;for(;(d=u.next())&&!d.done;){const h=d.value;r.call(s,h[0],h[1])}},wv=(s,r)=>{let c;const u=[];for(;(c=s.exec(r))!==null;)u.push(c);return u},Nv=Ut("HTMLFormElement"),Av=s=>s.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(c,u,d){return u.toUpperCase()+d}),Jm=(({hasOwnProperty:s})=>(r,c)=>s.call(r,c))(Object.prototype),Tv=Ut("RegExp"),W0=(s,r)=>{const c=Object.getOwnPropertyDescriptors(s),u={};cs(c,(d,h)=>{let g;(g=r(d,h,s))!==!1&&(u[h]=g||d)}),Object.defineProperties(s,u)},Ev=s=>{W0(s,(r,c)=>{if(rt(s)&&["arguments","caller","callee"].indexOf(c)!==-1)return!1;const u=s[c];if(rt(u)){if(r.enumerable=!1,"writable"in r){r.writable=!1;return}r.set||(r.set=()=>{throw Error("Can not rewrite read-only method '"+c+"'")})}})},zv=(s,r)=>{const c={},u=d=>{d.forEach(h=>{c[h]=!0})};return en(s)?u(s):u(String(s).split(r)),c},Mv=()=>{},Ov=(s,r)=>s!=null&&Number.isFinite(s=+s)?s:r;function Dv(s){return!!(s&&rt(s.append)&&s[Z0]==="FormData"&&s[Li])}const Rv=s=>{const r=new Array(10),c=(u,d)=>{if(Xi(u)){if(r.indexOf(u)>=0)return;if(!("toJSON"in u)){r[d]=u;const h=en(u)?[]:{};return cs(u,(g,b)=>{const N=c(g,d+1);!us(N)&&(h[b]=N)}),r[d]=void 0,h}}return u};return c(s,0)},Cv=Ut("AsyncFunction"),kv=s=>s&&(Xi(s)||rt(s))&&rt(s.then)&&rt(s.catch),F0=((s,r)=>s?setImmediate:r?((c,u)=>(sl.addEventListener("message",({source:d,data:h})=>{d===sl&&h===c&&u.length&&u.shift()()},!1),d=>{u.push(d),sl.postMessage(c,"*")}))(`axios@${Math.random()}`,[]):c=>setTimeout(c))(typeof setImmediate=="function",rt(sl.postMessage)),Uv=typeof queueMicrotask<"u"?queueMicrotask.bind(sl):typeof process<"u"&&process.nextTick||F0,_v=s=>s!=null&&rt(s[Li]),z={isArray:en,isArrayBuffer:Q0,isBuffer:Ix,isFormData:uv,isArrayBufferView:ev,isString:tv,isNumber:V0,isBoolean:av,isObject:Xi,isPlainObject:Di,isReadableStream:ov,isRequest:fv,isResponse:dv,isHeaders:mv,isUndefined:us,isDate:lv,isFile:nv,isBlob:sv,isRegExp:Tv,isFunction:rt,isStream:rv,isURLSearchParams:cv,isTypedArray:Sv,isFileList:iv,forEach:cs,merge:jc,extend:gv,trim:hv,stripBOM:yv,inherits:bv,toFlatObject:pv,kindOf:Yi,kindOfTest:Ut,endsWith:xv,toArray:vv,forEachEntry:jv,matchAll:wv,isHTMLForm:Nv,hasOwnProperty:Jm,hasOwnProp:Jm,reduceDescriptors:W0,freezeMethods:Ev,toObjectSet:zv,toCamelCase:Av,noop:Mv,toFiniteNumber:Ov,findKey:K0,global:sl,isContextDefined:J0,isSpecCompliantForm:Dv,toJSONObject:Rv,isAsyncFn:Cv,isThenable:kv,setImmediate:F0,asap:Uv,isIterable:_v};function $(s,r,c,u,d){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=s,this.name="AxiosError",r&&(this.code=r),c&&(this.config=c),u&&(this.request=u),d&&(this.response=d,this.status=d.status?d.status:null)}z.inherits($,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:z.toJSONObject(this.config),code:this.code,status:this.status}}});const $0=$.prototype,P0={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(s=>{P0[s]={value:s}});Object.defineProperties($,P0);Object.defineProperty($0,"isAxiosError",{value:!0});$.from=(s,r,c,u,d,h)=>{const g=Object.create($0);return z.toFlatObject(s,g,function(N){return N!==Error.prototype},b=>b!=="isAxiosError"),$.call(g,s.message,r,c,u,d),g.cause=s,g.name=s.name,h&&Object.assign(g,h),g};const Bv=null;function wc(s){return z.isPlainObject(s)||z.isArray(s)}function I0(s){return z.endsWith(s,"[]")?s.slice(0,-2):s}function Wm(s,r,c){return s?s.concat(r).map(function(d,h){return d=I0(d),!c&&h?"["+d+"]":d}).join(c?".":""):r}function qv(s){return z.isArray(s)&&!s.some(wc)}const Hv=z.toFlatObject(z,{},null,function(r){return/^is[A-Z]/.test(r)});function Zi(s,r,c){if(!z.isObject(s))throw new TypeError("target must be an object");r=r||new FormData,c=z.toFlatObject(c,{metaTokens:!0,dots:!1,indexes:!1},!1,function(B,q){return!z.isUndefined(q[B])});const u=c.metaTokens,d=c.visitor||S,h=c.dots,g=c.indexes,N=(c.Blob||typeof Blob<"u"&&Blob)&&z.isSpecCompliantForm(r);if(!z.isFunction(d))throw new TypeError("visitor must be a function");function j(_){if(_===null)return"";if(z.isDate(_))return _.toISOString();if(!N&&z.isBlob(_))throw new $("Blob is not supported. Use a Buffer instead.");return z.isArrayBuffer(_)||z.isTypedArray(_)?N&&typeof Blob=="function"?new Blob([_]):Buffer.from(_):_}function S(_,B,q){let Q=_;if(_&&!q&&typeof _=="object"){if(z.endsWith(B,"{}"))B=u?B:B.slice(0,-2),_=JSON.stringify(_);else if(z.isArray(_)&&qv(_)||(z.isFileList(_)||z.endsWith(B,"[]"))&&(Q=z.toArray(_)))return B=I0(B),Q.forEach(function(K,de){!(z.isUndefined(K)||K===null)&&r.append(g===!0?Wm([B],de,h):g===null?B:B+"[]",j(K))}),!1}return wc(_)?!0:(r.append(Wm(q,B,h),j(_)),!1)}const D=[],k=Object.assign(Hv,{defaultVisitor:S,convertValue:j,isVisitable:wc});function X(_,B){if(!z.isUndefined(_)){if(D.indexOf(_)!==-1)throw Error("Circular reference detected in "+B.join("."));D.push(_),z.forEach(_,function(Q,P){(!(z.isUndefined(Q)||Q===null)&&d.call(r,Q,z.isString(P)?P.trim():P,B,k))===!0&&X(Q,B?B.concat(P):[P])}),D.pop()}}if(!z.isObject(s))throw new TypeError("data must be an object");return X(s),r}function Fm(s){const r={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(s).replace(/[!'()~]|%20|%00/g,function(u){return r[u]})}function qc(s,r){this._pairs=[],s&&Zi(s,this,r)}const eh=qc.prototype;eh.append=function(r,c){this._pairs.push([r,c])};eh.toString=function(r){const c=r?function(u){return r.call(this,u,Fm)}:Fm;return this._pairs.map(function(d){return c(d[0])+"="+c(d[1])},"").join("&")};function Lv(s){return encodeURIComponent(s).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function th(s,r,c){if(!r)return s;const u=c&&c.encode||Lv;z.isFunction(c)&&(c={serialize:c});const d=c&&c.serialize;let h;if(d?h=d(r,c):h=z.isURLSearchParams(r)?r.toString():new qc(r,c).toString(u),h){const g=s.indexOf("#");g!==-1&&(s=s.slice(0,g)),s+=(s.indexOf("?")===-1?"?":"&")+h}return s}class $m{constructor(){this.handlers=[]}use(r,c,u){return this.handlers.push({fulfilled:r,rejected:c,synchronous:u?u.synchronous:!1,runWhen:u?u.runWhen:null}),this.handlers.length-1}eject(r){this.handlers[r]&&(this.handlers[r]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(r){z.forEach(this.handlers,function(u){u!==null&&r(u)})}}const ah={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Yv=typeof URLSearchParams<"u"?URLSearchParams:qc,Gv=typeof FormData<"u"?FormData:null,Xv=typeof Blob<"u"?Blob:null,Zv={isBrowser:!0,classes:{URLSearchParams:Yv,FormData:Gv,Blob:Xv},protocols:["http","https","file","blob","url","data"]},Hc=typeof window<"u"&&typeof document<"u",Nc=typeof navigator=="object"&&navigator||void 0,Qv=Hc&&(!Nc||["ReactNative","NativeScript","NS"].indexOf(Nc.product)<0),Vv=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Kv=Hc&&window.location.href||"http://localhost",Jv=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Hc,hasStandardBrowserEnv:Qv,hasStandardBrowserWebWorkerEnv:Vv,navigator:Nc,origin:Kv},Symbol.toStringTag,{value:"Module"})),$e={...Jv,...Zv};function Wv(s,r){return Zi(s,new $e.classes.URLSearchParams,Object.assign({visitor:function(c,u,d,h){return $e.isNode&&z.isBuffer(c)?(this.append(u,c.toString("base64")),!1):h.defaultVisitor.apply(this,arguments)}},r))}function Fv(s){return z.matchAll(/\w+|\[(\w*)]/g,s).map(r=>r[0]==="[]"?"":r[1]||r[0])}function $v(s){const r={},c=Object.keys(s);let u;const d=c.length;let h;for(u=0;u<d;u++)h=c[u],r[h]=s[h];return r}function lh(s){function r(c,u,d,h){let g=c[h++];if(g==="__proto__")return!0;const b=Number.isFinite(+g),N=h>=c.length;return g=!g&&z.isArray(d)?d.length:g,N?(z.hasOwnProp(d,g)?d[g]=[d[g],u]:d[g]=u,!b):((!d[g]||!z.isObject(d[g]))&&(d[g]=[]),r(c,u,d[g],h)&&z.isArray(d[g])&&(d[g]=$v(d[g])),!b)}if(z.isFormData(s)&&z.isFunction(s.entries)){const c={};return z.forEachEntry(s,(u,d)=>{r(Fv(u),d,c,0)}),c}return null}function Pv(s,r,c){if(z.isString(s))try{return(r||JSON.parse)(s),z.trim(s)}catch(u){if(u.name!=="SyntaxError")throw u}return(c||JSON.stringify)(s)}const os={transitional:ah,adapter:["xhr","http","fetch"],transformRequest:[function(r,c){const u=c.getContentType()||"",d=u.indexOf("application/json")>-1,h=z.isObject(r);if(h&&z.isHTMLForm(r)&&(r=new FormData(r)),z.isFormData(r))return d?JSON.stringify(lh(r)):r;if(z.isArrayBuffer(r)||z.isBuffer(r)||z.isStream(r)||z.isFile(r)||z.isBlob(r)||z.isReadableStream(r))return r;if(z.isArrayBufferView(r))return r.buffer;if(z.isURLSearchParams(r))return c.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),r.toString();let b;if(h){if(u.indexOf("application/x-www-form-urlencoded")>-1)return Wv(r,this.formSerializer).toString();if((b=z.isFileList(r))||u.indexOf("multipart/form-data")>-1){const N=this.env&&this.env.FormData;return Zi(b?{"files[]":r}:r,N&&new N,this.formSerializer)}}return h||d?(c.setContentType("application/json",!1),Pv(r)):r}],transformResponse:[function(r){const c=this.transitional||os.transitional,u=c&&c.forcedJSONParsing,d=this.responseType==="json";if(z.isResponse(r)||z.isReadableStream(r))return r;if(r&&z.isString(r)&&(u&&!this.responseType||d)){const g=!(c&&c.silentJSONParsing)&&d;try{return JSON.parse(r)}catch(b){if(g)throw b.name==="SyntaxError"?$.from(b,$.ERR_BAD_RESPONSE,this,null,this.response):b}}return r}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:$e.classes.FormData,Blob:$e.classes.Blob},validateStatus:function(r){return r>=200&&r<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};z.forEach(["delete","get","head","post","put","patch"],s=>{os.headers[s]={}});const Iv=z.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),e1=s=>{const r={};let c,u,d;return s&&s.split(`
`).forEach(function(g){d=g.indexOf(":"),c=g.substring(0,d).trim().toLowerCase(),u=g.substring(d+1).trim(),!(!c||r[c]&&Iv[c])&&(c==="set-cookie"?r[c]?r[c].push(u):r[c]=[u]:r[c]=r[c]?r[c]+", "+u:u)}),r},Pm=Symbol("internals");function ns(s){return s&&String(s).trim().toLowerCase()}function Ri(s){return s===!1||s==null?s:z.isArray(s)?s.map(Ri):String(s)}function t1(s){const r=Object.create(null),c=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let u;for(;u=c.exec(s);)r[u[1]]=u[2];return r}const a1=s=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(s.trim());function mc(s,r,c,u,d){if(z.isFunction(u))return u.call(this,r,c);if(d&&(r=c),!!z.isString(r)){if(z.isString(u))return r.indexOf(u)!==-1;if(z.isRegExp(u))return u.test(r)}}function l1(s){return s.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(r,c,u)=>c.toUpperCase()+u)}function n1(s,r){const c=z.toCamelCase(" "+r);["get","set","has"].forEach(u=>{Object.defineProperty(s,u+c,{value:function(d,h,g){return this[u].call(this,r,d,h,g)},configurable:!0})})}let ut=class{constructor(r){r&&this.set(r)}set(r,c,u){const d=this;function h(b,N,j){const S=ns(N);if(!S)throw new Error("header name must be a non-empty string");const D=z.findKey(d,S);(!D||d[D]===void 0||j===!0||j===void 0&&d[D]!==!1)&&(d[D||N]=Ri(b))}const g=(b,N)=>z.forEach(b,(j,S)=>h(j,S,N));if(z.isPlainObject(r)||r instanceof this.constructor)g(r,c);else if(z.isString(r)&&(r=r.trim())&&!a1(r))g(e1(r),c);else if(z.isObject(r)&&z.isIterable(r)){let b={},N,j;for(const S of r){if(!z.isArray(S))throw TypeError("Object iterator must return a key-value pair");b[j=S[0]]=(N=b[j])?z.isArray(N)?[...N,S[1]]:[N,S[1]]:S[1]}g(b,c)}else r!=null&&h(c,r,u);return this}get(r,c){if(r=ns(r),r){const u=z.findKey(this,r);if(u){const d=this[u];if(!c)return d;if(c===!0)return t1(d);if(z.isFunction(c))return c.call(this,d,u);if(z.isRegExp(c))return c.exec(d);throw new TypeError("parser must be boolean|regexp|function")}}}has(r,c){if(r=ns(r),r){const u=z.findKey(this,r);return!!(u&&this[u]!==void 0&&(!c||mc(this,this[u],u,c)))}return!1}delete(r,c){const u=this;let d=!1;function h(g){if(g=ns(g),g){const b=z.findKey(u,g);b&&(!c||mc(u,u[b],b,c))&&(delete u[b],d=!0)}}return z.isArray(r)?r.forEach(h):h(r),d}clear(r){const c=Object.keys(this);let u=c.length,d=!1;for(;u--;){const h=c[u];(!r||mc(this,this[h],h,r,!0))&&(delete this[h],d=!0)}return d}normalize(r){const c=this,u={};return z.forEach(this,(d,h)=>{const g=z.findKey(u,h);if(g){c[g]=Ri(d),delete c[h];return}const b=r?l1(h):String(h).trim();b!==h&&delete c[h],c[b]=Ri(d),u[b]=!0}),this}concat(...r){return this.constructor.concat(this,...r)}toJSON(r){const c=Object.create(null);return z.forEach(this,(u,d)=>{u!=null&&u!==!1&&(c[d]=r&&z.isArray(u)?u.join(", "):u)}),c}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([r,c])=>r+": "+c).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(r){return r instanceof this?r:new this(r)}static concat(r,...c){const u=new this(r);return c.forEach(d=>u.set(d)),u}static accessor(r){const u=(this[Pm]=this[Pm]={accessors:{}}).accessors,d=this.prototype;function h(g){const b=ns(g);u[b]||(n1(d,g),u[b]=!0)}return z.isArray(r)?r.forEach(h):h(r),this}};ut.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);z.reduceDescriptors(ut.prototype,({value:s},r)=>{let c=r[0].toUpperCase()+r.slice(1);return{get:()=>s,set(u){this[c]=u}}});z.freezeMethods(ut);function hc(s,r){const c=this||os,u=r||c,d=ut.from(u.headers);let h=u.data;return z.forEach(s,function(b){h=b.call(c,h,d.normalize(),r?r.status:void 0)}),d.normalize(),h}function nh(s){return!!(s&&s.__CANCEL__)}function tn(s,r,c){$.call(this,s??"canceled",$.ERR_CANCELED,r,c),this.name="CanceledError"}z.inherits(tn,$,{__CANCEL__:!0});function sh(s,r,c){const u=c.config.validateStatus;!c.status||!u||u(c.status)?s(c):r(new $("Request failed with status code "+c.status,[$.ERR_BAD_REQUEST,$.ERR_BAD_RESPONSE][Math.floor(c.status/100)-4],c.config,c.request,c))}function s1(s){const r=/^([-+\w]{1,25})(:?\/\/|:)/.exec(s);return r&&r[1]||""}function i1(s,r){s=s||10;const c=new Array(s),u=new Array(s);let d=0,h=0,g;return r=r!==void 0?r:1e3,function(N){const j=Date.now(),S=u[h];g||(g=j),c[d]=N,u[d]=j;let D=h,k=0;for(;D!==d;)k+=c[D++],D=D%s;if(d=(d+1)%s,d===h&&(h=(h+1)%s),j-g<r)return;const X=S&&j-S;return X?Math.round(k*1e3/X):void 0}}function r1(s,r){let c=0,u=1e3/r,d,h;const g=(j,S=Date.now())=>{c=S,d=null,h&&(clearTimeout(h),h=null),s.apply(null,j)};return[(...j)=>{const S=Date.now(),D=S-c;D>=u?g(j,S):(d=j,h||(h=setTimeout(()=>{h=null,g(d)},u-D)))},()=>d&&g(d)]}const _i=(s,r,c=3)=>{let u=0;const d=i1(50,250);return r1(h=>{const g=h.loaded,b=h.lengthComputable?h.total:void 0,N=g-u,j=d(N),S=g<=b;u=g;const D={loaded:g,total:b,progress:b?g/b:void 0,bytes:N,rate:j||void 0,estimated:j&&b&&S?(b-g)/j:void 0,event:h,lengthComputable:b!=null,[r?"download":"upload"]:!0};s(D)},c)},Im=(s,r)=>{const c=s!=null;return[u=>r[0]({lengthComputable:c,total:s,loaded:u}),r[1]]},e0=s=>(...r)=>z.asap(()=>s(...r)),u1=$e.hasStandardBrowserEnv?((s,r)=>c=>(c=new URL(c,$e.origin),s.protocol===c.protocol&&s.host===c.host&&(r||s.port===c.port)))(new URL($e.origin),$e.navigator&&/(msie|trident)/i.test($e.navigator.userAgent)):()=>!0,c1=$e.hasStandardBrowserEnv?{write(s,r,c,u,d,h){const g=[s+"="+encodeURIComponent(r)];z.isNumber(c)&&g.push("expires="+new Date(c).toGMTString()),z.isString(u)&&g.push("path="+u),z.isString(d)&&g.push("domain="+d),h===!0&&g.push("secure"),document.cookie=g.join("; ")},read(s){const r=document.cookie.match(new RegExp("(^|;\\s*)("+s+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove(s){this.write(s,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function o1(s){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(s)}function f1(s,r){return r?s.replace(/\/?\/$/,"")+"/"+r.replace(/^\/+/,""):s}function ih(s,r,c){let u=!o1(r);return s&&(u||c==!1)?f1(s,r):r}const t0=s=>s instanceof ut?{...s}:s;function ul(s,r){r=r||{};const c={};function u(j,S,D,k){return z.isPlainObject(j)&&z.isPlainObject(S)?z.merge.call({caseless:k},j,S):z.isPlainObject(S)?z.merge({},S):z.isArray(S)?S.slice():S}function d(j,S,D,k){if(z.isUndefined(S)){if(!z.isUndefined(j))return u(void 0,j,D,k)}else return u(j,S,D,k)}function h(j,S){if(!z.isUndefined(S))return u(void 0,S)}function g(j,S){if(z.isUndefined(S)){if(!z.isUndefined(j))return u(void 0,j)}else return u(void 0,S)}function b(j,S,D){if(D in r)return u(j,S);if(D in s)return u(void 0,j)}const N={url:h,method:h,data:h,baseURL:g,transformRequest:g,transformResponse:g,paramsSerializer:g,timeout:g,timeoutMessage:g,withCredentials:g,withXSRFToken:g,adapter:g,responseType:g,xsrfCookieName:g,xsrfHeaderName:g,onUploadProgress:g,onDownloadProgress:g,decompress:g,maxContentLength:g,maxBodyLength:g,beforeRedirect:g,transport:g,httpAgent:g,httpsAgent:g,cancelToken:g,socketPath:g,responseEncoding:g,validateStatus:b,headers:(j,S,D)=>d(t0(j),t0(S),D,!0)};return z.forEach(Object.keys(Object.assign({},s,r)),function(S){const D=N[S]||d,k=D(s[S],r[S],S);z.isUndefined(k)&&D!==b||(c[S]=k)}),c}const rh=s=>{const r=ul({},s);let{data:c,withXSRFToken:u,xsrfHeaderName:d,xsrfCookieName:h,headers:g,auth:b}=r;r.headers=g=ut.from(g),r.url=th(ih(r.baseURL,r.url,r.allowAbsoluteUrls),s.params,s.paramsSerializer),b&&g.set("Authorization","Basic "+btoa((b.username||"")+":"+(b.password?unescape(encodeURIComponent(b.password)):"")));let N;if(z.isFormData(c)){if($e.hasStandardBrowserEnv||$e.hasStandardBrowserWebWorkerEnv)g.setContentType(void 0);else if((N=g.getContentType())!==!1){const[j,...S]=N?N.split(";").map(D=>D.trim()).filter(Boolean):[];g.setContentType([j||"multipart/form-data",...S].join("; "))}}if($e.hasStandardBrowserEnv&&(u&&z.isFunction(u)&&(u=u(r)),u||u!==!1&&u1(r.url))){const j=d&&h&&c1.read(h);j&&g.set(d,j)}return r},d1=typeof XMLHttpRequest<"u",m1=d1&&function(s){return new Promise(function(c,u){const d=rh(s);let h=d.data;const g=ut.from(d.headers).normalize();let{responseType:b,onUploadProgress:N,onDownloadProgress:j}=d,S,D,k,X,_;function B(){X&&X(),_&&_(),d.cancelToken&&d.cancelToken.unsubscribe(S),d.signal&&d.signal.removeEventListener("abort",S)}let q=new XMLHttpRequest;q.open(d.method.toUpperCase(),d.url,!0),q.timeout=d.timeout;function Q(){if(!q)return;const K=ut.from("getAllResponseHeaders"in q&&q.getAllResponseHeaders()),re={data:!b||b==="text"||b==="json"?q.responseText:q.response,status:q.status,statusText:q.statusText,headers:K,config:s,request:q};sh(function(Se){c(Se),B()},function(Se){u(Se),B()},re),q=null}"onloadend"in q?q.onloadend=Q:q.onreadystatechange=function(){!q||q.readyState!==4||q.status===0&&!(q.responseURL&&q.responseURL.indexOf("file:")===0)||setTimeout(Q)},q.onabort=function(){q&&(u(new $("Request aborted",$.ECONNABORTED,s,q)),q=null)},q.onerror=function(){u(new $("Network Error",$.ERR_NETWORK,s,q)),q=null},q.ontimeout=function(){let de=d.timeout?"timeout of "+d.timeout+"ms exceeded":"timeout exceeded";const re=d.transitional||ah;d.timeoutErrorMessage&&(de=d.timeoutErrorMessage),u(new $(de,re.clarifyTimeoutError?$.ETIMEDOUT:$.ECONNABORTED,s,q)),q=null},h===void 0&&g.setContentType(null),"setRequestHeader"in q&&z.forEach(g.toJSON(),function(de,re){q.setRequestHeader(re,de)}),z.isUndefined(d.withCredentials)||(q.withCredentials=!!d.withCredentials),b&&b!=="json"&&(q.responseType=d.responseType),j&&([k,_]=_i(j,!0),q.addEventListener("progress",k)),N&&q.upload&&([D,X]=_i(N),q.upload.addEventListener("progress",D),q.upload.addEventListener("loadend",X)),(d.cancelToken||d.signal)&&(S=K=>{q&&(u(!K||K.type?new tn(null,s,q):K),q.abort(),q=null)},d.cancelToken&&d.cancelToken.subscribe(S),d.signal&&(d.signal.aborted?S():d.signal.addEventListener("abort",S)));const P=s1(d.url);if(P&&$e.protocols.indexOf(P)===-1){u(new $("Unsupported protocol "+P+":",$.ERR_BAD_REQUEST,s));return}q.send(h||null)})},h1=(s,r)=>{const{length:c}=s=s?s.filter(Boolean):[];if(r||c){let u=new AbortController,d;const h=function(j){if(!d){d=!0,b();const S=j instanceof Error?j:this.reason;u.abort(S instanceof $?S:new tn(S instanceof Error?S.message:S))}};let g=r&&setTimeout(()=>{g=null,h(new $(`timeout ${r} of ms exceeded`,$.ETIMEDOUT))},r);const b=()=>{s&&(g&&clearTimeout(g),g=null,s.forEach(j=>{j.unsubscribe?j.unsubscribe(h):j.removeEventListener("abort",h)}),s=null)};s.forEach(j=>j.addEventListener("abort",h));const{signal:N}=u;return N.unsubscribe=()=>z.asap(b),N}},g1=function*(s,r){let c=s.byteLength;if(c<r){yield s;return}let u=0,d;for(;u<c;)d=u+r,yield s.slice(u,d),u=d},y1=async function*(s,r){for await(const c of b1(s))yield*g1(c,r)},b1=async function*(s){if(s[Symbol.asyncIterator]){yield*s;return}const r=s.getReader();try{for(;;){const{done:c,value:u}=await r.read();if(c)break;yield u}}finally{await r.cancel()}},a0=(s,r,c,u)=>{const d=y1(s,r);let h=0,g,b=N=>{g||(g=!0,u&&u(N))};return new ReadableStream({async pull(N){try{const{done:j,value:S}=await d.next();if(j){b(),N.close();return}let D=S.byteLength;if(c){let k=h+=D;c(k)}N.enqueue(new Uint8Array(S))}catch(j){throw b(j),j}},cancel(N){return b(N),d.return()}},{highWaterMark:2})},Qi=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",uh=Qi&&typeof ReadableStream=="function",p1=Qi&&(typeof TextEncoder=="function"?(s=>r=>s.encode(r))(new TextEncoder):async s=>new Uint8Array(await new Response(s).arrayBuffer())),ch=(s,...r)=>{try{return!!s(...r)}catch{return!1}},x1=uh&&ch(()=>{let s=!1;const r=new Request($e.origin,{body:new ReadableStream,method:"POST",get duplex(){return s=!0,"half"}}).headers.has("Content-Type");return s&&!r}),l0=64*1024,Ac=uh&&ch(()=>z.isReadableStream(new Response("").body)),Bi={stream:Ac&&(s=>s.body)};Qi&&(s=>{["text","arrayBuffer","blob","formData","stream"].forEach(r=>{!Bi[r]&&(Bi[r]=z.isFunction(s[r])?c=>c[r]():(c,u)=>{throw new $(`Response type '${r}' is not supported`,$.ERR_NOT_SUPPORT,u)})})})(new Response);const v1=async s=>{if(s==null)return 0;if(z.isBlob(s))return s.size;if(z.isSpecCompliantForm(s))return(await new Request($e.origin,{method:"POST",body:s}).arrayBuffer()).byteLength;if(z.isArrayBufferView(s)||z.isArrayBuffer(s))return s.byteLength;if(z.isURLSearchParams(s)&&(s=s+""),z.isString(s))return(await p1(s)).byteLength},S1=async(s,r)=>{const c=z.toFiniteNumber(s.getContentLength());return c??v1(r)},j1=Qi&&(async s=>{let{url:r,method:c,data:u,signal:d,cancelToken:h,timeout:g,onDownloadProgress:b,onUploadProgress:N,responseType:j,headers:S,withCredentials:D="same-origin",fetchOptions:k}=rh(s);j=j?(j+"").toLowerCase():"text";let X=h1([d,h&&h.toAbortSignal()],g),_;const B=X&&X.unsubscribe&&(()=>{X.unsubscribe()});let q;try{if(N&&x1&&c!=="get"&&c!=="head"&&(q=await S1(S,u))!==0){let re=new Request(r,{method:"POST",body:u,duplex:"half"}),ce;if(z.isFormData(u)&&(ce=re.headers.get("content-type"))&&S.setContentType(ce),re.body){const[Se,G]=Im(q,_i(e0(N)));u=a0(re.body,l0,Se,G)}}z.isString(D)||(D=D?"include":"omit");const Q="credentials"in Request.prototype;_=new Request(r,{...k,signal:X,method:c.toUpperCase(),headers:S.normalize().toJSON(),body:u,duplex:"half",credentials:Q?D:void 0});let P=await fetch(_);const K=Ac&&(j==="stream"||j==="response");if(Ac&&(b||K&&B)){const re={};["status","statusText","headers"].forEach(Te=>{re[Te]=P[Te]});const ce=z.toFiniteNumber(P.headers.get("content-length")),[Se,G]=b&&Im(ce,_i(e0(b),!0))||[];P=new Response(a0(P.body,l0,Se,()=>{G&&G(),B&&B()}),re)}j=j||"text";let de=await Bi[z.findKey(Bi,j)||"text"](P,s);return!K&&B&&B(),await new Promise((re,ce)=>{sh(re,ce,{data:de,headers:ut.from(P.headers),status:P.status,statusText:P.statusText,config:s,request:_})})}catch(Q){throw B&&B(),Q&&Q.name==="TypeError"&&/Load failed|fetch/i.test(Q.message)?Object.assign(new $("Network Error",$.ERR_NETWORK,s,_),{cause:Q.cause||Q}):$.from(Q,Q&&Q.code,s,_)}}),Tc={http:Bv,xhr:m1,fetch:j1};z.forEach(Tc,(s,r)=>{if(s){try{Object.defineProperty(s,"name",{value:r})}catch{}Object.defineProperty(s,"adapterName",{value:r})}});const n0=s=>`- ${s}`,w1=s=>z.isFunction(s)||s===null||s===!1,oh={getAdapter:s=>{s=z.isArray(s)?s:[s];const{length:r}=s;let c,u;const d={};for(let h=0;h<r;h++){c=s[h];let g;if(u=c,!w1(c)&&(u=Tc[(g=String(c)).toLowerCase()],u===void 0))throw new $(`Unknown adapter '${g}'`);if(u)break;d[g||"#"+h]=u}if(!u){const h=Object.entries(d).map(([b,N])=>`adapter ${b} `+(N===!1?"is not supported by the environment":"is not available in the build"));let g=r?h.length>1?`since :
`+h.map(n0).join(`
`):" "+n0(h[0]):"as no adapter specified";throw new $("There is no suitable adapter to dispatch the request "+g,"ERR_NOT_SUPPORT")}return u},adapters:Tc};function gc(s){if(s.cancelToken&&s.cancelToken.throwIfRequested(),s.signal&&s.signal.aborted)throw new tn(null,s)}function s0(s){return gc(s),s.headers=ut.from(s.headers),s.data=hc.call(s,s.transformRequest),["post","put","patch"].indexOf(s.method)!==-1&&s.headers.setContentType("application/x-www-form-urlencoded",!1),oh.getAdapter(s.adapter||os.adapter)(s).then(function(u){return gc(s),u.data=hc.call(s,s.transformResponse,u),u.headers=ut.from(u.headers),u},function(u){return nh(u)||(gc(s),u&&u.response&&(u.response.data=hc.call(s,s.transformResponse,u.response),u.response.headers=ut.from(u.response.headers))),Promise.reject(u)})}const fh="1.9.0",Vi={};["object","boolean","number","function","string","symbol"].forEach((s,r)=>{Vi[s]=function(u){return typeof u===s||"a"+(r<1?"n ":" ")+s}});const i0={};Vi.transitional=function(r,c,u){function d(h,g){return"[Axios v"+fh+"] Transitional option '"+h+"'"+g+(u?". "+u:"")}return(h,g,b)=>{if(r===!1)throw new $(d(g," has been removed"+(c?" in "+c:"")),$.ERR_DEPRECATED);return c&&!i0[g]&&(i0[g]=!0,console.warn(d(g," has been deprecated since v"+c+" and will be removed in the near future"))),r?r(h,g,b):!0}};Vi.spelling=function(r){return(c,u)=>(console.warn(`${u} is likely a misspelling of ${r}`),!0)};function N1(s,r,c){if(typeof s!="object")throw new $("options must be an object",$.ERR_BAD_OPTION_VALUE);const u=Object.keys(s);let d=u.length;for(;d-- >0;){const h=u[d],g=r[h];if(g){const b=s[h],N=b===void 0||g(b,h,s);if(N!==!0)throw new $("option "+h+" must be "+N,$.ERR_BAD_OPTION_VALUE);continue}if(c!==!0)throw new $("Unknown option "+h,$.ERR_BAD_OPTION)}}const Ci={assertOptions:N1,validators:Vi},Gt=Ci.validators;let il=class{constructor(r){this.defaults=r||{},this.interceptors={request:new $m,response:new $m}}async request(r,c){try{return await this._request(r,c)}catch(u){if(u instanceof Error){let d={};Error.captureStackTrace?Error.captureStackTrace(d):d=new Error;const h=d.stack?d.stack.replace(/^.+\n/,""):"";try{u.stack?h&&!String(u.stack).endsWith(h.replace(/^.+\n.+\n/,""))&&(u.stack+=`
`+h):u.stack=h}catch{}}throw u}}_request(r,c){typeof r=="string"?(c=c||{},c.url=r):c=r||{},c=ul(this.defaults,c);const{transitional:u,paramsSerializer:d,headers:h}=c;u!==void 0&&Ci.assertOptions(u,{silentJSONParsing:Gt.transitional(Gt.boolean),forcedJSONParsing:Gt.transitional(Gt.boolean),clarifyTimeoutError:Gt.transitional(Gt.boolean)},!1),d!=null&&(z.isFunction(d)?c.paramsSerializer={serialize:d}:Ci.assertOptions(d,{encode:Gt.function,serialize:Gt.function},!0)),c.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?c.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:c.allowAbsoluteUrls=!0),Ci.assertOptions(c,{baseUrl:Gt.spelling("baseURL"),withXsrfToken:Gt.spelling("withXSRFToken")},!0),c.method=(c.method||this.defaults.method||"get").toLowerCase();let g=h&&z.merge(h.common,h[c.method]);h&&z.forEach(["delete","get","head","post","put","patch","common"],_=>{delete h[_]}),c.headers=ut.concat(g,h);const b=[];let N=!0;this.interceptors.request.forEach(function(B){typeof B.runWhen=="function"&&B.runWhen(c)===!1||(N=N&&B.synchronous,b.unshift(B.fulfilled,B.rejected))});const j=[];this.interceptors.response.forEach(function(B){j.push(B.fulfilled,B.rejected)});let S,D=0,k;if(!N){const _=[s0.bind(this),void 0];for(_.unshift.apply(_,b),_.push.apply(_,j),k=_.length,S=Promise.resolve(c);D<k;)S=S.then(_[D++],_[D++]);return S}k=b.length;let X=c;for(D=0;D<k;){const _=b[D++],B=b[D++];try{X=_(X)}catch(q){B.call(this,q);break}}try{S=s0.call(this,X)}catch(_){return Promise.reject(_)}for(D=0,k=j.length;D<k;)S=S.then(j[D++],j[D++]);return S}getUri(r){r=ul(this.defaults,r);const c=ih(r.baseURL,r.url,r.allowAbsoluteUrls);return th(c,r.params,r.paramsSerializer)}};z.forEach(["delete","get","head","options"],function(r){il.prototype[r]=function(c,u){return this.request(ul(u||{},{method:r,url:c,data:(u||{}).data}))}});z.forEach(["post","put","patch"],function(r){function c(u){return function(h,g,b){return this.request(ul(b||{},{method:r,headers:u?{"Content-Type":"multipart/form-data"}:{},url:h,data:g}))}}il.prototype[r]=c(),il.prototype[r+"Form"]=c(!0)});let A1=class dh{constructor(r){if(typeof r!="function")throw new TypeError("executor must be a function.");let c;this.promise=new Promise(function(h){c=h});const u=this;this.promise.then(d=>{if(!u._listeners)return;let h=u._listeners.length;for(;h-- >0;)u._listeners[h](d);u._listeners=null}),this.promise.then=d=>{let h;const g=new Promise(b=>{u.subscribe(b),h=b}).then(d);return g.cancel=function(){u.unsubscribe(h)},g},r(function(h,g,b){u.reason||(u.reason=new tn(h,g,b),c(u.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(r){if(this.reason){r(this.reason);return}this._listeners?this._listeners.push(r):this._listeners=[r]}unsubscribe(r){if(!this._listeners)return;const c=this._listeners.indexOf(r);c!==-1&&this._listeners.splice(c,1)}toAbortSignal(){const r=new AbortController,c=u=>{r.abort(u)};return this.subscribe(c),r.signal.unsubscribe=()=>this.unsubscribe(c),r.signal}static source(){let r;return{token:new dh(function(d){r=d}),cancel:r}}};function T1(s){return function(c){return s.apply(null,c)}}function E1(s){return z.isObject(s)&&s.isAxiosError===!0}const Ec={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ec).forEach(([s,r])=>{Ec[r]=s});function mh(s){const r=new il(s),c=X0(il.prototype.request,r);return z.extend(c,il.prototype,r,{allOwnKeys:!0}),z.extend(c,r,null,{allOwnKeys:!0}),c.create=function(d){return mh(ul(s,d))},c}const Ce=mh(os);Ce.Axios=il;Ce.CanceledError=tn;Ce.CancelToken=A1;Ce.isCancel=nh;Ce.VERSION=fh;Ce.toFormData=Zi;Ce.AxiosError=$;Ce.Cancel=Ce.CanceledError;Ce.all=function(r){return Promise.all(r)};Ce.spread=T1;Ce.isAxiosError=E1;Ce.mergeConfig=ul;Ce.AxiosHeaders=ut;Ce.formToJSON=s=>lh(z.isHTMLForm(s)?new FormData(s):s);Ce.getAdapter=oh.getAdapter;Ce.HttpStatusCode=Ec;Ce.default=Ce;const{Axios:s2,AxiosError:i2,CanceledError:r2,isCancel:u2,CancelToken:c2,VERSION:o2,all:f2,Cancel:d2,isAxiosError:m2,spread:h2,toFormData:g2,AxiosHeaders:y2,HttpStatusCode:b2,formToJSON:p2,getAdapter:x2,mergeConfig:v2}=Ce,z1={cpl:45.5,cac:180,cpc:2.3,conversionRate:12.5,revenuePerAgent:15420,activeLeads:234,totalConversations:1847,qualifiedConversations:456},M1=[{id:1,name:"Bot WhatsApp Vendas",channel:"WhatsApp",leadsAttended:156,activeConversations:23,responseRate:94.2,status:"online",lastActivity:"2024-01-15T10:30:00Z"},{id:2,name:"Assistente Instagram",channel:"Instagram",leadsAttended:89,activeConversations:12,responseRate:87.5,status:"online",lastActivity:"2024-01-15T09:45:00Z"},{id:3,name:"Chat Site Principal",channel:"Site",leadsAttended:203,activeConversations:31,responseRate:91.8,status:"online",lastActivity:"2024-01-15T11:15:00Z"},{id:4,name:"Bot Facebook Messenger",channel:"Facebook",leadsAttended:67,activeConversations:8,responseRate:83.1,status:"offline",lastActivity:"2024-01-15T08:20:00Z"},{id:5,name:"Suporte Telegram",channel:"Telegram",leadsAttended:34,activeConversations:5,responseRate:96.7,status:"online",lastActivity:"2024-01-15T11:00:00Z"}],O1=[{id:1,leadName:"Maria Silva",channel:"WhatsApp",agentId:1,status:"quente",intent:"orçamento",lastMessage:"Gostaria de saber o preço do plano premium",lastMessageTime:"2024-01-15T11:30:00Z",messagesCount:8,phone:"+5511999887766",email:"<EMAIL>",messages:[{id:1,sender:"lead",content:"Olá, gostaria de informações sobre seus serviços",timestamp:"2024-01-15T10:00:00Z"},{id:2,sender:"agent",content:"Olá Maria! Fico feliz em ajudar. Que tipo de serviço você está procurando?",timestamp:"2024-01-15T10:01:00Z"},{id:3,sender:"lead",content:"Preciso de uma solução para automação de vendas",timestamp:"2024-01-15T10:05:00Z"},{id:4,sender:"agent",content:"Perfeito! Temos várias opções. Você já tem alguma ferramenta atualmente?",timestamp:"2024-01-15T10:06:00Z"},{id:5,sender:"lead",content:"Não, seria minha primeira vez usando algo assim",timestamp:"2024-01-15T10:10:00Z"},{id:6,sender:"agent",content:"Entendi! Vou te mostrar nosso plano ideal para iniciantes. Posso enviar um material?",timestamp:"2024-01-15T10:11:00Z"},{id:7,sender:"lead",content:"Sim, por favor! E gostaria de saber o preço também",timestamp:"2024-01-15T10:15:00Z"},{id:8,sender:"lead",content:"Gostaria de saber o preço do plano premium",timestamp:"2024-01-15T11:30:00Z"}]},{id:2,leadName:"João Santos",channel:"Instagram",agentId:2,status:"morno",intent:"suporte",lastMessage:"Estou com dificuldades para configurar",lastMessageTime:"2024-01-15T10:45:00Z",messagesCount:5,phone:"+5511888776655",email:"<EMAIL>",messages:[{id:1,sender:"lead",content:"Oi, comprei o produto mas estou com dificuldades",timestamp:"2024-01-15T09:30:00Z"},{id:2,sender:"agent",content:"Olá João! Vou te ajudar. Qual dificuldade específica você está enfrentando?",timestamp:"2024-01-15T09:31:00Z"},{id:3,sender:"lead",content:"Não consigo fazer a integração com meu sistema",timestamp:"2024-01-15T09:35:00Z"},{id:4,sender:"agent",content:"Entendo. Que sistema você está usando? Posso te enviar um tutorial específico.",timestamp:"2024-01-15T09:36:00Z"},{id:5,sender:"lead",content:"Estou com dificuldades para configurar",timestamp:"2024-01-15T10:45:00Z"}]},{id:3,leadName:"Ana Costa",channel:"Site",agentId:3,status:"quente",intent:"agendamento",lastMessage:"Posso agendar uma demonstração?",lastMessageTime:"2024-01-15T11:20:00Z",messagesCount:6,phone:"+5511777665544",email:"<EMAIL>",messages:[{id:1,sender:"lead",content:"Olá, vi vocês no Google e gostei do que fazem",timestamp:"2024-01-15T10:30:00Z"},{id:2,sender:"agent",content:"Olá Ana! Muito obrigado pelo interesse. Como posso ajudá-la?",timestamp:"2024-01-15T10:31:00Z"},{id:3,sender:"lead",content:"Gostaria de ver uma demonstração do produto",timestamp:"2024-01-15T10:35:00Z"},{id:4,sender:"agent",content:"Claro! Seria ótimo mostrar como nossa solução pode ajudar sua empresa.",timestamp:"2024-01-15T10:36:00Z"},{id:5,sender:"lead",content:"Que horários vocês têm disponível esta semana?",timestamp:"2024-01-15T11:00:00Z"},{id:6,sender:"lead",content:"Posso agendar uma demonstração?",timestamp:"2024-01-15T11:20:00Z"}]},{id:4,leadName:"Pedro Oliveira",channel:"Facebook",agentId:4,status:"frio",intent:"informação",lastMessage:"Obrigado pelas informações",lastMessageTime:"2024-01-15T08:30:00Z",messagesCount:3,phone:"+5511666554433",email:"<EMAIL>",messages:[{id:1,sender:"lead",content:"Oi, vi o anúncio de vocês no Facebook",timestamp:"2024-01-15T08:00:00Z"},{id:2,sender:"agent",content:"Olá Pedro! Que bom que nos encontrou. Em que posso ajudá-lo?",timestamp:"2024-01-15T08:01:00Z"},{id:3,sender:"lead",content:"Obrigado pelas informações",timestamp:"2024-01-15T08:30:00Z"}]}],D1={conversionByChannel:[{name:"WhatsApp",value:35,color:"#25D366"},{name:"Instagram",value:25,color:"#E4405F"},{name:"Site",value:30,color:"#3b82f6"},{name:"Facebook",value:10,color:"#1877F2"}],revenueOverTime:[{month:"Jan",revenue:12e3},{month:"Fev",revenue:15e3},{month:"Mar",revenue:18e3},{month:"Abr",revenue:16e3},{month:"Mai",revenue:22e3},{month:"Jun",revenue:25e3}],leadsOverTime:[{month:"Jan",leads:120},{month:"Fev",leads:150},{month:"Mar",leads:180},{month:"Abr",leads:160},{month:"Mai",leads:220},{month:"Jun",leads:250}]},Ki=s=>new Promise(r=>setTimeout(r,s)),r0=async()=>(await Ki(500),z1),u0=async()=>(await Ki(300),M1),c0=async(s={})=>{await Ki(400);let r=[...O1];return s.channel&&(r=r.filter(c=>c.channel===s.channel)),s.status&&(r=r.filter(c=>c.status===s.status)),s.agentId&&(r=r.filter(c=>c.agentId===s.agentId)),r},o0=async()=>(await Ki(600),D1);var hh={};const R1=hh.REACT_APP_API_URL||"http://localhost:3001",C1=hh.REACT_APP_N8N_WEBHOOK_URL||"",Qt=Ce.create({baseURL:R1,timeout:1e4,headers:{"Content-Type":"application/json"}});Qt.interceptors.request.use(s=>{const r=localStorage.getItem("authToken");return r&&(s.headers.Authorization=`Bearer ${r}`),s},s=>Promise.reject(s));Qt.interceptors.response.use(s=>s,s=>{var r;return console.error("API Error:",s),((r=s.response)==null?void 0:r.status)===401&&(localStorage.removeItem("authToken"),window.location.href="/login"),Promise.reject(s)});const qa=!C1,k1=async(s={})=>{if(qa)return await r0();try{return(await Qt.get("/api/kpis",{params:s})).data}catch(r){return console.error("Erro ao buscar KPIs:",r),await r0()}},gh=async()=>{if(qa)return await u0();try{return(await Qt.get("/api/agents")).data}catch(s){return console.error("Erro ao buscar agentes:",s),await u0()}},U1=async(s={})=>{if(qa)return await c0(s);try{return(await Qt.get("/api/conversations",{params:s})).data}catch(r){return console.error("Erro ao buscar conversas:",r),await c0(s)}},_1=async(s="all",r={})=>{if(qa)return await o0();try{return(await Qt.get("/api/charts",{params:{type:s,...r}})).data}catch(c){return console.error("Erro ao buscar dados dos gráficos:",c),await o0()}},B1=async(s,r)=>{if(qa)return console.log("Mock: Enviando WhatsApp para",s,":",r),{success:!0,message:"Mensagem enviada com sucesso (mock)"};try{return(await Qt.post("/api/whatsapp/send",{phone:s,message:r})).data}catch(c){throw console.error("Erro ao enviar WhatsApp:",c),c}},q1=async(s,r,c="")=>{if(qa)return console.log("Mock: Agendando call para lead",s,"em",r),{success:!0,message:"Call agendada com sucesso (mock)"};try{return(await Qt.post("/api/schedule",{leadId:s,datetime:r,notes:c})).data}catch(u){throw console.error("Erro ao agendar call:",u),u}},H1=async(s,r="medium")=>{if(qa)return console.log("Mock: Marcando conversa",s,"como oportunidade"),{success:!0,message:"Marcado como oportunidade (mock)"};try{return(await Qt.patch(`/api/conversations/${s}/opportunity`,{priority:r})).data}catch(c){throw console.error("Erro ao marcar como oportunidade:",c),c}},L1=async(s,r)=>{if(qa)return console.log("Mock: Atualizando status do agente",s,"para",r),{success:!0};try{return(await Qt.patch(`/api/agents/${s}/status`,{status:r})).data}catch(c){throw console.error("Erro ao atualizar status do agente:",c),c}},Y1=()=>{const[s,r]=ie.useState(null),[c,u]=ie.useState([]),[d,h]=ie.useState(null),[g,b]=ie.useState(!0),[N,j]=ie.useState(null);ie.useEffect(()=>{S()},[]);const S=async()=>{try{b(!0),j(null);const[k,X,_]=await Promise.all([k1(),gh(),_1()]);r(k),u(X),h(_)}catch(k){console.error("Erro ao carregar dados do dashboard:",k),j("Erro ao carregar dados. Tente novamente.")}finally{b(!1)}},D=s?[{title:"Custo por Lead (CPL)",value:s.cpl,type:"currency",icon:Vy,color:"blue",trend:"down",trendValue:5.2,subtitle:"Redução de 5.2% vs. mês anterior"},{title:"Custo de Aquisição (CAC)",value:s.cac,type:"currency",icon:Ky,color:"green",trend:"down",trendValue:8.1,subtitle:"Otimização de 8.1% no período"},{title:"Custo por Clique (CPC)",value:s.cpc,type:"currency",icon:Jy,color:"yellow",trend:"up",trendValue:2.3,subtitle:"Aumento de 2.3% vs. período anterior"},{title:"Taxa de Conversão",value:s.conversionRate,type:"percentage",icon:ss,color:"green",trend:"up",trendValue:12.5,subtitle:"Melhoria significativa de 12.5%"},{title:"Receita por Agente",value:s.revenuePerAgent,type:"currency",icon:km,color:"blue",trend:"up",trendValue:15.8,subtitle:"Crescimento de 15.8% na produtividade"},{title:"Leads Ativos",value:s.activeLeads,type:"number",icon:rl,color:"green",trend:"up",trendValue:7.2,subtitle:"Aumento de 7.2% em leads qualificados"},{title:"Total de Conversas",value:s.totalConversations,type:"number",icon:ki,color:"blue",trend:"up",trendValue:18.9,subtitle:"Crescimento de 18.9% no engajamento"},{title:"Conversas Qualificadas",value:s.qualifiedConversations,type:"number",icon:Um,color:"green",trend:"up",trendValue:22.1,subtitle:"Melhoria de 22.1% na qualificação"}]:[];return N?o.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4",children:o.jsxs(ca,{className:"max-w-md w-full text-center",children:[o.jsx(he.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",duration:.5},className:"w-16 h-16 bg-danger-100 dark:bg-danger-900/20 rounded-full flex items-center justify-center mx-auto mb-4",children:o.jsx(Um,{size:32,className:"text-danger-500"})}),o.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Erro ao carregar dashboard"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:N}),o.jsx(et,{onClick:S,leftIcon:o.jsx(yc,{size:16}),className:"w-full",children:"Tentar novamente"})]})}):o.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:o.jsxs("div",{className:"max-w-7xl mx-auto px-6 lg:px-8 py-6",children:[o.jsx(he.div,{className:"mb-10",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:o.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx("h1",{className:"text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white gradient-text",children:"Dashboard CRM"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-base lg:text-lg",children:"Visão geral do desempenho dos seus agentes de IA"})]}),o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx(et,{variant:"secondary",size:"sm",leftIcon:o.jsx(m0,{size:16}),className:"whitespace-nowrap",children:"Últimos 30 dias"}),o.jsx(et,{variant:"ghost",size:"sm",leftIcon:o.jsx(yc,{size:16,className:g?"animate-spin":""}),onClick:S,disabled:g,children:"Atualizar"})]})]})}),o.jsxs(he.div,{className:"mb-16",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2,duration:.5},children:[o.jsxs("div",{className:"flex items-center gap-3 mb-8",children:[o.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg",children:o.jsx(rs,{size:22,className:"text-white"})}),o.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Indicadores Principais"})]}),o.jsx(Vx,{kpis:D,loading:g})]}),o.jsxs(he.div,{className:"mb-12",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4,duration:.5},children:[o.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[o.jsx("div",{className:"w-8 h-8 bg-success-100 dark:bg-success-900/20 rounded-lg flex items-center justify-center",children:o.jsx(ss,{size:20,className:"text-success-600 dark:text-success-400"})}),o.jsx("h2",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Análise de Performance"})]}),o.jsx(Fx,{chartData:d,loading:g})]}),o.jsxs(he.div,{className:"mb-12",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6,duration:.5},children:[o.jsxs("div",{className:"flex items-center justify-between mb-6",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("div",{className:"w-8 h-8 bg-warning-100 dark:bg-warning-900/20 rounded-lg flex items-center justify-center",children:o.jsx(rl,{size:20,className:"text-warning-600 dark:text-warning-400"})}),o.jsx("h2",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Agentes de IA"})]}),o.jsx(et,{variant:"outline",size:"sm",children:"Ver Todos"})]}),o.jsx(G0,{agents:c.slice(0,3),loading:g,onViewDetails:k=>{console.log("Ver detalhes do agente:",k)},onToggleStatus:k=>{console.log("Toggle status do agente:",k)},onSettings:k=>{console.log("Configurações do agente:",k)}})]}),o.jsxs(he.div,{className:"grid grid-cols-1 md:grid-cols-3 gap-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8,duration:.5},children:[o.jsxs(ca,{className:"text-center group hover:shadow-glow transition-all duration-300",children:[o.jsx(he.div,{className:"w-14 h-14 bg-primary-100 dark:bg-primary-900/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200",whileHover:{rotate:5},children:o.jsx(ki,{size:28,className:"text-primary-600 dark:text-primary-400"})}),o.jsx("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-1",children:g?"-":$l((s==null?void 0:s.totalConversations)||0)}),o.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Conversas Hoje"})]}),o.jsxs(ca,{className:"text-center group hover:shadow-glow-success transition-all duration-300",children:[o.jsx(he.div,{className:"w-14 h-14 bg-success-100 dark:bg-success-900/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200",whileHover:{rotate:5},children:o.jsx(ss,{size:28,className:"text-success-600 dark:text-success-400"})}),o.jsx("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-1",children:g?"-":is((s==null?void 0:s.conversionRate)||0)}),o.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Taxa de Conversão"})]}),o.jsxs(ca,{className:"text-center group hover:shadow-glow-warning transition-all duration-300",children:[o.jsx(he.div,{className:"w-14 h-14 bg-warning-100 dark:bg-warning-900/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-200",whileHover:{rotate:5},children:o.jsx(km,{size:28,className:"text-warning-600 dark:text-warning-400"})}),o.jsx("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-1",children:g?"-":Fl((s==null?void 0:s.revenuePerAgent)||0)}),o.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Receita por Agente"})]})]})]})})},G1=({conversation:s,isOpen:r,onClose:c})=>{const[u,d]=ie.useState(!1),[h,g]=ie.useState(""),[b,N]=ie.useState(""),[j,S]=ie.useState("");if(!r||!s)return null;const{id:D,leadName:k,channel:X,status:_,intent:B,lastMessage:q,lastMessageTime:Q,messagesCount:P,phone:K,email:de,messages:re=[]}=s,ce=z0(_),Se=Cc(X),G=async()=>{if(!(!h.trim()||!K)){d(!0);try{await B1(K,h),g(""),alert("Mensagem enviada com sucesso!")}catch{alert("Erro ao enviar mensagem")}finally{d(!1)}}},Te=async()=>{if(b){d(!0);try{await q1(D,b,j),N(""),S(""),alert("Call agendada com sucesso!")}catch{alert("Erro ao agendar call")}finally{d(!1)}}},Et=async()=>{d(!0);try{await H1(D),alert("Marcado como oportunidade!")}catch{alert("Erro ao marcar como oportunidade")}finally{d(!1)}};return o.jsx("div",{className:"modal-overlay",onClick:c,children:o.jsxs("div",{className:"modal-content",onClick:ge=>ge.stopPropagation(),children:[o.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-xl",children:Se}),o.jsx("div",{className:"absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white",style:{backgroundColor:ce}})]}),o.jsxs("div",{children:[o.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:k}),o.jsxs("p",{className:"text-sm text-gray-500",children:[X," • ",ux(B)]})]})]}),o.jsx("button",{onClick:c,className:"p-2 hover:bg-gray-100 rounded-lg",children:o.jsx(zc,{size:20})})]}),o.jsxs("div",{className:"p-6 border-b border-gray-200",children:[o.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Informações do Lead"}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx(h0,{size:16,className:"text-gray-500"}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm text-gray-500",children:"Nome"}),o.jsx("p",{className:"font-medium",children:k})]})]}),K&&o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx(_m,{size:16,className:"text-gray-500"}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm text-gray-500",children:"Telefone"}),o.jsx("p",{className:"font-medium",children:ix(K)})]})]}),de&&o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx(Wy,{size:16,className:"text-gray-500"}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),o.jsx("p",{className:"font-medium",children:de})]})]}),o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx(ki,{size:16,className:"text-gray-500"}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm text-gray-500",children:"Total de Mensagens"}),o.jsx("p",{className:"font-medium",children:P})]})]})]})]}),o.jsxs("div",{className:"p-6 border-b border-gray-200",children:[o.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Histórico de Mensagens"}),o.jsx("div",{className:"max-h-64 overflow-y-auto space-y-3",children:re.map(ge=>o.jsx("div",{className:`flex ${ge.sender==="agent"?"justify-end":"justify-start"}`,children:o.jsxs("div",{className:`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${ge.sender==="agent"?"bg-blue-500 text-white":"bg-gray-100 text-gray-900"}`,children:[o.jsx("p",{className:"text-sm",children:ge.content}),o.jsx("p",{className:`text-xs mt-1 ${ge.sender==="agent"?"text-blue-100":"text-gray-500"}`,children:E0(ge.timestamp)})]})},ge.id))})]}),o.jsxs("div",{className:"p-6",children:[o.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Ações Rápidas"}),K&&o.jsxs("div",{className:"mb-6",children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Enviar WhatsApp"}),o.jsxs("div",{className:"flex gap-2",children:[o.jsx("input",{type:"text",value:h,onChange:ge=>g(ge.target.value),placeholder:"Digite sua mensagem...",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),o.jsxs("button",{onClick:G,disabled:u||!h.trim(),className:"btn btn-primary",children:[o.jsx(_m,{size:16}),"Enviar"]})]})]}),o.jsxs("div",{className:"mb-6",children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Agendar Call"}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("input",{type:"datetime-local",value:b,onChange:ge=>N(ge.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),o.jsx("input",{type:"text",value:j,onChange:ge=>S(ge.target.value),placeholder:"Observações (opcional)",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),o.jsxs("button",{onClick:Te,disabled:u||!b,className:"btn btn-secondary",children:[o.jsx(m0,{size:16}),"Agendar"]})]})]}),o.jsxs("div",{className:"flex gap-3",children:[o.jsxs("button",{onClick:Et,disabled:u,className:"btn btn-primary flex-1",children:[o.jsx(Fy,{size:16}),"Marcar como Oportunidade"]}),K&&o.jsxs("a",{href:`https://wa.me/${K.replace(/\D/g,"")}`,target:"_blank",rel:"noopener noreferrer",className:"btn btn-secondary",children:[o.jsx($y,{size:16}),"Abrir WhatsApp"]})]})]})]})})},X1=({children:s,variant:r="gray",size:c="md",dot:u=!1,pulse:d=!1,animate:h=!0,className:g,...b})=>{const N=tt("inline-flex items-center gap-1.5 font-medium rounded-full",_c.default,Uc.badge[r],{"px-2 py-0.5 text-xs":c==="sm","px-2.5 py-1 text-xs":c==="md","px-3 py-1.5 text-sm":c==="lg"},g),j=o.jsxs(o.Fragment,{children:[u&&o.jsx("span",{className:tt("w-1.5 h-1.5 rounded-full",{"bg-primary-500":r==="primary","bg-success-500":r==="success","bg-warning-500":r==="warning","bg-danger-500":r==="danger","bg-gray-500":r==="gray"},d&&"animate-pulse")}),s]});return h?o.jsx(he.span,{className:N,initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.2,ease:"easeOut"},...b,children:j}):o.jsx("span",{className:N,...b,children:j})},f0=({status:s,...r})=>{const c={online:{variant:"success",children:"Online",dot:!0,pulse:!0},offline:{variant:"gray",children:"Offline",dot:!0},busy:{variant:"warning",children:"Ocupado",dot:!0},away:{variant:"warning",children:"Ausente",dot:!0}},u=c[s]||c.offline;return o.jsx(X1,{...u,...r})},Z1=({agent:s,isOpen:r,onClose:c,onSave:u,onDelete:d})=>{const[h,g]=ie.useState(s||{}),[b,N]=ie.useState(!1),[j,S]=ie.useState(!1);if(!r||!s)return null;const D=async()=>{S(!0);try{await(u==null?void 0:u(h)),N(!1)}catch(B){console.error("Erro ao salvar agente:",B)}finally{S(!1)}},k=async()=>{if(window.confirm("Tem certeza que deseja excluir este agente?"))try{await(d==null?void 0:d(s.id)),c()}catch(B){console.error("Erro ao excluir agente:",B)}},X=()=>{const B={...h,id:Date.now(),name:`${h.name} (Cópia)`,status:"offline"};u==null||u(B)},_=Cc(h.channel);return o.jsx(Ui,{children:o.jsx(he.div,{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:c,children:o.jsxs(he.div,{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},onClick:B=>B.stopPropagation(),children:[o.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-2xl shadow-lg",children:_}),o.jsxs("div",{children:[o.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:h.name}),o.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[o.jsx(f0,{status:h.status,size:"sm"}),o.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:h.channel})]})]})]}),o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(et,{variant:"ghost",size:"sm",onClick:()=>N(!b),leftIcon:o.jsx(d0,{size:16}),children:b?"Cancelar":"Editar"}),o.jsx(et,{variant:"ghost",size:"sm",onClick:c,children:o.jsx(zc,{size:20})})]})]}),o.jsxs("div",{className:"p-6 space-y-6",children:[o.jsxs(ca,{className:"p-6",children:[o.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2",children:[o.jsx(h0,{size:20}),"Informações Básicas"]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Nome do Agente"}),b?o.jsx("input",{type:"text",value:h.name||"",onChange:B=>g({...h,name:B.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"}):o.jsx("p",{className:"text-gray-900 dark:text-white font-medium",children:h.name})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Canal"}),b?o.jsxs("select",{value:h.channel||"",onChange:B=>g({...h,channel:B.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[o.jsx("option",{value:"WhatsApp",children:"WhatsApp"}),o.jsx("option",{value:"Instagram",children:"Instagram"}),o.jsx("option",{value:"Facebook",children:"Facebook"}),o.jsx("option",{value:"Site",children:"Site"}),o.jsx("option",{value:"Telegram",children:"Telegram"}),o.jsx("option",{value:"Email",children:"Email"})]}):o.jsx("p",{className:"text-gray-900 dark:text-white font-medium",children:h.channel})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Status"}),b?o.jsxs("select",{value:h.status||"",onChange:B=>g({...h,status:B.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[o.jsx("option",{value:"online",children:"Online"}),o.jsx("option",{value:"offline",children:"Offline"}),o.jsx("option",{value:"busy",children:"Ocupado"}),o.jsx("option",{value:"away",children:"Ausente"})]}):o.jsx(f0,{status:h.status})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Taxa de Resposta"}),b?o.jsx("input",{type:"number",min:"0",max:"100",step:"0.1",value:h.responseRate||"",onChange:B=>g({...h,responseRate:parseFloat(B.target.value)}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"}):o.jsxs("p",{className:"text-gray-900 dark:text-white font-medium",children:[h.responseRate,"%"]})]})]})]}),o.jsxs(ca,{className:"p-6",children:[o.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2",children:[o.jsx(Mc,{size:20}),"Métricas de Performance"]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:h.leadsAttended||0}),o.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Leads Atendidos"})]}),o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:h.activeConversations||0}),o.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Conversas Ativas"})]}),o.jsxs("div",{className:"text-center",children:[o.jsxs("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:[h.responseRate||0,"%"]}),o.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Taxa de Resposta"})]})]})]}),o.jsxs(ca,{className:"p-6",children:[o.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2",children:[o.jsx(qi,{size:20}),"Configurações Avançadas"]}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{children:[o.jsx("p",{className:"font-medium text-gray-900 dark:text-white",children:"Auto-resposta"}),o.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Responder automaticamente às mensagens"})]}),o.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[o.jsx("input",{type:"checkbox",className:"sr-only peer",defaultChecked:!0}),o.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{children:[o.jsx("p",{className:"font-medium text-gray-900 dark:text-white",children:"Notificações"}),o.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Receber notificações de novas mensagens"})]}),o.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[o.jsx("input",{type:"checkbox",className:"sr-only peer",defaultChecked:!0}),o.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]})]})]}),o.jsxs("div",{className:"flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(et,{variant:"ghost",size:"sm",onClick:X,leftIcon:o.jsx(Py,{size:16}),children:"Duplicar"}),o.jsx(et,{variant:"ghost",size:"sm",onClick:k,leftIcon:o.jsx(Iy,{size:16}),className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:"Excluir"})]}),o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx(et,{variant:"secondary",onClick:c,children:"Cancelar"}),b&&o.jsx(et,{variant:"primary",onClick:D,loading:j,leftIcon:o.jsx(eb,{size:16}),children:"Salvar Alterações"})]})]})]})})})},Q1=()=>{const[s,r]=ie.useState([]),[c,u]=ie.useState([]),[d,h]=ie.useState(!0),[g,b]=ie.useState(""),[N,j]=ie.useState("all"),[S,D]=ie.useState("all"),[k,X]=ie.useState(null),[_,B]=ie.useState(!1),[q,Q]=ie.useState(null),[P,K]=ie.useState(!1),[de,re]=ie.useState(null);ie.useEffect(()=>{ce()},[]),ie.useEffect(()=>{Se()},[s,g,N,S]);const ce=async()=>{try{h(!0),re(null);const v=await gh();r(v)}catch(v){console.error("Erro ao carregar agentes:",v),re("Erro ao carregar agentes. Tente novamente.")}finally{h(!1)}},Se=()=>{let v=[...s];g&&(v=v.filter(C=>C.name.toLowerCase().includes(g.toLowerCase())||C.channel.toLowerCase().includes(g.toLowerCase()))),N!=="all"&&(v=v.filter(C=>C.status===N)),S!=="all"&&(v=v.filter(C=>C.channel===S)),u(v)},G=async v=>{try{const C=await U1({agentId:v.id});C.length>0?(X(C[0]),B(!0)):alert("Nenhuma conversa encontrada para este agente.")}catch(C){console.error("Erro ao buscar conversas:",C),alert("Erro ao carregar conversas do agente.")}},Te=async v=>{try{const C=v.status==="online"?"offline":"online";await L1(v.id,C),r(U=>U.map(se=>se.id===v.id?{...se,status:C}:se))}catch(C){console.error("Erro ao atualizar status:",C),alert("Erro ao atualizar status do agente.")}},Et=v=>{console.log("Configurações do agente:",v),alert(`Configurações do ${v.name} - Em desenvolvimento`)},ge=v=>{Q(v),K(!0)},_e=async v=>{try{r(C=>C.map(U=>U.id===v.id?v:U)),K(!1),Q(null)}catch(C){console.error("Erro ao salvar agente:",C),alert("Erro ao salvar agente.")}},Vt=async v=>{try{r(C=>C.filter(U=>U.id!==v)),K(!1),Q(null)}catch(C){console.error("Erro ao deletar agente:",C),alert("Erro ao deletar agente.")}},zt=()=>[...new Set(s.map(C=>C.channel))],Le=()=>{const v=[["Nome","Canal","Status","Leads Atendidos","Conversas Ativas","Taxa de Resposta"],...c.map(ae=>[ae.name,ae.channel,ae.status,ae.leadsAttended,ae.activeConversations,`${ae.responseRate}%`])].map(ae=>ae.join(",")).join(`
`),C=new Blob([v],{type:"text/csv"}),U=window.URL.createObjectURL(C),se=document.createElement("a");se.href=U,se.download="agentes-crm.csv",se.click(),window.URL.revokeObjectURL(U)};return de?o.jsx("div",{className:"container py-8",children:o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:o.jsx(rl,{size:32,className:"text-red-500"})}),o.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Erro ao carregar agentes"}),o.jsx("p",{className:"text-gray-600 mb-4",children:de}),o.jsx("button",{onClick:ce,className:"btn btn-primary",children:"Tentar novamente"})]})}):o.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:o.jsxs("div",{className:"max-w-7xl mx-auto px-6 lg:px-8 py-6",children:[o.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-10",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx("h1",{className:"text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white gradient-text",children:"Agentes de IA"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-base lg:text-lg",children:"Gerencie e monitore seus agentes de atendimento automatizado"})]}),o.jsxs("div",{className:"flex items-center gap-3 mt-6 lg:mt-0",children:[o.jsxs("button",{onClick:ce,disabled:d,className:"flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-gray-700 dark:text-gray-300 font-medium",children:[o.jsx(yc,{size:16,className:d?"animate-spin":""}),"Atualizar"]}),o.jsxs("button",{onClick:Le,className:"flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-gray-700 dark:text-gray-300 font-medium",children:[o.jsx(tb,{size:16}),"Exportar"]}),o.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl",children:[o.jsx(ab,{size:16}),"Novo Agente"]})]})]}),o.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8",children:o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[o.jsxs("div",{className:"relative",children:[o.jsx(lb,{size:18,className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"}),o.jsx("input",{type:"text",placeholder:"Buscar agentes...",value:g,onChange:v=>b(v.target.value),className:"w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"})]}),o.jsxs("select",{value:N,onChange:v=>j(v.target.value),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[o.jsx("option",{value:"all",children:"Todos os Status"}),o.jsx("option",{value:"online",children:"Online"}),o.jsx("option",{value:"offline",children:"Offline"})]}),o.jsxs("select",{value:S,onChange:v=>D(v.target.value),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[o.jsx("option",{value:"all",children:"Todos os Canais"}),zt().map(v=>o.jsx("option",{value:v,children:v},v))]}),o.jsxs("button",{onClick:()=>{b(""),j("all"),D("all")},className:"flex items-center justify-center gap-2 px-4 py-3 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-xl transition-colors",children:[o.jsx(nb,{size:16}),"Limpar Filtros"]})]})}),o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-10",children:[o.jsxs("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6 rounded-2xl border border-blue-200 dark:border-blue-800 shadow-sm",children:[o.jsx("p",{className:"text-sm text-blue-600 dark:text-blue-400 mb-2 font-medium",children:"Total de Agentes"}),o.jsx("p",{className:"text-3xl font-bold text-blue-900 dark:text-blue-100",children:s.length})]}),o.jsxs("div",{className:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-6 rounded-2xl border border-green-200 dark:border-green-800 shadow-sm",children:[o.jsx("p",{className:"text-sm text-green-600 dark:text-green-400 mb-2 font-medium",children:"Online"}),o.jsx("p",{className:"text-3xl font-bold text-green-900 dark:text-green-100",children:s.filter(v=>v.status==="online").length})]}),o.jsxs("div",{className:"bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 p-6 rounded-2xl border border-yellow-200 dark:border-yellow-800 shadow-sm",children:[o.jsx("p",{className:"text-sm text-yellow-600 dark:text-yellow-400 mb-2 font-medium",children:"Conversas Ativas"}),o.jsx("p",{className:"text-3xl font-bold text-yellow-900 dark:text-yellow-100",children:s.reduce((v,C)=>v+C.activeConversations,0)})]}),o.jsxs("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-2xl border border-purple-200 dark:border-purple-800 shadow-sm",children:[o.jsx("p",{className:"text-sm text-purple-600 dark:text-purple-400 mb-2 font-medium",children:"Taxa Média"}),o.jsx("p",{className:"text-3xl font-bold text-purple-900 dark:text-purple-100",children:s.length>0?`${(s.reduce((v,C)=>v+C.responseRate,0)/s.length).toFixed(1)}%`:"0%"})]})]}),o.jsx(G0,{agents:c,loading:d,onViewDetails:G,onToggleStatus:Te,onSettings:Et,onEdit:ge}),o.jsx(G1,{conversation:k,isOpen:_,onClose:()=>{B(!1),X(null)}}),o.jsx(Z1,{agent:q,isOpen:P,onClose:()=>{K(!1),Q(null)},onSave:_e,onDelete:Vt})]})})},V1=()=>{const s=p0(),r=[{path:"/",icon:g0,label:"Dashboard",exact:!0},{path:"/agents",icon:rl,label:"Agentes"},{path:"/conversations",icon:Mc,label:"Conversas"},{path:"/analytics",icon:rs,label:"Analytics"},{path:"/settings",icon:qi,label:"Config"}],c=(u,d=!1)=>d?s.pathname===u:s.pathname.startsWith(u);return o.jsx("nav",{className:"mobile-nav",children:o.jsx("div",{className:"flex justify-around items-center",children:r.map(({path:u,icon:d,label:h,exact:g})=>o.jsxs(x0,{to:u,className:`mobile-nav-item ${c(u,g)?"active":""}`,children:[o.jsx(d,{size:20}),o.jsx("span",{children:h})]},u))})})},yh=ie.createContext(),bh=()=>{const s=ie.useContext(yh);if(!s)throw new Error("useTheme must be used within a ThemeProvider");return s},K1=({children:s})=>{const[r,c]=ie.useState(()=>{const b=localStorage.getItem("theme");return b||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")});ie.useEffect(()=>{const b=window.document.documentElement;b.classList.remove("light","dark"),b.classList.add(r),localStorage.setItem("theme",r)},[r]);const g={theme:r,toggleTheme:()=>{c(b=>b==="light"?"dark":"light")},setLightTheme:()=>c("light"),setDarkTheme:()=>c("dark"),isDark:r==="dark",isLight:r==="light"};return o.jsx(yh.Provider,{value:g,children:s})},J1=()=>{const[s,r]=ie.useState({width:typeof window<"u"?window.innerWidth:0,height:typeof window<"u"?window.innerHeight:0}),[c,u]=ie.useState("lg");return ie.useEffect(()=>{const D=()=>{const k=window.innerWidth,X=window.innerHeight;r({width:k,height:X}),k<640?u("xs"):k<768?u("sm"):k<1024?u("md"):k<1280?u("lg"):u("xl")};return D(),window.addEventListener("resize",D),()=>window.removeEventListener("resize",D)},[]),{windowSize:s,breakpoint:c,isMobile:c==="xs"||c==="sm",isTablet:c==="md",isDesktop:c==="lg"||c==="xl",isSmallScreen:c==="xs",isLargeScreen:c==="xl",isAbove:D=>{const k={xs:0,sm:640,md:768,lg:1024,xl:1280};return s.width>=k[D]},isBelow:D=>{const k={xs:640,sm:768,md:1024,lg:1280,xl:1/0};return s.width<k[D]}}},W1=({isOpen:s,onClose:r})=>{const c=p0(),{theme:u,toggleTheme:d}=bh(),h=[{path:"/",icon:g0,label:"Dashboard",exact:!0},{path:"/agents",icon:rl,label:"Agentes de IA"},{path:"/conversations",icon:Mc,label:"Conversas"},{path:"/analytics",icon:rs,label:"Analytics"},{path:"/settings",icon:qi,label:"Configurações"}],g=(b,N=!1)=>N?c.pathname===b:c.pathname.startsWith(b);return o.jsx(Ui,{children:(s||window.innerWidth>=768)&&o.jsxs(he.div,{className:tt("fixed left-0 top-0 bottom-0 w-72 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 z-50","flex flex-col p-6 glass-effect","md:relative md:translate-x-0",!s&&"md:block hidden"),initial:{x:-288},animate:{x:0},exit:{x:-288},transition:{type:"spring",damping:25,stiffness:200},children:[o.jsxs("div",{className:"flex items-center justify-between mb-8",children:[o.jsxs(he.div,{className:"flex items-center gap-3",whileHover:{scale:1.05},transition:{type:"spring",stiffness:300},children:[o.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center shadow-lg",children:o.jsx(rs,{size:24,className:"text-white"})}),o.jsx("h1",{className:"text-xl font-bold text-gray-900 dark:text-white gradient-text",children:"CRM AI"})]}),o.jsx(et,{variant:"ghost",size:"sm",onClick:r,className:"md:hidden",children:o.jsx(zc,{size:20})})]}),o.jsx("nav",{className:"flex-1 space-y-2",children:h.map(({path:b,icon:N,label:j,exact:S},D)=>o.jsx(he.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:D*.1},children:o.jsxs(x0,{to:b,onClick:r,className:tt("flex items-center gap-3 px-4 py-3 rounded-xl font-medium transition-all duration-200","hover:bg-gray-100 dark:hover:bg-gray-800",g(b,S)?"bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 shadow-sm":"text-gray-700 dark:text-gray-300"),children:[o.jsx(N,{size:20}),o.jsx("span",{children:j}),g(b,S)&&o.jsx(he.div,{className:"ml-auto w-2 h-2 bg-primary-500 rounded-full",layoutId:"activeIndicator",transition:{type:"spring",stiffness:300,damping:30}})]})},b))}),o.jsx("div",{className:"mb-6",children:o.jsx(et,{variant:"ghost",onClick:d,className:"w-full justify-start",leftIcon:u==="dark"?o.jsx(y0,{size:20}):o.jsx(b0,{size:20}),children:u==="dark"?"Modo Claro":"Modo Escuro"})}),o.jsx("div",{className:"pt-6 border-t border-gray-200 dark:border-gray-700",children:o.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 text-center space-y-1",children:[o.jsx("p",{className:"font-medium",children:"CRM AI v2.0"}),o.jsx("p",{children:"Powered by n8n"})]})})]})})},F1=({onMenuClick:s})=>{const{theme:r,toggleTheme:c}=bh();return o.jsxs(he.header,{className:"md:hidden bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex items-center justify-between glass-effect",initial:{y:-60},animate:{y:0},transition:{type:"spring",damping:20,stiffness:300},children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center shadow-md",children:o.jsx(rs,{size:20,className:"text-white"})}),o.jsx("h1",{className:"text-xl font-bold text-gray-900 dark:text-white gradient-text",children:"CRM AI"})]}),o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(et,{variant:"ghost",size:"sm",onClick:c,children:r==="dark"?o.jsx(y0,{size:18}):o.jsx(b0,{size:18})}),o.jsx(et,{variant:"ghost",size:"sm",onClick:s,children:o.jsx(sb,{size:20})})]})]})},$1=()=>{const[s,r]=rb.useState(!1),{isMobile:c}=J1(),u=()=>r(!1),d=()=>r(!0);return o.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300",children:[o.jsx(W1,{isOpen:s,onClose:u}),o.jsx(Ui,{children:c&&s&&o.jsx(he.div,{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-40",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:u})}),o.jsxs("div",{className:tt("transition-all duration-300","md:ml-72"),children:[o.jsx(F1,{onMenuClick:d}),o.jsx("main",{className:"min-h-screen",children:o.jsx(Ui,{mode:"wait",children:o.jsxs(ub,{children:[o.jsx(es,{path:"/",element:o.jsx(Y1,{})}),o.jsx(es,{path:"/agents",element:o.jsx(Q1,{})}),o.jsx(es,{path:"/conversations",element:o.jsxs(he.div,{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:[o.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Conversas"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Em desenvolvimento..."})]})}),o.jsx(es,{path:"/analytics",element:o.jsxs(he.div,{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:[o.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Analytics"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Em desenvolvimento..."})]})}),o.jsx(es,{path:"/settings",element:o.jsxs(he.div,{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:[o.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Configurações"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Em desenvolvimento..."})]})})]})})})]}),o.jsx(V1,{})]})};function P1(){return o.jsx(K1,{children:o.jsx(ib,{children:o.jsx($1,{})})})}vb.createRoot(document.getElementById("root")).render(o.jsx(ie.StrictMode,{children:o.jsx(P1,{})}));

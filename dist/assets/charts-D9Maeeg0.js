import{r as q,R as S}from"./router-Dh5s4hGl.js";import{c as vi,g as oe}from"./vendor-Csw2ODfV.js";function Fb(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=Fb(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function J(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=Fb(e))&&(n&&(n+=" "),n+=t);return n}var ho,Uh;function qe(){if(Uh)return ho;Uh=1;var e=Array.isArray;return ho=e,ho}var po,Hh;function Wb(){if(Hh)return po;Hh=1;var e=typeof vi=="object"&&vi&&vi.Object===Object&&vi;return po=e,po}var vo,Kh;function ft(){if(Kh)return vo;Kh=1;var e=Wb(),t=typeof self=="object"&&self&&self.Object===Object&&self,r=e||t||Function("return this")();return vo=r,vo}var yo,Gh;function ii(){if(Gh)return yo;Gh=1;var e=ft(),t=e.Symbol;return yo=t,yo}var mo,Vh;function cO(){if(Vh)return mo;Vh=1;var e=ii(),t=Object.prototype,r=t.hasOwnProperty,n=t.toString,i=e?e.toStringTag:void 0;function a(o){var u=r.call(o,i),c=o[i];try{o[i]=void 0;var s=!0}catch{}var f=n.call(o);return s&&(u?o[i]=c:delete o[i]),f}return mo=a,mo}var go,Xh;function sO(){if(Xh)return go;Xh=1;var e=Object.prototype,t=e.toString;function r(n){return t.call(n)}return go=r,go}var bo,Yh;function St(){if(Yh)return bo;Yh=1;var e=ii(),t=cO(),r=sO(),n="[object Null]",i="[object Undefined]",a=e?e.toStringTag:void 0;function o(u){return u==null?u===void 0?i:n:a&&a in Object(u)?t(u):r(u)}return bo=o,bo}var xo,Zh;function Pt(){if(Zh)return xo;Zh=1;function e(t){return t!=null&&typeof t=="object"}return xo=e,xo}var wo,Jh;function Xr(){if(Jh)return wo;Jh=1;var e=St(),t=Pt(),r="[object Symbol]";function n(i){return typeof i=="symbol"||t(i)&&e(i)==r}return wo=n,wo}var Oo,Qh;function Df(){if(Qh)return Oo;Qh=1;var e=qe(),t=Xr(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;function i(a,o){if(e(a))return!1;var u=typeof a;return u=="number"||u=="symbol"||u=="boolean"||a==null||t(a)?!0:n.test(a)||!r.test(a)||o!=null&&a in Object(o)}return Oo=i,Oo}var _o,ep;function It(){if(ep)return _o;ep=1;function e(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}return _o=e,_o}var Ao,tp;function Nf(){if(tp)return Ao;tp=1;var e=St(),t=It(),r="[object AsyncFunction]",n="[object Function]",i="[object GeneratorFunction]",a="[object Proxy]";function o(u){if(!t(u))return!1;var c=e(u);return c==n||c==i||c==r||c==a}return Ao=o,Ao}var So,rp;function lO(){if(rp)return So;rp=1;var e=ft(),t=e["__core-js_shared__"];return So=t,So}var Po,np;function fO(){if(np)return Po;np=1;var e=lO(),t=function(){var n=/[^.]+$/.exec(e&&e.keys&&e.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}();function r(n){return!!t&&t in n}return Po=r,Po}var To,ip;function zb(){if(ip)return To;ip=1;var e=Function.prototype,t=e.toString;function r(n){if(n!=null){try{return t.call(n)}catch{}try{return n+""}catch{}}return""}return To=r,To}var Eo,ap;function hO(){if(ap)return Eo;ap=1;var e=Nf(),t=fO(),r=It(),n=zb(),i=/[\\^$.*+?()[\]{}|]/g,a=/^\[object .+?Constructor\]$/,o=Function.prototype,u=Object.prototype,c=o.toString,s=u.hasOwnProperty,f=RegExp("^"+c.call(s).replace(i,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function l(h){if(!r(h)||t(h))return!1;var p=e(h)?f:a;return p.test(n(h))}return Eo=l,Eo}var jo,op;function pO(){if(op)return jo;op=1;function e(t,r){return t==null?void 0:t[r]}return jo=e,jo}var Mo,up;function ar(){if(up)return Mo;up=1;var e=hO(),t=pO();function r(n,i){var a=t(n,i);return e(a)?a:void 0}return Mo=r,Mo}var $o,cp;function Ea(){if(cp)return $o;cp=1;var e=ar(),t=e(Object,"create");return $o=t,$o}var Co,sp;function dO(){if(sp)return Co;sp=1;var e=Ea();function t(){this.__data__=e?e(null):{},this.size=0}return Co=t,Co}var Io,lp;function vO(){if(lp)return Io;lp=1;function e(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}return Io=e,Io}var ko,fp;function yO(){if(fp)return ko;fp=1;var e=Ea(),t="__lodash_hash_undefined__",r=Object.prototype,n=r.hasOwnProperty;function i(a){var o=this.__data__;if(e){var u=o[a];return u===t?void 0:u}return n.call(o,a)?o[a]:void 0}return ko=i,ko}var Ro,hp;function mO(){if(hp)return Ro;hp=1;var e=Ea(),t=Object.prototype,r=t.hasOwnProperty;function n(i){var a=this.__data__;return e?a[i]!==void 0:r.call(a,i)}return Ro=n,Ro}var Do,pp;function gO(){if(pp)return Do;pp=1;var e=Ea(),t="__lodash_hash_undefined__";function r(n,i){var a=this.__data__;return this.size+=this.has(n)?0:1,a[n]=e&&i===void 0?t:i,this}return Do=r,Do}var No,dp;function bO(){if(dp)return No;dp=1;var e=dO(),t=vO(),r=yO(),n=mO(),i=gO();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,No=a,No}var qo,vp;function xO(){if(vp)return qo;vp=1;function e(){this.__data__=[],this.size=0}return qo=e,qo}var Lo,yp;function qf(){if(yp)return Lo;yp=1;function e(t,r){return t===r||t!==t&&r!==r}return Lo=e,Lo}var Bo,mp;function ja(){if(mp)return Bo;mp=1;var e=qf();function t(r,n){for(var i=r.length;i--;)if(e(r[i][0],n))return i;return-1}return Bo=t,Bo}var Fo,gp;function wO(){if(gp)return Fo;gp=1;var e=ja(),t=Array.prototype,r=t.splice;function n(i){var a=this.__data__,o=e(a,i);if(o<0)return!1;var u=a.length-1;return o==u?a.pop():r.call(a,o,1),--this.size,!0}return Fo=n,Fo}var Wo,bp;function OO(){if(bp)return Wo;bp=1;var e=ja();function t(r){var n=this.__data__,i=e(n,r);return i<0?void 0:n[i][1]}return Wo=t,Wo}var zo,xp;function _O(){if(xp)return zo;xp=1;var e=ja();function t(r){return e(this.__data__,r)>-1}return zo=t,zo}var Uo,wp;function AO(){if(wp)return Uo;wp=1;var e=ja();function t(r,n){var i=this.__data__,a=e(i,r);return a<0?(++this.size,i.push([r,n])):i[a][1]=n,this}return Uo=t,Uo}var Ho,Op;function Ma(){if(Op)return Ho;Op=1;var e=xO(),t=wO(),r=OO(),n=_O(),i=AO();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,Ho=a,Ho}var Ko,_p;function Lf(){if(_p)return Ko;_p=1;var e=ar(),t=ft(),r=e(t,"Map");return Ko=r,Ko}var Go,Ap;function SO(){if(Ap)return Go;Ap=1;var e=bO(),t=Ma(),r=Lf();function n(){this.size=0,this.__data__={hash:new e,map:new(r||t),string:new e}}return Go=n,Go}var Vo,Sp;function PO(){if(Sp)return Vo;Sp=1;function e(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}return Vo=e,Vo}var Xo,Pp;function $a(){if(Pp)return Xo;Pp=1;var e=PO();function t(r,n){var i=r.__data__;return e(n)?i[typeof n=="string"?"string":"hash"]:i.map}return Xo=t,Xo}var Yo,Tp;function TO(){if(Tp)return Yo;Tp=1;var e=$a();function t(r){var n=e(this,r).delete(r);return this.size-=n?1:0,n}return Yo=t,Yo}var Zo,Ep;function EO(){if(Ep)return Zo;Ep=1;var e=$a();function t(r){return e(this,r).get(r)}return Zo=t,Zo}var Jo,jp;function jO(){if(jp)return Jo;jp=1;var e=$a();function t(r){return e(this,r).has(r)}return Jo=t,Jo}var Qo,Mp;function MO(){if(Mp)return Qo;Mp=1;var e=$a();function t(r,n){var i=e(this,r),a=i.size;return i.set(r,n),this.size+=i.size==a?0:1,this}return Qo=t,Qo}var eu,$p;function Bf(){if($p)return eu;$p=1;var e=SO(),t=TO(),r=EO(),n=jO(),i=MO();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,eu=a,eu}var tu,Cp;function Ub(){if(Cp)return tu;Cp=1;var e=Bf(),t="Expected a function";function r(n,i){if(typeof n!="function"||i!=null&&typeof i!="function")throw new TypeError(t);var a=function(){var o=arguments,u=i?i.apply(this,o):o[0],c=a.cache;if(c.has(u))return c.get(u);var s=n.apply(this,o);return a.cache=c.set(u,s)||c,s};return a.cache=new(r.Cache||e),a}return r.Cache=e,tu=r,tu}var ru,Ip;function $O(){if(Ip)return ru;Ip=1;var e=Ub(),t=500;function r(n){var i=e(n,function(o){return a.size===t&&a.clear(),o}),a=i.cache;return i}return ru=r,ru}var nu,kp;function CO(){if(kp)return nu;kp=1;var e=$O(),t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,n=e(function(i){var a=[];return i.charCodeAt(0)===46&&a.push(""),i.replace(t,function(o,u,c,s){a.push(c?s.replace(r,"$1"):u||o)}),a});return nu=n,nu}var iu,Rp;function Ff(){if(Rp)return iu;Rp=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=Array(i);++n<i;)a[n]=r(t[n],n,t);return a}return iu=e,iu}var au,Dp;function IO(){if(Dp)return au;Dp=1;var e=ii(),t=Ff(),r=qe(),n=Xr(),i=e?e.prototype:void 0,a=i?i.toString:void 0;function o(u){if(typeof u=="string")return u;if(r(u))return t(u,o)+"";if(n(u))return a?a.call(u):"";var c=u+"";return c=="0"&&1/u==-1/0?"-0":c}return au=o,au}var ou,Np;function Hb(){if(Np)return ou;Np=1;var e=IO();function t(r){return r==null?"":e(r)}return ou=t,ou}var uu,qp;function Kb(){if(qp)return uu;qp=1;var e=qe(),t=Df(),r=CO(),n=Hb();function i(a,o){return e(a)?a:t(a,o)?[a]:r(n(a))}return uu=i,uu}var cu,Lp;function Ca(){if(Lp)return cu;Lp=1;var e=Xr();function t(r){if(typeof r=="string"||e(r))return r;var n=r+"";return n=="0"&&1/r==-1/0?"-0":n}return cu=t,cu}var su,Bp;function Wf(){if(Bp)return su;Bp=1;var e=Kb(),t=Ca();function r(n,i){i=e(i,n);for(var a=0,o=i.length;n!=null&&a<o;)n=n[t(i[a++])];return a&&a==o?n:void 0}return su=r,su}var lu,Fp;function Gb(){if(Fp)return lu;Fp=1;var e=Wf();function t(r,n,i){var a=r==null?void 0:e(r,n);return a===void 0?i:a}return lu=t,lu}var kO=Gb();const He=oe(kO);var fu,Wp;function RO(){if(Wp)return fu;Wp=1;function e(t){return t==null}return fu=e,fu}var DO=RO();const Y=oe(DO);var hu,zp;function NO(){if(zp)return hu;zp=1;var e=St(),t=qe(),r=Pt(),n="[object String]";function i(a){return typeof a=="string"||!t(a)&&r(a)&&e(a)==n}return hu=i,hu}var qO=NO();const Qt=oe(qO);var LO=Nf();const X=oe(LO);var BO=It();const Yr=oe(BO);var pu={exports:{}},ne={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Up;function FO(){if(Up)return ne;Up=1;var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),o=Symbol.for("react.context"),u=Symbol.for("react.server_context"),c=Symbol.for("react.forward_ref"),s=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),l=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),p=Symbol.for("react.offscreen"),y;y=Symbol.for("react.module.reference");function v(d){if(typeof d=="object"&&d!==null){var g=d.$$typeof;switch(g){case e:switch(d=d.type,d){case r:case i:case n:case s:case f:return d;default:switch(d=d&&d.$$typeof,d){case u:case o:case c:case h:case l:case a:return d;default:return g}}case t:return g}}}return ne.ContextConsumer=o,ne.ContextProvider=a,ne.Element=e,ne.ForwardRef=c,ne.Fragment=r,ne.Lazy=h,ne.Memo=l,ne.Portal=t,ne.Profiler=i,ne.StrictMode=n,ne.Suspense=s,ne.SuspenseList=f,ne.isAsyncMode=function(){return!1},ne.isConcurrentMode=function(){return!1},ne.isContextConsumer=function(d){return v(d)===o},ne.isContextProvider=function(d){return v(d)===a},ne.isElement=function(d){return typeof d=="object"&&d!==null&&d.$$typeof===e},ne.isForwardRef=function(d){return v(d)===c},ne.isFragment=function(d){return v(d)===r},ne.isLazy=function(d){return v(d)===h},ne.isMemo=function(d){return v(d)===l},ne.isPortal=function(d){return v(d)===t},ne.isProfiler=function(d){return v(d)===i},ne.isStrictMode=function(d){return v(d)===n},ne.isSuspense=function(d){return v(d)===s},ne.isSuspenseList=function(d){return v(d)===f},ne.isValidElementType=function(d){return typeof d=="string"||typeof d=="function"||d===r||d===i||d===n||d===s||d===f||d===p||typeof d=="object"&&d!==null&&(d.$$typeof===h||d.$$typeof===l||d.$$typeof===a||d.$$typeof===o||d.$$typeof===c||d.$$typeof===y||d.getModuleId!==void 0)},ne.typeOf=v,ne}var Hp;function WO(){return Hp||(Hp=1,pu.exports=FO()),pu.exports}var zO=WO(),du,Kp;function Vb(){if(Kp)return du;Kp=1;var e=St(),t=Pt(),r="[object Number]";function n(i){return typeof i=="number"||t(i)&&e(i)==r}return du=n,du}var vu,Gp;function UO(){if(Gp)return vu;Gp=1;var e=Vb();function t(r){return e(r)&&r!=+r}return vu=t,vu}var HO=UO();const ai=oe(HO);var KO=Vb();const GO=oe(KO);var Ce=function(t){return t===0?0:t>0?1:-1},Gt=function(t){return Qt(t)&&t.indexOf("%")===t.length-1},N=function(t){return GO(t)&&!ai(t)},_e=function(t){return N(t)||Qt(t)},VO=0,Zr=function(t){var r=++VO;return"".concat(t||"").concat(r)},Ie=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!N(t)&&!Qt(t))return n;var a;if(Gt(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return ai(a)&&(a=n),i&&a>r&&(a=r),a},Mt=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},XO=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},ze=function(t,r){return N(t)&&N(r)?function(n){return t+n*(r-t)}:function(){return r}};function Mi(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):He(n,t))===r})}var YO=function(t,r){return N(t)&&N(r)?t-r:Qt(t)&&Qt(r)?t.localeCompare(r):t instanceof Date&&r instanceof Date?t.getTime()-r.getTime():String(t).localeCompare(String(r))};function xr(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function dl(e){"@babel/helpers - typeof";return dl=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dl(e)}var ZO=["viewBox","children"],JO=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Vp=["points","pathLength"],yu={svg:ZO,polygon:Vp,polyline:Vp},zf=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],$i=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(q.isValidElement(t)&&(n=t.props),!Yr(n))return null;var i={};return Object.keys(n).forEach(function(a){zf.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},QO=function(t,r,n){return function(i){return t(r,n,i),null}},er=function(t,r,n){if(!Yr(t)||dl(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];zf.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=QO(o,r,n))}),i},e_=["children"],t_=["children"];function Xp(e,t){if(e==null)return{};var r=r_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function r_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function vl(e){"@babel/helpers - typeof";return vl=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vl(e)}var Yp={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},bt=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},Zp=null,mu=null,Uf=function e(t){if(t===Zp&&Array.isArray(mu))return mu;var r=[];return q.Children.forEach(t,function(n){Y(n)||(zO.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),mu=r,Zp=t,r};function Ke(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return bt(i)}):n=[bt(t)],Uf(e).forEach(function(i){var a=He(i,"type.displayName")||He(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function We(e,t){var r=Ke(e,t);return r&&r[0]}var Jp=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!N(n)||n<=0||!N(i)||i<=0)},n_=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],i_=function(t){return t&&t.type&&Qt(t.type)&&n_.indexOf(t.type)>=0},a_=function(t){return t&&vl(t)==="object"&&"clipDot"in t},o_=function(t,r,n,i){var a,o=(a=yu==null?void 0:yu[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!X(t)&&(i&&o.includes(r)||JO.includes(r))||n&&zf.includes(r)},H=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(q.isValidElement(t)&&(i=t.props),!Yr(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;o_((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},yl=function e(t,r){if(t===r)return!0;var n=q.Children.count(t);if(n!==q.Children.count(r))return!1;if(n===0)return!0;if(n===1)return Qp(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!Qp(a,o))return!1}return!0},Qp=function(t,r){if(Y(t)&&Y(r))return!0;if(!Y(t)&&!Y(r)){var n=t.props||{},i=n.children,a=Xp(n,e_),o=r.props||{},u=o.children,c=Xp(o,t_);return i&&u?xr(a,c)&&yl(i,u):!i&&!u?xr(a,c):!1}return!1},ed=function(t,r){var n=[],i={};return Uf(t).forEach(function(a,o){if(i_(a))n.push(a);else if(a){var u=bt(a.type),c=r[u]||{},s=c.handler,f=c.once;if(s&&(!f||!i[u])){var l=s(a,u,o);n.push(l),i[u]=!0}}}),n},u_=function(t){var r=t&&t.type;return r&&Yp[r]?Yp[r]:null},c_=function(t,r){return Uf(r).indexOf(t)},s_=["children","width","height","viewBox","className","style","title","desc"];function ml(){return ml=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ml.apply(this,arguments)}function l_(e,t){if(e==null)return{};var r=f_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function f_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function gl(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,s=l_(e,s_),f=i||{width:r,height:n,x:0,y:0},l=J("recharts-surface",a);return S.createElement("svg",ml({},H(s,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),S.createElement("title",null,u),S.createElement("desc",null,c),t)}var h_=["children","className"];function bl(){return bl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bl.apply(this,arguments)}function p_(e,t){if(e==null)return{};var r=d_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function d_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var te=S.forwardRef(function(e,t){var r=e.children,n=e.className,i=p_(e,h_),a=J("recharts-layer",n);return S.createElement("g",bl({className:a},H(i,!0),{ref:t}),r)}),it=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]},gu,td;function v_(){if(td)return gu;td=1;function e(t,r,n){var i=-1,a=t.length;r<0&&(r=-r>a?0:a+r),n=n>a?a:n,n<0&&(n+=a),a=r>n?0:n-r>>>0,r>>>=0;for(var o=Array(a);++i<a;)o[i]=t[i+r];return o}return gu=e,gu}var bu,rd;function y_(){if(rd)return bu;rd=1;var e=v_();function t(r,n,i){var a=r.length;return i=i===void 0?a:i,!n&&i>=a?r:e(r,n,i)}return bu=t,bu}var xu,nd;function Xb(){if(nd)return xu;nd=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="\\u200d",u=RegExp("["+o+e+i+a+"]");function c(s){return u.test(s)}return xu=c,xu}var wu,id;function m_(){if(id)return wu;id=1;function e(t){return t.split("")}return wu=e,wu}var Ou,ad;function g_(){if(ad)return Ou;ad=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="["+e+"]",u="["+i+"]",c="\\ud83c[\\udffb-\\udfff]",s="(?:"+u+"|"+c+")",f="[^"+e+"]",l="(?:\\ud83c[\\udde6-\\uddff]){2}",h="[\\ud800-\\udbff][\\udc00-\\udfff]",p="\\u200d",y=s+"?",v="["+a+"]?",d="(?:"+p+"(?:"+[f,l,h].join("|")+")"+v+y+")*",g=v+y+d,x="(?:"+[f+u+"?",u,l,h,o].join("|")+")",w=RegExp(c+"(?="+c+")|"+x+g,"g");function O(m){return m.match(w)||[]}return Ou=O,Ou}var _u,od;function b_(){if(od)return _u;od=1;var e=m_(),t=Xb(),r=g_();function n(i){return t(i)?r(i):e(i)}return _u=n,_u}var Au,ud;function x_(){if(ud)return Au;ud=1;var e=y_(),t=Xb(),r=b_(),n=Hb();function i(a){return function(o){o=n(o);var u=t(o)?r(o):void 0,c=u?u[0]:o.charAt(0),s=u?e(u,1).join(""):o.slice(1);return c[a]()+s}}return Au=i,Au}var Su,cd;function w_(){if(cd)return Su;cd=1;var e=x_(),t=e("toUpperCase");return Su=t,Su}var O_=w_();const Ia=oe(O_);function se(e){return function(){return e}}const Yb=Math.cos,Ci=Math.sin,at=Math.sqrt,Ii=Math.PI,ka=2*Ii,xl=Math.PI,wl=2*xl,Ut=1e-6,__=wl-Ut;function Zb(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function A_(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Zb;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class S_{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Zb:A_(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,s=i-r,f=o-t,l=u-r,h=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(h>Ut)if(!(Math.abs(l*c-s*f)>Ut)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let p=n-o,y=i-u,v=c*c+s*s,d=p*p+y*y,g=Math.sqrt(v),x=Math.sqrt(h),w=a*Math.tan((xl-Math.acos((v+h-d)/(2*g*x)))/2),O=w/x,m=w/g;Math.abs(O-1)>Ut&&this._append`L${t+O*f},${r+O*l}`,this._append`A${a},${a},0,0,${+(l*p>f*y)},${this._x1=t+m*c},${this._y1=r+m*s}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),s=t+u,f=r+c,l=1^o,h=o?i-a:a-i;this._x1===null?this._append`M${s},${f}`:(Math.abs(this._x1-s)>Ut||Math.abs(this._y1-f)>Ut)&&this._append`L${s},${f}`,n&&(h<0&&(h=h%wl+wl),h>__?this._append`A${n},${n},0,1,${l},${t-u},${r-c}A${n},${n},0,1,${l},${this._x1=s},${this._y1=f}`:h>Ut&&this._append`A${n},${n},0,${+(h>=xl)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function Hf(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new S_(t)}function Kf(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Jb(e){this._context=e}Jb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function Ra(e){return new Jb(e)}function Qb(e){return e[0]}function e0(e){return e[1]}function t0(e,t){var r=se(!0),n=null,i=Ra,a=null,o=Hf(u);e=typeof e=="function"?e:e===void 0?Qb:se(e),t=typeof t=="function"?t:t===void 0?e0:se(t);function u(c){var s,f=(c=Kf(c)).length,l,h=!1,p;for(n==null&&(a=i(p=o())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+e(l,s,c),+t(l,s,c));if(p)return a=null,p+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:se(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:se(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:se(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function yi(e,t,r){var n=null,i=se(!0),a=null,o=Ra,u=null,c=Hf(s);e=typeof e=="function"?e:e===void 0?Qb:se(+e),t=typeof t=="function"?t:se(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?e0:se(+r);function s(l){var h,p,y,v=(l=Kf(l)).length,d,g=!1,x,w=new Array(v),O=new Array(v);for(a==null&&(u=o(x=c())),h=0;h<=v;++h){if(!(h<v&&i(d=l[h],h,l))===g)if(g=!g)p=h,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),y=h-1;y>=p;--y)u.point(w[y],O[y]);u.lineEnd(),u.areaEnd()}g&&(w[h]=+e(d,h,l),O[h]=+t(d,h,l),u.point(n?+n(d,h,l):w[h],r?+r(d,h,l):O[h]))}if(x)return u=null,x+""||null}function f(){return t0().defined(i).curve(o).context(a)}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:se(+l),n=null,s):e},s.x0=function(l){return arguments.length?(e=typeof l=="function"?l:se(+l),s):e},s.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:se(+l),s):n},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:se(+l),r=null,s):t},s.y0=function(l){return arguments.length?(t=typeof l=="function"?l:se(+l),s):t},s.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:se(+l),s):r},s.lineX0=s.lineY0=function(){return f().x(e).y(t)},s.lineY1=function(){return f().x(e).y(r)},s.lineX1=function(){return f().x(n).y(t)},s.defined=function(l){return arguments.length?(i=typeof l=="function"?l:se(!!l),s):i},s.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),s):o},s.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),s):a},s}class r0{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function P_(e){return new r0(e,!0)}function T_(e){return new r0(e,!1)}const Gf={draw(e,t){const r=at(t/Ii);e.moveTo(r,0),e.arc(0,0,r,0,ka)}},E_={draw(e,t){const r=at(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},n0=at(1/3),j_=n0*2,M_={draw(e,t){const r=at(t/j_),n=r*n0;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},$_={draw(e,t){const r=at(t),n=-r/2;e.rect(n,n,r,r)}},C_=.8908130915292852,i0=Ci(Ii/10)/Ci(7*Ii/10),I_=Ci(ka/10)*i0,k_=-Yb(ka/10)*i0,R_={draw(e,t){const r=at(t*C_),n=I_*r,i=k_*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=ka*a/5,u=Yb(o),c=Ci(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},Pu=at(3),D_={draw(e,t){const r=-at(t/(Pu*3));e.moveTo(0,r*2),e.lineTo(-Pu*r,-r),e.lineTo(Pu*r,-r),e.closePath()}},Ge=-.5,Ve=at(3)/2,Ol=1/at(12),N_=(Ol/2+1)*3,q_={draw(e,t){const r=at(t/N_),n=r/2,i=r*Ol,a=n,o=r*Ol+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(Ge*n-Ve*i,Ve*n+Ge*i),e.lineTo(Ge*a-Ve*o,Ve*a+Ge*o),e.lineTo(Ge*u-Ve*c,Ve*u+Ge*c),e.lineTo(Ge*n+Ve*i,Ge*i-Ve*n),e.lineTo(Ge*a+Ve*o,Ge*o-Ve*a),e.lineTo(Ge*u+Ve*c,Ge*c-Ve*u),e.closePath()}};function L_(e,t){let r=null,n=Hf(i);e=typeof e=="function"?e:se(e||Gf),t=typeof t=="function"?t:se(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:se(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:se(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function ki(){}function Ri(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function a0(e){this._context=e}a0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Ri(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Ri(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function B_(e){return new a0(e)}function o0(e){this._context=e}o0.prototype={areaStart:ki,areaEnd:ki,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Ri(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function F_(e){return new o0(e)}function u0(e){this._context=e}u0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Ri(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function W_(e){return new u0(e)}function c0(e){this._context=e}c0.prototype={areaStart:ki,areaEnd:ki,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function z_(e){return new c0(e)}function sd(e){return e<0?-1:1}function ld(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(sd(a)+sd(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function fd(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Tu(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function Di(e){this._context=e}Di.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Tu(this,this._t0,fd(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Tu(this,fd(this,r=ld(this,e,t)),r);break;default:Tu(this,this._t0,r=ld(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function s0(e){this._context=new l0(e)}(s0.prototype=Object.create(Di.prototype)).point=function(e,t){Di.prototype.point.call(this,t,e)};function l0(e){this._context=e}l0.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function U_(e){return new Di(e)}function H_(e){return new s0(e)}function f0(e){this._context=e}f0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=hd(e),i=hd(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function hd(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function K_(e){return new f0(e)}function Da(e,t){this._context=e,this._t=t}Da.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function G_(e){return new Da(e,.5)}function V_(e){return new Da(e,0)}function X_(e){return new Da(e,1)}function Ar(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function _l(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function Y_(e,t){return e[t]}function Z_(e){const t=[];return t.key=e,t}function J_(){var e=se([]),t=_l,r=Ar,n=Y_;function i(a){var o=Array.from(e.apply(this,arguments),Z_),u,c=o.length,s=-1,f;for(const l of a)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(l,o[u].key,s,a)]).data=l;for(u=0,f=Kf(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:se(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:se(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?_l:typeof a=="function"?a:se(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??Ar,i):r},i}function Q_(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}Ar(e,t)}}function e1(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}Ar(e,t)}}function t1(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,s=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,h=f[n-1][1]||0,p=(l-h)/2,y=0;y<u;++y){var v=e[t[y]],d=v[n][1]||0,g=v[n-1][1]||0;p+=d-g}c+=l,s+=p*l}i[n-1][1]+=i[n-1][0]=r,c&&(r-=s/c)}i[n-1][1]+=i[n-1][0]=r,Ar(e,t)}}function _n(e){"@babel/helpers - typeof";return _n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_n(e)}var r1=["type","size","sizeType"];function Al(){return Al=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Al.apply(this,arguments)}function pd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function dd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?pd(Object(r),!0).forEach(function(n){n1(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function n1(e,t,r){return t=i1(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i1(e){var t=a1(e,"string");return _n(t)=="symbol"?t:t+""}function a1(e,t){if(_n(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(_n(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function o1(e,t){if(e==null)return{};var r=u1(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function u1(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var h0={symbolCircle:Gf,symbolCross:E_,symbolDiamond:M_,symbolSquare:$_,symbolStar:R_,symbolTriangle:D_,symbolWye:q_},c1=Math.PI/180,s1=function(t){var r="symbol".concat(Ia(t));return h0[r]||Gf},l1=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*c1;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},f1=function(t,r){h0["symbol".concat(Ia(t))]=r},Vf=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=o1(t,r1),s=dd(dd({},c),{},{type:n,size:a,sizeType:u}),f=function(){var d=s1(n),g=L_().type(d).size(l1(a,u,n));return g()},l=s.className,h=s.cx,p=s.cy,y=H(s,!0);return h===+h&&p===+p&&a===+a?S.createElement("path",Al({},y,{className:J("recharts-symbols",l),transform:"translate(".concat(h,", ").concat(p,")"),d:f()})):null};Vf.registerSymbol=f1;function Sr(e){"@babel/helpers - typeof";return Sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sr(e)}function Sl(){return Sl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sl.apply(this,arguments)}function vd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function h1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vd(Object(r),!0).forEach(function(n){An(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function p1(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d1(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,d0(n.key),n)}}function v1(e,t,r){return t&&d1(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function y1(e,t,r){return t=Ni(t),m1(e,p0()?Reflect.construct(t,r||[],Ni(e).constructor):t.apply(e,r))}function m1(e,t){if(t&&(Sr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return g1(e)}function g1(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(p0=function(){return!!e})()}function Ni(e){return Ni=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ni(e)}function b1(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pl(e,t)}function Pl(e,t){return Pl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Pl(e,t)}function An(e,t,r){return t=d0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d0(e){var t=x1(e,"string");return Sr(t)=="symbol"?t:t+""}function x1(e,t){if(Sr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Sr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Qe=32,Xf=function(e){function t(){return p1(this,t),y1(this,t,arguments)}return b1(t,e),v1(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=Qe/2,o=Qe/6,u=Qe/3,c=n.inactive?i:n.color;if(n.type==="plainline")return S.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:Qe,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return S.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(Qe,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return S.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(Qe/8,"h").concat(Qe,"v").concat(Qe*3/4,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(S.isValidElement(n.legendIcon)){var s=h1({},n);return delete s.legendIcon,S.cloneElement(n.legendIcon,s)}return S.createElement(Vf,{fill:c,cx:a,cy:a,size:Qe,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,s=i.inactiveColor,f={x:0,y:0,width:Qe,height:Qe},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},h={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(p,y){var v=p.formatter||c,d=J(An(An({"recharts-legend-item":!0},"legend-item-".concat(y),!0),"inactive",p.inactive));if(p.type==="none")return null;var g=X(p.value)?null:p.value;it(!X(p.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var x=p.inactive?s:p.color;return S.createElement("li",Sl({className:d,style:l,key:"legend-item-".concat(y)},er(n.props,p,y)),S.createElement(gl,{width:o,height:o,viewBox:f,style:h},n.renderIcon(p)),S.createElement("span",{className:"recharts-legend-item-text",style:{color:x}},v?v(g,p,y):g))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return S.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(q.PureComponent);An(Xf,"displayName","Legend");An(Xf,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var Eu,yd;function w1(){if(yd)return Eu;yd=1;var e=Ma();function t(){this.__data__=new e,this.size=0}return Eu=t,Eu}var ju,md;function O1(){if(md)return ju;md=1;function e(t){var r=this.__data__,n=r.delete(t);return this.size=r.size,n}return ju=e,ju}var Mu,gd;function _1(){if(gd)return Mu;gd=1;function e(t){return this.__data__.get(t)}return Mu=e,Mu}var $u,bd;function A1(){if(bd)return $u;bd=1;function e(t){return this.__data__.has(t)}return $u=e,$u}var Cu,xd;function S1(){if(xd)return Cu;xd=1;var e=Ma(),t=Lf(),r=Bf(),n=200;function i(a,o){var u=this.__data__;if(u instanceof e){var c=u.__data__;if(!t||c.length<n-1)return c.push([a,o]),this.size=++u.size,this;u=this.__data__=new r(c)}return u.set(a,o),this.size=u.size,this}return Cu=i,Cu}var Iu,wd;function v0(){if(wd)return Iu;wd=1;var e=Ma(),t=w1(),r=O1(),n=_1(),i=A1(),a=S1();function o(u){var c=this.__data__=new e(u);this.size=c.size}return o.prototype.clear=t,o.prototype.delete=r,o.prototype.get=n,o.prototype.has=i,o.prototype.set=a,Iu=o,Iu}var ku,Od;function P1(){if(Od)return ku;Od=1;var e="__lodash_hash_undefined__";function t(r){return this.__data__.set(r,e),this}return ku=t,ku}var Ru,_d;function T1(){if(_d)return Ru;_d=1;function e(t){return this.__data__.has(t)}return Ru=e,Ru}var Du,Ad;function y0(){if(Ad)return Du;Ad=1;var e=Bf(),t=P1(),r=T1();function n(i){var a=-1,o=i==null?0:i.length;for(this.__data__=new e;++a<o;)this.add(i[a])}return n.prototype.add=n.prototype.push=t,n.prototype.has=r,Du=n,Du}var Nu,Sd;function m0(){if(Sd)return Nu;Sd=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(r(t[n],n,t))return!0;return!1}return Nu=e,Nu}var qu,Pd;function g0(){if(Pd)return qu;Pd=1;function e(t,r){return t.has(r)}return qu=e,qu}var Lu,Td;function b0(){if(Td)return Lu;Td=1;var e=y0(),t=m0(),r=g0(),n=1,i=2;function a(o,u,c,s,f,l){var h=c&n,p=o.length,y=u.length;if(p!=y&&!(h&&y>p))return!1;var v=l.get(o),d=l.get(u);if(v&&d)return v==u&&d==o;var g=-1,x=!0,w=c&i?new e:void 0;for(l.set(o,u),l.set(u,o);++g<p;){var O=o[g],m=u[g];if(s)var b=h?s(m,O,g,u,o,l):s(O,m,g,o,u,l);if(b!==void 0){if(b)continue;x=!1;break}if(w){if(!t(u,function(_,A){if(!r(w,A)&&(O===_||f(O,_,c,s,l)))return w.push(A)})){x=!1;break}}else if(!(O===m||f(O,m,c,s,l))){x=!1;break}}return l.delete(o),l.delete(u),x}return Lu=a,Lu}var Bu,Ed;function E1(){if(Ed)return Bu;Ed=1;var e=ft(),t=e.Uint8Array;return Bu=t,Bu}var Fu,jd;function j1(){if(jd)return Fu;jd=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i,a){n[++r]=[a,i]}),n}return Fu=e,Fu}var Wu,Md;function Yf(){if(Md)return Wu;Md=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i){n[++r]=i}),n}return Wu=e,Wu}var zu,$d;function M1(){if($d)return zu;$d=1;var e=ii(),t=E1(),r=qf(),n=b0(),i=j1(),a=Yf(),o=1,u=2,c="[object Boolean]",s="[object Date]",f="[object Error]",l="[object Map]",h="[object Number]",p="[object RegExp]",y="[object Set]",v="[object String]",d="[object Symbol]",g="[object ArrayBuffer]",x="[object DataView]",w=e?e.prototype:void 0,O=w?w.valueOf:void 0;function m(b,_,A,T,M,P,E){switch(A){case x:if(b.byteLength!=_.byteLength||b.byteOffset!=_.byteOffset)return!1;b=b.buffer,_=_.buffer;case g:return!(b.byteLength!=_.byteLength||!P(new t(b),new t(_)));case c:case s:case h:return r(+b,+_);case f:return b.name==_.name&&b.message==_.message;case p:case v:return b==_+"";case l:var j=i;case y:var C=T&o;if(j||(j=a),b.size!=_.size&&!C)return!1;var $=E.get(b);if($)return $==_;T|=u,E.set(b,_);var k=n(j(b),j(_),T,M,P,E);return E.delete(b),k;case d:if(O)return O.call(b)==O.call(_)}return!1}return zu=m,zu}var Uu,Cd;function x0(){if(Cd)return Uu;Cd=1;function e(t,r){for(var n=-1,i=r.length,a=t.length;++n<i;)t[a+n]=r[n];return t}return Uu=e,Uu}var Hu,Id;function $1(){if(Id)return Hu;Id=1;var e=x0(),t=qe();function r(n,i,a){var o=i(n);return t(n)?o:e(o,a(n))}return Hu=r,Hu}var Ku,kd;function C1(){if(kd)return Ku;kd=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=0,o=[];++n<i;){var u=t[n];r(u,n,t)&&(o[a++]=u)}return o}return Ku=e,Ku}var Gu,Rd;function I1(){if(Rd)return Gu;Rd=1;function e(){return[]}return Gu=e,Gu}var Vu,Dd;function k1(){if(Dd)return Vu;Dd=1;var e=C1(),t=I1(),r=Object.prototype,n=r.propertyIsEnumerable,i=Object.getOwnPropertySymbols,a=i?function(o){return o==null?[]:(o=Object(o),e(i(o),function(u){return n.call(o,u)}))}:t;return Vu=a,Vu}var Xu,Nd;function R1(){if(Nd)return Xu;Nd=1;function e(t,r){for(var n=-1,i=Array(t);++n<t;)i[n]=r(n);return i}return Xu=e,Xu}var Yu,qd;function D1(){if(qd)return Yu;qd=1;var e=St(),t=Pt(),r="[object Arguments]";function n(i){return t(i)&&e(i)==r}return Yu=n,Yu}var Zu,Ld;function Zf(){if(Ld)return Zu;Ld=1;var e=D1(),t=Pt(),r=Object.prototype,n=r.hasOwnProperty,i=r.propertyIsEnumerable,a=e(function(){return arguments}())?e:function(o){return t(o)&&n.call(o,"callee")&&!i.call(o,"callee")};return Zu=a,Zu}var pn={exports:{}},Ju,Bd;function N1(){if(Bd)return Ju;Bd=1;function e(){return!1}return Ju=e,Ju}pn.exports;var Fd;function w0(){return Fd||(Fd=1,function(e,t){var r=ft(),n=N1(),i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,s=c||n;e.exports=s}(pn,pn.exports)),pn.exports}var Qu,Wd;function Jf(){if(Wd)return Qu;Wd=1;var e=9007199254740991,t=/^(?:0|[1-9]\d*)$/;function r(n,i){var a=typeof n;return i=i??e,!!i&&(a=="number"||a!="symbol"&&t.test(n))&&n>-1&&n%1==0&&n<i}return Qu=r,Qu}var ec,zd;function Qf(){if(zd)return ec;zd=1;var e=9007199254740991;function t(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=e}return ec=t,ec}var tc,Ud;function q1(){if(Ud)return tc;Ud=1;var e=St(),t=Qf(),r=Pt(),n="[object Arguments]",i="[object Array]",a="[object Boolean]",o="[object Date]",u="[object Error]",c="[object Function]",s="[object Map]",f="[object Number]",l="[object Object]",h="[object RegExp]",p="[object Set]",y="[object String]",v="[object WeakMap]",d="[object ArrayBuffer]",g="[object DataView]",x="[object Float32Array]",w="[object Float64Array]",O="[object Int8Array]",m="[object Int16Array]",b="[object Int32Array]",_="[object Uint8Array]",A="[object Uint8ClampedArray]",T="[object Uint16Array]",M="[object Uint32Array]",P={};P[x]=P[w]=P[O]=P[m]=P[b]=P[_]=P[A]=P[T]=P[M]=!0,P[n]=P[i]=P[d]=P[a]=P[g]=P[o]=P[u]=P[c]=P[s]=P[f]=P[l]=P[h]=P[p]=P[y]=P[v]=!1;function E(j){return r(j)&&t(j.length)&&!!P[e(j)]}return tc=E,tc}var rc,Hd;function O0(){if(Hd)return rc;Hd=1;function e(t){return function(r){return t(r)}}return rc=e,rc}var dn={exports:{}};dn.exports;var Kd;function L1(){return Kd||(Kd=1,function(e,t){var r=Wb(),n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch{}}();e.exports=u}(dn,dn.exports)),dn.exports}var nc,Gd;function _0(){if(Gd)return nc;Gd=1;var e=q1(),t=O0(),r=L1(),n=r&&r.isTypedArray,i=n?t(n):e;return nc=i,nc}var ic,Vd;function B1(){if(Vd)return ic;Vd=1;var e=R1(),t=Zf(),r=qe(),n=w0(),i=Jf(),a=_0(),o=Object.prototype,u=o.hasOwnProperty;function c(s,f){var l=r(s),h=!l&&t(s),p=!l&&!h&&n(s),y=!l&&!h&&!p&&a(s),v=l||h||p||y,d=v?e(s.length,String):[],g=d.length;for(var x in s)(f||u.call(s,x))&&!(v&&(x=="length"||p&&(x=="offset"||x=="parent")||y&&(x=="buffer"||x=="byteLength"||x=="byteOffset")||i(x,g)))&&d.push(x);return d}return ic=c,ic}var ac,Xd;function F1(){if(Xd)return ac;Xd=1;var e=Object.prototype;function t(r){var n=r&&r.constructor,i=typeof n=="function"&&n.prototype||e;return r===i}return ac=t,ac}var oc,Yd;function A0(){if(Yd)return oc;Yd=1;function e(t,r){return function(n){return t(r(n))}}return oc=e,oc}var uc,Zd;function W1(){if(Zd)return uc;Zd=1;var e=A0(),t=e(Object.keys,Object);return uc=t,uc}var cc,Jd;function z1(){if(Jd)return cc;Jd=1;var e=F1(),t=W1(),r=Object.prototype,n=r.hasOwnProperty;function i(a){if(!e(a))return t(a);var o=[];for(var u in Object(a))n.call(a,u)&&u!="constructor"&&o.push(u);return o}return cc=i,cc}var sc,Qd;function oi(){if(Qd)return sc;Qd=1;var e=Nf(),t=Qf();function r(n){return n!=null&&t(n.length)&&!e(n)}return sc=r,sc}var lc,ev;function Na(){if(ev)return lc;ev=1;var e=B1(),t=z1(),r=oi();function n(i){return r(i)?e(i):t(i)}return lc=n,lc}var fc,tv;function U1(){if(tv)return fc;tv=1;var e=$1(),t=k1(),r=Na();function n(i){return e(i,r,t)}return fc=n,fc}var hc,rv;function H1(){if(rv)return hc;rv=1;var e=U1(),t=1,r=Object.prototype,n=r.hasOwnProperty;function i(a,o,u,c,s,f){var l=u&t,h=e(a),p=h.length,y=e(o),v=y.length;if(p!=v&&!l)return!1;for(var d=p;d--;){var g=h[d];if(!(l?g in o:n.call(o,g)))return!1}var x=f.get(a),w=f.get(o);if(x&&w)return x==o&&w==a;var O=!0;f.set(a,o),f.set(o,a);for(var m=l;++d<p;){g=h[d];var b=a[g],_=o[g];if(c)var A=l?c(_,b,g,o,a,f):c(b,_,g,a,o,f);if(!(A===void 0?b===_||s(b,_,u,c,f):A)){O=!1;break}m||(m=g=="constructor")}if(O&&!m){var T=a.constructor,M=o.constructor;T!=M&&"constructor"in a&&"constructor"in o&&!(typeof T=="function"&&T instanceof T&&typeof M=="function"&&M instanceof M)&&(O=!1)}return f.delete(a),f.delete(o),O}return hc=i,hc}var pc,nv;function K1(){if(nv)return pc;nv=1;var e=ar(),t=ft(),r=e(t,"DataView");return pc=r,pc}var dc,iv;function G1(){if(iv)return dc;iv=1;var e=ar(),t=ft(),r=e(t,"Promise");return dc=r,dc}var vc,av;function S0(){if(av)return vc;av=1;var e=ar(),t=ft(),r=e(t,"Set");return vc=r,vc}var yc,ov;function V1(){if(ov)return yc;ov=1;var e=ar(),t=ft(),r=e(t,"WeakMap");return yc=r,yc}var mc,uv;function X1(){if(uv)return mc;uv=1;var e=K1(),t=Lf(),r=G1(),n=S0(),i=V1(),a=St(),o=zb(),u="[object Map]",c="[object Object]",s="[object Promise]",f="[object Set]",l="[object WeakMap]",h="[object DataView]",p=o(e),y=o(t),v=o(r),d=o(n),g=o(i),x=a;return(e&&x(new e(new ArrayBuffer(1)))!=h||t&&x(new t)!=u||r&&x(r.resolve())!=s||n&&x(new n)!=f||i&&x(new i)!=l)&&(x=function(w){var O=a(w),m=O==c?w.constructor:void 0,b=m?o(m):"";if(b)switch(b){case p:return h;case y:return u;case v:return s;case d:return f;case g:return l}return O}),mc=x,mc}var gc,cv;function Y1(){if(cv)return gc;cv=1;var e=v0(),t=b0(),r=M1(),n=H1(),i=X1(),a=qe(),o=w0(),u=_0(),c=1,s="[object Arguments]",f="[object Array]",l="[object Object]",h=Object.prototype,p=h.hasOwnProperty;function y(v,d,g,x,w,O){var m=a(v),b=a(d),_=m?f:i(v),A=b?f:i(d);_=_==s?l:_,A=A==s?l:A;var T=_==l,M=A==l,P=_==A;if(P&&o(v)){if(!o(d))return!1;m=!0,T=!1}if(P&&!T)return O||(O=new e),m||u(v)?t(v,d,g,x,w,O):r(v,d,_,g,x,w,O);if(!(g&c)){var E=T&&p.call(v,"__wrapped__"),j=M&&p.call(d,"__wrapped__");if(E||j){var C=E?v.value():v,$=j?d.value():d;return O||(O=new e),w(C,$,g,x,O)}}return P?(O||(O=new e),n(v,d,g,x,w,O)):!1}return gc=y,gc}var bc,sv;function eh(){if(sv)return bc;sv=1;var e=Y1(),t=Pt();function r(n,i,a,o,u){return n===i?!0:n==null||i==null||!t(n)&&!t(i)?n!==n&&i!==i:e(n,i,a,o,r,u)}return bc=r,bc}var xc,lv;function Z1(){if(lv)return xc;lv=1;var e=v0(),t=eh(),r=1,n=2;function i(a,o,u,c){var s=u.length,f=s,l=!c;if(a==null)return!f;for(a=Object(a);s--;){var h=u[s];if(l&&h[2]?h[1]!==a[h[0]]:!(h[0]in a))return!1}for(;++s<f;){h=u[s];var p=h[0],y=a[p],v=h[1];if(l&&h[2]){if(y===void 0&&!(p in a))return!1}else{var d=new e;if(c)var g=c(y,v,p,a,o,d);if(!(g===void 0?t(v,y,r|n,c,d):g))return!1}}return!0}return xc=i,xc}var wc,fv;function P0(){if(fv)return wc;fv=1;var e=It();function t(r){return r===r&&!e(r)}return wc=t,wc}var Oc,hv;function J1(){if(hv)return Oc;hv=1;var e=P0(),t=Na();function r(n){for(var i=t(n),a=i.length;a--;){var o=i[a],u=n[o];i[a]=[o,u,e(u)]}return i}return Oc=r,Oc}var _c,pv;function T0(){if(pv)return _c;pv=1;function e(t,r){return function(n){return n==null?!1:n[t]===r&&(r!==void 0||t in Object(n))}}return _c=e,_c}var Ac,dv;function Q1(){if(dv)return Ac;dv=1;var e=Z1(),t=J1(),r=T0();function n(i){var a=t(i);return a.length==1&&a[0][2]?r(a[0][0],a[0][1]):function(o){return o===i||e(o,i,a)}}return Ac=n,Ac}var Sc,vv;function eA(){if(vv)return Sc;vv=1;function e(t,r){return t!=null&&r in Object(t)}return Sc=e,Sc}var Pc,yv;function tA(){if(yv)return Pc;yv=1;var e=Kb(),t=Zf(),r=qe(),n=Jf(),i=Qf(),a=Ca();function o(u,c,s){c=e(c,u);for(var f=-1,l=c.length,h=!1;++f<l;){var p=a(c[f]);if(!(h=u!=null&&s(u,p)))break;u=u[p]}return h||++f!=l?h:(l=u==null?0:u.length,!!l&&i(l)&&n(p,l)&&(r(u)||t(u)))}return Pc=o,Pc}var Tc,mv;function rA(){if(mv)return Tc;mv=1;var e=eA(),t=tA();function r(n,i){return n!=null&&t(n,i,e)}return Tc=r,Tc}var Ec,gv;function nA(){if(gv)return Ec;gv=1;var e=eh(),t=Gb(),r=rA(),n=Df(),i=P0(),a=T0(),o=Ca(),u=1,c=2;function s(f,l){return n(f)&&i(l)?a(o(f),l):function(h){var p=t(h,f);return p===void 0&&p===l?r(h,f):e(l,p,u|c)}}return Ec=s,Ec}var jc,bv;function Jr(){if(bv)return jc;bv=1;function e(t){return t}return jc=e,jc}var Mc,xv;function iA(){if(xv)return Mc;xv=1;function e(t){return function(r){return r==null?void 0:r[t]}}return Mc=e,Mc}var $c,wv;function aA(){if(wv)return $c;wv=1;var e=Wf();function t(r){return function(n){return e(n,r)}}return $c=t,$c}var Cc,Ov;function oA(){if(Ov)return Cc;Ov=1;var e=iA(),t=aA(),r=Df(),n=Ca();function i(a){return r(a)?e(n(a)):t(a)}return Cc=i,Cc}var Ic,_v;function ht(){if(_v)return Ic;_v=1;var e=Q1(),t=nA(),r=Jr(),n=qe(),i=oA();function a(o){return typeof o=="function"?o:o==null?r:typeof o=="object"?n(o)?t(o[0],o[1]):e(o):i(o)}return Ic=a,Ic}var kc,Av;function E0(){if(Av)return kc;Av=1;function e(t,r,n,i){for(var a=t.length,o=n+(i?1:-1);i?o--:++o<a;)if(r(t[o],o,t))return o;return-1}return kc=e,kc}var Rc,Sv;function uA(){if(Sv)return Rc;Sv=1;function e(t){return t!==t}return Rc=e,Rc}var Dc,Pv;function cA(){if(Pv)return Dc;Pv=1;function e(t,r,n){for(var i=n-1,a=t.length;++i<a;)if(t[i]===r)return i;return-1}return Dc=e,Dc}var Nc,Tv;function sA(){if(Tv)return Nc;Tv=1;var e=E0(),t=uA(),r=cA();function n(i,a,o){return a===a?r(i,a,o):e(i,t,o)}return Nc=n,Nc}var qc,Ev;function lA(){if(Ev)return qc;Ev=1;var e=sA();function t(r,n){var i=r==null?0:r.length;return!!i&&e(r,n,0)>-1}return qc=t,qc}var Lc,jv;function fA(){if(jv)return Lc;jv=1;function e(t,r,n){for(var i=-1,a=t==null?0:t.length;++i<a;)if(n(r,t[i]))return!0;return!1}return Lc=e,Lc}var Bc,Mv;function hA(){if(Mv)return Bc;Mv=1;function e(){}return Bc=e,Bc}var Fc,$v;function pA(){if($v)return Fc;$v=1;var e=S0(),t=hA(),r=Yf(),n=1/0,i=e&&1/r(new e([,-0]))[1]==n?function(a){return new e(a)}:t;return Fc=i,Fc}var Wc,Cv;function dA(){if(Cv)return Wc;Cv=1;var e=y0(),t=lA(),r=fA(),n=g0(),i=pA(),a=Yf(),o=200;function u(c,s,f){var l=-1,h=t,p=c.length,y=!0,v=[],d=v;if(f)y=!1,h=r;else if(p>=o){var g=s?null:i(c);if(g)return a(g);y=!1,h=n,d=new e}else d=s?[]:v;e:for(;++l<p;){var x=c[l],w=s?s(x):x;if(x=f||x!==0?x:0,y&&w===w){for(var O=d.length;O--;)if(d[O]===w)continue e;s&&d.push(w),v.push(x)}else h(d,w,f)||(d!==v&&d.push(w),v.push(x))}return v}return Wc=u,Wc}var zc,Iv;function vA(){if(Iv)return zc;Iv=1;var e=ht(),t=dA();function r(n,i){return n&&n.length?t(n,e(i,2)):[]}return zc=r,zc}var yA=vA();const kv=oe(yA);function j0(e,t,r){return t===!0?kv(e,r):X(t)?kv(e,t):e}function Pr(e){"@babel/helpers - typeof";return Pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pr(e)}var mA=["ref"];function Rv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function pt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rv(Object(r),!0).forEach(function(n){qa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gA(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Dv(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,$0(n.key),n)}}function bA(e,t,r){return t&&Dv(e.prototype,t),r&&Dv(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function xA(e,t,r){return t=qi(t),wA(e,M0()?Reflect.construct(t,r||[],qi(e).constructor):t.apply(e,r))}function wA(e,t){if(t&&(Pr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return OA(e)}function OA(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function M0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(M0=function(){return!!e})()}function qi(e){return qi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},qi(e)}function _A(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tl(e,t)}function Tl(e,t){return Tl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Tl(e,t)}function qa(e,t,r){return t=$0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $0(e){var t=AA(e,"string");return Pr(t)=="symbol"?t:t+""}function AA(e,t){if(Pr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Pr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function SA(e,t){if(e==null)return{};var r=PA(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function PA(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function TA(e){return e.value}function EA(e,t){if(S.isValidElement(e))return S.cloneElement(e,t);if(typeof e=="function")return S.createElement(e,t);t.ref;var r=SA(t,mA);return S.createElement(Xf,r)}var Nv=1,wr=function(e){function t(){var r;gA(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=xA(this,t,[].concat(i)),qa(r,"lastBoundingBox",{width:-1,height:-1}),r}return _A(t,e),bA(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>Nv||Math.abs(i.height-this.lastBoundingBox.height)>Nv)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?pt({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,s=i.chartWidth,f=i.chartHeight,l,h;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var p=this.getBBoxSnapshot();l={left:((s||0)-p.width)/2}}else l=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var y=this.getBBoxSnapshot();h={top:((f||0)-y.height)/2}}else h=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return pt(pt({},l),h)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,s=i.payloadUniqBy,f=i.payload,l=pt(pt({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return S.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(p){n.wrapperNode=p}},EA(a,pt(pt({},this.props),{},{payload:j0(f,s,TA)})))}}],[{key:"getWithHeight",value:function(n,i){var a=pt(pt({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&N(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(q.PureComponent);qa(wr,"displayName","Legend");qa(wr,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var Uc,qv;function jA(){if(qv)return Uc;qv=1;var e=ii(),t=Zf(),r=qe(),n=e?e.isConcatSpreadable:void 0;function i(a){return r(a)||t(a)||!!(n&&a&&a[n])}return Uc=i,Uc}var Hc,Lv;function C0(){if(Lv)return Hc;Lv=1;var e=x0(),t=jA();function r(n,i,a,o,u){var c=-1,s=n.length;for(a||(a=t),u||(u=[]);++c<s;){var f=n[c];i>0&&a(f)?i>1?r(f,i-1,a,o,u):e(u,f):o||(u[u.length]=f)}return u}return Hc=r,Hc}var Kc,Bv;function MA(){if(Bv)return Kc;Bv=1;function e(t){return function(r,n,i){for(var a=-1,o=Object(r),u=i(r),c=u.length;c--;){var s=u[t?c:++a];if(n(o[s],s,o)===!1)break}return r}}return Kc=e,Kc}var Gc,Fv;function $A(){if(Fv)return Gc;Fv=1;var e=MA(),t=e();return Gc=t,Gc}var Vc,Wv;function I0(){if(Wv)return Vc;Wv=1;var e=$A(),t=Na();function r(n,i){return n&&e(n,i,t)}return Vc=r,Vc}var Xc,zv;function CA(){if(zv)return Xc;zv=1;var e=oi();function t(r,n){return function(i,a){if(i==null)return i;if(!e(i))return r(i,a);for(var o=i.length,u=n?o:-1,c=Object(i);(n?u--:++u<o)&&a(c[u],u,c)!==!1;);return i}}return Xc=t,Xc}var Yc,Uv;function th(){if(Uv)return Yc;Uv=1;var e=I0(),t=CA(),r=t(e);return Yc=r,Yc}var Zc,Hv;function k0(){if(Hv)return Zc;Hv=1;var e=th(),t=oi();function r(n,i){var a=-1,o=t(n)?Array(n.length):[];return e(n,function(u,c,s){o[++a]=i(u,c,s)}),o}return Zc=r,Zc}var Jc,Kv;function IA(){if(Kv)return Jc;Kv=1;function e(t,r){var n=t.length;for(t.sort(r);n--;)t[n]=t[n].value;return t}return Jc=e,Jc}var Qc,Gv;function kA(){if(Gv)return Qc;Gv=1;var e=Xr();function t(r,n){if(r!==n){var i=r!==void 0,a=r===null,o=r===r,u=e(r),c=n!==void 0,s=n===null,f=n===n,l=e(n);if(!s&&!l&&!u&&r>n||u&&c&&f&&!s&&!l||a&&c&&f||!i&&f||!o)return 1;if(!a&&!u&&!l&&r<n||l&&i&&o&&!a&&!u||s&&i&&o||!c&&o||!f)return-1}return 0}return Qc=t,Qc}var es,Vv;function RA(){if(Vv)return es;Vv=1;var e=kA();function t(r,n,i){for(var a=-1,o=r.criteria,u=n.criteria,c=o.length,s=i.length;++a<c;){var f=e(o[a],u[a]);if(f){if(a>=s)return f;var l=i[a];return f*(l=="desc"?-1:1)}}return r.index-n.index}return es=t,es}var ts,Xv;function DA(){if(Xv)return ts;Xv=1;var e=Ff(),t=Wf(),r=ht(),n=k0(),i=IA(),a=O0(),o=RA(),u=Jr(),c=qe();function s(f,l,h){l.length?l=e(l,function(v){return c(v)?function(d){return t(d,v.length===1?v[0]:v)}:v}):l=[u];var p=-1;l=e(l,a(r));var y=n(f,function(v,d,g){var x=e(l,function(w){return w(v)});return{criteria:x,index:++p,value:v}});return i(y,function(v,d){return o(v,d,h)})}return ts=s,ts}var rs,Yv;function NA(){if(Yv)return rs;Yv=1;function e(t,r,n){switch(n.length){case 0:return t.call(r);case 1:return t.call(r,n[0]);case 2:return t.call(r,n[0],n[1]);case 3:return t.call(r,n[0],n[1],n[2])}return t.apply(r,n)}return rs=e,rs}var ns,Zv;function qA(){if(Zv)return ns;Zv=1;var e=NA(),t=Math.max;function r(n,i,a){return i=t(i===void 0?n.length-1:i,0),function(){for(var o=arguments,u=-1,c=t(o.length-i,0),s=Array(c);++u<c;)s[u]=o[i+u];u=-1;for(var f=Array(i+1);++u<i;)f[u]=o[u];return f[i]=a(s),e(n,this,f)}}return ns=r,ns}var is,Jv;function LA(){if(Jv)return is;Jv=1;function e(t){return function(){return t}}return is=e,is}var as,Qv;function R0(){if(Qv)return as;Qv=1;var e=ar(),t=function(){try{var r=e(Object,"defineProperty");return r({},"",{}),r}catch{}}();return as=t,as}var os,ey;function BA(){if(ey)return os;ey=1;var e=LA(),t=R0(),r=Jr(),n=t?function(i,a){return t(i,"toString",{configurable:!0,enumerable:!1,value:e(a),writable:!0})}:r;return os=n,os}var us,ty;function FA(){if(ty)return us;ty=1;var e=800,t=16,r=Date.now;function n(i){var a=0,o=0;return function(){var u=r(),c=t-(u-o);if(o=u,c>0){if(++a>=e)return arguments[0]}else a=0;return i.apply(void 0,arguments)}}return us=n,us}var cs,ry;function WA(){if(ry)return cs;ry=1;var e=BA(),t=FA(),r=t(e);return cs=r,cs}var ss,ny;function zA(){if(ny)return ss;ny=1;var e=Jr(),t=qA(),r=WA();function n(i,a){return r(t(i,a,e),i+"")}return ss=n,ss}var ls,iy;function La(){if(iy)return ls;iy=1;var e=qf(),t=oi(),r=Jf(),n=It();function i(a,o,u){if(!n(u))return!1;var c=typeof o;return(c=="number"?t(u)&&r(o,u.length):c=="string"&&o in u)?e(u[o],a):!1}return ls=i,ls}var fs,ay;function UA(){if(ay)return fs;ay=1;var e=C0(),t=DA(),r=zA(),n=La(),i=r(function(a,o){if(a==null)return[];var u=o.length;return u>1&&n(a,o[0],o[1])?o=[]:u>2&&n(o[0],o[1],o[2])&&(o=[o[0]]),t(a,e(o,1),[])});return fs=i,fs}var HA=UA();const rh=oe(HA);function Sn(e){"@babel/helpers - typeof";return Sn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sn(e)}function El(){return El=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},El.apply(this,arguments)}function KA(e,t){return YA(e)||XA(e,t)||VA(e,t)||GA()}function GA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function VA(e,t){if(e){if(typeof e=="string")return oy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return oy(e,t)}}function oy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function XA(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function YA(e){if(Array.isArray(e))return e}function uy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?uy(Object(r),!0).forEach(function(n){ZA(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ZA(e,t,r){return t=JA(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function JA(e){var t=QA(e,"string");return Sn(t)=="symbol"?t:t+""}function QA(e,t){if(Sn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Sn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function eS(e){return Array.isArray(e)&&_e(e[0])&&_e(e[1])?e.join(" ~ "):e}var tS=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,s=c===void 0?{}:c,f=t.payload,l=t.formatter,h=t.itemSorter,p=t.wrapperClassName,y=t.labelClassName,v=t.label,d=t.labelFormatter,g=t.accessibilityLayer,x=g===void 0?!1:g,w=function(){if(f&&f.length){var E={padding:0,margin:0},j=(h?rh(f,h):f).map(function(C,$){if(C.type==="none")return null;var k=hs({display:"block",paddingTop:4,paddingBottom:4,color:C.color||"#000"},u),R=C.formatter||l||eS,L=C.value,B=C.name,U=L,G=B;if(R&&U!=null&&G!=null){var W=R(L,B,C,$,f);if(Array.isArray(W)){var V=KA(W,2);U=V[0],G=V[1]}else U=W}return S.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat($),style:k},_e(G)?S.createElement("span",{className:"recharts-tooltip-item-name"},G):null,_e(G)?S.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,S.createElement("span",{className:"recharts-tooltip-item-value"},U),S.createElement("span",{className:"recharts-tooltip-item-unit"},C.unit||""))});return S.createElement("ul",{className:"recharts-tooltip-item-list",style:E},j)}return null},O=hs({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),m=hs({margin:0},s),b=!Y(v),_=b?v:"",A=J("recharts-default-tooltip",p),T=J("recharts-tooltip-label",y);b&&d&&f!==void 0&&f!==null&&(_=d(v,f));var M=x?{role:"status","aria-live":"assertive"}:{};return S.createElement("div",El({className:A,style:O},M),S.createElement("p",{className:T,style:m},S.isValidElement(_)?_:"".concat(_)),w())};function Pn(e){"@babel/helpers - typeof";return Pn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pn(e)}function mi(e,t,r){return t=rS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rS(e){var t=nS(e,"string");return Pn(t)=="symbol"?t:t+""}function nS(e,t){if(Pn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Pn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var nn="recharts-tooltip-wrapper",iS={visibility:"hidden"};function aS(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return J(nn,mi(mi(mi(mi({},"".concat(nn,"-right"),N(r)&&t&&N(t.x)&&r>=t.x),"".concat(nn,"-left"),N(r)&&t&&N(t.x)&&r<t.x),"".concat(nn,"-bottom"),N(n)&&t&&N(t.y)&&n>=t.y),"".concat(nn,"-top"),N(n)&&t&&N(t.y)&&n<t.y))}function cy(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(a&&N(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var h=f,p=c[n];return h<p?Math.max(l,c[n]):Math.max(f,c[n])}var y=l+u,v=c[n]+s;return y>v?Math.max(f,c[n]):Math.max(l,c[n])}function oS(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function uS(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,s,f,l;return o.height>0&&o.width>0&&r?(f=cy({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=cy({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=oS({translateX:f,translateY:l,useTranslate3d:u})):s=iS,{cssProperties:s,cssClasses:aS({translateX:f,translateY:l,coordinate:r})}}function Tr(e){"@babel/helpers - typeof";return Tr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tr(e)}function sy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ly(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sy(Object(r),!0).forEach(function(n){Ml(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function sS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,N0(n.key),n)}}function lS(e,t,r){return t&&sS(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function fS(e,t,r){return t=Li(t),hS(e,D0()?Reflect.construct(t,r||[],Li(e).constructor):t.apply(e,r))}function hS(e,t){if(t&&(Tr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return pS(e)}function pS(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function D0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(D0=function(){return!!e})()}function Li(e){return Li=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Li(e)}function dS(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&jl(e,t)}function jl(e,t){return jl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},jl(e,t)}function Ml(e,t,r){return t=N0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function N0(e){var t=vS(e,"string");return Tr(t)=="symbol"?t:t+""}function vS(e,t){if(Tr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var fy=1,yS=function(e){function t(){var r;cS(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=fS(this,t,[].concat(i)),Ml(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Ml(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,s,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(s=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&s!==void 0?s:0}})}}),r}return dS(t,e),lS(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>fy||Math.abs(n.height-this.state.lastBoundingBox.height)>fy)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.children,f=i.coordinate,l=i.hasPayload,h=i.isAnimationActive,p=i.offset,y=i.position,v=i.reverseDirection,d=i.useTranslate3d,g=i.viewBox,x=i.wrapperStyle,w=uS({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:p,position:y,reverseDirection:v,tooltipBox:this.state.lastBoundingBox,useTranslate3d:d,viewBox:g}),O=w.cssClasses,m=w.cssProperties,b=ly(ly({transition:h&&a?"transform ".concat(u,"ms ").concat(c):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},x);return S.createElement("div",{tabIndex:-1,className:O,style:b,ref:function(A){n.wrapperNode=A}},s)}}])}(q.PureComponent),mS=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},or={isSsr:mS()};function Er(e){"@babel/helpers - typeof";return Er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Er(e)}function hy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function py(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hy(Object(r),!0).forEach(function(n){nh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function bS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,L0(n.key),n)}}function xS(e,t,r){return t&&bS(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function wS(e,t,r){return t=Bi(t),OS(e,q0()?Reflect.construct(t,r||[],Bi(e).constructor):t.apply(e,r))}function OS(e,t){if(t&&(Er(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return _S(e)}function _S(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function q0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(q0=function(){return!!e})()}function Bi(e){return Bi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Bi(e)}function AS(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&$l(e,t)}function $l(e,t){return $l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},$l(e,t)}function nh(e,t,r){return t=L0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function L0(e){var t=SS(e,"string");return Er(t)=="symbol"?t:t+""}function SS(e,t){if(Er(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Er(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function PS(e){return e.dataKey}function TS(e,t){return S.isValidElement(e)?S.cloneElement(e,t):typeof e=="function"?S.createElement(e,t):S.createElement(tS,t)}var dt=function(e){function t(){return gS(this,t),wS(this,t,arguments)}return AS(t,e),xS(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.content,f=i.coordinate,l=i.filterNull,h=i.isAnimationActive,p=i.offset,y=i.payload,v=i.payloadUniqBy,d=i.position,g=i.reverseDirection,x=i.useTranslate3d,w=i.viewBox,O=i.wrapperStyle,m=y??[];l&&m.length&&(m=j0(y.filter(function(_){return _.value!=null&&(_.hide!==!0||n.props.includeHidden)}),v,PS));var b=m.length>0;return S.createElement(yS,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:h,active:a,coordinate:f,hasPayload:b,offset:p,position:d,reverseDirection:g,useTranslate3d:x,viewBox:w,wrapperStyle:O},TS(s,py(py({},this.props),{},{payload:m})))}}])}(q.PureComponent);nh(dt,"displayName","Tooltip");nh(dt,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!or.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var ps,dy;function ES(){if(dy)return ps;dy=1;var e=ft(),t=function(){return e.Date.now()};return ps=t,ps}var ds,vy;function jS(){if(vy)return ds;vy=1;var e=/\s/;function t(r){for(var n=r.length;n--&&e.test(r.charAt(n)););return n}return ds=t,ds}var vs,yy;function MS(){if(yy)return vs;yy=1;var e=jS(),t=/^\s+/;function r(n){return n&&n.slice(0,e(n)+1).replace(t,"")}return vs=r,vs}var ys,my;function B0(){if(my)return ys;my=1;var e=MS(),t=It(),r=Xr(),n=NaN,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,o=/^0o[0-7]+$/i,u=parseInt;function c(s){if(typeof s=="number")return s;if(r(s))return n;if(t(s)){var f=typeof s.valueOf=="function"?s.valueOf():s;s=t(f)?f+"":f}if(typeof s!="string")return s===0?s:+s;s=e(s);var l=a.test(s);return l||o.test(s)?u(s.slice(2),l?2:8):i.test(s)?n:+s}return ys=c,ys}var ms,gy;function $S(){if(gy)return ms;gy=1;var e=It(),t=ES(),r=B0(),n="Expected a function",i=Math.max,a=Math.min;function o(u,c,s){var f,l,h,p,y,v,d=0,g=!1,x=!1,w=!0;if(typeof u!="function")throw new TypeError(n);c=r(c)||0,e(s)&&(g=!!s.leading,x="maxWait"in s,h=x?i(r(s.maxWait)||0,c):h,w="trailing"in s?!!s.trailing:w);function O(j){var C=f,$=l;return f=l=void 0,d=j,p=u.apply($,C),p}function m(j){return d=j,y=setTimeout(A,c),g?O(j):p}function b(j){var C=j-v,$=j-d,k=c-C;return x?a(k,h-$):k}function _(j){var C=j-v,$=j-d;return v===void 0||C>=c||C<0||x&&$>=h}function A(){var j=t();if(_(j))return T(j);y=setTimeout(A,b(j))}function T(j){return y=void 0,w&&f?O(j):(f=l=void 0,p)}function M(){y!==void 0&&clearTimeout(y),d=0,f=v=l=y=void 0}function P(){return y===void 0?p:T(t())}function E(){var j=t(),C=_(j);if(f=arguments,l=this,v=j,C){if(y===void 0)return m(v);if(x)return clearTimeout(y),y=setTimeout(A,c),O(v)}return y===void 0&&(y=setTimeout(A,c)),p}return E.cancel=M,E.flush=P,E}return ms=o,ms}var gs,by;function CS(){if(by)return gs;by=1;var e=$S(),t=It(),r="Expected a function";function n(i,a,o){var u=!0,c=!0;if(typeof i!="function")throw new TypeError(r);return t(o)&&(u="leading"in o?!!o.leading:u,c="trailing"in o?!!o.trailing:c),e(i,a,{leading:u,maxWait:a,trailing:c})}return gs=n,gs}var IS=CS();const F0=oe(IS);function Tn(e){"@babel/helpers - typeof";return Tn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tn(e)}function xy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function gi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xy(Object(r),!0).forEach(function(n){kS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function kS(e,t,r){return t=RS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function RS(e){var t=DS(e,"string");return Tn(t)=="symbol"?t:t+""}function DS(e,t){if(Tn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function NS(e,t){return FS(e)||BS(e,t)||LS(e,t)||qS()}function qS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function LS(e,t){if(e){if(typeof e=="string")return wy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wy(e,t)}}function wy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function BS(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function FS(e){if(Array.isArray(e))return e}var z2=q.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,s=e.minWidth,f=s===void 0?0:s,l=e.minHeight,h=e.maxHeight,p=e.children,y=e.debounce,v=y===void 0?0:y,d=e.id,g=e.className,x=e.onResize,w=e.style,O=w===void 0?{}:w,m=q.useRef(null),b=q.useRef();b.current=x,q.useImperativeHandle(t,function(){return Object.defineProperty(m.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),m.current},configurable:!0})});var _=q.useState({containerWidth:i.width,containerHeight:i.height}),A=NS(_,2),T=A[0],M=A[1],P=q.useCallback(function(j,C){M(function($){var k=Math.round(j),R=Math.round(C);return $.containerWidth===k&&$.containerHeight===R?$:{containerWidth:k,containerHeight:R}})},[]);q.useEffect(function(){var j=function(B){var U,G=B[0].contentRect,W=G.width,V=G.height;P(W,V),(U=b.current)===null||U===void 0||U.call(b,W,V)};v>0&&(j=F0(j,v,{trailing:!0,leading:!1}));var C=new ResizeObserver(j),$=m.current.getBoundingClientRect(),k=$.width,R=$.height;return P(k,R),C.observe(m.current),function(){C.disconnect()}},[P,v]);var E=q.useMemo(function(){var j=T.containerWidth,C=T.containerHeight;if(j<0||C<0)return null;it(Gt(o)||Gt(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),it(!r||r>0,"The aspect(%s) must be greater than zero.",r);var $=Gt(o)?j:o,k=Gt(c)?C:c;r&&r>0&&($?k=$/r:k&&($=k*r),h&&k>h&&(k=h)),it($>0||k>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,$,k,o,c,f,l,r);var R=!Array.isArray(p)&&bt(p.type).endsWith("Chart");return S.Children.map(p,function(L){return S.isValidElement(L)?q.cloneElement(L,gi({width:$,height:k},R?{style:gi({height:"100%",width:"100%",maxHeight:k,maxWidth:$},L.props.style)}:{})):L})},[r,p,c,h,l,f,T,o]);return S.createElement("div",{id:d?"".concat(d):void 0,className:J("recharts-responsive-container",g),style:gi(gi({},O),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:h}),ref:m},E)}),ih=function(t){return null};ih.displayName="Cell";function En(e){"@babel/helpers - typeof";return En=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},En(e)}function Oy(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Cl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Oy(Object(r),!0).forEach(function(n){WS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Oy(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function WS(e,t,r){return t=zS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zS(e){var t=US(e,"string");return En(t)=="symbol"?t:t+""}function US(e,t){if(En(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(En(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var fr={widthCache:{},cacheCount:0},HS=2e3,KS={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},_y="recharts_measurement_span";function GS(e){var t=Cl({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var yn=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||or.isSsr)return{width:0,height:0};var n=GS(r),i=JSON.stringify({text:t,copyStyle:n});if(fr.widthCache[i])return fr.widthCache[i];try{var a=document.getElementById(_y);a||(a=document.createElement("span"),a.setAttribute("id",_y),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Cl(Cl({},KS),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return fr.widthCache[i]=c,++fr.cacheCount>HS&&(fr.cacheCount=0,fr.widthCache={}),c}catch{return{width:0,height:0}}},VS=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function jn(e){"@babel/helpers - typeof";return jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jn(e)}function Fi(e,t){return JS(e)||ZS(e,t)||YS(e,t)||XS()}function XS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function YS(e,t){if(e){if(typeof e=="string")return Ay(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ay(e,t)}}function Ay(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ZS(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function JS(e){if(Array.isArray(e))return e}function QS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Sy(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,tP(n.key),n)}}function eP(e,t,r){return t&&Sy(e.prototype,t),r&&Sy(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function tP(e){var t=rP(e,"string");return jn(t)=="symbol"?t:t+""}function rP(e,t){if(jn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Py=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Ty=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nP=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,iP=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,W0={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},aP=Object.keys(W0),vr="NaN";function oP(e,t){return e*W0[t]}var bi=function(){function e(t,r){QS(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!nP.test(r)&&(this.num=NaN,this.unit=""),aP.includes(r)&&(this.num=oP(t,r),this.unit="px")}return eP(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=iP.exec(r))!==null&&n!==void 0?n:[],a=Fi(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u??"")}}])}();function z0(e){if(e.includes(vr))return vr;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=Py.exec(t))!==null&&r!==void 0?r:[],i=Fi(n,4),a=i[1],o=i[2],u=i[3],c=bi.parse(a??""),s=bi.parse(u??""),f=o==="*"?c.multiply(s):c.divide(s);if(f.isNaN())return vr;t=t.replace(Py,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,h=(l=Ty.exec(t))!==null&&l!==void 0?l:[],p=Fi(h,4),y=p[1],v=p[2],d=p[3],g=bi.parse(y??""),x=bi.parse(d??""),w=v==="+"?g.add(x):g.subtract(x);if(w.isNaN())return vr;t=t.replace(Ty,w.toString())}return t}var Ey=/\(([^()]*)\)/;function uP(e){for(var t=e;t.includes("(");){var r=Ey.exec(t),n=Fi(r,2),i=n[1];t=t.replace(Ey,z0(i))}return t}function cP(e){var t=e.replace(/\s+/g,"");return t=uP(t),t=z0(t),t}function sP(e){try{return cP(e)}catch{return vr}}function bs(e){var t=sP(e.slice(5,-1));return t===vr?"":t}var lP=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],fP=["dx","dy","angle","className","breakAll"];function Il(){return Il=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Il.apply(this,arguments)}function jy(e,t){if(e==null)return{};var r=hP(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function hP(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function My(e,t){return yP(e)||vP(e,t)||dP(e,t)||pP()}function pP(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function dP(e,t){if(e){if(typeof e=="string")return $y(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $y(e,t)}}function $y(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function vP(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function yP(e){if(Array.isArray(e))return e}var U0=/[ \f\n\r\t\v\u2028\u2029]+/,H0=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];Y(r)||(n?a=r.toString().split(""):a=r.toString().split(U0));var o=a.map(function(c){return{word:c,width:yn(c,i).width}}),u=n?0:yn(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},mP=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,s=t.breakAll,f=N(o),l=u,h=function(){var $=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return $.reduce(function(k,R){var L=R.word,B=R.width,U=k[k.length-1];if(U&&(i==null||a||U.width+B+n<Number(i)))U.words.push(L),U.width+=B+n;else{var G={words:[L],width:B};k.push(G)}return k},[])},p=h(r),y=function($){return $.reduce(function(k,R){return k.width>R.width?k:R})};if(!f)return p;for(var v="…",d=function($){var k=l.slice(0,$),R=H0({breakAll:s,style:c,children:k+v}).wordsWithComputedWidth,L=h(R),B=L.length>o||y(L).width>Number(i);return[B,L]},g=0,x=l.length-1,w=0,O;g<=x&&w<=l.length-1;){var m=Math.floor((g+x)/2),b=m-1,_=d(b),A=My(_,2),T=A[0],M=A[1],P=d(m),E=My(P,1),j=E[0];if(!T&&!j&&(g=m+1),T&&j&&(x=m-1),!T&&j){O=M;break}w++}return O||p},Cy=function(t){var r=Y(t)?[]:t.toString().split(U0);return[{words:r}]},gP=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!or.isSsr){var c,s,f=H0({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,h=f.spaceWidth;c=l,s=h}else return Cy(i);return mP({breakAll:o,children:i,maxLines:u,style:a},c,s,r,n)}return Cy(i)},Iy="#808080",tr=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,s=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,h=t.textAnchor,p=h===void 0?"start":h,y=t.verticalAnchor,v=y===void 0?"end":y,d=t.fill,g=d===void 0?Iy:d,x=jy(t,lP),w=q.useMemo(function(){return gP({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:l,style:x.style,width:x.width})},[x.breakAll,x.children,x.maxLines,l,x.style,x.width]),O=x.dx,m=x.dy,b=x.angle,_=x.className,A=x.breakAll,T=jy(x,fP);if(!_e(n)||!_e(a))return null;var M=n+(N(O)?O:0),P=a+(N(m)?m:0),E;switch(v){case"start":E=bs("calc(".concat(s,")"));break;case"middle":E=bs("calc(".concat((w.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:E=bs("calc(".concat(w.length-1," * -").concat(u,")"));break}var j=[];if(l){var C=w[0].width,$=x.width;j.push("scale(".concat((N($)?$/C:1)/C,")"))}return b&&j.push("rotate(".concat(b,", ").concat(M,", ").concat(P,")")),j.length&&(T.transform=j.join(" ")),S.createElement("text",Il({},H(T,!0),{x:M,y:P,className:J("recharts-text",_),textAnchor:p,fill:g.includes("url")?Iy:g}),w.map(function(k,R){var L=k.words.join(A?"":" ");return S.createElement("tspan",{x:M,dy:R===0?E:u,key:"".concat(L,"-").concat(R)},L)}))};function Ct(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function bP(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ah(e){let t,r,n;e.length!==2?(t=Ct,r=(u,c)=>Ct(e(u),c),n=(u,c)=>e(u)-c):(t=e===Ct||e===bP?e:xP,r=e,n=e);function i(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<0?s=l+1:f=l}while(s<f)}return s}function a(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<=0?s=l+1:f=l}while(s<f)}return s}function o(u,c,s=0,f=u.length){const l=i(u,c,s,f-1);return l>s&&n(u[l-1],c)>-n(u[l],c)?l-1:l}return{left:i,center:o,right:a}}function xP(){return 0}function K0(e){return e===null?NaN:+e}function*wP(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const OP=ah(Ct),ui=OP.right;ah(K0).center;class ky extends Map{constructor(t,r=SP){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(Ry(this,t))}has(t){return super.has(Ry(this,t))}set(t,r){return super.set(_P(this,t),r)}delete(t){return super.delete(AP(this,t))}}function Ry({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function _P({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function AP({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function SP(e){return e!==null&&typeof e=="object"?e.valueOf():e}function PP(e=Ct){if(e===Ct)return G0;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function G0(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const TP=Math.sqrt(50),EP=Math.sqrt(10),jP=Math.sqrt(2);function Wi(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=TP?10:a>=EP?5:a>=jP?2:1;let u,c,s;return i<0?(s=Math.pow(10,-i)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?Wi(e,t,r*2):[u,c,s]}function kl(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?Wi(t,e,r):Wi(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(a-s)/-o;else for(let s=0;s<u;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(i+s)/-o;else for(let s=0;s<u;++s)c[s]=(i+s)*o;return c}function Rl(e,t,r){return t=+t,e=+e,r=+r,Wi(e,t,r)[2]}function Dl(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?Rl(t,e,r):Rl(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function Dy(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function Ny(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function V0(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?G0:PP(i);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),h=.5*Math.sqrt(f*l*(c-l)/c)*(s-c/2<0?-1:1),p=Math.max(r,Math.floor(t-s*l/c+h)),y=Math.min(n,Math.floor(t+(c-s)*l/c+h));V0(e,t,p,y,i)}const a=e[t];let o=r,u=n;for(an(e,r,t),i(e[n],a)>0&&an(e,r,n);o<u;){for(an(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?an(e,r,u):(++u,an(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function an(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function MP(e,t,r){if(e=Float64Array.from(wP(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return Ny(e);if(t>=1)return Dy(e);var n,i=(n-1)*t,a=Math.floor(i),o=Dy(V0(e,a).subarray(0,a+1)),u=Ny(e.subarray(a+1));return o+(u-o)*(i-a)}}function $P(e,t,r=K0){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function CP(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function Je(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function Tt(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const Nl=Symbol("implicit");function oh(){var e=new ky,t=[],r=[],n=Nl;function i(a){let o=e.get(a);if(o===void 0){if(n!==Nl)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new ky;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return oh(t,r).unknown(n)},Je.apply(i,arguments),i}function Mn(){var e=oh().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,s=0,f=.5;delete e.unknown;function l(){var h=t().length,p=i<n,y=p?i:n,v=p?n:i;a=(v-y)/Math.max(1,h-c+s*2),u&&(a=Math.floor(a)),y+=(v-y-a*(h-c))*f,o=a*(1-c),u&&(y=Math.round(y),o=Math.round(o));var d=CP(h).map(function(g){return y+a*g});return r(p?d.reverse():d)}return e.domain=function(h){return arguments.length?(t(h),l()):t()},e.range=function(h){return arguments.length?([n,i]=h,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(h){return[n,i]=h,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(h){return arguments.length?(u=!!h,l()):u},e.padding=function(h){return arguments.length?(c=Math.min(1,s=+h),l()):c},e.paddingInner=function(h){return arguments.length?(c=Math.min(1,h),l()):c},e.paddingOuter=function(h){return arguments.length?(s=+h,l()):s},e.align=function(h){return arguments.length?(f=Math.max(0,Math.min(1,h)),l()):f},e.copy=function(){return Mn(t(),[n,i]).round(u).paddingInner(c).paddingOuter(s).align(f)},Je.apply(l(),arguments)}function X0(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return X0(t())},e}function mn(){return X0(Mn.apply(null,arguments).paddingInner(1))}function uh(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function Y0(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function ci(){}var $n=.7,zi=1/$n,Or="\\s*([+-]?\\d+)\\s*",Cn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",ut="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",IP=/^#([0-9a-f]{3,8})$/,kP=new RegExp(`^rgb\\(${Or},${Or},${Or}\\)$`),RP=new RegExp(`^rgb\\(${ut},${ut},${ut}\\)$`),DP=new RegExp(`^rgba\\(${Or},${Or},${Or},${Cn}\\)$`),NP=new RegExp(`^rgba\\(${ut},${ut},${ut},${Cn}\\)$`),qP=new RegExp(`^hsl\\(${Cn},${ut},${ut}\\)$`),LP=new RegExp(`^hsla\\(${Cn},${ut},${ut},${Cn}\\)$`),qy={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};uh(ci,In,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Ly,formatHex:Ly,formatHex8:BP,formatHsl:FP,formatRgb:By,toString:By});function Ly(){return this.rgb().formatHex()}function BP(){return this.rgb().formatHex8()}function FP(){return Z0(this).formatHsl()}function By(){return this.rgb().formatRgb()}function In(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=IP.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?Fy(t):r===3?new Ne(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?xi(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?xi(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=kP.exec(e))?new Ne(t[1],t[2],t[3],1):(t=RP.exec(e))?new Ne(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=DP.exec(e))?xi(t[1],t[2],t[3],t[4]):(t=NP.exec(e))?xi(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=qP.exec(e))?Uy(t[1],t[2]/100,t[3]/100,1):(t=LP.exec(e))?Uy(t[1],t[2]/100,t[3]/100,t[4]):qy.hasOwnProperty(e)?Fy(qy[e]):e==="transparent"?new Ne(NaN,NaN,NaN,0):null}function Fy(e){return new Ne(e>>16&255,e>>8&255,e&255,1)}function xi(e,t,r,n){return n<=0&&(e=t=r=NaN),new Ne(e,t,r,n)}function WP(e){return e instanceof ci||(e=In(e)),e?(e=e.rgb(),new Ne(e.r,e.g,e.b,e.opacity)):new Ne}function ql(e,t,r,n){return arguments.length===1?WP(e):new Ne(e,t,r,n??1)}function Ne(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}uh(Ne,ql,Y0(ci,{brighter(e){return e=e==null?zi:Math.pow(zi,e),new Ne(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?$n:Math.pow($n,e),new Ne(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Ne(Zt(this.r),Zt(this.g),Zt(this.b),Ui(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Wy,formatHex:Wy,formatHex8:zP,formatRgb:zy,toString:zy}));function Wy(){return`#${Vt(this.r)}${Vt(this.g)}${Vt(this.b)}`}function zP(){return`#${Vt(this.r)}${Vt(this.g)}${Vt(this.b)}${Vt((isNaN(this.opacity)?1:this.opacity)*255)}`}function zy(){const e=Ui(this.opacity);return`${e===1?"rgb(":"rgba("}${Zt(this.r)}, ${Zt(this.g)}, ${Zt(this.b)}${e===1?")":`, ${e})`}`}function Ui(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Zt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Vt(e){return e=Zt(e),(e<16?"0":"")+e.toString(16)}function Uy(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new nt(e,t,r,n)}function Z0(e){if(e instanceof nt)return new nt(e.h,e.s,e.l,e.opacity);if(e instanceof ci||(e=In(e)),!e)return new nt;if(e instanceof nt)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new nt(o,u,c,e.opacity)}function UP(e,t,r,n){return arguments.length===1?Z0(e):new nt(e,t,r,n??1)}function nt(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}uh(nt,UP,Y0(ci,{brighter(e){return e=e==null?zi:Math.pow(zi,e),new nt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?$n:Math.pow($n,e),new nt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Ne(xs(e>=240?e-240:e+120,i,n),xs(e,i,n),xs(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new nt(Hy(this.h),wi(this.s),wi(this.l),Ui(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Ui(this.opacity);return`${e===1?"hsl(":"hsla("}${Hy(this.h)}, ${wi(this.s)*100}%, ${wi(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Hy(e){return e=(e||0)%360,e<0?e+360:e}function wi(e){return Math.max(0,Math.min(1,e||0))}function xs(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const ch=e=>()=>e;function HP(e,t){return function(r){return e+r*t}}function KP(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function GP(e){return(e=+e)==1?J0:function(t,r){return r-t?KP(t,r,e):ch(isNaN(t)?r:t)}}function J0(e,t){var r=t-e;return r?HP(e,r):ch(isNaN(e)?t:e)}const Ky=function e(t){var r=GP(t);function n(i,a){var o=r((i=ql(i)).r,(a=ql(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),s=J0(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=s(f),i+""}}return n.gamma=e,n}(1);function VP(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function XP(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function YP(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=Qr(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function ZP(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function Hi(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function JP(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=Qr(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var Ll=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ws=new RegExp(Ll.source,"g");function QP(e){return function(){return e}}function eT(e){return function(t){return e(t)+""}}function tT(e,t){var r=Ll.lastIndex=ws.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=Ll.exec(e))&&(i=ws.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:Hi(n,i)})),r=ws.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?eT(c[0].x):QP(t):(t=c.length,function(s){for(var f=0,l;f<t;++f)u[(l=c[f]).i]=l.x(s);return u.join("")})}function Qr(e,t){var r=typeof t,n;return t==null||r==="boolean"?ch(t):(r==="number"?Hi:r==="string"?(n=In(t))?(t=n,Ky):tT:t instanceof In?Ky:t instanceof Date?ZP:XP(t)?VP:Array.isArray(t)?YP:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?JP:Hi)(e,t)}function sh(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function rT(e,t){t===void 0&&(t=e,e=Qr);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function nT(e){return function(){return e}}function Ki(e){return+e}var Gy=[0,1];function ke(e){return e}function Bl(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:nT(isNaN(t)?NaN:.5)}function iT(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function aT(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=Bl(i,n),a=r(o,a)):(n=Bl(n,i),a=r(a,o)),function(u){return a(n(u))}}function oT(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=Bl(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=ui(e,u,1,n)-1;return a[c](i[c](u))}}function si(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function Ba(){var e=Gy,t=Gy,r=Qr,n,i,a,o=ke,u,c,s;function f(){var h=Math.min(e.length,t.length);return o!==ke&&(o=iT(e[0],e[h-1])),u=h>2?oT:aT,c=s=null,l}function l(h){return h==null||isNaN(h=+h)?a:(c||(c=u(e.map(n),t,r)))(n(o(h)))}return l.invert=function(h){return o(i((s||(s=u(t,e.map(n),Hi)))(h)))},l.domain=function(h){return arguments.length?(e=Array.from(h,Ki),f()):e.slice()},l.range=function(h){return arguments.length?(t=Array.from(h),f()):t.slice()},l.rangeRound=function(h){return t=Array.from(h),r=sh,f()},l.clamp=function(h){return arguments.length?(o=h?!0:ke,f()):o!==ke},l.interpolate=function(h){return arguments.length?(r=h,f()):r},l.unknown=function(h){return arguments.length?(a=h,l):a},function(h,p){return n=h,i=p,f()}}function lh(){return Ba()(ke,ke)}function uT(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function Gi(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function jr(e){return e=Gi(Math.abs(e)),e?e[1]:NaN}function cT(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function sT(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var lT=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function kn(e){if(!(t=lT.exec(e)))throw new Error("invalid format: "+e);var t;return new fh({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}kn.prototype=fh.prototype;function fh(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}fh.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function fT(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var Q0;function hT(e,t){var r=Gi(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(Q0=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+Gi(e,Math.max(0,t+a-1))[0]}function Vy(e,t){var r=Gi(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const Xy={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:uT,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Vy(e*100,t),r:Vy,s:hT,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Yy(e){return e}var Zy=Array.prototype.map,Jy=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function pT(e){var t=e.grouping===void 0||e.thousands===void 0?Yy:cT(Zy.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?Yy:sT(Zy.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(l){l=kn(l);var h=l.fill,p=l.align,y=l.sign,v=l.symbol,d=l.zero,g=l.width,x=l.comma,w=l.precision,O=l.trim,m=l.type;m==="n"?(x=!0,m="g"):Xy[m]||(w===void 0&&(w=12),O=!0,m="g"),(d||h==="0"&&p==="=")&&(d=!0,h="0",p="=");var b=v==="$"?r:v==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",_=v==="$"?n:/[%p]/.test(m)?o:"",A=Xy[m],T=/[defgprs%]/.test(m);w=w===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,w)):Math.max(0,Math.min(20,w));function M(P){var E=b,j=_,C,$,k;if(m==="c")j=A(P)+j,P="";else{P=+P;var R=P<0||1/P<0;if(P=isNaN(P)?c:A(Math.abs(P),w),O&&(P=fT(P)),R&&+P==0&&y!=="+"&&(R=!1),E=(R?y==="("?y:u:y==="-"||y==="("?"":y)+E,j=(m==="s"?Jy[8+Q0/3]:"")+j+(R&&y==="("?")":""),T){for(C=-1,$=P.length;++C<$;)if(k=P.charCodeAt(C),48>k||k>57){j=(k===46?i+P.slice(C+1):P.slice(C))+j,P=P.slice(0,C);break}}}x&&!d&&(P=t(P,1/0));var L=E.length+P.length+j.length,B=L<g?new Array(g-L+1).join(h):"";switch(x&&d&&(P=t(B+P,B.length?g-j.length:1/0),B=""),p){case"<":P=E+P+j+B;break;case"=":P=E+B+P+j;break;case"^":P=B.slice(0,L=B.length>>1)+E+P+j+B.slice(L);break;default:P=B+E+P+j;break}return a(P)}return M.toString=function(){return l+""},M}function f(l,h){var p=s((l=kn(l),l.type="f",l)),y=Math.max(-8,Math.min(8,Math.floor(jr(h)/3)))*3,v=Math.pow(10,-y),d=Jy[8+y/3];return function(g){return p(v*g)+d}}return{format:s,formatPrefix:f}}var Oi,hh,ex;dT({thousands:",",grouping:[3],currency:["$",""]});function dT(e){return Oi=pT(e),hh=Oi.format,ex=Oi.formatPrefix,Oi}function vT(e){return Math.max(0,-jr(Math.abs(e)))}function yT(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(jr(t)/3)))*3-jr(Math.abs(e)))}function mT(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,jr(t)-jr(e))+1}function tx(e,t,r,n){var i=Dl(e,t,r),a;switch(n=kn(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=yT(i,o))&&(n.precision=a),ex(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=mT(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=vT(i))&&(n.precision=a-(n.type==="%")*2);break}}return hh(n)}function kt(e){var t=e.domain;return e.ticks=function(r){var n=t();return kl(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return tx(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,s,f=10;for(u<o&&(s=o,o=u,u=s,s=i,i=a,a=s);f-- >0;){if(s=Rl(o,u,r),s===c)return n[i]=o,n[a]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function Vi(){var e=lh();return e.copy=function(){return si(e,Vi())},Je.apply(e,arguments),kt(e)}function rx(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,Ki),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return rx(e).unknown(t)},e=arguments.length?Array.from(e,Ki):[0,1],kt(r)}function nx(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function Qy(e){return Math.log(e)}function em(e){return Math.exp(e)}function gT(e){return-Math.log(-e)}function bT(e){return-Math.exp(-e)}function xT(e){return isFinite(e)?+("1e"+e):e<0?0:e}function wT(e){return e===10?xT:e===Math.E?Math.exp:t=>Math.pow(e,t)}function OT(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function tm(e){return(t,r)=>-e(-t,r)}function ph(e){const t=e(Qy,em),r=t.domain;let n=10,i,a;function o(){return i=OT(n),a=wT(n),r()[0]<0?(i=tm(i),a=tm(a),e(gT,bT)):e(Qy,em),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],f=c[c.length-1];const l=f<s;l&&([s,f]=[f,s]);let h=i(s),p=i(f),y,v;const d=u==null?10:+u;let g=[];if(!(n%1)&&p-h<d){if(h=Math.floor(h),p=Math.ceil(p),s>0){for(;h<=p;++h)for(y=1;y<n;++y)if(v=h<0?y/a(-h):y*a(h),!(v<s)){if(v>f)break;g.push(v)}}else for(;h<=p;++h)for(y=n-1;y>=1;--y)if(v=h>0?y/a(-h):y*a(h),!(v<s)){if(v>f)break;g.push(v)}g.length*2<d&&(g=kl(s,f,d))}else g=kl(h,p,Math.min(p-h,d)).map(a);return l?g.reverse():g},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=kn(c)).precision==null&&(c.trim=!0),c=hh(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=s?c(f):""}},t.nice=()=>r(nx(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function ix(){const e=ph(Ba()).domain([1,10]);return e.copy=()=>si(e,ix()).base(e.base()),Je.apply(e,arguments),e}function rm(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function nm(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function dh(e){var t=1,r=e(rm(t),nm(t));return r.constant=function(n){return arguments.length?e(rm(t=+n),nm(t)):t},kt(r)}function ax(){var e=dh(Ba());return e.copy=function(){return si(e,ax()).constant(e.constant())},Je.apply(e,arguments)}function im(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function _T(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function AT(e){return e<0?-e*e:e*e}function vh(e){var t=e(ke,ke),r=1;function n(){return r===1?e(ke,ke):r===.5?e(_T,AT):e(im(r),im(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},kt(t)}function yh(){var e=vh(Ba());return e.copy=function(){return si(e,yh()).exponent(e.exponent())},Je.apply(e,arguments),e}function ST(){return yh.apply(null,arguments).exponent(.5)}function am(e){return Math.sign(e)*e*e}function PT(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function ox(){var e=lh(),t=[0,1],r=!1,n;function i(a){var o=PT(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(am(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,Ki)).map(am)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return ox(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},Je.apply(i,arguments),kt(i)}function ux(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=$P(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[ui(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(Ct),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return ux().domain(e).range(t).unknown(n)},Je.apply(a,arguments)}function cx(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[ui(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return cx().domain([e,t]).range(i).unknown(a)},Je.apply(kt(o),arguments)}function sx(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[ui(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return sx().domain(e).range(t).unknown(r)},Je.apply(i,arguments)}const Os=new Date,_s=new Date;function Ae(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let s;do c.push(s=new Date(+a)),t(a,u),e(a);while(s<a&&a<o);return c},i.filter=a=>Ae(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(Os.setTime(+a),_s.setTime(+o),e(Os),e(_s),Math.floor(r(Os,_s))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const Xi=Ae(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);Xi.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?Ae(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):Xi);Xi.range;const yt=1e3,Ye=yt*60,mt=Ye*60,Ot=mt*24,mh=Ot*7,om=Ot*30,As=Ot*365,Xt=Ae(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*yt)},(e,t)=>(t-e)/yt,e=>e.getUTCSeconds());Xt.range;const gh=Ae(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*yt)},(e,t)=>{e.setTime(+e+t*Ye)},(e,t)=>(t-e)/Ye,e=>e.getMinutes());gh.range;const bh=Ae(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Ye)},(e,t)=>(t-e)/Ye,e=>e.getUTCMinutes());bh.range;const xh=Ae(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*yt-e.getMinutes()*Ye)},(e,t)=>{e.setTime(+e+t*mt)},(e,t)=>(t-e)/mt,e=>e.getHours());xh.range;const wh=Ae(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*mt)},(e,t)=>(t-e)/mt,e=>e.getUTCHours());wh.range;const li=Ae(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Ye)/Ot,e=>e.getDate()-1);li.range;const Fa=Ae(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Ot,e=>e.getUTCDate()-1);Fa.range;const lx=Ae(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Ot,e=>Math.floor(e/Ot));lx.range;function ur(e){return Ae(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*Ye)/mh)}const Wa=ur(0),Yi=ur(1),TT=ur(2),ET=ur(3),Mr=ur(4),jT=ur(5),MT=ur(6);Wa.range;Yi.range;TT.range;ET.range;Mr.range;jT.range;MT.range;function cr(e){return Ae(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/mh)}const za=cr(0),Zi=cr(1),$T=cr(2),CT=cr(3),$r=cr(4),IT=cr(5),kT=cr(6);za.range;Zi.range;$T.range;CT.range;$r.range;IT.range;kT.range;const Oh=Ae(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Oh.range;const _h=Ae(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());_h.range;const _t=Ae(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());_t.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Ae(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});_t.range;const At=Ae(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());At.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Ae(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});At.range;function fx(e,t,r,n,i,a){const o=[[Xt,1,yt],[Xt,5,5*yt],[Xt,15,15*yt],[Xt,30,30*yt],[a,1,Ye],[a,5,5*Ye],[a,15,15*Ye],[a,30,30*Ye],[i,1,mt],[i,3,3*mt],[i,6,6*mt],[i,12,12*mt],[n,1,Ot],[n,2,2*Ot],[r,1,mh],[t,1,om],[t,3,3*om],[e,1,As]];function u(s,f,l){const h=f<s;h&&([s,f]=[f,s]);const p=l&&typeof l.range=="function"?l:c(s,f,l),y=p?p.range(s,+f+1):[];return h?y.reverse():y}function c(s,f,l){const h=Math.abs(f-s)/l,p=ah(([,,d])=>d).right(o,h);if(p===o.length)return e.every(Dl(s/As,f/As,l));if(p===0)return Xi.every(Math.max(Dl(s,f,l),1));const[y,v]=o[h/o[p-1][2]<o[p][2]/h?p-1:p];return y.every(v)}return[u,c]}const[RT,DT]=fx(At,_h,za,lx,wh,bh),[NT,qT]=fx(_t,Oh,Wa,li,xh,gh);function Ss(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Ps(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function on(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function LT(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=un(i),f=cn(i),l=un(a),h=cn(a),p=un(o),y=cn(o),v=un(u),d=cn(u),g=un(c),x=cn(c),w={a:R,A:L,b:B,B:U,c:null,d:hm,e:hm,f:cE,g:gE,G:xE,H:aE,I:oE,j:uE,L:hx,m:sE,M:lE,p:G,q:W,Q:vm,s:ym,S:fE,u:hE,U:pE,V:dE,w:vE,W:yE,x:null,X:null,y:mE,Y:bE,Z:wE,"%":dm},O={a:V,A:fe,b:ye,B:Le,c:null,d:pm,e:pm,f:SE,g:RE,G:NE,H:OE,I:_E,j:AE,L:dx,m:PE,M:TE,p:qt,q:Re,Q:vm,s:ym,S:EE,u:jE,U:ME,V:$E,w:CE,W:IE,x:null,X:null,y:kE,Y:DE,Z:qE,"%":dm},m={a:M,A:P,b:E,B:j,c:C,d:lm,e:lm,f:tE,g:sm,G:cm,H:fm,I:fm,j:ZT,L:eE,m:YT,M:JT,p:T,q:XT,Q:nE,s:iE,S:QT,u:UT,U:HT,V:KT,w:zT,W:GT,x:$,X:k,y:sm,Y:cm,Z:VT,"%":rE};w.x=b(r,w),w.X=b(n,w),w.c=b(t,w),O.x=b(r,O),O.X=b(n,O),O.c=b(t,O);function b(F,Z){return function(Q){var D=[],de=-1,ee=0,be=F.length,xe,De,Et;for(Q instanceof Date||(Q=new Date(+Q));++de<be;)F.charCodeAt(de)===37&&(D.push(F.slice(ee,de)),(De=um[xe=F.charAt(++de)])!=null?xe=F.charAt(++de):De=xe==="e"?" ":"0",(Et=Z[xe])&&(xe=Et(Q,De)),D.push(xe),ee=de+1);return D.push(F.slice(ee,de)),D.join("")}}function _(F,Z){return function(Q){var D=on(1900,void 0,1),de=A(D,F,Q+="",0),ee,be;if(de!=Q.length)return null;if("Q"in D)return new Date(D.Q);if("s"in D)return new Date(D.s*1e3+("L"in D?D.L:0));if(Z&&!("Z"in D)&&(D.Z=0),"p"in D&&(D.H=D.H%12+D.p*12),D.m===void 0&&(D.m="q"in D?D.q:0),"V"in D){if(D.V<1||D.V>53)return null;"w"in D||(D.w=1),"Z"in D?(ee=Ps(on(D.y,0,1)),be=ee.getUTCDay(),ee=be>4||be===0?Zi.ceil(ee):Zi(ee),ee=Fa.offset(ee,(D.V-1)*7),D.y=ee.getUTCFullYear(),D.m=ee.getUTCMonth(),D.d=ee.getUTCDate()+(D.w+6)%7):(ee=Ss(on(D.y,0,1)),be=ee.getDay(),ee=be>4||be===0?Yi.ceil(ee):Yi(ee),ee=li.offset(ee,(D.V-1)*7),D.y=ee.getFullYear(),D.m=ee.getMonth(),D.d=ee.getDate()+(D.w+6)%7)}else("W"in D||"U"in D)&&("w"in D||(D.w="u"in D?D.u%7:"W"in D?1:0),be="Z"in D?Ps(on(D.y,0,1)).getUTCDay():Ss(on(D.y,0,1)).getDay(),D.m=0,D.d="W"in D?(D.w+6)%7+D.W*7-(be+5)%7:D.w+D.U*7-(be+6)%7);return"Z"in D?(D.H+=D.Z/100|0,D.M+=D.Z%100,Ps(D)):Ss(D)}}function A(F,Z,Q,D){for(var de=0,ee=Z.length,be=Q.length,xe,De;de<ee;){if(D>=be)return-1;if(xe=Z.charCodeAt(de++),xe===37){if(xe=Z.charAt(de++),De=m[xe in um?Z.charAt(de++):xe],!De||(D=De(F,Q,D))<0)return-1}else if(xe!=Q.charCodeAt(D++))return-1}return D}function T(F,Z,Q){var D=s.exec(Z.slice(Q));return D?(F.p=f.get(D[0].toLowerCase()),Q+D[0].length):-1}function M(F,Z,Q){var D=p.exec(Z.slice(Q));return D?(F.w=y.get(D[0].toLowerCase()),Q+D[0].length):-1}function P(F,Z,Q){var D=l.exec(Z.slice(Q));return D?(F.w=h.get(D[0].toLowerCase()),Q+D[0].length):-1}function E(F,Z,Q){var D=g.exec(Z.slice(Q));return D?(F.m=x.get(D[0].toLowerCase()),Q+D[0].length):-1}function j(F,Z,Q){var D=v.exec(Z.slice(Q));return D?(F.m=d.get(D[0].toLowerCase()),Q+D[0].length):-1}function C(F,Z,Q){return A(F,t,Z,Q)}function $(F,Z,Q){return A(F,r,Z,Q)}function k(F,Z,Q){return A(F,n,Z,Q)}function R(F){return o[F.getDay()]}function L(F){return a[F.getDay()]}function B(F){return c[F.getMonth()]}function U(F){return u[F.getMonth()]}function G(F){return i[+(F.getHours()>=12)]}function W(F){return 1+~~(F.getMonth()/3)}function V(F){return o[F.getUTCDay()]}function fe(F){return a[F.getUTCDay()]}function ye(F){return c[F.getUTCMonth()]}function Le(F){return u[F.getUTCMonth()]}function qt(F){return i[+(F.getUTCHours()>=12)]}function Re(F){return 1+~~(F.getUTCMonth()/3)}return{format:function(F){var Z=b(F+="",w);return Z.toString=function(){return F},Z},parse:function(F){var Z=_(F+="",!1);return Z.toString=function(){return F},Z},utcFormat:function(F){var Z=b(F+="",O);return Z.toString=function(){return F},Z},utcParse:function(F){var Z=_(F+="",!0);return Z.toString=function(){return F},Z}}}var um={"-":"",_:" ",0:"0"},Te=/^\s*\d+/,BT=/^%/,FT=/[\\^$*+?|[\]().{}]/g;function re(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function WT(e){return e.replace(FT,"\\$&")}function un(e){return new RegExp("^(?:"+e.map(WT).join("|")+")","i")}function cn(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function zT(e,t,r){var n=Te.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function UT(e,t,r){var n=Te.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function HT(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function KT(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function GT(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function cm(e,t,r){var n=Te.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function sm(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function VT(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function XT(e,t,r){var n=Te.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function YT(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function lm(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function ZT(e,t,r){var n=Te.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function fm(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function JT(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function QT(e,t,r){var n=Te.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function eE(e,t,r){var n=Te.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function tE(e,t,r){var n=Te.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function rE(e,t,r){var n=BT.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function nE(e,t,r){var n=Te.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function iE(e,t,r){var n=Te.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function hm(e,t){return re(e.getDate(),t,2)}function aE(e,t){return re(e.getHours(),t,2)}function oE(e,t){return re(e.getHours()%12||12,t,2)}function uE(e,t){return re(1+li.count(_t(e),e),t,3)}function hx(e,t){return re(e.getMilliseconds(),t,3)}function cE(e,t){return hx(e,t)+"000"}function sE(e,t){return re(e.getMonth()+1,t,2)}function lE(e,t){return re(e.getMinutes(),t,2)}function fE(e,t){return re(e.getSeconds(),t,2)}function hE(e){var t=e.getDay();return t===0?7:t}function pE(e,t){return re(Wa.count(_t(e)-1,e),t,2)}function px(e){var t=e.getDay();return t>=4||t===0?Mr(e):Mr.ceil(e)}function dE(e,t){return e=px(e),re(Mr.count(_t(e),e)+(_t(e).getDay()===4),t,2)}function vE(e){return e.getDay()}function yE(e,t){return re(Yi.count(_t(e)-1,e),t,2)}function mE(e,t){return re(e.getFullYear()%100,t,2)}function gE(e,t){return e=px(e),re(e.getFullYear()%100,t,2)}function bE(e,t){return re(e.getFullYear()%1e4,t,4)}function xE(e,t){var r=e.getDay();return e=r>=4||r===0?Mr(e):Mr.ceil(e),re(e.getFullYear()%1e4,t,4)}function wE(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+re(t/60|0,"0",2)+re(t%60,"0",2)}function pm(e,t){return re(e.getUTCDate(),t,2)}function OE(e,t){return re(e.getUTCHours(),t,2)}function _E(e,t){return re(e.getUTCHours()%12||12,t,2)}function AE(e,t){return re(1+Fa.count(At(e),e),t,3)}function dx(e,t){return re(e.getUTCMilliseconds(),t,3)}function SE(e,t){return dx(e,t)+"000"}function PE(e,t){return re(e.getUTCMonth()+1,t,2)}function TE(e,t){return re(e.getUTCMinutes(),t,2)}function EE(e,t){return re(e.getUTCSeconds(),t,2)}function jE(e){var t=e.getUTCDay();return t===0?7:t}function ME(e,t){return re(za.count(At(e)-1,e),t,2)}function vx(e){var t=e.getUTCDay();return t>=4||t===0?$r(e):$r.ceil(e)}function $E(e,t){return e=vx(e),re($r.count(At(e),e)+(At(e).getUTCDay()===4),t,2)}function CE(e){return e.getUTCDay()}function IE(e,t){return re(Zi.count(At(e)-1,e),t,2)}function kE(e,t){return re(e.getUTCFullYear()%100,t,2)}function RE(e,t){return e=vx(e),re(e.getUTCFullYear()%100,t,2)}function DE(e,t){return re(e.getUTCFullYear()%1e4,t,4)}function NE(e,t){var r=e.getUTCDay();return e=r>=4||r===0?$r(e):$r.ceil(e),re(e.getUTCFullYear()%1e4,t,4)}function qE(){return"+0000"}function dm(){return"%"}function vm(e){return+e}function ym(e){return Math.floor(+e/1e3)}var hr,yx,mx;LE({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function LE(e){return hr=LT(e),yx=hr.format,hr.parse,mx=hr.utcFormat,hr.utcParse,hr}function BE(e){return new Date(e)}function FE(e){return e instanceof Date?+e:+new Date(+e)}function Ah(e,t,r,n,i,a,o,u,c,s){var f=lh(),l=f.invert,h=f.domain,p=s(".%L"),y=s(":%S"),v=s("%I:%M"),d=s("%I %p"),g=s("%a %d"),x=s("%b %d"),w=s("%B"),O=s("%Y");function m(b){return(c(b)<b?p:u(b)<b?y:o(b)<b?v:a(b)<b?d:n(b)<b?i(b)<b?g:x:r(b)<b?w:O)(b)}return f.invert=function(b){return new Date(l(b))},f.domain=function(b){return arguments.length?h(Array.from(b,FE)):h().map(BE)},f.ticks=function(b){var _=h();return e(_[0],_[_.length-1],b??10)},f.tickFormat=function(b,_){return _==null?m:s(_)},f.nice=function(b){var _=h();return(!b||typeof b.range!="function")&&(b=t(_[0],_[_.length-1],b??10)),b?h(nx(_,b)):f},f.copy=function(){return si(f,Ah(e,t,r,n,i,a,o,u,c,s))},f}function WE(){return Je.apply(Ah(NT,qT,_t,Oh,Wa,li,xh,gh,Xt,yx).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function zE(){return Je.apply(Ah(RT,DT,At,_h,za,Fa,wh,bh,Xt,mx).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Ua(){var e=0,t=1,r,n,i,a,o=ke,u=!1,c;function s(l){return l==null||isNaN(l=+l)?c:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}s.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(l){return arguments.length?(u=!!l,s):u},s.interpolator=function(l){return arguments.length?(o=l,s):o};function f(l){return function(h){var p,y;return arguments.length?([p,y]=h,o=l(p,y),s):[o(0),o(1)]}}return s.range=f(Qr),s.rangeRound=f(sh),s.unknown=function(l){return arguments.length?(c=l,s):c},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),s}}function Rt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function gx(){var e=kt(Ua()(ke));return e.copy=function(){return Rt(e,gx())},Tt.apply(e,arguments)}function bx(){var e=ph(Ua()).domain([1,10]);return e.copy=function(){return Rt(e,bx()).base(e.base())},Tt.apply(e,arguments)}function xx(){var e=dh(Ua());return e.copy=function(){return Rt(e,xx()).constant(e.constant())},Tt.apply(e,arguments)}function Sh(){var e=vh(Ua());return e.copy=function(){return Rt(e,Sh()).exponent(e.exponent())},Tt.apply(e,arguments)}function UE(){return Sh.apply(null,arguments).exponent(.5)}function wx(){var e=[],t=ke;function r(n){if(n!=null&&!isNaN(n=+n))return t((ui(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Ct),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>MP(e,a/n))},r.copy=function(){return wx(t).domain(e)},Tt.apply(r,arguments)}function Ha(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,s=ke,f,l=!1,h;function p(v){return isNaN(v=+v)?h:(v=.5+((v=+f(v))-a)*(n*v<n*a?u:c),s(l?Math.max(0,Math.min(1,v)):v))}p.domain=function(v){return arguments.length?([e,t,r]=v,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,p):[e,t,r]},p.clamp=function(v){return arguments.length?(l=!!v,p):l},p.interpolator=function(v){return arguments.length?(s=v,p):s};function y(v){return function(d){var g,x,w;return arguments.length?([g,x,w]=d,s=rT(v,[g,x,w]),p):[s(0),s(.5),s(1)]}}return p.range=y(Qr),p.rangeRound=y(sh),p.unknown=function(v){return arguments.length?(h=v,p):h},function(v){return f=v,i=v(e),a=v(t),o=v(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,p}}function Ox(){var e=kt(Ha()(ke));return e.copy=function(){return Rt(e,Ox())},Tt.apply(e,arguments)}function _x(){var e=ph(Ha()).domain([.1,1,10]);return e.copy=function(){return Rt(e,_x()).base(e.base())},Tt.apply(e,arguments)}function Ax(){var e=dh(Ha());return e.copy=function(){return Rt(e,Ax()).constant(e.constant())},Tt.apply(e,arguments)}function Ph(){var e=vh(Ha());return e.copy=function(){return Rt(e,Ph()).exponent(e.exponent())},Tt.apply(e,arguments)}function HE(){return Ph.apply(null,arguments).exponent(.5)}const mm=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Mn,scaleDiverging:Ox,scaleDivergingLog:_x,scaleDivergingPow:Ph,scaleDivergingSqrt:HE,scaleDivergingSymlog:Ax,scaleIdentity:rx,scaleImplicit:Nl,scaleLinear:Vi,scaleLog:ix,scaleOrdinal:oh,scalePoint:mn,scalePow:yh,scaleQuantile:ux,scaleQuantize:cx,scaleRadial:ox,scaleSequential:gx,scaleSequentialLog:bx,scaleSequentialPow:Sh,scaleSequentialQuantile:wx,scaleSequentialSqrt:UE,scaleSequentialSymlog:xx,scaleSqrt:ST,scaleSymlog:ax,scaleThreshold:sx,scaleTime:WE,scaleUtc:zE,tickFormat:tx},Symbol.toStringTag,{value:"Module"}));var Ts,gm;function Ka(){if(gm)return Ts;gm=1;var e=Xr();function t(r,n,i){for(var a=-1,o=r.length;++a<o;){var u=r[a],c=n(u);if(c!=null&&(s===void 0?c===c&&!e(c):i(c,s)))var s=c,f=u}return f}return Ts=t,Ts}var Es,bm;function Sx(){if(bm)return Es;bm=1;function e(t,r){return t>r}return Es=e,Es}var js,xm;function KE(){if(xm)return js;xm=1;var e=Ka(),t=Sx(),r=Jr();function n(i){return i&&i.length?e(i,r,t):void 0}return js=n,js}var GE=KE();const Ga=oe(GE);var Ms,wm;function Px(){if(wm)return Ms;wm=1;function e(t,r){return t<r}return Ms=e,Ms}var $s,Om;function VE(){if(Om)return $s;Om=1;var e=Ka(),t=Px(),r=Jr();function n(i){return i&&i.length?e(i,r,t):void 0}return $s=n,$s}var XE=VE();const Va=oe(XE);var Cs,_m;function YE(){if(_m)return Cs;_m=1;var e=Ff(),t=ht(),r=k0(),n=qe();function i(a,o){var u=n(a)?e:r;return u(a,t(o,3))}return Cs=i,Cs}var Is,Am;function ZE(){if(Am)return Is;Am=1;var e=C0(),t=YE();function r(n,i){return e(t(n,i),1)}return Is=r,Is}var JE=ZE();const QE=oe(JE);var ks,Sm;function ej(){if(Sm)return ks;Sm=1;var e=eh();function t(r,n){return e(r,n)}return ks=t,ks}var tj=ej();const fi=oe(tj);var en=1e9,rj={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},Eh,pe=!0,Ze="[DecimalError] ",Jt=Ze+"Invalid argument: ",Th=Ze+"Exponent out of range: ",tn=Math.floor,Ht=Math.pow,nj=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Ue,Se=1e7,he=7,Tx=9007199254740991,Ji=tn(Tx/he),z={};z.absoluteValue=z.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};z.comparedTo=z.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};z.decimalPlaces=z.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*he;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};z.dividedBy=z.div=function(e){return xt(this,new this.constructor(e))};z.dividedToIntegerBy=z.idiv=function(e){var t=this,r=t.constructor;return ue(xt(t,new r(e),0,1),r.precision)};z.equals=z.eq=function(e){return!this.cmp(e)};z.exponent=function(){return ge(this)};z.greaterThan=z.gt=function(e){return this.cmp(e)>0};z.greaterThanOrEqualTo=z.gte=function(e){return this.cmp(e)>=0};z.isInteger=z.isint=function(){return this.e>this.d.length-2};z.isNegative=z.isneg=function(){return this.s<0};z.isPositive=z.ispos=function(){return this.s>0};z.isZero=function(){return this.s===0};z.lessThan=z.lt=function(e){return this.cmp(e)<0};z.lessThanOrEqualTo=z.lte=function(e){return this.cmp(e)<1};z.logarithm=z.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(Ue))throw Error(Ze+"NaN");if(r.s<1)throw Error(Ze+(r.s?"NaN":"-Infinity"));return r.eq(Ue)?new n(0):(pe=!1,t=xt(Rn(r,a),Rn(e,a),a),pe=!0,ue(t,i))};z.minus=z.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Mx(t,e):Ex(t,(e.s=-e.s,e))};z.modulo=z.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(Ze+"NaN");return r.s?(pe=!1,t=xt(r,e,0,1).times(e),pe=!0,r.minus(t)):ue(new n(r),i)};z.naturalExponential=z.exp=function(){return jx(this)};z.naturalLogarithm=z.ln=function(){return Rn(this)};z.negated=z.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};z.plus=z.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Ex(t,e):Mx(t,(e.s=-e.s,e))};z.precision=z.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Jt+e);if(t=ge(i)+1,n=i.d.length-1,r=n*he+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};z.squareRoot=z.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(Ze+"NaN")}for(e=ge(u),pe=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=ot(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=tn((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(xt(u,a,o+2)).times(.5),ot(a.d).slice(0,o)===(t=ot(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(ue(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return pe=!0,ue(n,r)};z.times=z.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this,l=f.constructor,h=f.d,p=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=h.length,s=p.length,c<s&&(a=h,h=p,p=a,o=c,c=s,s=o),a=[],o=c+s,n=o;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+p[n]*h[i-n-1]+t,a[i--]=u%Se|0,t=u/Se|0;a[i]=(a[i]+t)%Se|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,pe?ue(e,l.precision):e};z.toDecimalPlaces=z.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(st(e,0,en),t===void 0?t=n.rounding:st(t,0,8),ue(r,e+ge(r)+1,t))};z.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=rr(n,!0):(st(e,0,en),t===void 0?t=i.rounding:st(t,0,8),n=ue(new i(n),e+1,t),r=rr(n,!0,e+1)),r};z.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?rr(i):(st(e,0,en),t===void 0?t=a.rounding:st(t,0,8),n=ue(new a(i),e+ge(i)+1,t),r=rr(n.abs(),!1,e+ge(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};z.toInteger=z.toint=function(){var e=this,t=e.constructor;return ue(new t(e),ge(e)+1,t.rounding)};z.toNumber=function(){return+this};z.toPower=z.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,s=12,f=+(e=new c(e));if(!e.s)return new c(Ue);if(u=new c(u),!u.s){if(e.s<1)throw Error(Ze+"Infinity");return u}if(u.eq(Ue))return u;if(n=c.precision,e.eq(Ue))return ue(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=Tx){for(i=new c(Ue),t=Math.ceil(n/he+4),pe=!1;r%2&&(i=i.times(u),Tm(i.d,t)),r=tn(r/2),r!==0;)u=u.times(u),Tm(u.d,t);return pe=!0,e.s<0?new c(Ue).div(i):ue(i,n)}}else if(a<0)throw Error(Ze+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,pe=!1,i=e.times(Rn(u,n+s)),pe=!0,i=jx(i),i.s=a,i};z.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=ge(i),n=rr(i,r<=a.toExpNeg||r>=a.toExpPos)):(st(e,1,en),t===void 0?t=a.rounding:st(t,0,8),i=ue(new a(i),e,t),r=ge(i),n=rr(i,e<=r||r<=a.toExpNeg,e)),n};z.toSignificantDigits=z.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(st(e,1,en),t===void 0?t=n.rounding:st(t,0,8)),ue(new n(r),e,t)};z.toString=z.valueOf=z.val=z.toJSON=z[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=ge(e),r=e.constructor;return rr(e,t<=r.toExpNeg||t>=r.toExpPos)};function Ex(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),pe?ue(t,l):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),o=Math.ceil(l/he),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=s.length,u-a<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/Se|0,c[a]%=Se;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,pe?ue(t,l):t}function st(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Jt+e)}function ot(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=he-n.length,r&&(a+=jt(r)),a+=n;o=e[t],n=o+"",r=he-n.length,r&&(a+=jt(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var xt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%Se|0,o=a/Se|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*Se+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,s,f,l,h,p,y,v,d,g,x,w,O,m,b,_,A,T=n.constructor,M=n.s==i.s?1:-1,P=n.d,E=i.d;if(!n.s)return new T(n);if(!i.s)throw Error(Ze+"Division by zero");for(c=n.e-i.e,_=E.length,m=P.length,p=new T(M),y=p.d=[],s=0;E[s]==(P[s]||0);)++s;if(E[s]>(P[s]||0)&&--c,a==null?x=a=T.precision:o?x=a+(ge(n)-ge(i))+1:x=a,x<0)return new T(0);if(x=x/he+2|0,s=0,_==1)for(f=0,E=E[0],x++;(s<m||f)&&x--;s++)w=f*Se+(P[s]||0),y[s]=w/E|0,f=w%E|0;else{for(f=Se/(E[0]+1)|0,f>1&&(E=e(E,f),P=e(P,f),_=E.length,m=P.length),O=_,v=P.slice(0,_),d=v.length;d<_;)v[d++]=0;A=E.slice(),A.unshift(0),b=E[0],E[1]>=Se/2&&++b;do f=0,u=t(E,v,_,d),u<0?(g=v[0],_!=d&&(g=g*Se+(v[1]||0)),f=g/b|0,f>1?(f>=Se&&(f=Se-1),l=e(E,f),h=l.length,d=v.length,u=t(l,v,h,d),u==1&&(f--,r(l,_<h?A:E,h))):(f==0&&(u=f=1),l=E.slice()),h=l.length,h<d&&l.unshift(0),r(v,l,d),u==-1&&(d=v.length,u=t(E,v,_,d),u<1&&(f++,r(v,_<d?A:E,d))),d=v.length):u===0&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[d++]=P[O]||0:(v=[P[O]],d=1);while((O++<m||v[0]!==void 0)&&x--)}return y[0]||y.shift(),p.e=c,ue(p,o?a+ge(p)+1:a)}}();function jx(e,t){var r,n,i,a,o,u,c=0,s=0,f=e.constructor,l=f.precision;if(ge(e)>16)throw Error(Th+ge(e));if(!e.s)return new f(Ue);for(pe=!1,u=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(Ht(2,s))/Math.LN10*2+5|0,u+=n,r=i=a=new f(Ue),f.precision=u;;){if(i=ue(i.times(e),u),r=r.times(++c),o=a.plus(xt(i,r,u)),ot(o.d).slice(0,u)===ot(a.d).slice(0,u)){for(;s--;)a=ue(a.times(a),u);return f.precision=l,t==null?(pe=!0,ue(a,l)):a}a=o}}function ge(e){for(var t=e.e*he,r=e.d[0];r>=10;r/=10)t++;return t}function Rs(e,t,r){if(t>e.LN10.sd())throw pe=!0,r&&(e.precision=r),Error(Ze+"LN10 precision limit exceeded");return ue(new e(e.LN10),t)}function jt(e){for(var t="";e--;)t+="0";return t}function Rn(e,t){var r,n,i,a,o,u,c,s,f,l=1,h=10,p=e,y=p.d,v=p.constructor,d=v.precision;if(p.s<1)throw Error(Ze+(p.s?"NaN":"-Infinity"));if(p.eq(Ue))return new v(0);if(t==null?(pe=!1,s=d):s=t,p.eq(10))return t==null&&(pe=!0),Rs(v,s);if(s+=h,v.precision=s,r=ot(y),n=r.charAt(0),a=ge(p),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)p=p.times(e),r=ot(p.d),n=r.charAt(0),l++;a=ge(p),n>1?(p=new v("0."+r),a++):p=new v(n+"."+r.slice(1))}else return c=Rs(v,s+2,d).times(a+""),p=Rn(new v(n+"."+r.slice(1)),s-h).plus(c),v.precision=d,t==null?(pe=!0,ue(p,d)):p;for(u=o=p=xt(p.minus(Ue),p.plus(Ue),s),f=ue(p.times(p),s),i=3;;){if(o=ue(o.times(f),s),c=u.plus(xt(o,new v(i),s)),ot(c.d).slice(0,s)===ot(u.d).slice(0,s))return u=u.times(2),a!==0&&(u=u.plus(Rs(v,s+2,d).times(a+""))),u=xt(u,new v(l),s),v.precision=d,t==null?(pe=!0,ue(u,d)):u;u=c,i+=2}}function Pm(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=tn(r/he),e.d=[],n=(r+1)%he,r<0&&(n+=he),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=he;n<i;)e.d.push(+t.slice(n,n+=he));t=t.slice(n),n=he-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),pe&&(e.e>Ji||e.e<-Ji))throw Error(Th+r)}else e.s=0,e.e=0,e.d=[0];return e}function ue(e,t,r){var n,i,a,o,u,c,s,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=he,i=t,s=l[f=0];else{if(f=Math.ceil((n+1)/he),a=l.length,f>=a)return e;for(s=a=l[f],o=1;a>=10;a/=10)o++;n%=he,i=n-he+o}if(r!==void 0&&(a=Ht(10,o-i-1),u=s/a%10|0,c=t<0||l[f+1]!==void 0||s%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?s/Ht(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(a=ge(e),l.length=1,t=t-a-1,l[0]=Ht(10,(he-t%he)%he),e.e=tn(-t/he)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=Ht(10,he-n),l[f]=i>0?(s/Ht(10,o-i)%Ht(10,i)|0)*a:0),c)for(;;)if(f==0){(l[0]+=a)==Se&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=Se)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(pe&&(e.e>Ji||e.e<-Ji))throw Error(Th+ge(e));return e}function Mx(e,t){var r,n,i,a,o,u,c,s,f,l,h=e.constructor,p=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),pe?ue(t,p):t;if(c=e.d,l=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(f=o<0,f?(r=c,o=-o,u=l.length):(r=l,n=s,u=c.length),i=Math.max(Math.ceil(p/he),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=l[i]){f=c[i]<l[i];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),u=c.length,i=l.length-u;i>0;--i)c[u++]=0;for(i=l.length;i>o;){if(c[--i]<l[i]){for(a=i;a&&c[--a]===0;)c[a]=Se-1;--c[a],c[i]+=Se}c[i]-=l[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,pe?ue(t,p):t):new h(0)}function rr(e,t,r){var n,i=ge(e),a=ot(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+jt(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+jt(-i-1)+a,r&&(n=r-o)>0&&(a+=jt(n))):i>=o?(a+=jt(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+jt(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=jt(n))),e.s<0?"-"+a:a}function Tm(e,t){if(e.length>t)return e.length=t,!0}function $x(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Jt+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return Pm(o,a.toString())}else if(typeof a!="string")throw Error(Jt+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,nj.test(a))Pm(o,a);else throw Error(Jt+a)}if(i.prototype=z,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=$x,i.config=i.set=ij,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function ij(e){if(!e||typeof e!="object")throw Error(Ze+"Object expected");var t,r,n,i=["precision",1,en,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(tn(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Jt+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Jt+r+": "+n);return this}var Eh=$x(rj);Ue=new Eh(1);const ae=Eh;function aj(e){return sj(e)||cj(e)||uj(e)||oj()}function oj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uj(e,t){if(e){if(typeof e=="string")return Fl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Fl(e,t)}}function cj(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function sj(e){if(Array.isArray(e))return Fl(e)}function Fl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var lj=function(t){return t},Cx={},Ix=function(t){return t===Cx},Em=function(t){return function r(){return arguments.length===0||arguments.length===1&&Ix(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},fj=function e(t,r){return t===1?r:Em(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==Cx}).length;return o>=t?r.apply(void 0,i):e(t-o,Em(function(){for(var u=arguments.length,c=new Array(u),s=0;s<u;s++)c[s]=arguments[s];var f=i.map(function(l){return Ix(l)?c.shift():l});return r.apply(void 0,aj(f).concat(c))}))})},Xa=function(t){return fj(t.length,t)},Wl=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},hj=Xa(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),pj=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return lj;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},zl=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},kx=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function dj(e){var t;return e===0?t=1:t=Math.floor(new ae(e).abs().log(10).toNumber())+1,t}function vj(e,t,r){for(var n=new ae(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var yj=Xa(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),mj=Xa(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),gj=Xa(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const Ya={rangeStep:vj,getDigitCount:dj,interpolateNumber:yj,uninterpolateNumber:mj,uninterpolateTruncation:gj};function Ul(e){return wj(e)||xj(e)||Rx(e)||bj()}function bj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xj(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function wj(e){if(Array.isArray(e))return Hl(e)}function Dn(e,t){return Aj(e)||_j(e,t)||Rx(e,t)||Oj()}function Oj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Rx(e,t){if(e){if(typeof e=="string")return Hl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Hl(e,t)}}function Hl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _j(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function Aj(e){if(Array.isArray(e))return e}function Dx(e){var t=Dn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function Nx(e,t,r){if(e.lte(0))return new ae(0);var n=Ya.getDigitCount(e.toNumber()),i=new ae(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ae(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ae(Math.ceil(c))}function Sj(e,t,r){var n=1,i=new ae(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ae(10).pow(Ya.getDigitCount(e)-1),i=new ae(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ae(Math.floor(e)))}else e===0?i=new ae(Math.floor((t-1)/2)):r||(i=new ae(Math.floor(e)));var o=Math.floor((t-1)/2),u=pj(hj(function(c){return i.add(new ae(c-o).mul(n)).toNumber()}),Wl);return u(0,t)}function qx(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ae(0),tickMin:new ae(0),tickMax:new ae(0)};var a=Nx(new ae(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ae(0):(o=new ae(e).add(t).div(2),o=o.sub(new ae(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ae(t).sub(o).div(a).toNumber()),s=u+c+1;return s>r?qx(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,u=t>0?u:u+(r-s)),{step:a,tickMin:o.sub(new ae(u).mul(a)),tickMax:o.add(new ae(c).mul(a))})}function Pj(e){var t=Dn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=Dx([r,n]),c=Dn(u,2),s=c[0],f=c[1];if(s===-1/0||f===1/0){var l=f===1/0?[s].concat(Ul(Wl(0,i-1).map(function(){return 1/0}))):[].concat(Ul(Wl(0,i-1).map(function(){return-1/0})),[f]);return r>n?zl(l):l}if(s===f)return Sj(s,i,a);var h=qx(s,f,o,a),p=h.step,y=h.tickMin,v=h.tickMax,d=Ya.rangeStep(y,v.add(new ae(.1).mul(p)),p);return r>n?zl(d):d}function Tj(e,t){var r=Dn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Dx([n,i]),u=Dn(o,2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[n,i];if(c===s)return[c];var f=Math.max(t,2),l=Nx(new ae(s).sub(c).div(f-1),a,0),h=[].concat(Ul(Ya.rangeStep(new ae(c),new ae(s).sub(new ae(.99).mul(l)),l)),[s]);return n>i?zl(h):h}var Ej=kx(Pj),jj=kx(Tj),Mj="Invariant failed";function nr(e,t){throw new Error(Mj)}var $j=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Cr(e){"@babel/helpers - typeof";return Cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cr(e)}function Qi(){return Qi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qi.apply(this,arguments)}function Cj(e,t){return Dj(e)||Rj(e,t)||kj(e,t)||Ij()}function Ij(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kj(e,t){if(e){if(typeof e=="string")return jm(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return jm(e,t)}}function jm(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Rj(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function Dj(e){if(Array.isArray(e))return e}function Nj(e,t){if(e==null)return{};var r=qj(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function qj(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Lj(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Bj(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Fx(n.key),n)}}function Fj(e,t,r){return t&&Bj(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Wj(e,t,r){return t=ea(t),zj(e,Lx()?Reflect.construct(t,r||[],ea(e).constructor):t.apply(e,r))}function zj(e,t){if(t&&(Cr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Uj(e)}function Uj(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Lx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Lx=function(){return!!e})()}function ea(e){return ea=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ea(e)}function Hj(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Kl(e,t)}function Kl(e,t){return Kl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Kl(e,t)}function Bx(e,t,r){return t=Fx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Fx(e){var t=Kj(e,"string");return Cr(t)=="symbol"?t:t+""}function Kj(e,t){if(Cr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var hi=function(e){function t(){return Lj(this,t),Wj(this,t,arguments)}return Hj(t,e),Fj(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,s=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,h=Nj(n,$j),p=H(h,!1);this.props.direction==="x"&&f.type!=="number"&&nr();var y=c.map(function(v){var d=s(v,u),g=d.x,x=d.y,w=d.value,O=d.errorVal;if(!O)return null;var m=[],b,_;if(Array.isArray(O)){var A=Cj(O,2);b=A[0],_=A[1]}else b=_=O;if(a==="vertical"){var T=f.scale,M=x+i,P=M+o,E=M-o,j=T(w-b),C=T(w+_);m.push({x1:C,y1:P,x2:C,y2:E}),m.push({x1:j,y1:M,x2:C,y2:M}),m.push({x1:j,y1:P,x2:j,y2:E})}else if(a==="horizontal"){var $=l.scale,k=g+i,R=k-o,L=k+o,B=$(w-b),U=$(w+_);m.push({x1:R,y1:U,x2:L,y2:U}),m.push({x1:k,y1:B,x2:k,y2:U}),m.push({x1:R,y1:B,x2:L,y2:B})}return S.createElement(te,Qi({className:"recharts-errorBar",key:"bar-".concat(m.map(function(G){return"".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))},p),m.map(function(G){return S.createElement("line",Qi({},G,{key:"line-".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))}))});return S.createElement(te,{className:"recharts-errorBars"},y)}}])}(S.Component);Bx(hi,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});Bx(hi,"displayName","ErrorBar");function Nn(e){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(e)}function Mm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ft(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Mm(Object(r),!0).forEach(function(n){Gj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Mm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Gj(e,t,r){return t=Vj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Vj(e){var t=Xj(e,"string");return Nn(t)=="symbol"?t:t+""}function Xj(e,t){if(Nn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Wx=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=We(r,wr);if(!o)return null;var u=wr.defaultProps,c=u!==void 0?Ft(Ft({},u),o.props):{},s;return o.props&&o.props.payload?s=o.props&&o.props.payload:a==="children"?s=(n||[]).reduce(function(f,l){var h=l.item,p=l.props,y=p.sectors||p.data||[];return f.concat(y.map(function(v){return{type:o.props.iconType||h.props.legendType,value:v.name,color:v.fill,payload:v}}))},[]):s=(n||[]).map(function(f){var l=f.item,h=l.type.defaultProps,p=h!==void 0?Ft(Ft({},h),l.props):{},y=p.dataKey,v=p.name,d=p.legendType,g=p.hide;return{inactive:g,dataKey:y,type:c.iconType||d||"square",color:jh(l),value:v||y,payload:p}}),Ft(Ft(Ft({},c),wr.getWithHeight(o,i)),{},{payload:s,item:o})};function qn(e){"@babel/helpers - typeof";return qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qn(e)}function $m(e){return Qj(e)||Jj(e)||Zj(e)||Yj()}function Yj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Zj(e,t){if(e){if(typeof e=="string")return Gl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gl(e,t)}}function Jj(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Qj(e){if(Array.isArray(e))return Gl(e)}function Gl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Cm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ve(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Cm(Object(r),!0).forEach(function(n){_r(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _r(e,t,r){return t=eM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eM(e){var t=tM(e,"string");return qn(t)=="symbol"?t:t+""}function tM(e,t){if(qn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Oe(e,t,r){return Y(e)||Y(t)?r:_e(t)?He(e,t,r):X(t)?t(e):r}function gn(e,t,r,n){var i=QE(e,function(u){return Oe(u,t)});if(r==="number"){var a=i.filter(function(u){return N(u)||parseFloat(u)});return a.length?[Va(a),Ga(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!Y(u)}):i;return o.map(function(u){return _e(u)||u instanceof Date?u:""})}var rM=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,s=0;s<u;s++){var f=s>0?i[s-1].coordinate:i[u-1].coordinate,l=i[s].coordinate,h=s>=u-1?i[0].coordinate:i[s+1].coordinate,p=void 0;if(Ce(l-f)!==Ce(h-l)){var y=[];if(Ce(h-l)===Ce(c[1]-c[0])){p=h;var v=l+c[1]-c[0];y[0]=Math.min(v,(v+f)/2),y[1]=Math.max(v,(v+f)/2)}else{p=f;var d=h+c[1]-c[0];y[0]=Math.min(l,(d+l)/2),y[1]=Math.max(l,(d+l)/2)}var g=[Math.min(l,(p+l)/2),Math.max(l,(p+l)/2)];if(t>g[0]&&t<=g[1]||t>=y[0]&&t<=y[1]){o=i[s].index;break}}else{var x=Math.min(f,h),w=Math.max(f,h);if(t>(x+l)/2&&t<=(w+l)/2){o=i[s].index;break}}}else for(var O=0;O<u;O++)if(O===0&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O>0&&O<u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O===u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2){o=n[O].index;break}return o},jh=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?ve(ve({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},nM=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,s=u.length;c<s;c++)for(var f=a[u[c]].stackGroups,l=Object.keys(f),h=0,p=l.length;h<p;h++){var y=f[l[h]],v=y.items,d=y.cateAxisId,g=v.filter(function(_){return bt(_.type).indexOf("Bar")>=0});if(g&&g.length){var x=g[0].type.defaultProps,w=x!==void 0?ve(ve({},x),g[0].props):g[0].props,O=w.barSize,m=w[d];o[m]||(o[m]=[]);var b=Y(O)?r:O;o[m].push({item:g[0],stackList:g.slice(1),barSize:Y(b)?void 0:Ie(b,n,0)})}}return o},iM=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var s=Ie(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var h=!1,p=i/c,y=o.reduce(function(O,m){return O+m.barSize||0},0);y+=(c-1)*s,y>=i&&(y-=(c-1)*s,s=0),y>=i&&p>0&&(h=!0,p*=.9,y=c*p);var v=(i-y)/2>>0,d={offset:v-s,size:0};f=o.reduce(function(O,m){var b={item:m.item,position:{offset:d.offset+d.size+s,size:h?p:m.barSize}},_=[].concat($m(O),[b]);return d=_[_.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(A){_.push({item:A,position:d})}),_},l)}else{var g=Ie(n,i,0,!0);i-2*g-(c-1)*s<=0&&(s=0);var x=(i-2*g-(c-1)*s)/c;x>1&&(x>>=0);var w=u===+u?Math.min(x,u):x;f=o.reduce(function(O,m,b){var _=[].concat($m(O),[{item:m.item,position:{offset:g+(x+s)*b+(x-w)/2,size:w}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(A){_.push({item:A,position:_[_.length-1].position})}),_},l)}return f},aM=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),s=Wx({children:a,legendWidth:c});if(s){var f=i||{},l=f.width,h=f.height,p=s.align,y=s.verticalAlign,v=s.layout;if((v==="vertical"||v==="horizontal"&&y==="middle")&&p!=="center"&&N(t[p]))return ve(ve({},t),{},_r({},p,t[p]+(l||0)));if((v==="horizontal"||v==="vertical"&&p==="center")&&y!=="middle"&&N(t[y]))return ve(ve({},t),{},_r({},y,t[y]+(h||0)))}return t},oM=function(t,r,n){return Y(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},zx=function(t,r,n,i,a){var o=r.props.children,u=Ke(o,hi).filter(function(s){return oM(i,a,s.props.direction)});if(u&&u.length){var c=u.map(function(s){return s.props.dataKey});return t.reduce(function(s,f){var l=Oe(f,n);if(Y(l))return s;var h=Array.isArray(l)?[Va(l),Ga(l)]:[l,l],p=c.reduce(function(y,v){var d=Oe(f,v,0),g=h[0]-Math.abs(Array.isArray(d)?d[0]:d),x=h[1]+Math.abs(Array.isArray(d)?d[1]:d);return[Math.min(g,y[0]),Math.max(x,y[1])]},[1/0,-1/0]);return[Math.min(p[0],s[0]),Math.max(p[1],s[1])]},[1/0,-1/0])}return null},uM=function(t,r,n,i,a){var o=r.map(function(u){return zx(t,u,n,a,i)}).filter(function(u){return!Y(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},Ux=function(t,r,n,i,a){var o=r.map(function(c){var s=c.props.dataKey;return n==="number"&&s&&zx(t,c,s,i)||gn(t,s,n,a)});if(n==="number")return o.reduce(function(c,s){return[Math.min(c[0],s[0]),Math.max(c[1],s[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,s){for(var f=0,l=s.length;f<l;f++)u[s[f]]||(u[s[f]]=!0,c.push(s[f]));return c},[])},Hx=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},Kx=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},gt=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,s=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(s=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?Ce(u[0]-u[1])*2*s:s,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var h=a?a.indexOf(l):l;return{coordinate:i(h)+s,value:l,offset:s}});return f.filter(function(l){return!ai(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,h){return{coordinate:i(l)+s,value:l,index:h,offset:s}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+s,value:l,offset:s}}):i.domain().map(function(l,h){return{coordinate:i(l)+s,value:a?a[l]:l,index:h,offset:s}})},Ds=new WeakMap,_i=function(t,r){if(typeof r!="function")return t;Ds.has(t)||Ds.set(t,new WeakMap);var n=Ds.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},Gx=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:Mn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:Vi(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:mn(),realScaleType:"point"}:a==="category"?{scale:Mn(),realScaleType:"band"}:{scale:Vi(),realScaleType:"linear"};if(Qt(i)){var c="scale".concat(Ia(i));return{scale:(mm[c]||mn)(),realScaleType:mm[c]?c:"point"}}return X(i)?{scale:i}:{scale:mn(),realScaleType:"point"}},Im=1e-4,Vx=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-Im,o=Math.max(i[0],i[1])+Im,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},cM=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},sM=function(t,r){if(!r||r.length!==2||!N(r[0])||!N(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!N(t[0])||t[0]<n)&&(a[0]=n),(!N(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},lM=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=ai(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},fM=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=ai(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},hM={sign:lM,expand:Q_,none:Ar,silhouette:e1,wiggle:t1,positive:fM},pM=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=hM[n],o=J_().keys(i).value(function(u,c){return+Oe(u,c,0)}).order(_l).offset(a);return o(t)},dM=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},s=u.reduce(function(l,h){var p,y=(p=h.type)!==null&&p!==void 0&&p.defaultProps?ve(ve({},h.type.defaultProps),h.props):h.props,v=y.stackId,d=y.hide;if(d)return l;var g=y[n],x=l[g]||{hasStack:!1,stackGroups:{}};if(_e(v)){var w=x.stackGroups[v]||{numericAxisId:n,cateAxisId:i,items:[]};w.items.push(h),x.hasStack=!0,x.stackGroups[v]=w}else x.stackGroups[Zr("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[h]};return ve(ve({},l),{},_r({},g,x))},c),f={};return Object.keys(s).reduce(function(l,h){var p=s[h];if(p.hasStack){var y={};p.stackGroups=Object.keys(p.stackGroups).reduce(function(v,d){var g=p.stackGroups[d];return ve(ve({},v),{},_r({},d,{numericAxisId:n,cateAxisId:i,items:g.items,stackedData:pM(t,g.items,a)}))},y)}return ve(ve({},l),{},_r({},h,p))},f)},Xx=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var s=t.domain();if(!s.length)return null;var f=Ej(s,a,u);return t.domain([Va(f),Ga(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),h=jj(l,a,u);return{niceTicks:h}}return null};function km(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!Y(i[t.dataKey])){var u=Mi(r,"value",i[t.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=Oe(i,Y(o)?t.dataKey:o);return Y(c)?null:t.scale(c)}var Rm=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=Oe(o,r.dataKey,r.domain[u]);return Y(c)?null:r.scale(c)-a/2+i},vM=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},yM=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?ve(ve({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(_e(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},mM=function(t){return t.reduce(function(r,n){return[Va(n.concat([r[0]]).filter(N)),Ga(n.concat([r[1]]).filter(N))]},[1/0,-1/0])},Yx=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(s,f){var l=mM(f.slice(r,n+1));return[Math.min(s[0],l[0]),Math.max(s[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},Dm=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Nm=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Vl=function(t,r,n){if(X(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(N(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(Dm.test(t[0])){var a=+Dm.exec(t[0])[1];i[0]=r[0]-a}else X(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(N(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(Nm.test(t[1])){var o=+Nm.exec(t[1])[1];i[1]=r[1]+o}else X(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},ta=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=rh(r,function(l){return l.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var s=a[u],f=a[u-1];o=Math.min((s.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},qm=function(t,r,n){return!t||!t.length||fi(t,He(n,"type.defaultProps.domain"))?r:t},Zx=function(t,r){var n=t.type.defaultProps?ve(ve({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,s=n.chartType,f=n.hide;return ve(ve({},H(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:jh(t),value:Oe(r,i),type:c,payload:r,chartType:s,hide:f})};function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}function Lm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lm(Object(r),!0).forEach(function(n){Jx(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Jx(e,t,r){return t=gM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gM(e){var t=bM(e,"string");return Ln(t)=="symbol"?t:t+""}function bM(e,t){if(Ln(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ln(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function xM(e,t){return AM(e)||_M(e,t)||OM(e,t)||wM()}function wM(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function OM(e,t){if(e){if(typeof e=="string")return Bm(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Bm(e,t)}}function Bm(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _M(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function AM(e){if(Array.isArray(e))return e}var ra=Math.PI/180,SM=function(t){return t*180/Math.PI},le=function(t,r,n,i){return{x:t+Math.cos(-ra*i)*n,y:r+Math.sin(-ra*i)*n}},Qx=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},PM=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.startAngle,s=t.endAngle,f=Ie(t.cx,o,o/2),l=Ie(t.cy,u,u/2),h=Qx(o,u,n),p=Ie(t.innerRadius,h,0),y=Ie(t.outerRadius,h,h*.8),v=Object.keys(r);return v.reduce(function(d,g){var x=r[g],w=x.domain,O=x.reversed,m;if(Y(x.range))i==="angleAxis"?m=[c,s]:i==="radiusAxis"&&(m=[p,y]),O&&(m=[m[1],m[0]]);else{m=x.range;var b=m,_=xM(b,2);c=_[0],s=_[1]}var A=Gx(x,a),T=A.realScaleType,M=A.scale;M.domain(w).range(m),Vx(M);var P=Xx(M,vt(vt({},x),{},{realScaleType:T})),E=vt(vt(vt({},x),P),{},{range:m,radius:y,realScaleType:T,scale:M,cx:f,cy:l,innerRadius:p,outerRadius:y,startAngle:c,endAngle:s});return vt(vt({},d),{},Jx({},g,E))},{})},TM=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},EM=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=TM({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,s=Math.acos(c);return i>o&&(s=2*Math.PI-s),{radius:u,angle:SM(s),angleInRadian:s}},jM=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},MM=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},Fm=function(t,r){var n=t.x,i=t.y,a=EM({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,s=r.outerRadius;if(o<c||o>s)return!1;if(o===0)return!0;var f=jM(r),l=f.startAngle,h=f.endAngle,p=u,y;if(l<=h){for(;p>h;)p-=360;for(;p<l;)p+=360;y=p>=l&&p<=h}else{for(;p>l;)p-=360;for(;p<h;)p+=360;y=p>=h&&p<=l}return y?vt(vt({},r),{},{radius:o,angle:MM(p,r)}):null},ew=function(t){return!q.isValidElement(t)&&!X(t)&&typeof t!="boolean"?t.className:""};function Bn(e){"@babel/helpers - typeof";return Bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bn(e)}var $M=["offset"];function CM(e){return DM(e)||RM(e)||kM(e)||IM()}function IM(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kM(e,t){if(e){if(typeof e=="string")return Xl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xl(e,t)}}function RM(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function DM(e){if(Array.isArray(e))return Xl(e)}function Xl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function NM(e,t){if(e==null)return{};var r=qM(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function qM(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Wm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function we(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wm(Object(r),!0).forEach(function(n){LM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function LM(e,t,r){return t=BM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function BM(e){var t=FM(e,"string");return Bn(t)=="symbol"?t:t+""}function FM(e,t){if(Bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Fn(){return Fn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Fn.apply(this,arguments)}var WM=function(t){var r=t.value,n=t.formatter,i=Y(t.children)?r:t.children;return X(n)?n(i):i},zM=function(t,r){var n=Ce(r-t),i=Math.min(Math.abs(r-t),360);return n*i},UM=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,s=c.cx,f=c.cy,l=c.innerRadius,h=c.outerRadius,p=c.startAngle,y=c.endAngle,v=c.clockWise,d=(l+h)/2,g=zM(p,y),x=g>=0?1:-1,w,O;i==="insideStart"?(w=p+x*o,O=v):i==="insideEnd"?(w=y-x*o,O=!v):i==="end"&&(w=y+x*o,O=v),O=g<=0?O:!O;var m=le(s,f,d,w),b=le(s,f,d,w+(O?1:-1)*359),_="M".concat(m.x,",").concat(m.y,`
    A`).concat(d,",").concat(d,",0,1,").concat(O?0:1,`,
    `).concat(b.x,",").concat(b.y),A=Y(t.id)?Zr("recharts-radial-line-"):t.id;return S.createElement("text",Fn({},n,{dominantBaseline:"central",className:J("recharts-radial-bar-label",u)}),S.createElement("defs",null,S.createElement("path",{id:A,d:_})),S.createElement("textPath",{xlinkHref:"#".concat(A)},r))},HM=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,s=a.outerRadius,f=a.startAngle,l=a.endAngle,h=(f+l)/2;if(i==="outside"){var p=le(o,u,s+n,h),y=p.x,v=p.y;return{x:y,y:v,textAnchor:y>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var d=(c+s)/2,g=le(o,u,d,h),x=g.x,w=g.y;return{x,y:w,textAnchor:"middle",verticalAnchor:"middle"}},KM=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,s=o.width,f=o.height,l=f>=0?1:-1,h=l*i,p=l>0?"end":"start",y=l>0?"start":"end",v=s>=0?1:-1,d=v*i,g=v>0?"end":"start",x=v>0?"start":"end";if(a==="top"){var w={x:u+s/2,y:c-l*i,textAnchor:"middle",verticalAnchor:p};return we(we({},w),n?{height:Math.max(c-n.y,0),width:s}:{})}if(a==="bottom"){var O={x:u+s/2,y:c+f+h,textAnchor:"middle",verticalAnchor:y};return we(we({},O),n?{height:Math.max(n.y+n.height-(c+f),0),width:s}:{})}if(a==="left"){var m={x:u-d,y:c+f/2,textAnchor:g,verticalAnchor:"middle"};return we(we({},m),n?{width:Math.max(m.x-n.x,0),height:f}:{})}if(a==="right"){var b={x:u+s+d,y:c+f/2,textAnchor:x,verticalAnchor:"middle"};return we(we({},b),n?{width:Math.max(n.x+n.width-b.x,0),height:f}:{})}var _=n?{width:s,height:f}:{};return a==="insideLeft"?we({x:u+d,y:c+f/2,textAnchor:x,verticalAnchor:"middle"},_):a==="insideRight"?we({x:u+s-d,y:c+f/2,textAnchor:g,verticalAnchor:"middle"},_):a==="insideTop"?we({x:u+s/2,y:c+h,textAnchor:"middle",verticalAnchor:y},_):a==="insideBottom"?we({x:u+s/2,y:c+f-h,textAnchor:"middle",verticalAnchor:p},_):a==="insideTopLeft"?we({x:u+d,y:c+h,textAnchor:x,verticalAnchor:y},_):a==="insideTopRight"?we({x:u+s-d,y:c+h,textAnchor:g,verticalAnchor:y},_):a==="insideBottomLeft"?we({x:u+d,y:c+f-h,textAnchor:x,verticalAnchor:p},_):a==="insideBottomRight"?we({x:u+s-d,y:c+f-h,textAnchor:g,verticalAnchor:p},_):Yr(a)&&(N(a.x)||Gt(a.x))&&(N(a.y)||Gt(a.y))?we({x:u+Ie(a.x,s),y:c+Ie(a.y,f),textAnchor:"end",verticalAnchor:"end"},_):we({x:u+s/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},_)},GM=function(t){return"cx"in t&&N(t.cx)};function Pe(e){var t=e.offset,r=t===void 0?5:t,n=NM(e,$M),i=we({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,s=i.content,f=i.className,l=f===void 0?"":f,h=i.textBreakAll;if(!a||Y(u)&&Y(c)&&!q.isValidElement(s)&&!X(s))return null;if(q.isValidElement(s))return q.cloneElement(s,i);var p;if(X(s)){if(p=q.createElement(s,i),q.isValidElement(p))return p}else p=WM(i);var y=GM(a),v=H(i,!0);if(y&&(o==="insideStart"||o==="insideEnd"||o==="end"))return UM(i,p,v);var d=y?HM(i):KM(i);return S.createElement(tr,Fn({className:J("recharts-label",l)},v,d,{breakAll:h}),p)}Pe.displayName="Label";var tw=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,s=t.innerRadius,f=t.outerRadius,l=t.x,h=t.y,p=t.top,y=t.left,v=t.width,d=t.height,g=t.clockWise,x=t.labelViewBox;if(x)return x;if(N(v)&&N(d)){if(N(l)&&N(h))return{x:l,y:h,width:v,height:d};if(N(p)&&N(y))return{x:p,y,width:v,height:d}}return N(l)&&N(h)?{x:l,y:h,width:0,height:0}:N(r)&&N(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:s||0,outerRadius:f||c||u||0,clockWise:g}:t.viewBox?t.viewBox:{}},VM=function(t,r){return t?t===!0?S.createElement(Pe,{key:"label-implicit",viewBox:r}):_e(t)?S.createElement(Pe,{key:"label-implicit",viewBox:r,value:t}):q.isValidElement(t)?t.type===Pe?q.cloneElement(t,{key:"label-implicit",viewBox:r}):S.createElement(Pe,{key:"label-implicit",content:t,viewBox:r}):X(t)?S.createElement(Pe,{key:"label-implicit",content:t,viewBox:r}):Yr(t)?S.createElement(Pe,Fn({viewBox:r},t,{key:"label-implicit"})):null:null},XM=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=tw(t),o=Ke(i,Pe).map(function(c,s){return q.cloneElement(c,{viewBox:r||a,key:"label-".concat(s)})});if(!n)return o;var u=VM(t.label,r||a);return[u].concat(CM(o))};Pe.parseViewBox=tw;Pe.renderCallByParent=XM;var Ns,zm;function YM(){if(zm)return Ns;zm=1;function e(t){var r=t==null?0:t.length;return r?t[r-1]:void 0}return Ns=e,Ns}var ZM=YM();const JM=oe(ZM);function Wn(e){"@babel/helpers - typeof";return Wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wn(e)}var QM=["valueAccessor"],e$=["data","dataKey","clockWise","id","textBreakAll"];function t$(e){return a$(e)||i$(e)||n$(e)||r$()}function r$(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function n$(e,t){if(e){if(typeof e=="string")return Yl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Yl(e,t)}}function i$(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function a$(e){if(Array.isArray(e))return Yl(e)}function Yl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function na(){return na=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},na.apply(this,arguments)}function Um(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Hm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Um(Object(r),!0).forEach(function(n){o$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Um(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function o$(e,t,r){return t=u$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u$(e){var t=c$(e,"string");return Wn(t)=="symbol"?t:t+""}function c$(e,t){if(Wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Km(e,t){if(e==null)return{};var r=s$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function s$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var l$=function(t){return Array.isArray(t.value)?JM(t.value):t.value};function wt(e){var t=e.valueAccessor,r=t===void 0?l$:t,n=Km(e,QM),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,s=Km(n,e$);return!i||!i.length?null:S.createElement(te,{className:"recharts-label-list"},i.map(function(f,l){var h=Y(a)?r(f,l):Oe(f&&f.payload,a),p=Y(u)?{}:{id:"".concat(u,"-").concat(l)};return S.createElement(Pe,na({},H(f,!0),s,p,{parentViewBox:f.parentViewBox,value:h,textBreakAll:c,viewBox:Pe.parseViewBox(Y(o)?f:Hm(Hm({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}wt.displayName="LabelList";function f$(e,t){return e?e===!0?S.createElement(wt,{key:"labelList-implicit",data:t}):S.isValidElement(e)||X(e)?S.createElement(wt,{key:"labelList-implicit",data:t,content:e}):Yr(e)?S.createElement(wt,na({data:t},e,{key:"labelList-implicit"})):null:null}function h$(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Ke(n,wt).map(function(o,u){return q.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=f$(e.label,t);return[a].concat(t$(i))}wt.renderCallByParent=h$;function zn(e){"@babel/helpers - typeof";return zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(e)}function Zl(){return Zl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zl.apply(this,arguments)}function Gm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Vm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gm(Object(r),!0).forEach(function(n){p$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function p$(e,t,r){return t=d$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d$(e){var t=v$(e,"string");return zn(t)=="symbol"?t:t+""}function v$(e,t){if(zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var y$=function(t,r){var n=Ce(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},Ai=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,f=c*(u?1:-1)+i,l=Math.asin(c/f)/ra,h=s?a:a+o*l,p=le(r,n,f,h),y=le(r,n,i,h),v=s?a-o*l:a,d=le(r,n,f*Math.cos(l*ra),v);return{center:p,circleTangency:y,lineTangency:d,theta:l}},rw=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=y$(o,u),s=o+c,f=le(r,n,a,o),l=le(r,n,a,s),h="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>s),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var p=le(r,n,i,o),y=le(r,n,i,s);h+="L ".concat(y.x,",").concat(y.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=s),`,
            `).concat(p.x,",").concat(p.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},m$=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,f=t.endAngle,l=Ce(f-s),h=Ai({cx:r,cy:n,radius:a,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),p=h.circleTangency,y=h.lineTangency,v=h.theta,d=Ai({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),g=d.circleTangency,x=d.lineTangency,w=d.theta,O=c?Math.abs(s-f):Math.abs(s-f)-v-w;if(O<0)return u?"M ".concat(y.x,",").concat(y.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):rw({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f});var m="M ".concat(y.x,",").concat(y.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(p.x,",").concat(p.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(O>180),",").concat(+(l<0),",").concat(g.x,",").concat(g.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(x.x,",").concat(x.y,`
  `);if(i>0){var b=Ai({cx:r,cy:n,radius:i,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),_=b.circleTangency,A=b.lineTangency,T=b.theta,M=Ai({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),P=M.circleTangency,E=M.lineTangency,j=M.theta,C=c?Math.abs(s-f):Math.abs(s-f)-T-j;if(C<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(E.x,",").concat(E.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(P.x,",").concat(P.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(C>180),",").concat(+(l>0),",").concat(_.x,",").concat(_.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},g$={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},nw=function(t){var r=Vm(Vm({},g$),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,h=r.className;if(o<a||f===l)return null;var p=J("recharts-sector",h),y=o-a,v=Ie(u,y,0,!0),d;return v>0&&Math.abs(f-l)<360?d=m$({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,y/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:l}):d=rw({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),S.createElement("path",Zl({},H(r,!0),{className:p,d,role:"img"}))};function Un(e){"@babel/helpers - typeof";return Un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(e)}function Jl(){return Jl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Jl.apply(this,arguments)}function Xm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ym(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xm(Object(r),!0).forEach(function(n){b$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function b$(e,t,r){return t=x$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function x$(e){var t=w$(e,"string");return Un(t)=="symbol"?t:t+""}function w$(e,t){if(Un(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Zm={curveBasisClosed:F_,curveBasisOpen:W_,curveBasis:B_,curveBumpX:P_,curveBumpY:T_,curveLinearClosed:z_,curveLinear:Ra,curveMonotoneX:U_,curveMonotoneY:H_,curveNatural:K_,curveStep:G_,curveStepAfter:X_,curveStepBefore:V_},Si=function(t){return t.x===+t.x&&t.y===+t.y},sn=function(t){return t.x},ln=function(t){return t.y},O$=function(t,r){if(X(t))return t;var n="curve".concat(Ia(t));return(n==="curveMonotone"||n==="curveBump")&&r?Zm["".concat(n).concat(r==="vertical"?"Y":"X")]:Zm[n]||Ra},_$=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,s=c===void 0?!1:c,f=O$(n,u),l=s?a.filter(function(v){return Si(v)}):a,h;if(Array.isArray(o)){var p=s?o.filter(function(v){return Si(v)}):o,y=l.map(function(v,d){return Ym(Ym({},v),{},{base:p[d]})});return u==="vertical"?h=yi().y(ln).x1(sn).x0(function(v){return v.base.x}):h=yi().x(sn).y1(ln).y0(function(v){return v.base.y}),h.defined(Si).curve(f),h(y)}return u==="vertical"&&N(o)?h=yi().y(ln).x1(sn).x0(o):N(o)?h=yi().x(sn).y1(ln).y0(o):h=t0().x(sn).y(ln),h.defined(Si).curve(f),h(l)},ia=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?_$(t):i;return S.createElement("path",Jl({},H(t,!1),$i(t),{className:J("recharts-curve",r),d:o,ref:a}))},qs={exports:{}},Ls,Jm;function A$(){if(Jm)return Ls;Jm=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return Ls=e,Ls}var Bs,Qm;function S$(){if(Qm)return Bs;Qm=1;var e=A$();function t(){}function r(){}return r.resetWarningCache=t,Bs=function(){function n(o,u,c,s,f,l){if(l!==e){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}n.isRequired=n;function i(){return n}var a={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:i,element:n,elementType:n,instanceOf:i,node:n,objectOf:i,oneOf:i,oneOfType:i,shape:i,exact:i,checkPropTypes:r,resetWarningCache:t};return a.PropTypes=a,a},Bs}var eg;function P$(){return eg||(eg=1,qs.exports=S$()()),qs.exports}var T$=P$();const ie=oe(T$);var E$=Object.getOwnPropertyNames,j$=Object.getOwnPropertySymbols,M$=Object.prototype.hasOwnProperty;function tg(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function Pi(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function rg(e){return E$(e).concat(j$(e))}var $$=Object.hasOwn||function(e,t){return M$.call(e,t)};function sr(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var C$="__v",I$="__o",k$="_owner",ng=Object.getOwnPropertyDescriptor,ig=Object.keys;function R$(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function D$(e,t){return sr(e.getTime(),t.getTime())}function N$(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function q$(e,t){return e===t}function ag(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,c=0;(o=a.next())&&!o.done;){for(var s=t.entries(),f=!1,l=0;(u=s.next())&&!u.done;){if(i[l]){l++;continue}var h=o.value,p=u.value;if(r.equals(h[0],p[0],c,l,e,t,r)&&r.equals(h[1],p[1],h[0],p[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;c++}return!0}var L$=sr;function B$(e,t,r){var n=ig(e),i=n.length;if(ig(t).length!==i)return!1;for(;i-- >0;)if(!iw(e,t,r,n[i]))return!1;return!0}function fn(e,t,r){var n=rg(e),i=n.length;if(rg(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!iw(e,t,r,a)||(o=ng(e,a),u=ng(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function F$(e,t){return sr(e.valueOf(),t.valueOf())}function W$(e,t){return e.source===t.source&&e.flags===t.flags}function og(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var c=t.values(),s=!1,f=0;(u=c.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){s=i[f]=!0;break}f++}if(!s)return!1}return!0}function z$(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function U$(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function iw(e,t,r,n){return(n===k$||n===I$||n===C$)&&(e.$$typeof||t.$$typeof)?!0:$$(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var H$="[object Arguments]",K$="[object Boolean]",G$="[object Date]",V$="[object Error]",X$="[object Map]",Y$="[object Number]",Z$="[object Object]",J$="[object RegExp]",Q$="[object Set]",eC="[object String]",tC="[object URL]",rC=Array.isArray,ug=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,cg=Object.assign,nC=Object.prototype.toString.call.bind(Object.prototype.toString);function iC(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,h=e.areUrlsEqual;return function(y,v,d){if(y===v)return!0;if(y==null||v==null)return!1;var g=typeof y;if(g!==typeof v)return!1;if(g!=="object")return g==="number"?o(y,v,d):g==="function"?i(y,v,d):!1;var x=y.constructor;if(x!==v.constructor)return!1;if(x===Object)return u(y,v,d);if(rC(y))return t(y,v,d);if(ug!=null&&ug(y))return l(y,v,d);if(x===Date)return r(y,v,d);if(x===RegExp)return s(y,v,d);if(x===Map)return a(y,v,d);if(x===Set)return f(y,v,d);var w=nC(y);return w===G$?r(y,v,d):w===J$?s(y,v,d):w===X$?a(y,v,d):w===Q$?f(y,v,d):w===Z$?typeof y.then!="function"&&typeof v.then!="function"&&u(y,v,d):w===tC?h(y,v,d):w===V$?n(y,v,d):w===H$?u(y,v,d):w===K$||w===Y$||w===eC?c(y,v,d):!1}}function aC(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?fn:R$,areDatesEqual:D$,areErrorsEqual:N$,areFunctionsEqual:q$,areMapsEqual:n?tg(ag,fn):ag,areNumbersEqual:L$,areObjectsEqual:n?fn:B$,arePrimitiveWrappersEqual:F$,areRegExpsEqual:W$,areSetsEqual:n?tg(og,fn):og,areTypedArraysEqual:n?fn:z$,areUrlsEqual:U$};if(r&&(i=cg({},i,r(i))),t){var a=Pi(i.areArraysEqual),o=Pi(i.areMapsEqual),u=Pi(i.areObjectsEqual),c=Pi(i.areSetsEqual);i=cg({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function oC(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function uC(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,s){var f=n(),l=f.cache,h=l===void 0?t?new WeakMap:void 0:l,p=f.meta;return r(c,s,{cache:h,equals:i,meta:p,strict:a})};if(t)return function(c,s){return r(c,s,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,s){return r(c,s,o)}}var cC=Dt();Dt({strict:!0});Dt({circular:!0});Dt({circular:!0,strict:!0});Dt({createInternalComparator:function(){return sr}});Dt({strict:!0,createInternalComparator:function(){return sr}});Dt({circular:!0,createInternalComparator:function(){return sr}});Dt({circular:!0,createInternalComparator:function(){return sr},strict:!0});function Dt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=aC(e),c=iC(u),s=n?n(c):oC(c);return uC({circular:r,comparator:c,createState:i,equals:s,strict:o})}function sC(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function sg(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):sC(i)};requestAnimationFrame(n)}function Ql(e){"@babel/helpers - typeof";return Ql=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ql(e)}function lC(e){return dC(e)||pC(e)||hC(e)||fC()}function fC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function hC(e,t){if(e){if(typeof e=="string")return lg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lg(e,t)}}function lg(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function pC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function dC(e){if(Array.isArray(e))return e}function vC(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=lC(o),c=u[0],s=u.slice(1);if(typeof c=="number"){sg(i.bind(null,s),c);return}i(c),sg(i.bind(null,s));return}Ql(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function Hn(e){"@babel/helpers - typeof";return Hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hn(e)}function fg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?fg(Object(r),!0).forEach(function(n){aw(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function aw(e,t,r){return t=yC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yC(e){var t=mC(e,"string");return Hn(t)==="symbol"?t:String(t)}function mC(e,t){if(Hn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Hn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var gC=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},bC=function(t){return t},xC=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},bn=function(t,r){return Object.keys(r).reduce(function(n,i){return hg(hg({},n),{},aw({},i,t(i,r[i])))},{})},pg=function(t,r,n){return t.map(function(i){return"".concat(xC(i)," ").concat(r,"ms ").concat(n)}).join(",")};function wC(e,t){return AC(e)||_C(e,t)||ow(e,t)||OC()}function OC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _C(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function AC(e){if(Array.isArray(e))return e}function SC(e){return EC(e)||TC(e)||ow(e)||PC()}function PC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ow(e,t){if(e){if(typeof e=="string")return ef(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ef(e,t)}}function TC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function EC(e){if(Array.isArray(e))return ef(e)}function ef(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var aa=1e-4,uw=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},cw=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},dg=function(t,r){return function(n){var i=uw(t,r);return cw(i,n)}},jC=function(t,r){return function(n){var i=uw(t,r),a=[].concat(SC(i.map(function(o,u){return o*u}).slice(1)),[0]);return cw(a,n)}},vg=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var s=c[1].split(")")[0].split(",").map(function(d){return parseFloat(d)}),f=wC(s,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var l=dg(i,o),h=dg(a,u),p=jC(i,o),y=function(g){return g>1?1:g<0?0:g},v=function(g){for(var x=g>1?1:g,w=x,O=0;O<8;++O){var m=l(w)-x,b=p(w);if(Math.abs(m-x)<aa||b<aa)return h(w);w=y(w-m/b)}return h(w)};return v.isStepper=!1,v},MC=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,l,h){var p=-(f-l)*n,y=h*a,v=h+(p-y)*u/1e3,d=h*u/1e3+f;return Math.abs(d-l)<aa&&Math.abs(v)<aa?[l,0]:[d,v]};return c.isStepper=!0,c.dt=u,c},$C=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return vg(i);case"spring":return MC();default:if(i.split("(")[0]==="cubic-bezier")return vg(i)}return typeof i=="function"?i:null};function Kn(e){"@babel/helpers - typeof";return Kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kn(e)}function yg(e){return kC(e)||IC(e)||sw(e)||CC()}function CC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function IC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function kC(e){if(Array.isArray(e))return rf(e)}function mg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ee(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?mg(Object(r),!0).forEach(function(n){tf(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function tf(e,t,r){return t=RC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function RC(e){var t=DC(e,"string");return Kn(t)==="symbol"?t:String(t)}function DC(e,t){if(Kn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function NC(e,t){return BC(e)||LC(e,t)||sw(e,t)||qC()}function qC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function sw(e,t){if(e){if(typeof e=="string")return rf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rf(e,t)}}function rf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function LC(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function BC(e){if(Array.isArray(e))return e}var oa=function(t,r,n){return t+(r-t)*n},nf=function(t){var r=t.from,n=t.to;return r!==n},FC=function e(t,r,n){var i=bn(function(a,o){if(nf(o)){var u=t(o.from,o.to,o.velocity),c=NC(u,2),s=c[0],f=c[1];return Ee(Ee({},o),{},{from:s,velocity:f})}return o},r);return n<1?bn(function(a,o){return nf(o)?Ee(Ee({},o),{},{velocity:oa(o.velocity,i[a].velocity,n),from:oa(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const WC=function(e,t,r,n,i){var a=gC(e,t),o=a.reduce(function(d,g){return Ee(Ee({},d),{},tf({},g,[e[g],t[g]]))},{}),u=a.reduce(function(d,g){return Ee(Ee({},d),{},tf({},g,{from:e[g],velocity:0,to:t[g]}))},{}),c=-1,s,f,l=function(){return null},h=function(){return bn(function(g,x){return x.from},u)},p=function(){return!Object.values(u).filter(nf).length},y=function(g){s||(s=g);var x=g-s,w=x/r.dt;u=FC(r,u,w),i(Ee(Ee(Ee({},e),t),h())),s=g,p()||(c=requestAnimationFrame(l))},v=function(g){f||(f=g);var x=(g-f)/n,w=bn(function(m,b){return oa.apply(void 0,yg(b).concat([r(x)]))},o);if(i(Ee(Ee(Ee({},e),t),w)),x<1)c=requestAnimationFrame(l);else{var O=bn(function(m,b){return oa.apply(void 0,yg(b).concat([r(1)]))},o);i(Ee(Ee(Ee({},e),t),O))}};return l=r.isStepper?y:v,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}};function Ir(e){"@babel/helpers - typeof";return Ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ir(e)}var zC=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function UC(e,t){if(e==null)return{};var r=HC(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function HC(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Fs(e){return XC(e)||VC(e)||GC(e)||KC()}function KC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function GC(e,t){if(e){if(typeof e=="string")return af(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return af(e,t)}}function VC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function XC(e){if(Array.isArray(e))return af(e)}function af(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function gg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function et(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gg(Object(r),!0).forEach(function(n){vn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vn(e,t,r){return t=lw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function YC(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ZC(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,lw(n.key),n)}}function JC(e,t,r){return t&&ZC(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function lw(e){var t=QC(e,"string");return Ir(t)==="symbol"?t:String(t)}function QC(e,t){if(Ir(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ir(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function eI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&of(e,t)}function of(e,t){return of=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},of(e,t)}function tI(e){var t=rI();return function(){var n=ua(e),i;if(t){var a=ua(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return uf(this,i)}}function uf(e,t){if(t&&(Ir(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cf(e)}function cf(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function rI(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ua(e){return ua=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ua(e)}var lt=function(e){eI(r,e);var t=tI(r);function r(n,i){var a;YC(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,s=o.from,f=o.to,l=o.steps,h=o.children,p=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(cf(a)),a.changeStyle=a.changeStyle.bind(cf(a)),!u||p<=0)return a.state={style:{}},typeof h=="function"&&(a.state={style:f}),uf(a);if(l&&l.length)a.state={style:l[0].style};else if(s){if(typeof h=="function")return a.state={style:s},uf(a);a.state={style:c?vn({},c,s):s}}else a.state={style:{}};return a}return JC(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,s=a.shouldReAnimate,f=a.to,l=a.from,h=this.state.style;if(u){if(!o){var p={style:c?vn({},c,f):f};this.state&&h&&(c&&h[c]!==f||!c&&h!==f)&&this.setState(p);return}if(!(cC(i.to,f)&&i.canBegin&&i.isActive)){var y=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=y||s?l:i.to;if(this.state&&h){var d={style:c?vn({},c,v):v};(c&&h[c]!==v||!c&&h!==v)&&this.setState(d)}this.runAnimation(et(et({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,s=i.easing,f=i.begin,l=i.onAnimationEnd,h=i.onAnimationStart,p=WC(o,u,$C(s),c,this.changeStyle),y=function(){a.stopJSAnimation=p()};this.manager.start([h,f,y,c,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,s=o[0],f=s.style,l=s.duration,h=l===void 0?0:l,p=function(v,d,g){if(g===0)return v;var x=d.duration,w=d.easing,O=w===void 0?"ease":w,m=d.style,b=d.properties,_=d.onAnimationEnd,A=g>0?o[g-1]:d,T=b||Object.keys(m);if(typeof O=="function"||O==="spring")return[].concat(Fs(v),[a.runJSAnimation.bind(a,{from:A.style,to:m,duration:x,easing:O}),x]);var M=pg(T,x,O),P=et(et(et({},A.style),m),{},{transition:M});return[].concat(Fs(v),[P,x,_]).filter(bC)};return this.manager.start([c].concat(Fs(o.reduce(p,[f,Math.max(h,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=vC());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,s=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,h=i.steps,p=i.children,y=this.manager;if(this.unSubscribe=y.subscribe(this.handleStyleChange),typeof s=="function"||typeof p=="function"||s==="spring"){this.runJSAnimation(i);return}if(h.length>1){this.runStepAnimation(i);return}var v=u?vn({},u,c):c,d=pg(Object.keys(v),o,s);y.start([f,a,et(et({},v),{},{transition:d}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=UC(i,zC),s=q.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||s===0||o<=0)return a;var l=function(p){var y=p.props,v=y.style,d=v===void 0?{}:v,g=y.className,x=q.cloneElement(p,et(et({},c),{},{style:et(et({},d),f),className:g}));return x};return s===1?l(q.Children.only(a)):S.createElement("div",null,q.Children.map(a,function(h){return l(h)}))}}]),r}(q.PureComponent);lt.displayName="Animate";lt.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};lt.propTypes={from:ie.oneOfType([ie.object,ie.string]),to:ie.oneOfType([ie.object,ie.string]),attributeName:ie.string,duration:ie.number,begin:ie.number,easing:ie.oneOfType([ie.string,ie.func]),steps:ie.arrayOf(ie.shape({duration:ie.number.isRequired,style:ie.object.isRequired,easing:ie.oneOfType([ie.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ie.func]),properties:ie.arrayOf("string"),onAnimationEnd:ie.func})),children:ie.oneOfType([ie.node,ie.func]),isActive:ie.bool,canBegin:ie.bool,onAnimationEnd:ie.func,shouldReAnimate:ie.bool,onAnimationStart:ie.func,onAnimationReStart:ie.func};function Gn(e){"@babel/helpers - typeof";return Gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gn(e)}function ca(){return ca=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ca.apply(this,arguments)}function nI(e,t){return uI(e)||oI(e,t)||aI(e,t)||iI()}function iI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function aI(e,t){if(e){if(typeof e=="string")return bg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return bg(e,t)}}function bg(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function oI(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function uI(e){if(Array.isArray(e))return e}function xg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function wg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xg(Object(r),!0).forEach(function(n){cI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cI(e,t,r){return t=sI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sI(e){var t=lI(e,"string");return Gn(t)=="symbol"?t:t+""}function lI(e,t){if(Gn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Gn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Og=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,s=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],h=0,p=4;h<p;h++)l[h]=a[h]>o?o:a[h];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,`,
        `).concat(t+n-c*l[2],",").concat(r+i)),f+="L ".concat(t+c*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var y=Math.min(o,a);f="M ".concat(t,",").concat(r+u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+c*y,",").concat(r,`
            L `).concat(t+n-c*y,",").concat(r,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n,",").concat(r+u*y,`
            L `).concat(t+n,",").concat(r+i-u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n-c*y,",").concat(r+i,`
            L `).concat(t+c*y,",").concat(r+i,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t,",").concat(r+i-u*y," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},fI=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var s=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+c),h=Math.max(o,o+c);return n>=s&&n<=f&&i>=l&&i<=h}return!1},hI={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Mh=function(t){var r=wg(wg({},hI),t),n=q.useRef(),i=q.useState(-1),a=nI(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var O=n.current.getTotalLength();O&&u(O)}catch{}},[]);var c=r.x,s=r.y,f=r.width,l=r.height,h=r.radius,p=r.className,y=r.animationEasing,v=r.animationDuration,d=r.animationBegin,g=r.isAnimationActive,x=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||f===0||l===0)return null;var w=J("recharts-rectangle",p);return x?S.createElement(lt,{canBegin:o>0,from:{width:f,height:l,x:c,y:s},to:{width:f,height:l,x:c,y:s},duration:v,animationEasing:y,isActive:x},function(O){var m=O.width,b=O.height,_=O.x,A=O.y;return S.createElement(lt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:d,duration:v,isActive:g,easing:y},S.createElement("path",ca({},H(r,!0),{className:w,d:Og(_,A,m,b,h),ref:n})))}):S.createElement("path",ca({},H(r,!0),{className:w,d:Og(c,s,f,l,h)}))},pI=["points","className","baseLinePoints","connectNulls"];function yr(){return yr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},yr.apply(this,arguments)}function dI(e,t){if(e==null)return{};var r=vI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function vI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function _g(e){return bI(e)||gI(e)||mI(e)||yI()}function yI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mI(e,t){if(e){if(typeof e=="string")return sf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sf(e,t)}}function gI(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function bI(e){if(Array.isArray(e))return sf(e)}function sf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Ag=function(t){return t&&t.x===+t.x&&t.y===+t.y},xI=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=[[]];return t.forEach(function(n){Ag(n)?r[r.length-1].push(n):r[r.length-1].length>0&&r.push([])}),Ag(t[0])&&r[r.length-1].push(t[0]),r[r.length-1].length<=0&&(r=r.slice(0,-1)),r},xn=function(t,r){var n=xI(t);r&&(n=[n.reduce(function(a,o){return[].concat(_g(a),_g(o))},[])]);var i=n.map(function(a){return a.reduce(function(o,u,c){return"".concat(o).concat(c===0?"M":"L").concat(u.x,",").concat(u.y)},"")}).join("");return n.length===1?"".concat(i,"Z"):i},wI=function(t,r,n){var i=xn(t,n);return"".concat(i.slice(-1)==="Z"?i.slice(0,-1):i,"L").concat(xn(r.reverse(),n).slice(1))},OI=function(t){var r=t.points,n=t.className,i=t.baseLinePoints,a=t.connectNulls,o=dI(t,pI);if(!r||!r.length)return null;var u=J("recharts-polygon",n);if(i&&i.length){var c=o.stroke&&o.stroke!=="none",s=wI(r,i,a);return S.createElement("g",{className:u},S.createElement("path",yr({},H(o,!0),{fill:s.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:s})),c?S.createElement("path",yr({},H(o,!0),{fill:"none",d:xn(r,a)})):null,c?S.createElement("path",yr({},H(o,!0),{fill:"none",d:xn(i,a)})):null)}var f=xn(r,a);return S.createElement("path",yr({},H(o,!0),{fill:f.slice(-1)==="Z"?o.fill:"none",className:u,d:f}))};function lf(){return lf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},lf.apply(this,arguments)}var Za=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=J("recharts-dot",a);return r===+r&&n===+n&&i===+i?S.createElement("circle",lf({},H(t,!1),$i(t),{className:o,cx:r,cy:n,r:i})):null};function Vn(e){"@babel/helpers - typeof";return Vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vn(e)}var _I=["x","y","top","left","width","height","className"];function ff(){return ff=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ff.apply(this,arguments)}function Sg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function AI(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sg(Object(r),!0).forEach(function(n){SI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function SI(e,t,r){return t=PI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function PI(e){var t=TI(e,"string");return Vn(t)=="symbol"?t:t+""}function TI(e,t){if(Vn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Vn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function EI(e,t){if(e==null)return{};var r=jI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function jI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var MI=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},$I=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,s=c===void 0?0:c,f=t.width,l=f===void 0?0:f,h=t.height,p=h===void 0?0:h,y=t.className,v=EI(t,_I),d=AI({x:n,y:a,top:u,left:s,width:l,height:p},v);return!N(n)||!N(a)||!N(l)||!N(p)||!N(u)||!N(s)?null:S.createElement("path",ff({},H(d,!0),{className:J("recharts-cross",y),d:MI(n,a,l,p,u,s)}))},Ws,Pg;function CI(){if(Pg)return Ws;Pg=1;var e=Ka(),t=Sx(),r=ht();function n(i,a){return i&&i.length?e(i,r(a,2),t):void 0}return Ws=n,Ws}var II=CI();const kI=oe(II);var zs,Tg;function RI(){if(Tg)return zs;Tg=1;var e=Ka(),t=ht(),r=Px();function n(i,a){return i&&i.length?e(i,t(a,2),r):void 0}return zs=n,zs}var DI=RI();const NI=oe(DI);var qI=["cx","cy","angle","ticks","axisLine"],LI=["ticks","tick","angle","tickFormatter","stroke"];function kr(e){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kr(e)}function wn(){return wn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},wn.apply(this,arguments)}function Eg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Wt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Eg(Object(r),!0).forEach(function(n){Ja(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Eg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function jg(e,t){if(e==null)return{};var r=BI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function BI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function FI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Mg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hw(n.key),n)}}function WI(e,t,r){return t&&Mg(e.prototype,t),r&&Mg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function zI(e,t,r){return t=sa(t),UI(e,fw()?Reflect.construct(t,r||[],sa(e).constructor):t.apply(e,r))}function UI(e,t){if(t&&(kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return HI(e)}function HI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function fw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(fw=function(){return!!e})()}function sa(e){return sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},sa(e)}function KI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&hf(e,t)}function hf(e,t){return hf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},hf(e,t)}function Ja(e,t,r){return t=hw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hw(e){var t=GI(e,"string");return kr(t)=="symbol"?t:t+""}function GI(e,t){if(kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Qa=function(e){function t(){return FI(this,t),zI(this,t,arguments)}return KI(t,e),WI(t,[{key:"getTickValueCoord",value:function(n){var i=n.coordinate,a=this.props,o=a.angle,u=a.cx,c=a.cy;return le(u,c,i,o)}},{key:"getTickTextAnchor",value:function(){var n=this.props.orientation,i;switch(n){case"left":i="end";break;case"right":i="start";break;default:i="middle";break}return i}},{key:"getViewBox",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=kI(u,function(f){return f.coordinate||0}),s=NI(u,function(f){return f.coordinate||0});return{cx:i,cy:a,startAngle:o,endAngle:o,innerRadius:s.coordinate||0,outerRadius:c.coordinate||0}}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=n.axisLine,s=jg(n,qI),f=u.reduce(function(y,v){return[Math.min(y[0],v.coordinate),Math.max(y[1],v.coordinate)]},[1/0,-1/0]),l=le(i,a,f[0],o),h=le(i,a,f[1],o),p=Wt(Wt(Wt({},H(s,!1)),{},{fill:"none"},H(c,!1)),{},{x1:l.x,y1:l.y,x2:h.x,y2:h.y});return S.createElement("line",wn({className:"recharts-polar-radius-axis-line"},p))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.angle,c=i.tickFormatter,s=i.stroke,f=jg(i,LI),l=this.getTickTextAnchor(),h=H(f,!1),p=H(o,!1),y=a.map(function(v,d){var g=n.getTickValueCoord(v),x=Wt(Wt(Wt(Wt({textAnchor:l,transform:"rotate(".concat(90-u,", ").concat(g.x,", ").concat(g.y,")")},h),{},{stroke:"none",fill:s},p),{},{index:d},g),{},{payload:v});return S.createElement(te,wn({className:J("recharts-polar-radius-axis-tick",ew(o)),key:"tick-".concat(v.coordinate)},er(n.props,v,d)),t.renderTickItem(o,x,c?c(v.value,d):v.value))});return S.createElement(te,{className:"recharts-polar-radius-axis-ticks"},y)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.axisLine,o=n.tick;return!i||!i.length?null:S.createElement(te,{className:J("recharts-polar-radius-axis",this.props.className)},a&&this.renderAxisLine(),o&&this.renderTicks(),Pe.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):X(n)?o=n(i):o=S.createElement(tr,wn({},i,{className:"recharts-polar-radius-axis-tick-value"}),a),o}}])}(q.PureComponent);Ja(Qa,"displayName","PolarRadiusAxis");Ja(Qa,"axisType","radiusAxis");Ja(Qa,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function Rr(e){"@babel/helpers - typeof";return Rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rr(e)}function Kt(){return Kt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Kt.apply(this,arguments)}function $g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$g(Object(r),!0).forEach(function(n){eo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$g(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function VI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Cg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dw(n.key),n)}}function XI(e,t,r){return t&&Cg(e.prototype,t),r&&Cg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function YI(e,t,r){return t=la(t),ZI(e,pw()?Reflect.construct(t,r||[],la(e).constructor):t.apply(e,r))}function ZI(e,t){if(t&&(Rr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return JI(e)}function JI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function pw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(pw=function(){return!!e})()}function la(e){return la=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},la(e)}function QI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pf(e,t)}function pf(e,t){return pf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},pf(e,t)}function eo(e,t,r){return t=dw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dw(e){var t=ek(e,"string");return Rr(t)=="symbol"?t:t+""}function ek(e,t){if(Rr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var tk=Math.PI/180,rk=1e-5,to=function(e){function t(){return VI(this,t),YI(this,t,arguments)}return QI(t,e),XI(t,[{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.cx,o=i.cy,u=i.radius,c=i.orientation,s=i.tickSize,f=s||8,l=le(a,o,u,n.coordinate),h=le(a,o,u+(c==="inner"?-1:1)*f,n.coordinate);return{x1:l.x,y1:l.y,x2:h.x,y2:h.y}}},{key:"getTickTextAnchor",value:function(n){var i=this.props.orientation,a=Math.cos(-n.coordinate*tk),o;return a>rk?o=i==="outer"?"start":"end":a<-1e-5?o=i==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.radius,u=n.axisLine,c=n.axisLineType,s=zt(zt({},H(this.props,!1)),{},{fill:"none"},H(u,!1));if(c==="circle")return S.createElement(Za,Kt({className:"recharts-polar-angle-axis-line"},s,{cx:i,cy:a,r:o}));var f=this.props.ticks,l=f.map(function(h){return le(i,a,o,h.coordinate)});return S.createElement(OI,Kt({className:"recharts-polar-angle-axis-line"},s,{points:l}))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.tickLine,c=i.tickFormatter,s=i.stroke,f=H(this.props,!1),l=H(o,!1),h=zt(zt({},f),{},{fill:"none"},H(u,!1)),p=a.map(function(y,v){var d=n.getTickLineCoord(y),g=n.getTickTextAnchor(y),x=zt(zt(zt({textAnchor:g},f),{},{stroke:"none",fill:s},l),{},{index:v,payload:y,x:d.x2,y:d.y2});return S.createElement(te,Kt({className:J("recharts-polar-angle-axis-tick",ew(o)),key:"tick-".concat(y.coordinate)},er(n.props,y,v)),u&&S.createElement("line",Kt({className:"recharts-polar-angle-axis-tick-line"},h,d)),o&&t.renderTickItem(o,x,c?c(y.value,v):y.value))});return S.createElement(te,{className:"recharts-polar-angle-axis-ticks"},p)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.radius,o=n.axisLine;return a<=0||!i||!i.length?null:S.createElement(te,{className:J("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):X(n)?o=n(i):o=S.createElement(tr,Kt({},i,{className:"recharts-polar-angle-axis-tick-value"}),a),o}}])}(q.PureComponent);eo(to,"displayName","PolarAngleAxis");eo(to,"axisType","angleAxis");eo(to,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var Us,Ig;function nk(){if(Ig)return Us;Ig=1;var e=A0(),t=e(Object.getPrototypeOf,Object);return Us=t,Us}var Hs,kg;function ik(){if(kg)return Hs;kg=1;var e=St(),t=nk(),r=Pt(),n="[object Object]",i=Function.prototype,a=Object.prototype,o=i.toString,u=a.hasOwnProperty,c=o.call(Object);function s(f){if(!r(f)||e(f)!=n)return!1;var l=t(f);if(l===null)return!0;var h=u.call(l,"constructor")&&l.constructor;return typeof h=="function"&&h instanceof h&&o.call(h)==c}return Hs=s,Hs}var ak=ik();const ok=oe(ak);var Ks,Rg;function uk(){if(Rg)return Ks;Rg=1;var e=St(),t=Pt(),r="[object Boolean]";function n(i){return i===!0||i===!1||t(i)&&e(i)==r}return Ks=n,Ks}var ck=uk();const sk=oe(ck);function Xn(e){"@babel/helpers - typeof";return Xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xn(e)}function fa(){return fa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fa.apply(this,arguments)}function lk(e,t){return dk(e)||pk(e,t)||hk(e,t)||fk()}function fk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function hk(e,t){if(e){if(typeof e=="string")return Dg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Dg(e,t)}}function Dg(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function pk(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function dk(e){if(Array.isArray(e))return e}function Ng(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function qg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ng(Object(r),!0).forEach(function(n){vk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ng(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vk(e,t,r){return t=yk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yk(e){var t=mk(e,"string");return Xn(t)=="symbol"?t:t+""}function mk(e,t){if(Xn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Lg=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},gk={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},bk=function(t){var r=qg(qg({},gk),t),n=q.useRef(),i=q.useState(-1),a=lk(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var w=n.current.getTotalLength();w&&u(w)}catch{}},[]);var c=r.x,s=r.y,f=r.upperWidth,l=r.lowerWidth,h=r.height,p=r.className,y=r.animationEasing,v=r.animationDuration,d=r.animationBegin,g=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||h!==+h||f===0&&l===0||h===0)return null;var x=J("recharts-trapezoid",p);return g?S.createElement(lt,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:h,x:c,y:s},to:{upperWidth:f,lowerWidth:l,height:h,x:c,y:s},duration:v,animationEasing:y,isActive:g},function(w){var O=w.upperWidth,m=w.lowerWidth,b=w.height,_=w.x,A=w.y;return S.createElement(lt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:d,duration:v,easing:y},S.createElement("path",fa({},H(r,!0),{className:x,d:Lg(_,A,O,m,b),ref:n})))}):S.createElement("g",null,S.createElement("path",fa({},H(r,!0),{className:x,d:Lg(c,s,f,l,h)})))},xk=["option","shapeType","propTransformer","activeClassName","isActive"];function Yn(e){"@babel/helpers - typeof";return Yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yn(e)}function wk(e,t){if(e==null)return{};var r=Ok(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Ok(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Bg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ha(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Bg(Object(r),!0).forEach(function(n){_k(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Bg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _k(e,t,r){return t=Ak(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ak(e){var t=Sk(e,"string");return Yn(t)=="symbol"?t:t+""}function Sk(e,t){if(Yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Pk(e,t){return ha(ha({},t),e)}function Tk(e,t){return e==="symbols"}function Fg(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return S.createElement(Mh,r);case"trapezoid":return S.createElement(bk,r);case"sector":return S.createElement(nw,r);case"symbols":if(Tk(t))return S.createElement(Vf,r);break;default:return null}}function Ek(e){return q.isValidElement(e)?e.props:e}function vw(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?Pk:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=wk(e,xk),s;if(q.isValidElement(t))s=q.cloneElement(t,ha(ha({},c),Ek(t)));else if(X(t))s=t(c);else if(ok(t)&&!sk(t)){var f=i(t,c);s=S.createElement(Fg,{shapeType:r,elementProps:f})}else{var l=c;s=S.createElement(Fg,{shapeType:r,elementProps:l})}return u?S.createElement(te,{className:o},s):s}function ro(e,t){return t!=null&&"trapezoids"in e.props}function no(e,t){return t!=null&&"sectors"in e.props}function Zn(e,t){return t!=null&&"points"in e.props}function jk(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function Mk(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function $k(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function Ck(e,t){var r;return ro(e,t)?r=jk:no(e,t)?r=Mk:Zn(e,t)&&(r=$k),r}function Ik(e,t){var r;return ro(e,t)?r="trapezoids":no(e,t)?r="sectors":Zn(e,t)&&(r="points"),r}function kk(e,t){if(ro(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(no(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return Zn(e,t)?t.payload:{}}function Rk(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=Ik(r,t),a=kk(r,t),o=n.filter(function(c,s){var f=fi(a,c),l=r.props[i].filter(function(y){var v=Ck(r,t);return v(y,t)}),h=r.props[i].indexOf(l[l.length-1]),p=s===h;return f&&p}),u=n.indexOf(o[o.length-1]);return u}var ji;function Dr(e){"@babel/helpers - typeof";return Dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(e)}function mr(){return mr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},mr.apply(this,arguments)}function Wg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ce(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wg(Object(r),!0).forEach(function(n){Xe(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Dk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function zg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mw(n.key),n)}}function Nk(e,t,r){return t&&zg(e.prototype,t),r&&zg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function qk(e,t,r){return t=pa(t),Lk(e,yw()?Reflect.construct(t,r||[],pa(e).constructor):t.apply(e,r))}function Lk(e,t){if(t&&(Dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Bk(e)}function Bk(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function yw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(yw=function(){return!!e})()}function pa(e){return pa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},pa(e)}function Fk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&df(e,t)}function df(e,t){return df=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},df(e,t)}function Xe(e,t,r){return t=mw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mw(e){var t=Wk(e,"string");return Dr(t)=="symbol"?t:t+""}function Wk(e,t){if(Dr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Nt=function(e){function t(r){var n;return Dk(this,t),n=qk(this,t,[r]),Xe(n,"pieRef",null),Xe(n,"sectorRefs",[]),Xe(n,"id",Zr("recharts-pie-")),Xe(n,"handleAnimationEnd",function(){var i=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),X(i)&&i()}),Xe(n,"handleAnimationStart",function(){var i=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),X(i)&&i()}),n.state={isAnimationFinished:!r.isAnimationActive,prevIsAnimationActive:r.isAnimationActive,prevAnimationId:r.animationId,sectorToFocus:0},n}return Fk(t,e),Nk(t,[{key:"isActiveIndex",value:function(n){var i=this.props.activeIndex;return Array.isArray(i)?i.indexOf(n)!==-1:n===i}},{key:"hasActiveIndex",value:function(){var n=this.props.activeIndex;return Array.isArray(n)?n.length!==0:n||n===0}},{key:"renderLabels",value:function(n){var i=this.props.isAnimationActive;if(i&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.label,u=a.labelLine,c=a.dataKey,s=a.valueKey,f=H(this.props,!1),l=H(o,!1),h=H(u,!1),p=o&&o.offsetRadius||20,y=n.map(function(v,d){var g=(v.startAngle+v.endAngle)/2,x=le(v.cx,v.cy,v.outerRadius+p,g),w=ce(ce(ce(ce({},f),v),{},{stroke:"none"},l),{},{index:d,textAnchor:t.getTextAnchor(x.x,v.cx)},x),O=ce(ce(ce(ce({},f),v),{},{fill:"none",stroke:v.fill},h),{},{index:d,points:[le(v.cx,v.cy,v.outerRadius,g),x]}),m=c;return Y(c)&&Y(s)?m="value":Y(c)&&(m=s),S.createElement(te,{key:"label-".concat(v.startAngle,"-").concat(v.endAngle,"-").concat(v.midAngle,"-").concat(d)},u&&t.renderLabelLineItem(u,O,"line"),t.renderLabelItem(o,w,Oe(v,m)))});return S.createElement(te,{className:"recharts-pie-labels"},y)}},{key:"renderSectorsStatically",value:function(n){var i=this,a=this.props,o=a.activeShape,u=a.blendStroke,c=a.inactiveShape;return n.map(function(s,f){if((s==null?void 0:s.startAngle)===0&&(s==null?void 0:s.endAngle)===0&&n.length!==1)return null;var l=i.isActiveIndex(f),h=c&&i.hasActiveIndex()?c:null,p=l?o:h,y=ce(ce({},s),{},{stroke:u?s.fill:s.stroke,tabIndex:-1});return S.createElement(te,mr({ref:function(d){d&&!i.sectorRefs.includes(d)&&i.sectorRefs.push(d)},tabIndex:-1,className:"recharts-pie-sector"},er(i.props,s,f),{key:"sector-".concat(s==null?void 0:s.startAngle,"-").concat(s==null?void 0:s.endAngle,"-").concat(s.midAngle,"-").concat(f)}),S.createElement(vw,mr({option:p,isActive:l,shapeType:"sector"},y)))})}},{key:"renderSectorsWithAnimation",value:function(){var n=this,i=this.props,a=i.sectors,o=i.isAnimationActive,u=i.animationBegin,c=i.animationDuration,s=i.animationEasing,f=i.animationId,l=this.state,h=l.prevSectors,p=l.prevIsAnimationActive;return S.createElement(lt,{begin:u,duration:c,isActive:o,easing:s,from:{t:0},to:{t:1},key:"pie-".concat(f,"-").concat(p),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(y){var v=y.t,d=[],g=a&&a[0],x=g.startAngle;return a.forEach(function(w,O){var m=h&&h[O],b=O>0?He(w,"paddingAngle",0):0;if(m){var _=ze(m.endAngle-m.startAngle,w.endAngle-w.startAngle),A=ce(ce({},w),{},{startAngle:x+b,endAngle:x+_(v)+b});d.push(A),x=A.endAngle}else{var T=w.endAngle,M=w.startAngle,P=ze(0,T-M),E=P(v),j=ce(ce({},w),{},{startAngle:x+b,endAngle:x+E+b});d.push(j),x=j.endAngle}}),S.createElement(te,null,n.renderSectorsStatically(d))})}},{key:"attachKeyboardHandlers",value:function(n){var i=this;n.onkeydown=function(a){if(!a.altKey)switch(a.key){case"ArrowLeft":{var o=++i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[o].focus(),i.setState({sectorToFocus:o});break}case"ArrowRight":{var u=--i.state.sectorToFocus<0?i.sectorRefs.length-1:i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[u].focus(),i.setState({sectorToFocus:u});break}case"Escape":{i.sectorRefs[i.state.sectorToFocus].blur(),i.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var n=this.props,i=n.sectors,a=n.isAnimationActive,o=this.state.prevSectors;return a&&i&&i.length&&(!o||!fi(o,i))?this.renderSectorsWithAnimation():this.renderSectorsStatically(i)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var n=this,i=this.props,a=i.hide,o=i.sectors,u=i.className,c=i.label,s=i.cx,f=i.cy,l=i.innerRadius,h=i.outerRadius,p=i.isAnimationActive,y=this.state.isAnimationFinished;if(a||!o||!o.length||!N(s)||!N(f)||!N(l)||!N(h))return null;var v=J("recharts-pie",u);return S.createElement(te,{tabIndex:this.props.rootTabIndex,className:v,ref:function(g){n.pieRef=g}},this.renderSectors(),c&&this.renderLabels(o),Pe.renderCallByParent(this.props,null,!1),(!p||y)&&wt.renderCallByParent(this.props,o,!1))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return i.prevIsAnimationActive!==n.isAnimationActive?{prevIsAnimationActive:n.isAnimationActive,prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:[],isAnimationFinished:!0}:n.isAnimationActive&&n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:i.curSectors,isAnimationFinished:!0}:n.sectors!==i.curSectors?{curSectors:n.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(n,i){return n>i?"start":n<i?"end":"middle"}},{key:"renderLabelLineItem",value:function(n,i,a){if(S.isValidElement(n))return S.cloneElement(n,i);if(X(n))return n(i);var o=J("recharts-pie-label-line",typeof n!="boolean"?n.className:"");return S.createElement(ia,mr({},i,{key:a,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(n,i,a){if(S.isValidElement(n))return S.cloneElement(n,i);var o=a;if(X(n)&&(o=n(i),S.isValidElement(o)))return o;var u=J("recharts-pie-label-text",typeof n!="boolean"&&!X(n)?n.className:"");return S.createElement(tr,mr({},i,{alignmentBaseline:"middle",className:u}),o)}}])}(q.PureComponent);ji=Nt;Xe(Nt,"displayName","Pie");Xe(Nt,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!or.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});Xe(Nt,"parseDeltaAngle",function(e,t){var r=Ce(t-e),n=Math.min(Math.abs(t-e),360);return r*n});Xe(Nt,"getRealPieData",function(e){var t=e.data,r=e.children,n=H(e,!1),i=Ke(r,ih);return t&&t.length?t.map(function(a,o){return ce(ce(ce({payload:a},n),a),i&&i[o]&&i[o].props)}):i&&i.length?i.map(function(a){return ce(ce({},n),a.props)}):[]});Xe(Nt,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,i=t.width,a=t.height,o=Qx(i,a),u=n+Ie(e.cx,i,i/2),c=r+Ie(e.cy,a,a/2),s=Ie(e.innerRadius,o,0),f=Ie(e.outerRadius,o,o*.8),l=e.maxRadius||Math.sqrt(i*i+a*a)/2;return{cx:u,cy:c,innerRadius:s,outerRadius:f,maxRadius:l}});Xe(Nt,"getComposedData",function(e){var t=e.item,r=e.offset,n=t.type.defaultProps!==void 0?ce(ce({},t.type.defaultProps),t.props):t.props,i=ji.getRealPieData(n);if(!i||!i.length)return null;var a=n.cornerRadius,o=n.startAngle,u=n.endAngle,c=n.paddingAngle,s=n.dataKey,f=n.nameKey,l=n.valueKey,h=n.tooltipType,p=Math.abs(n.minAngle),y=ji.parseCoordinateOfPie(n,r),v=ji.parseDeltaAngle(o,u),d=Math.abs(v),g=s;Y(s)&&Y(l)?(it(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),g="value"):Y(s)&&(it(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),g=l);var x=i.filter(function(A){return Oe(A,g,0)!==0}).length,w=(d>=360?x:x-1)*c,O=d-x*p-w,m=i.reduce(function(A,T){var M=Oe(T,g,0);return A+(N(M)?M:0)},0),b;if(m>0){var _;b=i.map(function(A,T){var M=Oe(A,g,0),P=Oe(A,f,T),E=(N(M)?M:0)/m,j;T?j=_.endAngle+Ce(v)*c*(M!==0?1:0):j=o;var C=j+Ce(v)*((M!==0?p:0)+E*O),$=(j+C)/2,k=(y.innerRadius+y.outerRadius)/2,R=[{name:P,value:M,payload:A,dataKey:g,type:h}],L=le(y.cx,y.cy,k,$);return _=ce(ce(ce({percent:E,cornerRadius:a,name:P,tooltipPayload:R,midAngle:$,middleRadius:k,tooltipPosition:L},A),y),{},{value:Oe(A,g),startAngle:j,endAngle:C,payload:A,paddingAngle:Ce(v)*c}),_})}return ce(ce({},y),{},{sectors:b,data:i})});var Gs,Ug;function zk(){if(Ug)return Gs;Ug=1;var e=Math.ceil,t=Math.max;function r(n,i,a,o){for(var u=-1,c=t(e((i-n)/(a||1)),0),s=Array(c);c--;)s[o?c:++u]=n,n+=a;return s}return Gs=r,Gs}var Vs,Hg;function gw(){if(Hg)return Vs;Hg=1;var e=B0(),t=1/0,r=17976931348623157e292;function n(i){if(!i)return i===0?i:0;if(i=e(i),i===t||i===-1/0){var a=i<0?-1:1;return a*r}return i===i?i:0}return Vs=n,Vs}var Xs,Kg;function Uk(){if(Kg)return Xs;Kg=1;var e=zk(),t=La(),r=gw();function n(i){return function(a,o,u){return u&&typeof u!="number"&&t(a,o,u)&&(o=u=void 0),a=r(a),o===void 0?(o=a,a=0):o=r(o),u=u===void 0?a<o?1:-1:r(u),e(a,o,u,i)}}return Xs=n,Xs}var Ys,Gg;function Hk(){if(Gg)return Ys;Gg=1;var e=Uk(),t=e();return Ys=t,Ys}var Kk=Hk();const da=oe(Kk);function Jn(e){"@babel/helpers - typeof";return Jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jn(e)}function Vg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Xg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Vg(Object(r),!0).forEach(function(n){bw(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function bw(e,t,r){return t=Gk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Gk(e){var t=Vk(e,"string");return Jn(t)=="symbol"?t:t+""}function Vk(e,t){if(Jn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Xk=["Webkit","Moz","O","ms"],Yk=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=Xk.reduce(function(a,o){return Xg(Xg({},a),{},bw({},o+n,r))},{});return i[t]=r,i};function Nr(e){"@babel/helpers - typeof";return Nr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nr(e)}function va(){return va=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},va.apply(this,arguments)}function Yg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Zs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yg(Object(r),!0).forEach(function(n){Fe(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Zk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Zg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ww(n.key),n)}}function Jk(e,t,r){return t&&Zg(e.prototype,t),r&&Zg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Qk(e,t,r){return t=ya(t),eR(e,xw()?Reflect.construct(t,r||[],ya(e).constructor):t.apply(e,r))}function eR(e,t){if(t&&(Nr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return tR(e)}function tR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(xw=function(){return!!e})()}function ya(e){return ya=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ya(e)}function rR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&vf(e,t)}function vf(e,t){return vf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},vf(e,t)}function Fe(e,t,r){return t=ww(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ww(e){var t=nR(e,"string");return Nr(t)=="symbol"?t:t+""}function nR(e,t){if(Nr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var iR=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,s=mn().domain(da(0,c)).range([a,a+o-u]),f=s.domain().map(function(l){return s(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(i),scale:s,scaleValues:f}},Jg=function(t){return t.changedTouches&&!!t.changedTouches.length},qr=function(e){function t(r){var n;return Zk(this,t),n=Qk(this,t,[r]),Fe(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),Fe(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),Fe(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),Fe(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),Fe(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),Fe(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),Fe(n,"handleSlideDragStart",function(i){var a=Jg(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return rR(t,e),Jk(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,s=u.data,f=s.length-1,l=Math.min(i,a),h=Math.max(i,a),p=t.getIndexInRange(o,l),y=t.getIndexInRange(o,h);return{startIndex:p-p%c,endIndex:y===f?f:y-y%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=Oe(a[n],u,n);return X(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,s=c.x,f=c.width,l=c.travellerWidth,h=c.startIndex,p=c.endIndex,y=c.onChange,v=n.pageX-a;v>0?v=Math.min(v,s+f-l-u,s+f-l-o):v<0&&(v=Math.max(v,s-o,s-u));var d=this.getIndex({startX:o+v,endX:u+v});(d.startIndex!==h||d.endIndex!==p)&&y&&y(d),this.setState({startX:o+v,endX:u+v,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=Jg(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,s=this.state[o],f=this.props,l=f.x,h=f.width,p=f.travellerWidth,y=f.onChange,v=f.gap,d=f.data,g={startX:this.state.startX,endX:this.state.endX},x=n.pageX-a;x>0?x=Math.min(x,l+h-p-s):x<0&&(x=Math.max(x,l-s)),g[o]=s+x;var w=this.getIndex(g),O=w.startIndex,m=w.endIndex,b=function(){var A=d.length-1;return o==="startX"&&(u>c?O%v===0:m%v===0)||u<c&&m===A||o==="endX"&&(u>c?m%v===0:O%v===0)||u>c&&m===A};this.setState(Fe(Fe({},o,s+x),"brushMoveStartX",n.pageX),function(){y&&b()&&y(w)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,s=o.endX,f=this.state[i],l=u.indexOf(f);if(l!==-1){var h=l+n;if(!(h===-1||h>=u.length)){var p=u[h];i==="startX"&&p>=s||i==="endX"&&p<=c||this.setState(Fe({},i,p),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,s=n.stroke;return S.createElement("rect",{stroke:s,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,s=n.children,f=n.padding,l=q.Children.only(s);return l?S.cloneElement(l,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,s=c.y,f=c.travellerWidth,l=c.height,h=c.traveller,p=c.ariaLabel,y=c.data,v=c.startIndex,d=c.endIndex,g=Math.max(n,this.props.x),x=Zs(Zs({},H(this.props,!1)),{},{x:g,y:s,width:f,height:l}),w=p||"Min value: ".concat((a=y[v])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=y[d])===null||o===void 0?void 0:o.name);return S.createElement(te,{tabIndex:0,role:"slider","aria-label":w,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(h,x))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,s=a.travellerWidth,f=Math.min(n,i)+s,l=Math.max(Math.abs(i-n)-s,0);return S.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,s=n.stroke,f=this.state,l=f.startX,h=f.endX,p=5,y={pointerEvents:"none",fill:s};return S.createElement(te,{className:"recharts-brush-texts"},S.createElement(tr,va({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,h)-p,y:o+u/2},y),this.getTextOfTick(i)),S.createElement(tr,va({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,h)+c+p,y:o+u/2},y),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,s=n.width,f=n.height,l=n.alwaysShowText,h=this.state,p=h.startX,y=h.endX,v=h.isTextActive,d=h.isSlideMoving,g=h.isTravellerMoving,x=h.isTravellerFocused;if(!i||!i.length||!N(u)||!N(c)||!N(s)||!N(f)||s<=0||f<=0)return null;var w=J("recharts-brush",a),O=S.Children.count(o)===1,m=Yk("userSelect","none");return S.createElement(te,{className:w,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),O&&this.renderPanorama(),this.renderSlide(p,y),this.renderTravellerLayer(p,"startX"),this.renderTravellerLayer(y,"endX"),(v||d||g||x||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,s=Math.floor(a+u/2)-1;return S.createElement(S.Fragment,null,S.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),S.createElement("line",{x1:i+1,y1:s,x2:i+o-1,y2:s,fill:"none",stroke:"#fff"}),S.createElement("line",{x1:i+1,y1:s+2,x2:i+o-1,y2:s+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return S.isValidElement(n)?a=S.cloneElement(n,i):X(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,s=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||s!==i.prevUpdateId)return Zs({prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o},a&&a.length?iR({data:a,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var h=i.scale.domain().map(function(p){return i.scale(p)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:h}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(q.PureComponent);Fe(qr,"displayName","Brush");Fe(qr,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var Js,Qg;function aR(){if(Qg)return Js;Qg=1;var e=th();function t(r,n){var i;return e(r,function(a,o,u){return i=n(a,o,u),!i}),!!i}return Js=t,Js}var Qs,eb;function oR(){if(eb)return Qs;eb=1;var e=m0(),t=ht(),r=aR(),n=qe(),i=La();function a(o,u,c){var s=n(o)?e:r;return c&&i(o,u,c)&&(u=void 0),s(o,t(u,3))}return Qs=a,Qs}var uR=oR();const cR=oe(uR);var ct=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},el,tb;function sR(){if(tb)return el;tb=1;var e=R0();function t(r,n,i){n=="__proto__"&&e?e(r,n,{configurable:!0,enumerable:!0,value:i,writable:!0}):r[n]=i}return el=t,el}var tl,rb;function lR(){if(rb)return tl;rb=1;var e=sR(),t=I0(),r=ht();function n(i,a){var o={};return a=r(a,3),t(i,function(u,c,s){e(o,c,a(u,c,s))}),o}return tl=n,tl}var fR=lR();const hR=oe(fR);var rl,nb;function pR(){if(nb)return rl;nb=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(!r(t[n],n,t))return!1;return!0}return rl=e,rl}var nl,ib;function dR(){if(ib)return nl;ib=1;var e=th();function t(r,n){var i=!0;return e(r,function(a,o,u){return i=!!n(a,o,u),i}),i}return nl=t,nl}var il,ab;function vR(){if(ab)return il;ab=1;var e=pR(),t=dR(),r=ht(),n=qe(),i=La();function a(o,u,c){var s=n(o)?e:t;return c&&i(o,u,c)&&(u=void 0),s(o,r(u,3))}return il=a,il}var yR=vR();const Ow=oe(yR);var mR=["x","y"];function Qn(e){"@babel/helpers - typeof";return Qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qn(e)}function yf(){return yf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},yf.apply(this,arguments)}function ob(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hn(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ob(Object(r),!0).forEach(function(n){gR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ob(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gR(e,t,r){return t=bR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bR(e){var t=xR(e,"string");return Qn(t)=="symbol"?t:t+""}function xR(e,t){if(Qn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function wR(e,t){if(e==null)return{};var r=OR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function OR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function _R(e,t){var r=e.x,n=e.y,i=wR(e,mR),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||i.height),f=parseInt(s,10),l="".concat(t.width||i.width),h=parseInt(l,10);return hn(hn(hn(hn(hn({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:h,name:t.name,radius:t.radius})}function ub(e){return S.createElement(vw,yf({shapeType:"rectangle",propTransformer:_R,activeClassName:"recharts-active-bar"},e))}var AR=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=typeof n=="number";return a?t(n,i):(a||nr(),r)}},SR=["value","background"],_w;function Lr(e){"@babel/helpers - typeof";return Lr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lr(e)}function PR(e,t){if(e==null)return{};var r=TR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function TR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ma(){return ma=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ma.apply(this,arguments)}function cb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?cb(Object(r),!0).forEach(function(n){$t(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ER(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function sb(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Sw(n.key),n)}}function jR(e,t,r){return t&&sb(e.prototype,t),r&&sb(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function MR(e,t,r){return t=ga(t),$R(e,Aw()?Reflect.construct(t,r||[],ga(e).constructor):t.apply(e,r))}function $R(e,t){if(t&&(Lr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return CR(e)}function CR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Aw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Aw=function(){return!!e})()}function ga(e){return ga=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ga(e)}function IR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mf(e,t)}function mf(e,t){return mf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},mf(e,t)}function $t(e,t,r){return t=Sw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Sw(e){var t=kR(e,"string");return Lr(t)=="symbol"?t:t+""}function kR(e,t){if(Lr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Lr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var pi=function(e){function t(){var r;ER(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=MR(this,t,[].concat(i)),$t(r,"state",{isAnimationFinished:!1}),$t(r,"id",Zr("recharts-bar-")),$t(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),$t(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return IR(t,e),jR(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,s=a.activeBar,f=H(this.props,!1);return n&&n.map(function(l,h){var p=h===c,y=p?s:o,v=me(me(me({},f),l),{},{isActive:p,option:y,index:h,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return S.createElement(te,ma({className:"recharts-bar-rectangle"},er(i.props,l,h),{key:"rectangle-".concat(l==null?void 0:l.x,"-").concat(l==null?void 0:l.y,"-").concat(l==null?void 0:l.value,"-").concat(h)}),S.createElement(ub,v))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,s=i.animationDuration,f=i.animationEasing,l=i.animationId,h=this.state.prevData;return S.createElement(lt,{begin:c,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(p){var y=p.t,v=a.map(function(d,g){var x=h&&h[g];if(x){var w=ze(x.x,d.x),O=ze(x.y,d.y),m=ze(x.width,d.width),b=ze(x.height,d.height);return me(me({},d),{},{x:w(y),y:O(y),width:m(y),height:b(y)})}if(o==="horizontal"){var _=ze(0,d.height),A=_(y);return me(me({},d),{},{y:d.y+d.height-A,height:A})}var T=ze(0,d.width),M=T(y);return me(me({},d),{},{width:M})});return S.createElement(te,null,n.renderRectanglesStatically(v))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!fi(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=H(this.props.background,!1);return a.map(function(s,f){s.value;var l=s.background,h=PR(s,SR);if(!l)return null;var p=me(me(me(me(me({},h),{},{fill:"#eee"},l),c),er(n.props,s,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return S.createElement(ub,ma({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},p))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Ke(f,hi);if(!l)return null;var h=s==="vertical"?o[0].height/2:o[0].width/2,p=function(d,g){var x=Array.isArray(d.value)?d.value[1]:d.value;return{x:d.x,y:d.y,value:x,errorVal:Oe(d,g)}},y={clipPath:n?"url(#clipPath-".concat(i,")"):null};return S.createElement(te,y,l.map(function(v){return S.cloneElement(v,{key:"error-bar-".concat(i,"-").concat(v.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,offset:h,dataPointFormatter:p})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,s=n.left,f=n.top,l=n.width,h=n.height,p=n.isAnimationActive,y=n.background,v=n.id;if(i||!a||!a.length)return null;var d=this.state.isAnimationFinished,g=J("recharts-bar",o),x=u&&u.allowDataOverflow,w=c&&c.allowDataOverflow,O=x||w,m=Y(v)?this.id:v;return S.createElement(te,{className:g},x||w?S.createElement("defs",null,S.createElement("clipPath",{id:"clipPath-".concat(m)},S.createElement("rect",{x:x?s:s-l/2,y:w?f:f-h/2,width:x?l:l*2,height:w?h:h*2}))):null,S.createElement(te,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(m,")"):null},y?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,m),(!p||d)&&wt.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(q.PureComponent);_w=pi;$t(pi,"displayName","Bar");$t(pi,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!or.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});$t(pi,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,h=e.offset,p=cM(n,r);if(!p)return null;var y=t.layout,v=r.type.defaultProps,d=v!==void 0?me(me({},v),r.props):r.props,g=d.dataKey,x=d.children,w=d.minPointSize,O=y==="horizontal"?o:a,m=s?O.scale.domain():null,b=vM({numericAxis:O}),_=Ke(x,ih),A=l.map(function(T,M){var P,E,j,C,$,k;s?P=sM(s[f+M],m):(P=Oe(T,g),Array.isArray(P)||(P=[b,P]));var R=AR(w,_w.defaultProps.minPointSize)(P[1],M);if(y==="horizontal"){var L,B=[o.scale(P[0]),o.scale(P[1])],U=B[0],G=B[1];E=Rm({axis:a,ticks:u,bandSize:i,offset:p.offset,entry:T,index:M}),j=(L=G??U)!==null&&L!==void 0?L:void 0,C=p.size;var W=U-G;if($=Number.isNaN(W)?0:W,k={x:E,y:o.y,width:C,height:o.height},Math.abs(R)>0&&Math.abs($)<Math.abs(R)){var V=Ce($||R)*(Math.abs(R)-Math.abs($));j-=V,$+=V}}else{var fe=[a.scale(P[0]),a.scale(P[1])],ye=fe[0],Le=fe[1];if(E=ye,j=Rm({axis:o,ticks:c,bandSize:i,offset:p.offset,entry:T,index:M}),C=Le-ye,$=p.size,k={x:a.x,y:j,width:a.width,height:$},Math.abs(R)>0&&Math.abs(C)<Math.abs(R)){var qt=Ce(C||R)*(Math.abs(R)-Math.abs(C));C+=qt}}return me(me(me({},T),{},{x:E,y:j,width:C,height:$,value:s?P:P[1],payload:T,background:k},_&&_[M]&&_[M].props),{},{tooltipPayload:[Zx(r,T)],tooltipPosition:{x:E+C/2,y:j+$/2}})});return me({data:A,layout:y},h)});function ei(e){"@babel/helpers - typeof";return ei=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ei(e)}function RR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function lb(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Pw(n.key),n)}}function DR(e,t,r){return t&&lb(e.prototype,t),r&&lb(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function fb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function tt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?fb(Object(r),!0).forEach(function(n){io(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function io(e,t,r){return t=Pw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Pw(e){var t=NR(e,"string");return ei(t)=="symbol"?t:t+""}function NR(e,t){if(ei(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ei(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var qR=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,s=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},h=!!We(s,pi);return f.reduce(function(p,y){var v=r[y],d=v.orientation,g=v.domain,x=v.padding,w=x===void 0?{}:x,O=v.mirror,m=v.reversed,b="".concat(d).concat(O?"Mirror":""),_,A,T,M,P;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var E=g[1]-g[0],j=1/0,C=v.categoricalDomain.sort(YO);if(C.forEach(function(fe,ye){ye>0&&(j=Math.min((fe||0)-(C[ye-1]||0),j))}),Number.isFinite(j)){var $=j/E,k=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(_=$*k/2),v.padding==="no-gap"){var R=Ie(t.barCategoryGap,$*k),L=$*k/2;_=L-R-(L-R)/k*R}}}i==="xAxis"?A=[n.left+(w.left||0)+(_||0),n.left+n.width-(w.right||0)-(_||0)]:i==="yAxis"?A=c==="horizontal"?[n.top+n.height-(w.bottom||0),n.top+(w.top||0)]:[n.top+(w.top||0)+(_||0),n.top+n.height-(w.bottom||0)-(_||0)]:A=v.range,m&&(A=[A[1],A[0]]);var B=Gx(v,a,h),U=B.scale,G=B.realScaleType;U.domain(g).range(A),Vx(U);var W=Xx(U,tt(tt({},v),{},{realScaleType:G}));i==="xAxis"?(P=d==="top"&&!O||d==="bottom"&&O,T=n.left,M=l[b]-P*v.height):i==="yAxis"&&(P=d==="left"&&!O||d==="right"&&O,T=l[b]-P*v.width,M=n.top);var V=tt(tt(tt({},v),W),{},{realScaleType:G,x:T,y:M,scale:U,width:i==="xAxis"?n.width:v.width,height:i==="yAxis"?n.height:v.height});return V.bandSize=ta(V,W),!v.hide&&i==="xAxis"?l[b]+=(P?-1:1)*V.height:v.hide||(l[b]+=(P?-1:1)*V.width),tt(tt({},p),{},io({},y,V))},{})},Tw=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},LR=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return Tw({x:r,y:n},{x:i,y:a})},Ew=function(){function e(t){RR(this,e),this.scale=t}return DR(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();io(Ew,"EPS",1e-4);var $h=function(t){var r=Object.keys(t).reduce(function(n,i){return tt(tt({},n),{},io({},i,Ew.create(t[i])))},{});return tt(tt({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return hR(i,function(c,s){return r[s].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return Ow(i,function(a,o){return r[o].isInRange(a)})}})};function BR(e){return(e%180+180)%180}var FR=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=BR(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},al,hb;function WR(){if(hb)return al;hb=1;var e=ht(),t=oi(),r=Na();function n(i){return function(a,o,u){var c=Object(a);if(!t(a)){var s=e(o,3);a=r(a),o=function(l){return s(c[l],l,c)}}var f=i(a,o,u);return f>-1?c[s?a[f]:f]:void 0}}return al=n,al}var ol,pb;function zR(){if(pb)return ol;pb=1;var e=gw();function t(r){var n=e(r),i=n%1;return n===n?i?n-i:n:0}return ol=t,ol}var ul,db;function UR(){if(db)return ul;db=1;var e=E0(),t=ht(),r=zR(),n=Math.max;function i(a,o,u){var c=a==null?0:a.length;if(!c)return-1;var s=u==null?0:r(u);return s<0&&(s=n(c+s,0)),e(a,t(o,3),s)}return ul=i,ul}var cl,vb;function HR(){if(vb)return cl;vb=1;var e=WR(),t=UR(),r=e(t);return cl=r,cl}var KR=HR();const GR=oe(KR);var VR=Ub();const XR=oe(VR);var YR=XR(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),Ch=q.createContext(void 0),Ih=q.createContext(void 0),jw=q.createContext(void 0),Mw=q.createContext({}),$w=q.createContext(void 0),Cw=q.createContext(0),Iw=q.createContext(0),yb=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,c=t.width,s=t.height,f=YR(a);return S.createElement(Ch.Provider,{value:n},S.createElement(Ih.Provider,{value:i},S.createElement(Mw.Provider,{value:a},S.createElement(jw.Provider,{value:f},S.createElement($w.Provider,{value:o},S.createElement(Cw.Provider,{value:s},S.createElement(Iw.Provider,{value:c},u)))))))},ZR=function(){return q.useContext($w)},kw=function(t){var r=q.useContext(Ch);r==null&&nr();var n=r[t];return n==null&&nr(),n},JR=function(){var t=q.useContext(Ch);return Mt(t)},QR=function(){var t=q.useContext(Ih),r=GR(t,function(n){return Ow(n.domain,Number.isFinite)});return r||Mt(t)},Rw=function(t){var r=q.useContext(Ih);r==null&&nr();var n=r[t];return n==null&&nr(),n},eD=function(){var t=q.useContext(jw);return t},tD=function(){return q.useContext(Mw)},kh=function(){return q.useContext(Iw)},Rh=function(){return q.useContext(Cw)};function Br(e){"@babel/helpers - typeof";return Br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Br(e)}function rD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function nD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Nw(n.key),n)}}function iD(e,t,r){return t&&nD(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function aD(e,t,r){return t=ba(t),oD(e,Dw()?Reflect.construct(t,r||[],ba(e).constructor):t.apply(e,r))}function oD(e,t){if(t&&(Br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return uD(e)}function uD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Dw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Dw=function(){return!!e})()}function ba(e){return ba=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ba(e)}function cD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&gf(e,t)}function gf(e,t){return gf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},gf(e,t)}function mb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function gb(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?mb(Object(r),!0).forEach(function(n){Dh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Dh(e,t,r){return t=Nw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Nw(e){var t=sD(e,"string");return Br(t)=="symbol"?t:t+""}function sD(e,t){if(Br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function lD(e,t){return dD(e)||pD(e,t)||hD(e,t)||fD()}function fD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function hD(e,t){if(e){if(typeof e=="string")return bb(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return bb(e,t)}}function bb(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function pD(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function dD(e){if(Array.isArray(e))return e}function bf(){return bf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bf.apply(this,arguments)}var vD=function(t,r){var n;return S.isValidElement(t)?n=S.cloneElement(t,r):X(t)?n=t(r):n=S.createElement("line",bf({},r,{className:"recharts-reference-line-line"})),n},yD=function(t,r,n,i,a,o,u,c,s){var f=a.x,l=a.y,h=a.width,p=a.height;if(n){var y=s.y,v=t.y.apply(y,{position:o});if(ct(s,"discard")&&!t.y.isInRange(v))return null;var d=[{x:f+h,y:v},{x:f,y:v}];return c==="left"?d.reverse():d}if(r){var g=s.x,x=t.x.apply(g,{position:o});if(ct(s,"discard")&&!t.x.isInRange(x))return null;var w=[{x,y:l+p},{x,y:l}];return u==="top"?w.reverse():w}if(i){var O=s.segment,m=O.map(function(b){return t.apply(b,{position:o})});return ct(s,"discard")&&cR(m,function(b){return!t.isInRange(b)})?null:m}return null};function mD(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,s=ZR(),f=kw(i),l=Rw(a),h=eD();if(!s||!h)return null;it(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var p=$h({x:f.scale,y:l.scale}),y=_e(t),v=_e(r),d=n&&n.length===2,g=yD(p,y,v,d,h,e.position,f.orientation,l.orientation,e);if(!g)return null;var x=lD(g,2),w=x[0],O=w.x,m=w.y,b=x[1],_=b.x,A=b.y,T=ct(e,"hidden")?"url(#".concat(s,")"):void 0,M=gb(gb({clipPath:T},H(e,!0)),{},{x1:O,y1:m,x2:_,y2:A});return S.createElement(te,{className:J("recharts-reference-line",u)},vD(o,M),Pe.renderCallByParent(e,LR({x1:O,y1:m,x2:_,y2:A})))}var Nh=function(e){function t(){return rD(this,t),aD(this,t,arguments)}return cD(t,e),iD(t,[{key:"render",value:function(){return S.createElement(mD,this.props)}}])}(S.Component);Dh(Nh,"displayName","ReferenceLine");Dh(Nh,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function xf(){return xf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xf.apply(this,arguments)}function Fr(e){"@babel/helpers - typeof";return Fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fr(e)}function xb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function wb(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xb(Object(r),!0).forEach(function(n){ao(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function bD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Lw(n.key),n)}}function xD(e,t,r){return t&&bD(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function wD(e,t,r){return t=xa(t),OD(e,qw()?Reflect.construct(t,r||[],xa(e).constructor):t.apply(e,r))}function OD(e,t){if(t&&(Fr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return _D(e)}function _D(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function qw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(qw=function(){return!!e})()}function xa(e){return xa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},xa(e)}function AD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&wf(e,t)}function wf(e,t){return wf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},wf(e,t)}function ao(e,t,r){return t=Lw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Lw(e){var t=SD(e,"string");return Fr(t)=="symbol"?t:t+""}function SD(e,t){if(Fr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var PD=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=$h({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return ct(t,"discard")&&!o.isInRange(u)?null:u},oo=function(e){function t(){return gD(this,t),wD(this,t,arguments)}return AD(t,e),xD(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,s=_e(i),f=_e(a);if(it(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!f)return null;var l=PD(this.props);if(!l)return null;var h=l.x,p=l.y,y=this.props,v=y.shape,d=y.className,g=ct(this.props,"hidden")?"url(#".concat(c,")"):void 0,x=wb(wb({clipPath:g},H(this.props,!0)),{},{cx:h,cy:p});return S.createElement(te,{className:J("recharts-reference-dot",d)},t.renderDot(v,x),Pe.renderCallByParent(this.props,{x:h-o,y:p-o,width:2*o,height:2*o}))}}])}(S.Component);ao(oo,"displayName","ReferenceDot");ao(oo,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});ao(oo,"renderDot",function(e,t){var r;return S.isValidElement(e)?r=S.cloneElement(e,t):X(e)?r=e(t):r=S.createElement(Za,xf({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function Of(){return Of=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Of.apply(this,arguments)}function Wr(e){"@babel/helpers - typeof";return Wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wr(e)}function Ob(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function _b(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ob(Object(r),!0).forEach(function(n){uo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ob(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function TD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ED(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Fw(n.key),n)}}function jD(e,t,r){return t&&ED(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function MD(e,t,r){return t=wa(t),$D(e,Bw()?Reflect.construct(t,r||[],wa(e).constructor):t.apply(e,r))}function $D(e,t){if(t&&(Wr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return CD(e)}function CD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Bw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Bw=function(){return!!e})()}function wa(e){return wa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},wa(e)}function ID(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_f(e,t)}function _f(e,t){return _f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},_f(e,t)}function uo(e,t,r){return t=Fw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Fw(e){var t=kD(e,"string");return Wr(t)=="symbol"?t:t+""}function kD(e,t){if(Wr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var RD=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,s=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var h=$h({x:f.scale,y:l.scale}),p={x:t?h.x.apply(o,{position:"start"}):h.x.rangeMin,y:n?h.y.apply(c,{position:"start"}):h.y.rangeMin},y={x:r?h.x.apply(u,{position:"end"}):h.x.rangeMax,y:i?h.y.apply(s,{position:"end"}):h.y.rangeMax};return ct(a,"discard")&&(!h.isInRange(p)||!h.isInRange(y))?null:Tw(p,y)},co=function(e){function t(){return TD(this,t),MD(this,t,arguments)}return ID(t,e),jD(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,s=n.alwaysShow,f=n.clipPathId;it(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=_e(i),h=_e(a),p=_e(o),y=_e(u),v=this.props.shape;if(!l&&!h&&!p&&!y&&!v)return null;var d=RD(l,h,p,y,this.props);if(!d&&!v)return null;var g=ct(this.props,"hidden")?"url(#".concat(f,")"):void 0;return S.createElement(te,{className:J("recharts-reference-area",c)},t.renderRect(v,_b(_b({clipPath:g},H(this.props,!0)),d)),Pe.renderCallByParent(this.props,d))}}])}(S.Component);uo(co,"displayName","ReferenceArea");uo(co,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});uo(co,"renderRect",function(e,t){var r;return S.isValidElement(e)?r=S.cloneElement(e,t):X(e)?r=e(t):r=S.createElement(Mh,Of({},t,{className:"recharts-reference-area-rect"})),r});function Ww(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function DD(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return FR(n,r)}function ND(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function Oa(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function qD(e,t){return Ww(e,t+1)}function LD(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,s=1,f=o,l=function(){var y=n==null?void 0:n[c];if(y===void 0)return{v:Ww(n,s)};var v=c,d,g=function(){return d===void 0&&(d=r(y,v)),d},x=y.coordinate,w=c===0||Oa(e,x,g,f,u);w||(c=0,f=o,s+=1),w&&(f=x+e*(g()/2+i),c+=s)},h;s<=a.length;)if(h=l(),h)return h.v;return[]}function ti(e){"@babel/helpers - typeof";return ti=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ti(e)}function Ab(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ab(Object(r),!0).forEach(function(n){BD(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ab(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function BD(e,t,r){return t=FD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function FD(e){var t=WD(e,"string");return ti(t)=="symbol"?t:t+""}function WD(e,t){if(ti(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ti(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function zD(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,s=function(h){var p=a[h],y,v=function(){return y===void 0&&(y=r(p,h)),y};if(h===o-1){var d=e*(p.coordinate+e*v()/2-c);a[h]=p=je(je({},p),{},{tickCoord:d>0?p.coordinate-d*e:p.coordinate})}else a[h]=p=je(je({},p),{},{tickCoord:p.coordinate});var g=Oa(e,p.tickCoord,v,u,c);g&&(c=p.tickCoord-e*(v()/2+i),a[h]=je(je({},p),{},{isShow:!0}))},f=o-1;f>=0;f--)s(f);return a}function UD(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,s=t.end;if(a){var f=n[u-1],l=r(f,u-1),h=e*(f.coordinate+e*l/2-s);o[u-1]=f=je(je({},f),{},{tickCoord:h>0?f.coordinate-h*e:f.coordinate});var p=Oa(e,f.tickCoord,function(){return l},c,s);p&&(s=f.tickCoord-e*(l/2+i),o[u-1]=je(je({},f),{},{isShow:!0}))}for(var y=a?u-1:u,v=function(x){var w=o[x],O,m=function(){return O===void 0&&(O=r(w,x)),O};if(x===0){var b=e*(w.coordinate-e*m()/2-c);o[x]=w=je(je({},w),{},{tickCoord:b<0?w.coordinate-b*e:w.coordinate})}else o[x]=w=je(je({},w),{},{tickCoord:w.coordinate});var _=Oa(e,w.tickCoord,m,c,s);_&&(c=w.tickCoord+e*(m()/2+i),o[x]=je(je({},w),{},{isShow:!0}))},d=0;d<y;d++)v(d);return o}function qh(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,s=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(N(c)||or.isSsr)return qD(i,typeof c=="number"&&N(c)?c:0);var h=[],p=u==="top"||u==="bottom"?"width":"height",y=f&&p==="width"?yn(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=function(w,O){var m=X(s)?s(w.value,O):w.value;return p==="width"?DD(yn(m,{fontSize:t,letterSpacing:r}),y,l):yn(m,{fontSize:t,letterSpacing:r})[p]},d=i.length>=2?Ce(i[1].coordinate-i[0].coordinate):1,g=ND(a,d,p);return c==="equidistantPreserveStart"?LD(d,g,v,i,o):(c==="preserveStart"||c==="preserveStartEnd"?h=UD(d,g,v,i,o,c==="preserveStartEnd"):h=zD(d,g,v,i,o),h.filter(function(x){return x.isShow}))}var HD=["viewBox"],KD=["viewBox"],GD=["ticks"];function zr(e){"@babel/helpers - typeof";return zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zr(e)}function gr(){return gr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},gr.apply(this,arguments)}function Sb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function $e(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sb(Object(r),!0).forEach(function(n){Lh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function sl(e,t){if(e==null)return{};var r=VD(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function VD(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function XD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Pb(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Uw(n.key),n)}}function YD(e,t,r){return t&&Pb(e.prototype,t),r&&Pb(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function ZD(e,t,r){return t=_a(t),JD(e,zw()?Reflect.construct(t,r||[],_a(e).constructor):t.apply(e,r))}function JD(e,t){if(t&&(zr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return QD(e)}function QD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function zw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(zw=function(){return!!e})()}function _a(e){return _a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},_a(e)}function eN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Af(e,t)}function Af(e,t){return Af=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Af(e,t)}function Lh(e,t,r){return t=Uw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Uw(e){var t=tN(e,"string");return zr(t)=="symbol"?t:t+""}function tN(e,t){if(zr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var rn=function(e){function t(r){var n;return XD(this,t),n=ZD(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return eN(t,e),YD(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=sl(n,HD),u=this.props,c=u.viewBox,s=sl(u,KD);return!xr(a,c)||!xr(o,s)||!xr(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.orientation,f=i.tickSize,l=i.mirror,h=i.tickMargin,p,y,v,d,g,x,w=l?-1:1,O=n.tickSize||f,m=N(n.tickCoord)?n.tickCoord:n.coordinate;switch(s){case"top":p=y=n.coordinate,d=o+ +!l*c,v=d-w*O,x=v-w*h,g=m;break;case"left":v=d=n.coordinate,y=a+ +!l*u,p=y-w*O,g=p-w*h,x=m;break;case"right":v=d=n.coordinate,y=a+ +l*u,p=y+w*O,g=p+w*h,x=m;break;default:p=y=n.coordinate,d=o+ +l*c,v=d+w*O,x=v+w*h,g=m;break}return{line:{x1:p,y1:v,x2:y,y2:d},tick:{x:g,y:x}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,s=n.mirror,f=n.axisLine,l=$e($e($e({},H(this.props,!1)),H(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var h=+(c==="top"&&!s||c==="bottom"&&s);l=$e($e({},l),{},{x1:i,y1:a+h*u,x2:i+o,y2:a+h*u})}else{var p=+(c==="left"&&!s||c==="right"&&s);l=$e($e({},l),{},{x1:i+p*o,y1:a,x2:i+p*o,y2:a+u})}return S.createElement("line",gr({},l,{className:J("recharts-cartesian-axis-line",He(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,s=u.stroke,f=u.tick,l=u.tickFormatter,h=u.unit,p=qh($e($e({},this.props),{},{ticks:n}),i,a),y=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),d=H(this.props,!1),g=H(f,!1),x=$e($e({},d),{},{fill:"none"},H(c,!1)),w=p.map(function(O,m){var b=o.getTickLineCoord(O),_=b.line,A=b.tick,T=$e($e($e($e({textAnchor:y,verticalAnchor:v},d),{},{stroke:"none",fill:s},g),A),{},{index:m,payload:O,visibleTicksCount:p.length,tickFormatter:l});return S.createElement(te,gr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(O.value,"-").concat(O.coordinate,"-").concat(O.tickCoord)},er(o.props,O,m)),c&&S.createElement("line",gr({},x,_,{className:J("recharts-cartesian-axis-tick-line",He(c,"className"))})),f&&t.renderTickItem(f,T,"".concat(X(l)?l(O.value,m):O.value).concat(h||"")))});return S.createElement("g",{className:"recharts-cartesian-axis-ticks"},w)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,s=i.className,f=i.hide;if(f)return null;var l=this.props,h=l.ticks,p=sl(l,GD),y=h;return X(c)&&(y=h&&h.length>0?c(this.props):c(p)),o<=0||u<=0||!y||!y.length?null:S.createElement(te,{className:J("recharts-cartesian-axis",s),ref:function(d){n.layerReference=d}},a&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),Pe.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):X(n)?o=n(i):o=S.createElement(tr,gr({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(q.Component);Lh(rn,"displayName","CartesianAxis");Lh(rn,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var rN=["x1","y1","x2","y2","key"],nN=["offset"];function ir(e){"@babel/helpers - typeof";return ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ir(e)}function Tb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Tb(Object(r),!0).forEach(function(n){iN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function iN(e,t,r){return t=aN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function aN(e){var t=oN(e,"string");return ir(t)=="symbol"?t:t+""}function oN(e,t){if(ir(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ir(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Yt(){return Yt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yt.apply(this,arguments)}function Eb(e,t){if(e==null)return{};var r=uN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function uN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var cN=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,c=t.ry;return S.createElement("rect",{x:i,y:a,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function Hw(e,t){var r;if(S.isValidElement(e))r=S.cloneElement(e,t);else if(X(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,c=Eb(t,rN),s=H(c,!1);s.offset;var f=Eb(s,nN);r=S.createElement("line",Yt({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function sN(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=Me(Me({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return Hw(i,s)});return S.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function lN(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=Me(Me({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return Hw(i,s)});return S.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function fN(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,c=e.horizontal,s=c===void 0?!0:c;if(!s||!t||!t.length)return null;var f=u.map(function(h){return Math.round(h+i-i)}).sort(function(h,p){return h-p});i!==f[0]&&f.unshift(0);var l=f.map(function(h,p){var y=!f[p+1],v=y?i+o-h:f[p+1]-h;if(v<=0)return null;var d=p%t.length;return S.createElement("rect",{key:"react-".concat(p),y:h,x:n,height:v,width:a,stroke:"none",fill:t[d],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return S.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function hN(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,c=e.height,s=e.verticalPoints;if(!r||!n||!n.length)return null;var f=s.map(function(h){return Math.round(h+a-a)}).sort(function(h,p){return h-p});a!==f[0]&&f.unshift(0);var l=f.map(function(h,p){var y=!f[p+1],v=y?a+u-h:f[p+1]-h;if(v<=0)return null;var d=p%n.length;return S.createElement("rect",{key:"react-".concat(p),x:h,y:o,width:v,height:c,stroke:"none",fill:n[d],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return S.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var pN=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return Kx(qh(Me(Me(Me({},rn.defaultProps),n),{},{ticks:gt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},dN=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return Kx(qh(Me(Me(Me({},rn.defaultProps),n),{},{ticks:gt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},pr={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function vN(e){var t,r,n,i,a,o,u=kh(),c=Rh(),s=tD(),f=Me(Me({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:pr.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:pr.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:pr.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:pr.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:pr.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:pr.verticalFill,x:N(e.x)?e.x:s.left,y:N(e.y)?e.y:s.top,width:N(e.width)?e.width:s.width,height:N(e.height)?e.height:s.height}),l=f.x,h=f.y,p=f.width,y=f.height,v=f.syncWithTicks,d=f.horizontalValues,g=f.verticalValues,x=JR(),w=QR();if(!N(p)||p<=0||!N(y)||y<=0||!N(l)||l!==+l||!N(h)||h!==+h)return null;var O=f.verticalCoordinatesGenerator||pN,m=f.horizontalCoordinatesGenerator||dN,b=f.horizontalPoints,_=f.verticalPoints;if((!b||!b.length)&&X(m)){var A=d&&d.length,T=m({yAxis:w?Me(Me({},w),{},{ticks:A?d:w.ticks}):void 0,width:u,height:c,offset:s},A?!0:v);it(Array.isArray(T),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(ir(T),"]")),Array.isArray(T)&&(b=T)}if((!_||!_.length)&&X(O)){var M=g&&g.length,P=O({xAxis:x?Me(Me({},x),{},{ticks:M?g:x.ticks}):void 0,width:u,height:c,offset:s},M?!0:v);it(Array.isArray(P),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(ir(P),"]")),Array.isArray(P)&&(_=P)}return S.createElement("g",{className:"recharts-cartesian-grid"},S.createElement(cN,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),S.createElement(sN,Yt({},f,{offset:s,horizontalPoints:b,xAxis:x,yAxis:w})),S.createElement(lN,Yt({},f,{offset:s,verticalPoints:_,xAxis:x,yAxis:w})),S.createElement(fN,Yt({},f,{horizontalPoints:b})),S.createElement(hN,Yt({},f,{verticalPoints:_})))}vN.displayName="CartesianGrid";var yN=["type","layout","connectNulls","ref"],mN=["key"];function Ur(e){"@babel/helpers - typeof";return Ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ur(e)}function jb(e,t){if(e==null)return{};var r=gN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function gN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function On(){return On=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},On.apply(this,arguments)}function Mb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Be(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Mb(Object(r),!0).forEach(function(n){rt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Mb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function dr(e){return ON(e)||wN(e)||xN(e)||bN()}function bN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xN(e,t){if(e){if(typeof e=="string")return Sf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Sf(e,t)}}function wN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ON(e){if(Array.isArray(e))return Sf(e)}function Sf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _N(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function $b(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Gw(n.key),n)}}function AN(e,t,r){return t&&$b(e.prototype,t),r&&$b(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function SN(e,t,r){return t=Aa(t),PN(e,Kw()?Reflect.construct(t,r||[],Aa(e).constructor):t.apply(e,r))}function PN(e,t){if(t&&(Ur(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return TN(e)}function TN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Kw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Kw=function(){return!!e})()}function Aa(e){return Aa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Aa(e)}function EN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pf(e,t)}function Pf(e,t){return Pf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Pf(e,t)}function rt(e,t,r){return t=Gw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Gw(e){var t=jN(e,"string");return Ur(t)=="symbol"?t:t+""}function jN(e,t){if(Ur(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ur(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var so=function(e){function t(){var r;_N(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=SN(this,t,[].concat(i)),rt(r,"state",{isAnimationFinished:!0,totalLength:0}),rt(r,"generateSimpleStrokeDasharray",function(o,u){return"".concat(u,"px ").concat(o-u,"px")}),rt(r,"getStrokeDasharray",function(o,u,c){var s=c.reduce(function(g,x){return g+x});if(!s)return r.generateSimpleStrokeDasharray(u,o);for(var f=Math.floor(o/s),l=o%s,h=u-o,p=[],y=0,v=0;y<c.length;v+=c[y],++y)if(v+c[y]>l){p=[].concat(dr(c.slice(0,y)),[l-v]);break}var d=p.length%2===0?[0,h]:[h];return[].concat(dr(t.repeat(c,f)),dr(p),d).map(function(g){return"".concat(g,"px")}).join(", ")}),rt(r,"id",Zr("recharts-line-")),rt(r,"pathRef",function(o){r.mainCurve=o}),rt(r,"handleAnimationEnd",function(){r.setState({isAnimationFinished:!0}),r.props.onAnimationEnd&&r.props.onAnimationEnd()}),rt(r,"handleAnimationStart",function(){r.setState({isAnimationFinished:!1}),r.props.onAnimationStart&&r.props.onAnimationStart()}),r}return EN(t,e),AN(t,[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();this.setState({totalLength:n})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();n!==this.state.totalLength&&this.setState({totalLength:n})}}},{key:"getTotalLength",value:function(){var n=this.mainCurve;try{return n&&n.getTotalLength&&n.getTotalLength()||0}catch{return 0}}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.points,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Ke(f,hi);if(!l)return null;var h=function(v,d){return{x:v.x,y:v.y,value:v.value,errorVal:Oe(v.payload,d)}},p={clipPath:n?"url(#clipPath-".concat(i,")"):null};return S.createElement(te,p,l.map(function(y){return S.cloneElement(y,{key:"bar-".concat(y.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,dataPointFormatter:h})}))}},{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive;if(o&&!this.state.isAnimationFinished)return null;var u=this.props,c=u.dot,s=u.points,f=u.dataKey,l=H(this.props,!1),h=H(c,!0),p=s.map(function(v,d){var g=Be(Be(Be({key:"dot-".concat(d),r:3},l),h),{},{index:d,cx:v.x,cy:v.y,value:v.value,dataKey:f,payload:v.payload,points:s});return t.renderDotItem(c,g)}),y={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return S.createElement(te,On({className:"recharts-line-dots",key:"dots"},y),p)}},{key:"renderCurveStatically",value:function(n,i,a,o){var u=this.props,c=u.type,s=u.layout,f=u.connectNulls;u.ref;var l=jb(u,yN),h=Be(Be(Be({},H(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:i?"url(#clipPath-".concat(a,")"):null,points:n},o),{},{type:c,layout:s,connectNulls:f});return S.createElement(ia,On({},h,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,c=o.strokeDasharray,s=o.isAnimationActive,f=o.animationBegin,l=o.animationDuration,h=o.animationEasing,p=o.animationId,y=o.animateNewValues,v=o.width,d=o.height,g=this.state,x=g.prevPoints,w=g.totalLength;return S.createElement(lt,{begin:f,duration:l,isActive:s,easing:h,from:{t:0},to:{t:1},key:"line-".concat(p),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(O){var m=O.t;if(x){var b=x.length/u.length,_=u.map(function(E,j){var C=Math.floor(j*b);if(x[C]){var $=x[C],k=ze($.x,E.x),R=ze($.y,E.y);return Be(Be({},E),{},{x:k(m),y:R(m)})}if(y){var L=ze(v*2,E.x),B=ze(d/2,E.y);return Be(Be({},E),{},{x:L(m),y:B(m)})}return Be(Be({},E),{},{x:E.x,y:E.y})});return a.renderCurveStatically(_,n,i)}var A=ze(0,w),T=A(m),M;if(c){var P="".concat(c).split(/[,\s]+/gim).map(function(E){return parseFloat(E)});M=a.getStrokeDasharray(T,w,P)}else M=a.generateSimpleStrokeDasharray(w,T);return a.renderCurveStatically(u,n,i,{strokeDasharray:M})})}},{key:"renderCurve",value:function(n,i){var a=this.props,o=a.points,u=a.isAnimationActive,c=this.state,s=c.prevPoints,f=c.totalLength;return u&&o&&o.length&&(!s&&f>0||!fi(s,o))?this.renderCurveWithAnimation(n,i):this.renderCurveStatically(o,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,c=i.className,s=i.xAxis,f=i.yAxis,l=i.top,h=i.left,p=i.width,y=i.height,v=i.isAnimationActive,d=i.id;if(a||!u||!u.length)return null;var g=this.state.isAnimationFinished,x=u.length===1,w=J("recharts-line",c),O=s&&s.allowDataOverflow,m=f&&f.allowDataOverflow,b=O||m,_=Y(d)?this.id:d,A=(n=H(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},T=A.r,M=T===void 0?3:T,P=A.strokeWidth,E=P===void 0?2:P,j=a_(o)?o:{},C=j.clipDot,$=C===void 0?!0:C,k=M*2+E;return S.createElement(te,{className:w},O||m?S.createElement("defs",null,S.createElement("clipPath",{id:"clipPath-".concat(_)},S.createElement("rect",{x:O?h:h-p/2,y:m?l:l-y/2,width:O?p:p*2,height:m?y:y*2})),!$&&S.createElement("clipPath",{id:"clipPath-dots-".concat(_)},S.createElement("rect",{x:h-k/2,y:l-k/2,width:p+k,height:y+k}))):null,!x&&this.renderCurve(b,_),this.renderErrorBar(b,_),(x||o)&&this.renderDots(b,$,_),(!v||g)&&wt.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,prevPoints:i.curPoints}:n.points!==i.curPoints?{curPoints:n.points}:null}},{key:"repeat",value:function(n,i){for(var a=n.length%2!==0?[].concat(dr(n),[0]):n,o=[],u=0;u<i;++u)o=[].concat(dr(o),dr(a));return o}},{key:"renderDotItem",value:function(n,i){var a;if(S.isValidElement(n))a=S.cloneElement(n,i);else if(X(n))a=n(i);else{var o=i.key,u=jb(i,mN),c=J("recharts-line-dot",typeof n!="boolean"?n.className:"");a=S.createElement(Za,On({key:o},u,{className:c}))}return a}}])}(q.PureComponent);rt(so,"displayName","Line");rt(so,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!or.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1});rt(so,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,i=e.xAxisTicks,a=e.yAxisTicks,o=e.dataKey,u=e.bandSize,c=e.displayedData,s=e.offset,f=t.layout,l=c.map(function(h,p){var y=Oe(h,o);return f==="horizontal"?{x:km({axis:r,ticks:i,bandSize:u,entry:h,index:p}),y:Y(y)?null:n.scale(y),value:y,payload:h}:{x:Y(y)?null:r.scale(y),y:km({axis:n,ticks:a,bandSize:u,entry:h,index:p}),value:y,payload:h}});return Be({points:l,layout:f},s)});function Hr(e){"@babel/helpers - typeof";return Hr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hr(e)}function MN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function $N(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Yw(n.key),n)}}function CN(e,t,r){return t&&$N(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function IN(e,t,r){return t=Sa(t),kN(e,Vw()?Reflect.construct(t,r||[],Sa(e).constructor):t.apply(e,r))}function kN(e,t){if(t&&(Hr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return RN(e)}function RN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Vw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Vw=function(){return!!e})()}function Sa(e){return Sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Sa(e)}function DN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tf(e,t)}function Tf(e,t){return Tf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Tf(e,t)}function Xw(e,t,r){return t=Yw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Yw(e){var t=NN(e,"string");return Hr(t)=="symbol"?t:t+""}function NN(e,t){if(Hr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Hr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Ef(){return Ef=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ef.apply(this,arguments)}function qN(e){var t=e.xAxisId,r=kh(),n=Rh(),i=kw(t);return i==null?null:S.createElement(rn,Ef({},i,{className:J("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return gt(o,!0)}}))}var Bh=function(e){function t(){return MN(this,t),IN(this,t,arguments)}return DN(t,e),CN(t,[{key:"render",value:function(){return S.createElement(qN,this.props)}}])}(S.Component);Xw(Bh,"displayName","XAxis");Xw(Bh,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Kr(e){"@babel/helpers - typeof";return Kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kr(e)}function LN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function BN(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Qw(n.key),n)}}function FN(e,t,r){return t&&BN(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function WN(e,t,r){return t=Pa(t),zN(e,Zw()?Reflect.construct(t,r||[],Pa(e).constructor):t.apply(e,r))}function zN(e,t){if(t&&(Kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return UN(e)}function UN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Zw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Zw=function(){return!!e})()}function Pa(e){return Pa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Pa(e)}function HN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&jf(e,t)}function jf(e,t){return jf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},jf(e,t)}function Jw(e,t,r){return t=Qw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Qw(e){var t=KN(e,"string");return Kr(t)=="symbol"?t:t+""}function KN(e,t){if(Kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function Mf(){return Mf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Mf.apply(this,arguments)}var GN=function(t){var r=t.yAxisId,n=kh(),i=Rh(),a=Rw(r);return a==null?null:S.createElement(rn,Mf({},a,{className:J("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return gt(u,!0)}}))},Fh=function(e){function t(){return LN(this,t),WN(this,t,arguments)}return HN(t,e),FN(t,[{key:"render",value:function(){return S.createElement(GN,this.props)}}])}(S.Component);Jw(Fh,"displayName","YAxis");Jw(Fh,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function Cb(e){return ZN(e)||YN(e)||XN(e)||VN()}function VN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function XN(e,t){if(e){if(typeof e=="string")return $f(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $f(e,t)}}function YN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ZN(e){if(Array.isArray(e))return $f(e)}function $f(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Cf=function(t,r,n,i,a){var o=Ke(t,Nh),u=Ke(t,oo),c=[].concat(Cb(o),Cb(u)),s=Ke(t,co),f="".concat(i,"Id"),l=i[0],h=r;if(c.length&&(h=c.reduce(function(v,d){if(d.props[f]===n&&ct(d.props,"extendDomain")&&N(d.props[l])){var g=d.props[l];return[Math.min(v[0],g),Math.max(v[1],g)]}return v},h)),s.length){var p="".concat(l,"1"),y="".concat(l,"2");h=s.reduce(function(v,d){if(d.props[f]===n&&ct(d.props,"extendDomain")&&N(d.props[p])&&N(d.props[y])){var g=d.props[p],x=d.props[y];return[Math.min(v[0],g,x),Math.max(v[1],g,x)]}return v},h)}return a&&a.length&&(h=a.reduce(function(v,d){return N(d)?[Math.min(v[0],d),Math.max(v[1],d)]:v},h)),h},ll={exports:{}},Ib;function JN(){return Ib||(Ib=1,function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,s,f){this.fn=c,this.context=s,this.once=f||!1}function a(c,s,f,l,h){if(typeof f!="function")throw new TypeError("The listener must be a function");var p=new i(f,l||c,h),y=r?r+s:s;return c._events[y]?c._events[y].fn?c._events[y]=[c._events[y],p]:c._events[y].push(p):(c._events[y]=p,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],f,l;if(this._eventsCount===0)return s;for(l in f=this._events)t.call(f,l)&&s.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(f)):s},u.prototype.listeners=function(s){var f=r?r+s:s,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var h=0,p=l.length,y=new Array(p);h<p;h++)y[h]=l[h].fn;return y},u.prototype.listenerCount=function(s){var f=r?r+s:s,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(s,f,l,h,p,y){var v=r?r+s:s;if(!this._events[v])return!1;var d=this._events[v],g=arguments.length,x,w;if(d.fn){switch(d.once&&this.removeListener(s,d.fn,void 0,!0),g){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,f),!0;case 3:return d.fn.call(d.context,f,l),!0;case 4:return d.fn.call(d.context,f,l,h),!0;case 5:return d.fn.call(d.context,f,l,h,p),!0;case 6:return d.fn.call(d.context,f,l,h,p,y),!0}for(w=1,x=new Array(g-1);w<g;w++)x[w-1]=arguments[w];d.fn.apply(d.context,x)}else{var O=d.length,m;for(w=0;w<O;w++)switch(d[w].once&&this.removeListener(s,d[w].fn,void 0,!0),g){case 1:d[w].fn.call(d[w].context);break;case 2:d[w].fn.call(d[w].context,f);break;case 3:d[w].fn.call(d[w].context,f,l);break;case 4:d[w].fn.call(d[w].context,f,l,h);break;default:if(!x)for(m=1,x=new Array(g-1);m<g;m++)x[m-1]=arguments[m];d[w].fn.apply(d[w].context,x)}}return!0},u.prototype.on=function(s,f,l){return a(this,s,f,l,!1)},u.prototype.once=function(s,f,l){return a(this,s,f,l,!0)},u.prototype.removeListener=function(s,f,l,h){var p=r?r+s:s;if(!this._events[p])return this;if(!f)return o(this,p),this;var y=this._events[p];if(y.fn)y.fn===f&&(!h||y.once)&&(!l||y.context===l)&&o(this,p);else{for(var v=0,d=[],g=y.length;v<g;v++)(y[v].fn!==f||h&&!y[v].once||l&&y[v].context!==l)&&d.push(y[v]);d.length?this._events[p]=d.length===1?d[0]:d:o(this,p)}return this},u.prototype.removeAllListeners=function(s){var f;return s?(f=r?r+s:s,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u}(ll)),ll.exports}var QN=JN();const e2=oe(QN);var fl=new e2,hl="recharts.syncMouseEvents";function ri(e){"@babel/helpers - typeof";return ri=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ri(e)}function t2(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r2(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,eO(n.key),n)}}function n2(e,t,r){return t&&r2(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function pl(e,t,r){return t=eO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eO(e){var t=i2(e,"string");return ri(t)=="symbol"?t:t+""}function i2(e,t){if(ri(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ri(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var a2=function(){function e(){t2(this,e),pl(this,"activeIndex",0),pl(this,"coordinateList",[]),pl(this,"layout","horizontal")}return n2(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,s=c===void 0?null:c,f=r.offset,l=f===void 0?null:f,h=r.mouseHandlerCallback,p=h===void 0?null:h;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=s??this.layout,this.offset=l??this.offset,this.mouseHandlerCallback=p??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,s=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+c+s,h=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:h})}}}])}();function o2(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&N(n)&&N(i))return!0}return!1}function u2(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function tO(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=le(t,r,n,i),u=le(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function c2(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,s=t.innerRadius,f=t.outerRadius,l=t.angle,h=le(u,c,s,l),p=le(u,c,f,l);n=h.x,i=h.y,a=p.x,o=p.y}else return tO(t);return[{x:n,y:i},{x:a,y:o}]}function ni(e){"@babel/helpers - typeof";return ni=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ni(e)}function kb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ti(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?kb(Object(r),!0).forEach(function(n){s2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):kb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function s2(e,t,r){return t=l2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l2(e){var t=f2(e,"string");return ni(t)=="symbol"?t:t+""}function f2(e,t){if(ni(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(ni(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function h2(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,s=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,h=e.chartName,p=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!p||!a||!o||h!=="ScatterChart"&&i!=="axis")return null;var y,v=ia;if(h==="ScatterChart")y=o,v=$I;else if(h==="BarChart")y=u2(l,o,c,f),v=Mh;else if(l==="radial"){var d=tO(o),g=d.cx,x=d.cy,w=d.radius,O=d.startAngle,m=d.endAngle;y={cx:g,cy:x,startAngle:O,endAngle:m,innerRadius:w,outerRadius:w},v=nw}else y={points:c2(l,o,c)},v=ia;var b=Ti(Ti(Ti(Ti({stroke:"#ccc",pointerEvents:"none"},c),y),H(p,!1)),{},{payload:u,payloadIndex:s,className:J("recharts-tooltip-cursor",p.className)});return q.isValidElement(p)?q.cloneElement(p,b):q.createElement(v,b)}var p2=["item"],d2=["children","className","width","height","style","compact","title","desc"];function Gr(e){"@babel/helpers - typeof";return Gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gr(e)}function br(){return br=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},br.apply(this,arguments)}function Rb(e,t){return m2(e)||y2(e,t)||nO(e,t)||v2()}function v2(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function y2(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function m2(e){if(Array.isArray(e))return e}function Db(e,t){if(e==null)return{};var r=g2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function g2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function b2(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function x2(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,iO(n.key),n)}}function w2(e,t,r){return t&&x2(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function O2(e,t,r){return t=Ta(t),_2(e,rO()?Reflect.construct(t,r||[],Ta(e).constructor):t.apply(e,r))}function _2(e,t){if(t&&(Gr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return A2(e)}function A2(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function rO(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(rO=function(){return!!e})()}function Ta(e){return Ta=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ta(e)}function S2(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&If(e,t)}function If(e,t){return If=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},If(e,t)}function Vr(e){return E2(e)||T2(e)||nO(e)||P2()}function P2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nO(e,t){if(e){if(typeof e=="string")return kf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return kf(e,t)}}function T2(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function E2(e){if(Array.isArray(e))return kf(e)}function kf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Nb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Nb(Object(r),!0).forEach(function(n){K(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Nb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function K(e,t,r){return t=iO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iO(e){var t=j2(e,"string");return Gr(t)=="symbol"?t:t+""}function j2(e,t){if(Gr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Gr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var M2={xAxis:["bottom","top"],yAxis:["left","right"]},$2={width:"100%",height:"100%"},aO={x:0,y:0};function Ei(e){return e}var C2=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},I2=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return I(I(I({},i),le(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,s=i.angle;return I(I(I({},i),le(i.cx,i.cy,c,s)),{},{angle:s,radius:c})}return aO},lo=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,c){var s=c.props.data;return s&&s.length?[].concat(Vr(u),Vr(s)):u},[]);return o.length>0?o:t&&t.length&&N(i)&&N(a)?t.slice(i,a+1):[]};function oO(e){return e==="number"?[0,"auto"]:void 0}var Rf=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=lo(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,s){var f,l=(f=s.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var h;if(o.dataKey&&!o.allowDuplicatedCategory){var p=l===void 0?u:l;h=Mi(p,o.dataKey,i)}else h=l&&l[n]||u[n];return h?[].concat(Vr(c),[Zx(s,h)]):c},[])},qb=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=C2(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,f=rM(o,u,s,c);if(f>=0&&s){var l=s[f]&&s[f].value,h=Rf(t,r,f,l),p=I2(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:h,activeCoordinate:p}}return null},k2=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=t.stackOffset,p=Hx(f,a);return n.reduce(function(y,v){var d,g=v.type.defaultProps!==void 0?I(I({},v.type.defaultProps),v.props):v.props,x=g.type,w=g.dataKey,O=g.allowDataOverflow,m=g.allowDuplicatedCategory,b=g.scale,_=g.ticks,A=g.includeHidden,T=g[o];if(y[T])return y;var M=lo(t.data,{graphicalItems:i.filter(function(W){var V,fe=o in W.props?W.props[o]:(V=W.type.defaultProps)===null||V===void 0?void 0:V[o];return fe===T}),dataStartIndex:c,dataEndIndex:s}),P=M.length,E,j,C;o2(g.domain,O,x)&&(E=Vl(g.domain,null,O),p&&(x==="number"||b!=="auto")&&(C=gn(M,w,"category")));var $=oO(x);if(!E||E.length===0){var k,R=(k=g.domain)!==null&&k!==void 0?k:$;if(w){if(E=gn(M,w,x),x==="category"&&p){var L=XO(E);m&&L?(j=E,E=da(0,P)):m||(E=qm(R,E,v).reduce(function(W,V){return W.indexOf(V)>=0?W:[].concat(Vr(W),[V])},[]))}else if(x==="category")m?E=E.filter(function(W){return W!==""&&!Y(W)}):E=qm(R,E,v).reduce(function(W,V){return W.indexOf(V)>=0||V===""||Y(V)?W:[].concat(Vr(W),[V])},[]);else if(x==="number"){var B=uM(M,i.filter(function(W){var V,fe,ye=o in W.props?W.props[o]:(V=W.type.defaultProps)===null||V===void 0?void 0:V[o],Le="hide"in W.props?W.props.hide:(fe=W.type.defaultProps)===null||fe===void 0?void 0:fe.hide;return ye===T&&(A||!Le)}),w,a,f);B&&(E=B)}p&&(x==="number"||b!=="auto")&&(C=gn(M,w,"category"))}else p?E=da(0,P):u&&u[T]&&u[T].hasStack&&x==="number"?E=h==="expand"?[0,1]:Yx(u[T].stackGroups,c,s):E=Ux(M,i.filter(function(W){var V=o in W.props?W.props[o]:W.type.defaultProps[o],fe="hide"in W.props?W.props.hide:W.type.defaultProps.hide;return V===T&&(A||!fe)}),x,f,!0);if(x==="number")E=Cf(l,E,T,a,_),R&&(E=Vl(R,E,O));else if(x==="category"&&R){var U=R,G=E.every(function(W){return U.indexOf(W)>=0});G&&(E=U)}}return I(I({},y),{},K({},T,I(I({},g),{},{axisType:a,domain:E,categoricalDomain:C,duplicateDomain:j,originalDomain:(d=g.domain)!==null&&d!==void 0?d:$,isCategorical:p,layout:f})))},{})},R2=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=lo(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:s}),p=h.length,y=Hx(f,a),v=-1;return n.reduce(function(d,g){var x=g.type.defaultProps!==void 0?I(I({},g.type.defaultProps),g.props):g.props,w=x[o],O=oO("number");if(!d[w]){v++;var m;return y?m=da(0,p):u&&u[w]&&u[w].hasStack?(m=Yx(u[w].stackGroups,c,s),m=Cf(l,m,w,a)):(m=Vl(O,Ux(h,n.filter(function(b){var _,A,T=o in b.props?b.props[o]:(_=b.type.defaultProps)===null||_===void 0?void 0:_[o],M="hide"in b.props?b.props.hide:(A=b.type.defaultProps)===null||A===void 0?void 0:A.hide;return T===w&&!M}),"number",f),i.defaultProps.allowDataOverflow),m=Cf(l,m,w,a)),I(I({},d),{},K({},w,I(I({axisType:a},i.defaultProps),{},{hide:!0,orientation:He(M2,"".concat(a,".").concat(v%2),null),domain:m,originalDomain:O,isCategorical:y,layout:f})))}return d},{})},D2=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),h=Ke(f,a),p={};return h&&h.length?p=k2(t,{axes:h,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(p=R2(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s})),p},N2=function(t){var r=Mt(t),n=gt(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:rh(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:ta(r,n)}},Lb=function(t){var r=t.children,n=t.defaultShowTooltip,i=We(r,qr),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},q2=function(t){return!t||!t.length?!1:t.some(function(r){var n=bt(r&&r.type);return n&&n.indexOf("Bar")>=0})},Bb=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},L2=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,s=n.width,f=n.height,l=n.children,h=n.margin||{},p=We(l,qr),y=We(l,wr),v=Object.keys(c).reduce(function(m,b){var _=c[b],A=_.orientation;return!_.mirror&&!_.hide?I(I({},m),{},K({},A,m[A]+_.width)):m},{left:h.left||0,right:h.right||0}),d=Object.keys(o).reduce(function(m,b){var _=o[b],A=_.orientation;return!_.mirror&&!_.hide?I(I({},m),{},K({},A,He(m,"".concat(A))+_.height)):m},{top:h.top||0,bottom:h.bottom||0}),g=I(I({},d),v),x=g.bottom;p&&(g.bottom+=p.props.height||qr.defaultProps.height),y&&r&&(g=aM(g,i,n,r));var w=s-g.left-g.right,O=f-g.top-g.bottom;return I(I({brushBottom:x},g),{},{width:Math.max(w,0),height:Math.max(O,0)})},B2=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},uO=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,h=function(g,x){var w=x.graphicalItems,O=x.stackGroups,m=x.offset,b=x.updateId,_=x.dataStartIndex,A=x.dataEndIndex,T=g.barSize,M=g.layout,P=g.barGap,E=g.barCategoryGap,j=g.maxBarSize,C=Bb(M),$=C.numericAxisName,k=C.cateAxisName,R=q2(w),L=[];return w.forEach(function(B,U){var G=lo(g.data,{graphicalItems:[B],dataStartIndex:_,dataEndIndex:A}),W=B.type.defaultProps!==void 0?I(I({},B.type.defaultProps),B.props):B.props,V=W.dataKey,fe=W.maxBarSize,ye=W["".concat($,"Id")],Le=W["".concat(k,"Id")],qt={},Re=c.reduce(function(Lt,Bt){var fo=x["".concat(Bt.axisType,"Map")],Wh=W["".concat(Bt.axisType,"Id")];fo&&fo[Wh]||Bt.axisType==="zAxis"||nr();var zh=fo[Wh];return I(I({},Lt),{},K(K({},Bt.axisType,zh),"".concat(Bt.axisType,"Ticks"),gt(zh)))},qt),F=Re[k],Z=Re["".concat(k,"Ticks")],Q=O&&O[ye]&&O[ye].hasStack&&yM(B,O[ye].stackGroups),D=bt(B.type).indexOf("Bar")>=0,de=ta(F,Z),ee=[],be=R&&nM({barSize:T,stackGroups:O,totalSize:B2(Re,k)});if(D){var xe,De,Et=Y(fe)?j:fe,lr=(xe=(De=ta(F,Z,!0))!==null&&De!==void 0?De:Et)!==null&&xe!==void 0?xe:0;ee=iM({barGap:P,barCategoryGap:E,bandSize:lr!==de?lr:de,sizeList:be[Le],maxBarSize:Et}),lr!==de&&(ee=ee.map(function(Lt){return I(I({},Lt),{},{position:I(I({},Lt.position),{},{offset:Lt.position.offset-lr/2})})}))}var di=B&&B.type&&B.type.getComposedData;di&&L.push({props:I(I({},di(I(I({},Re),{},{displayedData:G,props:g,dataKey:V,item:B,bandSize:de,barPosition:ee,offset:m,stackedData:Q,layout:M,dataStartIndex:_,dataEndIndex:A}))),{},K(K(K({key:B.key||"item-".concat(U)},$,Re[$]),k,Re[k]),"animationId",b)),childIndex:c_(B,g.children),item:B})}),L},p=function(g,x){var w=g.props,O=g.dataStartIndex,m=g.dataEndIndex,b=g.updateId;if(!Jp({props:w}))return null;var _=w.children,A=w.layout,T=w.stackOffset,M=w.data,P=w.reverseStackOrder,E=Bb(A),j=E.numericAxisName,C=E.cateAxisName,$=Ke(_,n),k=dM(M,$,"".concat(j,"Id"),"".concat(C,"Id"),T,P),R=c.reduce(function(W,V){var fe="".concat(V.axisType,"Map");return I(I({},W),{},K({},fe,D2(w,I(I({},V),{},{graphicalItems:$,stackGroups:V.axisType===j&&k,dataStartIndex:O,dataEndIndex:m}))))},{}),L=L2(I(I({},R),{},{props:w,graphicalItems:$}),x==null?void 0:x.legendBBox);Object.keys(R).forEach(function(W){R[W]=f(w,R[W],L,W.replace("Map",""),r)});var B=R["".concat(C,"Map")],U=N2(B),G=h(w,I(I({},R),{},{dataStartIndex:O,dataEndIndex:m,updateId:b,graphicalItems:$,stackGroups:k,offset:L}));return I(I({formattedGraphicalItems:G,graphicalItems:$,offset:L,stackGroups:k},U),R)},y=function(d){function g(x){var w,O,m;return b2(this,g),m=O2(this,g,[x]),K(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),K(m,"accessibilityManager",new a2),K(m,"handleLegendBBoxUpdate",function(b){if(b){var _=m.state,A=_.dataStartIndex,T=_.dataEndIndex,M=_.updateId;m.setState(I({legendBBox:b},p({props:m.props,dataStartIndex:A,dataEndIndex:T,updateId:M},I(I({},m.state),{},{legendBBox:b}))))}}),K(m,"handleReceiveSyncEvent",function(b,_,A){if(m.props.syncId===b){if(A===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(_)}}),K(m,"handleBrushChange",function(b){var _=b.startIndex,A=b.endIndex;if(_!==m.state.dataStartIndex||A!==m.state.dataEndIndex){var T=m.state.updateId;m.setState(function(){return I({dataStartIndex:_,dataEndIndex:A},p({props:m.props,dataStartIndex:_,dataEndIndex:A,updateId:T},m.state))}),m.triggerSyncEvent({dataStartIndex:_,dataEndIndex:A})}}),K(m,"handleMouseEnter",function(b){var _=m.getMouseInfo(b);if(_){var A=I(I({},_),{},{isTooltipActive:!0});m.setState(A),m.triggerSyncEvent(A);var T=m.props.onMouseEnter;X(T)&&T(A,b)}}),K(m,"triggeredAfterMouseMove",function(b){var _=m.getMouseInfo(b),A=_?I(I({},_),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(A),m.triggerSyncEvent(A);var T=m.props.onMouseMove;X(T)&&T(A,b)}),K(m,"handleItemMouseEnter",function(b){m.setState(function(){return{isTooltipActive:!0,activeItem:b,activePayload:b.tooltipPayload,activeCoordinate:b.tooltipPosition||{x:b.cx,y:b.cy}}})}),K(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),K(m,"handleMouseMove",function(b){b.persist(),m.throttleTriggeredAfterMouseMove(b)}),K(m,"handleMouseLeave",function(b){m.throttleTriggeredAfterMouseMove.cancel();var _={isTooltipActive:!1};m.setState(_),m.triggerSyncEvent(_);var A=m.props.onMouseLeave;X(A)&&A(_,b)}),K(m,"handleOuterEvent",function(b){var _=u_(b),A=He(m.props,"".concat(_));if(_&&X(A)){var T,M;/.*touch.*/i.test(_)?M=m.getMouseInfo(b.changedTouches[0]):M=m.getMouseInfo(b),A((T=M)!==null&&T!==void 0?T:{},b)}}),K(m,"handleClick",function(b){var _=m.getMouseInfo(b);if(_){var A=I(I({},_),{},{isTooltipActive:!0});m.setState(A),m.triggerSyncEvent(A);var T=m.props.onClick;X(T)&&T(A,b)}}),K(m,"handleMouseDown",function(b){var _=m.props.onMouseDown;if(X(_)){var A=m.getMouseInfo(b);_(A,b)}}),K(m,"handleMouseUp",function(b){var _=m.props.onMouseUp;if(X(_)){var A=m.getMouseInfo(b);_(A,b)}}),K(m,"handleTouchMove",function(b){b.changedTouches!=null&&b.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(b.changedTouches[0])}),K(m,"handleTouchStart",function(b){b.changedTouches!=null&&b.changedTouches.length>0&&m.handleMouseDown(b.changedTouches[0])}),K(m,"handleTouchEnd",function(b){b.changedTouches!=null&&b.changedTouches.length>0&&m.handleMouseUp(b.changedTouches[0])}),K(m,"handleDoubleClick",function(b){var _=m.props.onDoubleClick;if(X(_)){var A=m.getMouseInfo(b);_(A,b)}}),K(m,"handleContextMenu",function(b){var _=m.props.onContextMenu;if(X(_)){var A=m.getMouseInfo(b);_(A,b)}}),K(m,"triggerSyncEvent",function(b){m.props.syncId!==void 0&&fl.emit(hl,m.props.syncId,b,m.eventEmitterSymbol)}),K(m,"applySyncEvent",function(b){var _=m.props,A=_.layout,T=_.syncMethod,M=m.state.updateId,P=b.dataStartIndex,E=b.dataEndIndex;if(b.dataStartIndex!==void 0||b.dataEndIndex!==void 0)m.setState(I({dataStartIndex:P,dataEndIndex:E},p({props:m.props,dataStartIndex:P,dataEndIndex:E,updateId:M},m.state)));else if(b.activeTooltipIndex!==void 0){var j=b.chartX,C=b.chartY,$=b.activeTooltipIndex,k=m.state,R=k.offset,L=k.tooltipTicks;if(!R)return;if(typeof T=="function")$=T(L,b);else if(T==="value"){$=-1;for(var B=0;B<L.length;B++)if(L[B].value===b.activeLabel){$=B;break}}var U=I(I({},R),{},{x:R.left,y:R.top}),G=Math.min(j,U.x+U.width),W=Math.min(C,U.y+U.height),V=L[$]&&L[$].value,fe=Rf(m.state,m.props.data,$),ye=L[$]?{x:A==="horizontal"?L[$].coordinate:G,y:A==="horizontal"?W:L[$].coordinate}:aO;m.setState(I(I({},b),{},{activeLabel:V,activeCoordinate:ye,activePayload:fe,activeTooltipIndex:$}))}else m.setState(b)}),K(m,"renderCursor",function(b){var _,A=m.state,T=A.isTooltipActive,M=A.activeCoordinate,P=A.activePayload,E=A.offset,j=A.activeTooltipIndex,C=A.tooltipAxisBandSize,$=m.getTooltipEventType(),k=(_=b.props.active)!==null&&_!==void 0?_:T,R=m.props.layout,L=b.key||"_recharts-cursor";return S.createElement(h2,{key:L,activeCoordinate:M,activePayload:P,activeTooltipIndex:j,chartName:r,element:b,isActive:k,layout:R,offset:E,tooltipAxisBandSize:C,tooltipEventType:$})}),K(m,"renderPolarAxis",function(b,_,A){var T=He(b,"type.axisType"),M=He(m.state,"".concat(T,"Map")),P=b.type.defaultProps,E=P!==void 0?I(I({},P),b.props):b.props,j=M&&M[E["".concat(T,"Id")]];return q.cloneElement(b,I(I({},j),{},{className:J(T,j.className),key:b.key||"".concat(_,"-").concat(A),ticks:gt(j,!0)}))}),K(m,"renderPolarGrid",function(b){var _=b.props,A=_.radialLines,T=_.polarAngles,M=_.polarRadius,P=m.state,E=P.radiusAxisMap,j=P.angleAxisMap,C=Mt(E),$=Mt(j),k=$.cx,R=$.cy,L=$.innerRadius,B=$.outerRadius;return q.cloneElement(b,{polarAngles:Array.isArray(T)?T:gt($,!0).map(function(U){return U.coordinate}),polarRadius:Array.isArray(M)?M:gt(C,!0).map(function(U){return U.coordinate}),cx:k,cy:R,innerRadius:L,outerRadius:B,key:b.key||"polar-grid",radialLines:A})}),K(m,"renderLegend",function(){var b=m.state.formattedGraphicalItems,_=m.props,A=_.children,T=_.width,M=_.height,P=m.props.margin||{},E=T-(P.left||0)-(P.right||0),j=Wx({children:A,formattedGraphicalItems:b,legendWidth:E,legendContent:s});if(!j)return null;var C=j.item,$=Db(j,p2);return q.cloneElement(C,I(I({},$),{},{chartWidth:T,chartHeight:M,margin:P,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),K(m,"renderTooltip",function(){var b,_=m.props,A=_.children,T=_.accessibilityLayer,M=We(A,dt);if(!M)return null;var P=m.state,E=P.isTooltipActive,j=P.activeCoordinate,C=P.activePayload,$=P.activeLabel,k=P.offset,R=(b=M.props.active)!==null&&b!==void 0?b:E;return q.cloneElement(M,{viewBox:I(I({},k),{},{x:k.left,y:k.top}),active:R,label:$,payload:R?C:[],coordinate:j,accessibilityLayer:T})}),K(m,"renderBrush",function(b){var _=m.props,A=_.margin,T=_.data,M=m.state,P=M.offset,E=M.dataStartIndex,j=M.dataEndIndex,C=M.updateId;return q.cloneElement(b,{key:b.key||"_recharts-brush",onChange:_i(m.handleBrushChange,b.props.onChange),data:T,x:N(b.props.x)?b.props.x:P.left,y:N(b.props.y)?b.props.y:P.top+P.height+P.brushBottom-(A.bottom||0),width:N(b.props.width)?b.props.width:P.width,startIndex:E,endIndex:j,updateId:"brush-".concat(C)})}),K(m,"renderReferenceElement",function(b,_,A){if(!b)return null;var T=m,M=T.clipPathId,P=m.state,E=P.xAxisMap,j=P.yAxisMap,C=P.offset,$=b.type.defaultProps||{},k=b.props,R=k.xAxisId,L=R===void 0?$.xAxisId:R,B=k.yAxisId,U=B===void 0?$.yAxisId:B;return q.cloneElement(b,{key:b.key||"".concat(_,"-").concat(A),xAxis:E[L],yAxis:j[U],viewBox:{x:C.left,y:C.top,width:C.width,height:C.height},clipPathId:M})}),K(m,"renderActivePoints",function(b){var _=b.item,A=b.activePoint,T=b.basePoint,M=b.childIndex,P=b.isRange,E=[],j=_.props.key,C=_.item.type.defaultProps!==void 0?I(I({},_.item.type.defaultProps),_.item.props):_.item.props,$=C.activeDot,k=C.dataKey,R=I(I({index:M,dataKey:k,cx:A.x,cy:A.y,r:4,fill:jh(_.item),strokeWidth:2,stroke:"#fff",payload:A.payload,value:A.value},H($,!1)),$i($));return E.push(g.renderActiveDot($,R,"".concat(j,"-activePoint-").concat(M))),T?E.push(g.renderActiveDot($,I(I({},R),{},{cx:T.x,cy:T.y}),"".concat(j,"-basePoint-").concat(M))):P&&E.push(null),E}),K(m,"renderGraphicChild",function(b,_,A){var T=m.filterFormatItem(b,_,A);if(!T)return null;var M=m.getTooltipEventType(),P=m.state,E=P.isTooltipActive,j=P.tooltipAxis,C=P.activeTooltipIndex,$=P.activeLabel,k=m.props.children,R=We(k,dt),L=T.props,B=L.points,U=L.isRange,G=L.baseLine,W=T.item.type.defaultProps!==void 0?I(I({},T.item.type.defaultProps),T.item.props):T.item.props,V=W.activeDot,fe=W.hide,ye=W.activeBar,Le=W.activeShape,qt=!!(!fe&&E&&R&&(V||ye||Le)),Re={};M!=="axis"&&R&&R.props.trigger==="click"?Re={onClick:_i(m.handleItemMouseEnter,b.props.onClick)}:M!=="axis"&&(Re={onMouseLeave:_i(m.handleItemMouseLeave,b.props.onMouseLeave),onMouseEnter:_i(m.handleItemMouseEnter,b.props.onMouseEnter)});var F=q.cloneElement(b,I(I({},T.props),Re));function Z(Bt){return typeof j.dataKey=="function"?j.dataKey(Bt.payload):null}if(qt)if(C>=0){var Q,D;if(j.dataKey&&!j.allowDuplicatedCategory){var de=typeof j.dataKey=="function"?Z:"payload.".concat(j.dataKey.toString());Q=Mi(B,de,$),D=U&&G&&Mi(G,de,$)}else Q=B==null?void 0:B[C],D=U&&G&&G[C];if(Le||ye){var ee=b.props.activeIndex!==void 0?b.props.activeIndex:C;return[q.cloneElement(b,I(I(I({},T.props),Re),{},{activeIndex:ee})),null,null]}if(!Y(Q))return[F].concat(Vr(m.renderActivePoints({item:T,activePoint:Q,basePoint:D,childIndex:C,isRange:U})))}else{var be,xe=(be=m.getItemByXY(m.state.activeCoordinate))!==null&&be!==void 0?be:{graphicalItem:F},De=xe.graphicalItem,Et=De.item,lr=Et===void 0?b:Et,di=De.childIndex,Lt=I(I(I({},T.props),Re),{},{activeIndex:di});return[q.cloneElement(lr,Lt),null,null]}return U?[F,null,null]:[F,null]}),K(m,"renderCustomized",function(b,_,A){return q.cloneElement(b,I(I({key:"recharts-customized-".concat(A)},m.props),m.state))}),K(m,"renderMap",{CartesianGrid:{handler:Ei,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:Ei},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:Ei},YAxis:{handler:Ei},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((w=x.id)!==null&&w!==void 0?w:Zr("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=F0(m.triggeredAfterMouseMove,(O=x.throttleDelay)!==null&&O!==void 0?O:1e3/60),m.state={},m}return S2(g,d),w2(g,[{key:"componentDidMount",value:function(){var w,O;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(w=this.props.margin.left)!==null&&w!==void 0?w:0,top:(O=this.props.margin.top)!==null&&O!==void 0?O:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var w=this.props,O=w.children,m=w.data,b=w.height,_=w.layout,A=We(O,dt);if(A){var T=A.props.defaultIndex;if(!(typeof T!="number"||T<0||T>this.state.tooltipTicks.length-1)){var M=this.state.tooltipTicks[T]&&this.state.tooltipTicks[T].value,P=Rf(this.state,m,T,M),E=this.state.tooltipTicks[T].coordinate,j=(this.state.offset.top+b)/2,C=_==="horizontal",$=C?{x:E,y:j}:{y:E,x:j},k=this.state.formattedGraphicalItems.find(function(L){var B=L.item;return B.type.name==="Scatter"});k&&($=I(I({},$),k.props.points[T].tooltipPosition),P=k.props.points[T].tooltipPayload);var R={activeTooltipIndex:T,isTooltipActive:!0,activeLabel:M,activePayload:P,activeCoordinate:$};this.setState(R),this.renderCursor(A),this.accessibilityManager.setIndex(T)}}}},{key:"getSnapshotBeforeUpdate",value:function(w,O){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==O.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==w.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==w.margin){var m,b;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(b=this.props.margin.top)!==null&&b!==void 0?b:0}})}return null}},{key:"componentDidUpdate",value:function(w){yl([We(w.children,dt)],[We(this.props.children,dt)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var w=We(this.props.children,dt);if(w&&typeof w.props.shared=="boolean"){var O=w.props.shared?"axis":"item";return u.indexOf(O)>=0?O:a}return a}},{key:"getMouseInfo",value:function(w){if(!this.container)return null;var O=this.container,m=O.getBoundingClientRect(),b=VS(m),_={chartX:Math.round(w.pageX-b.left),chartY:Math.round(w.pageY-b.top)},A=m.width/O.offsetWidth||1,T=this.inRange(_.chartX,_.chartY,A);if(!T)return null;var M=this.state,P=M.xAxisMap,E=M.yAxisMap,j=this.getTooltipEventType(),C=qb(this.state,this.props.data,this.props.layout,T);if(j!=="axis"&&P&&E){var $=Mt(P).scale,k=Mt(E).scale,R=$&&$.invert?$.invert(_.chartX):null,L=k&&k.invert?k.invert(_.chartY):null;return I(I({},_),{},{xValue:R,yValue:L},C)}return C?I(I({},_),C):null}},{key:"inRange",value:function(w,O){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,b=this.props.layout,_=w/m,A=O/m;if(b==="horizontal"||b==="vertical"){var T=this.state.offset,M=_>=T.left&&_<=T.left+T.width&&A>=T.top&&A<=T.top+T.height;return M?{x:_,y:A}:null}var P=this.state,E=P.angleAxisMap,j=P.radiusAxisMap;if(E&&j){var C=Mt(E);return Fm({x:_,y:A},C)}return null}},{key:"parseEventsOfWrapper",value:function(){var w=this.props.children,O=this.getTooltipEventType(),m=We(w,dt),b={};m&&O==="axis"&&(m.props.trigger==="click"?b={onClick:this.handleClick}:b={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var _=$i(this.props,this.handleOuterEvent);return I(I({},_),b)}},{key:"addListener",value:function(){fl.on(hl,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){fl.removeListener(hl,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(w,O,m){for(var b=this.state.formattedGraphicalItems,_=0,A=b.length;_<A;_++){var T=b[_];if(T.item===w||T.props.key===w.key||O===bt(T.item.type)&&m===T.childIndex)return T}return null}},{key:"renderClipPath",value:function(){var w=this.clipPathId,O=this.state.offset,m=O.left,b=O.top,_=O.height,A=O.width;return S.createElement("defs",null,S.createElement("clipPath",{id:w},S.createElement("rect",{x:m,y:b,height:_,width:A})))}},{key:"getXScales",value:function(){var w=this.state.xAxisMap;return w?Object.entries(w).reduce(function(O,m){var b=Rb(m,2),_=b[0],A=b[1];return I(I({},O),{},K({},_,A.scale))},{}):null}},{key:"getYScales",value:function(){var w=this.state.yAxisMap;return w?Object.entries(w).reduce(function(O,m){var b=Rb(m,2),_=b[0],A=b[1];return I(I({},O),{},K({},_,A.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(w){var O;return(O=this.state.xAxisMap)===null||O===void 0||(O=O[w])===null||O===void 0?void 0:O.scale}},{key:"getYScaleByAxisId",value:function(w){var O;return(O=this.state.yAxisMap)===null||O===void 0||(O=O[w])===null||O===void 0?void 0:O.scale}},{key:"getItemByXY",value:function(w){var O=this.state,m=O.formattedGraphicalItems,b=O.activeItem;if(m&&m.length)for(var _=0,A=m.length;_<A;_++){var T=m[_],M=T.props,P=T.item,E=P.type.defaultProps!==void 0?I(I({},P.type.defaultProps),P.props):P.props,j=bt(P.type);if(j==="Bar"){var C=(M.data||[]).find(function(L){return fI(w,L)});if(C)return{graphicalItem:T,payload:C}}else if(j==="RadialBar"){var $=(M.data||[]).find(function(L){return Fm(w,L)});if($)return{graphicalItem:T,payload:$}}else if(ro(T,b)||no(T,b)||Zn(T,b)){var k=Rk({graphicalItem:T,activeTooltipItem:b,itemData:E.data}),R=E.activeIndex===void 0?k:E.activeIndex;return{graphicalItem:I(I({},T),{},{childIndex:R}),payload:Zn(T,b)?E.data[k]:T.props.data[k]}}}return null}},{key:"render",value:function(){var w=this;if(!Jp(this))return null;var O=this.props,m=O.children,b=O.className,_=O.width,A=O.height,T=O.style,M=O.compact,P=O.title,E=O.desc,j=Db(O,d2),C=H(j,!1);if(M)return S.createElement(yb,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},S.createElement(gl,br({},C,{width:_,height:A,title:P,desc:E}),this.renderClipPath(),ed(m,this.renderMap)));if(this.props.accessibilityLayer){var $,k;C.tabIndex=($=this.props.tabIndex)!==null&&$!==void 0?$:0,C.role=(k=this.props.role)!==null&&k!==void 0?k:"application",C.onKeyDown=function(L){w.accessibilityManager.keyboardEvent(L)},C.onFocus=function(){w.accessibilityManager.focus()}}var R=this.parseEventsOfWrapper();return S.createElement(yb,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},S.createElement("div",br({className:J("recharts-wrapper",b),style:I({position:"relative",cursor:"default",width:_,height:A},T)},R,{ref:function(B){w.container=B}}),S.createElement(gl,br({},C,{width:_,height:A,title:P,desc:E,style:$2}),this.renderClipPath(),ed(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(q.Component);K(y,"displayName",r),K(y,"defaultProps",I({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),K(y,"getDerivedStateFromProps",function(d,g){var x=d.dataKey,w=d.data,O=d.children,m=d.width,b=d.height,_=d.layout,A=d.stackOffset,T=d.margin,M=g.dataStartIndex,P=g.dataEndIndex;if(g.updateId===void 0){var E=Lb(d);return I(I(I({},E),{},{updateId:0},p(I(I({props:d},E),{},{updateId:0}),g)),{},{prevDataKey:x,prevData:w,prevWidth:m,prevHeight:b,prevLayout:_,prevStackOffset:A,prevMargin:T,prevChildren:O})}if(x!==g.prevDataKey||w!==g.prevData||m!==g.prevWidth||b!==g.prevHeight||_!==g.prevLayout||A!==g.prevStackOffset||!xr(T,g.prevMargin)){var j=Lb(d),C={chartX:g.chartX,chartY:g.chartY,isTooltipActive:g.isTooltipActive},$=I(I({},qb(g,w,_)),{},{updateId:g.updateId+1}),k=I(I(I({},j),C),$);return I(I(I({},k),p(I({props:d},k),g)),{},{prevDataKey:x,prevData:w,prevWidth:m,prevHeight:b,prevLayout:_,prevStackOffset:A,prevMargin:T,prevChildren:O})}if(!yl(O,g.prevChildren)){var R,L,B,U,G=We(O,qr),W=G&&(R=(L=G.props)===null||L===void 0?void 0:L.startIndex)!==null&&R!==void 0?R:M,V=G&&(B=(U=G.props)===null||U===void 0?void 0:U.endIndex)!==null&&B!==void 0?B:P,fe=W!==M||V!==P,ye=!Y(w),Le=ye&&!fe?g.updateId:g.updateId+1;return I(I({updateId:Le},p(I(I({props:d},g),{},{updateId:Le,dataStartIndex:W,dataEndIndex:V}),g)),{},{prevChildren:O,dataStartIndex:W,dataEndIndex:V})}return null}),K(y,"renderActiveDot",function(d,g,x){var w;return q.isValidElement(d)?w=q.cloneElement(d,g):X(d)?w=d(g):w=S.createElement(Za,g),S.createElement(te,{className:"recharts-active-dot",key:x},w)});var v=q.forwardRef(function(g,x){return S.createElement(y,br({},g,{ref:x}))});return v.displayName=y.displayName,v},U2=uO({chartName:"LineChart",GraphicalChild:so,axisComponents:[{axisType:"xAxis",AxisComp:Bh},{axisType:"yAxis",AxisComp:Fh}],formatAxisMap:qR}),H2=uO({chartName:"PieChart",GraphicalChild:Nt,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:to},{axisType:"radiusAxis",AxisComp:Qa}],formatAxisMap:PM,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});export{ih as C,U2 as L,H2 as P,z2 as R,dt as T,Bh as X,Fh as Y,Nt as a,vN as b,J as c,so as d,wr as e};
